import React, { useState, useEffect, useContext, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Platform,
  Alert,
  Image,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../utils/theme';
import { TopBar } from './TopBar';
import { 
  startTaskActivity, 
  isEmployeeActiveInTask,
  getActiveEmployeesForTask,
  completeTaskActivity,
  completeTaskActivityByEmployeeAndTask,
  TaskActivity,
  updateTaskActiveEmployeesCount,
  stopTaskActivity
} from '../services/timeTrackingService';
import { i18n } from '../utils/localization';
import * as Location from 'expo-location';

interface TaskDetailsProps {
  taskId: string;
  onBack: () => void;
  onMenuPress: () => void;
  onLogoPress: () => void; // Add new prop for logo press
  onEditTask?: (taskId: string) => void; // Dodajemy opcjonalną funkcję onEditTask
}

interface Task {
  id: string;
  date: string;
  client_name: string;
  address: string;
  work_scope: string;
  start_time: string;
  status: string;
  assigned_employees: string[];
  additional_info?: string;
  status_changed_at?: string;
  task_duration_minutes?: number;
  started_at?: string;
  completed_at?: string;
  active_employees_count?: number;
}

interface Employee {
  id: string;
  full_name: string;
  email?: string;
  phone?: string;
  // Dodatkowe pola do przechowywania danych o aktywności
  taskActivity?: {
    start_time?: string;
    end_time?: string | null;
    duration_minutes?: number | null;
    status?: 'active' | 'completed';
  };
}

// Interface for work session
interface WorkSession {
  id: string;
  employee_id: string;
  task_id: string;
  start_time: string;
  end_time: string | null;
  job_order: string;
}

// Interface for task times
interface TaskTime {
  id: string;
  task_id: string;
  employee_id: string;
  start_time: string;
  end_time: string | null;
  duration_minutes: number | null;
  status: 'active' | 'completed';
}

// Status options for the dropdown
const StatusOptions = [
  { label: i18n.t('pending'), value: 'pending' },
  { label: i18n.t('inProgress'), value: 'in_progress' },
  { label: i18n.t('completed'), value: 'completed' },
];

/**
 * Pobiera aktualną lokalizację użytkownika za pomocą expo-location
 * @returns Obiekt z danymi lokalizacyjnymi lub null jeśli lokalizacja jest niedostępna
 */
const getCurrentLocation = async (): Promise<{
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
} | null> => {
  try {
    // Poproś o pozwolenie na dostęp do lokalizacji
    const { status } = await Location.requestForegroundPermissionsAsync();
    
    if (status !== 'granted') {
      console.error('Nie udzielono pozwolenia na dostęp do lokalizacji');
      Alert.alert('Brak dostępu do lokalizacji', 
        'Aby zapisywać lokalizację podczas rozpoczynania i kończenia pracy, aplikacja potrzebuje dostępu do Twojej lokalizacji.');
      return null;
    }
    
    console.log('Pobieranie lokalizacji...');
    
    // Pobierz aktualną lokalizację
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High
    });
    
    const locationData = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: location.coords.accuracy || 0, // W przypadku null, używamy 0 jako wartość domyślną
      timestamp: location.timestamp
    };
    
    console.log('Pobrana lokalizacja:', locationData);
    return locationData;
  } catch (error) {
    console.error('Błąd pobierania lokalizacji:', error);
    return null;
  }
};

const TaskDetails: React.FC<TaskDetailsProps> = ({ 
  taskId, 
  onBack, 
  onMenuPress,
  onLogoPress, // Add this new prop
  onEditTask
}) => {
  const [loading, setLoading] = useState(true);
  const [task, setTask] = useState<Task | null>(null);
  const [taskEmployees, setTaskEmployees] = useState<Employee[]>([]);
  const [taskTimes, setTaskTimes] = useState<TaskTime[]>([]);
  const [statusLoading, setStatusLoading] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [photosBefore, setPhotosBefore] = useState<string[]>([]);
  const [photosAfter, setPhotosAfter] = useState<string[]>([]);
  const [activeEmployees, setActiveEmployees] = useState<string[]>([]);
  const [activeWorkSessions, setActiveWorkSessions] = useState<Record<string, WorkSession>>({});
  const [shouldShowJoinButton, setShouldShowJoinButton] = useState(false);
  const [currentEmployeeData, setCurrentEmployeeData] = useState<{ id: string, isActive: boolean, isAssigned: boolean } | null>(null);
  const [activeEmployeeIds, setActiveEmployeeIds] = useState<Record<string, boolean>>({});
  const [refreshTimestamp, setRefreshTimestamp] = useState<number>(0);
  const [userRole, setUserRole] = useState<'company' | 'employee' | 'coordinator' | null>(null);
  
  // Nowe zmienne stanu do obsługi powiększania zdjęć
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [photoModalVisible, setPhotoModalVisible] = useState(false);

  // Funkcje do obsługi powiększania zdjęć
  const openPhotoModal = (photoUrl: string) => {
    setSelectedImage(photoUrl);
    setPhotoModalVisible(true);
  };

  const closePhotoModal = () => {
    setPhotoModalVisible(false);
    setSelectedImage(null);
  };

  // Modify the initialFetch function in the first useEffect
  useEffect(() => {
    // Instead, just fetch data once when component mounts
    const initialFetch = async () => {
      try {
        setLoading(true);
        
        // First, get the current user
        const { data: { user } } = await supabase.auth.getUser();
        const userId = user?.id;
        let employeeId: string | null = null;
        
        if (user) {
          console.log(`Current user ID: ${userId}`);
          
          // Pobierz typ użytkownika
          const userType = user.user_metadata.user_type;
          setUserRole(userType);
          console.log(`User role: ${userType}`);
          
          // Check for active task activity with the current user's ID
          if (userId && taskId) {
            const isUserActive = await checkIfUserActiveInTask(userId, taskId);
            
            if (isUserActive) {
              // If user is active, set activeEmployees and hide the join button
              setActiveEmployees(prev => [...prev, userId as string]);
              setActiveEmployeeIds(prev => ({ ...prev, [userId as string]: true }));
              setShouldShowJoinButton(false);
            }
            
            // Try to get the employee ID for this user
            try {
              // Instead of directly querying by user_id, first check if user_id exists in the employees table
              let employeeData = null;
              
              // Try getting the employee by matching user_id to the actual user ID
              // This could fail silently if user_id column doesn't exist
              try {
                const { data } = await supabase
                  .from('employees')
                  .select('id')
                  .eq('id', userId);  // Try this first as it's more likely to work
                  
                if (data && data.length > 0) {
                  employeeId = data[0].id;
                  console.log(`User ID ${userId} is directly used as employee ID`);
                }
              } catch (directLookupError) {
                console.log('Direct ID lookup failed, trying alternatives');
              }
              
              // If that didn't work, try the company_id approach as fallback
              if (!employeeId) {
                const companyId = user?.user_metadata?.company_id;
                if (companyId) {
                  const { data: alternativeData } = await supabase
                    .from('employees')
                    .select('id')
                    .eq('company_id', companyId)
                    .limit(1);
                    
                  if (alternativeData && alternativeData.length > 0) {
                    employeeId = alternativeData[0].id;
                    console.log(`Found alternative employee ID: ${employeeId} for company ID: ${companyId}`);
                  }
                }
              }
            } catch (error) {
              console.error('Error finding employee ID:', error);
            }
            
            // If we still didn't find the employee ID, try the alternative method
            if (!employeeId) {
              try {
                const companyId = user?.user_metadata?.company_id;
                if (companyId) {
                  const { data: alternativeData } = await supabase
                    .from('employees')
                    .select('id')
                    .eq('company_id', companyId)
                    .limit(1);
                    
                  if (alternativeData && alternativeData.length > 0) {
                    employeeId = alternativeData[0].id;
                    console.log(`Found alternative employee ID: ${employeeId} for company ID: ${companyId}`);
                  }
                }
              } catch (error) {
                console.error('Error in alternative employee lookup:', error);
              }
            }
            
            // If we found an employee ID, check if that's active too
            if (employeeId && employeeId !== userId) {
              const isEmployeeActive = await checkIfUserActiveInTask(employeeId, taskId);
              
              if (isEmployeeActive) {
                // If employee is active, set activeEmployees and hide the join button
                setActiveEmployees(prev => [...prev, employeeId as string]);
                setActiveEmployeeIds(prev => ({ ...prev, [employeeId as string]: true }));
                setShouldShowJoinButton(false);
              }
            }
            
            // Set currentEmployeeData with the best ID we have and what we know about activity
            const bestId = employeeId || userId;
            if (bestId) {
              const isActive = await checkIfUserActiveInTask(bestId, taskId);
              setCurrentEmployeeData({
                id: bestId,
                isActive,
                isAssigned: false // We'll update this after fetching task details
              });
            }
          }
        }
        
        // Fetch all the data we need
        const taskDetailsResult = await fetchTaskDetailsWithoutStateUpdate();
        const taskTimesResult = await fetchTaskTimesWithoutStateUpdate();
        const activeWorkSessionsResult = await fetchActiveWorkSessionsWithoutStateUpdate();
        const activeEmployeesResult = await fetchActiveEmployeesWithoutStateUpdate();
        
        if (taskDetailsResult) {
          setTask(taskDetailsResult.task);
          setTaskEmployees(taskDetailsResult.employees);
          setPhotosBefore(taskDetailsResult.photosBefore || []);
          setPhotosAfter(taskDetailsResult.photosAfter || []);
          
          if (activeWorkSessionsResult) {
            // Poprawne przypisanie activeWorkSessions
            setActiveWorkSessions(activeWorkSessionsResult.sessionsMap || {});
          }
          
          // Update current employee status based on a combination of all the data we have
          if (taskDetailsResult.task && currentEmployeeData && (userId || employeeId)) {
            // Check if either the user ID or employee ID is in the assigned employees list
            const isAssignedByUserId = taskDetailsResult.task.assigned_employees?.includes(userId) || false;
            const isAssignedByEmployeeId = employeeId ? taskDetailsResult.task.assigned_employees?.includes(employeeId) : false;
            const isAssigned = isAssignedByUserId || isAssignedByEmployeeId || false;
            
            // Check if the user is in the active employees list from any source
            const isActiveInList = 
              (userId && activeEmployeesResult?.activeEmployeesList.includes(userId)) || 
              (employeeId && activeEmployeesResult?.activeEmployeesList.includes(employeeId)) || 
              (currentEmployeeData?.isActive) || 
              false;
            
            // Update currentEmployeeData
            setCurrentEmployeeData({
              id: userId || '', // Zapewnij, że ID nigdy nie jest undefined
              isActive: isActiveInList,
              isAssigned: isAssigned
            });
            
            // If employee is assigned but not active, and task is in progress, show Join button
            // Otherwise, hide it
            const taskInProgress = taskDetailsResult.task.status === 'in_progress';
            const shouldShow = isAssigned && !isActiveInList && taskInProgress;
            
            console.log(`Final button visibility check: isAssigned=${isAssigned}, !isActiveInList=${!isActiveInList}, taskInProgress=${taskInProgress}, shouldShow=${shouldShow}`);
            
            setShouldShowJoinButton(shouldShow);
          }
        }
        
        if (taskTimesResult) {
          setTaskTimes(taskTimesResult);
        }

        // Pobierz dane o aktywności pracowników po ustawieniu wszystkich pozostałych danych
        setTimeout(() => {
          fetchEmployeeActivityData();
        }, 100);
        
      } catch (error) {
        console.error('Error in initialFetch:', error);
      } finally {
        setLoading(false);
      }
    };
    
    if (taskId) {
      console.log(`TaskDetails: mounting with taskId ${taskId}`);
      initialFetch();
    }
    
    // Cleanup function
    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [taskId]);

  // Disable the useEffect that sets up active sessions polling
  useEffect(() => {
    console.log('Polling aktywnych sesji zostało wyłączone');
    
    return () => {};
  }, [taskId]);

  // Modify the useEffect to ensure we have fresh data whenever task or taskId changes
  useEffect(() => {
    if (taskId) {
      console.log(`TaskDetails: taskId zmienione na ${taskId}, odświeżam dane...`);
      // Odświeżamy wszystkie potrzebne dane
      const refresh = async () => {
        await fetchTaskDetailsWithoutStateUpdate();
        await fetchTaskTimesWithoutStateUpdate();
        await fetchActiveEmployeesWithoutStateUpdate();
      };
      refresh();
    }
  }, [taskId]);

  // Add explicit refresh when task status changes
  useEffect(() => {
    if (task?.status === 'in_progress') {
      console.log(`TaskDetails: Status zadania to "in_progress", odświeżam aktywnych pracowników`);
      fetchActiveEmployees();
    }
  }, [task?.status]);

  // Move debugCheckAllSessions to component scope
  const debugCheckAllSessions = async () => {
    try {
      // 1. Get current user info
      const { data: { user } } = await supabase.auth.getUser();
      console.log('DEBUG - Current user:', user?.id, 'User metadata:', user?.user_metadata);
      
      // 2. Check all work sessions without filters to see what's available
      const { data, error } = await supabase
        .from('work_sessions')
        .select('*');
      
      if (error) {
        console.error('Error checking all work sessions:', error);
        return;
      }
      
      console.log('DEBUG - All work sessions in database (including completed):', data?.length || 0);
      
      // 3. Check active work sessions
      const { data: activeData, error: activeError } = await supabase
            .from('work_sessions')
            .select('*')
            .is('end_time', null);
            
      if (activeError) {
        console.error('Error checking active work sessions:', activeError);
        return;
      }
      
      console.log('DEBUG - All active work sessions in database:', activeData);
      
      // 4. Check for this task with explicit company_id filter
      if (user?.user_metadata?.company_id && activeData) {
        const companyId = user.user_metadata.company_id;
        const forThisCompany = activeData.filter(s => s.company_id === companyId);
        console.log(`DEBUG - ${forThisCompany.length} active sessions for company ${companyId}:`, forThisCompany);
        
        const forThisTask = activeData.filter(s => s.task_id === taskId);
        console.log(`DEBUG - ${forThisTask.length} active sessions for task ${taskId}:`, forThisTask);
        
        // 5. Direct, manual update of activeWorkSessions state
        if (forThisTask.length > 0) {
          console.log('DEBUG - Manually updating activeWorkSessions with found sessions');
          const sessionsMap: Record<string, WorkSession> = {};
          forThisTask.forEach(session => {
            sessionsMap[session.employee_id] = session;
          });
          setActiveWorkSessions(sessionsMap);
        }
      }
    } catch (e) {
      console.error('Exception in debug check:', e);
    }
  };
  
  // Add a debug function to help troubleshoot employee status issues
  const logEmployeeStatus = () => {
    console.log('Current active work sessions:', activeWorkSessions);
    console.log('Task employees:', taskEmployees);
    
    taskEmployees.forEach(employee => {
      console.log(`Employee ${employee.full_name} (${employee.id}) active status:`, isEmployeeActive(employee.id));
    });
  };
  
  // Add a debug effect that logs employee status when activeWorkSessions changes
  useEffect(() => {
    if (Object.keys(activeWorkSessions).length > 0) {
      console.log('Active work sessions updated:', activeWorkSessions);
      logEmployeeStatus();
    }
  }, [activeWorkSessions, taskEmployees]);

  const fetchTaskDetailsWithoutStateUpdate = async () => {
    if (!taskId) return null;
    
    try {
      // Fetch task details
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError) {
        console.error('Error fetching task details:', taskError);
        return null;
      }
      
      // Debug log
      console.log('Task data fetched:', taskData);
      console.log('Photos before:', taskData.photos_before);
      console.log('Photos after:', taskData.photos_after);
        
      // Fetch assigned employees - tylko aktywni pracownicy
      let employees = [];
      if (taskData.assigned_employees && taskData.assigned_employees.length > 0) {
        const { data: employeesData, error: employeesError } = await supabase
          .from('employees')
          .select('*')
          .in('id', taskData.assigned_employees)
          .eq('subscription_status', 'ACTIVE'); // Tylko aktywni pracownicy

        if (employeesError) {
          console.error('Error fetching assigned employees:', employeesError);
        } else {
          employees = employeesData || [];
        }
      }
      
      return {
        task: taskData,
        employees,
        photosBefore: taskData.photos_before || [],
        photosAfter: taskData.photos_after || []
      };
    } catch (error) {
      console.error('Exception in fetchTaskDetailsWithoutStateUpdate:', error);
      return null;
    }
  };

  const fetchTaskTimesWithoutStateUpdate = async () => {
    if (!taskId) return null;

    try {
      const { data, error } = await supabase
        .from('task_times')
        .select('*')
        .eq('task_id', taskId)
        .order('start_time', { ascending: false });

      if (error) {
        console.error('Error fetching task times:', error);
        return null;
      }
      
      return data || [];
    } catch (error) {
      console.error('Exception in fetchTaskTimesWithoutStateUpdate:', error);
      return null;
    }
  };

  const fetchActiveWorkSessionsWithoutStateUpdate = async () => {
    if (!taskId) return null;
    
    try {
      // Pobierz aktywne sesje pracy dla tego zadania
      const { data, error } = await supabase
        .from('work_sessions')
        .select('*')
        .eq('task_id', taskId)
        .is('end_time', null);
      
      if (error) {
        console.error('fetchActiveWorkSessionsWithoutStateUpdate: Błąd pobierania aktywnych sesji:', error);
        return null;
      }
      
      // Przekształć dane na mapę dla łatwiejszego dostępu
      const sessionsMap: Record<string, WorkSession> = {};
      const activeEmployeesList: string[] = [];
      
      if (data && data.length > 0) {
        data.forEach(session => {
          sessionsMap[session.employee_id] = session;
          if (!activeEmployeesList.includes(session.employee_id)) {
            activeEmployeesList.push(session.employee_id);
          }
        });
      }
      
      return { sessionsMap, activeEmployeesList };
    } catch (error) {
      console.error('fetchActiveWorkSessionsWithoutStateUpdate: Nieoczekiwany błąd:', error);
      return null;
    }
  };

  const fetchActiveEmployeesWithoutStateUpdate = async () => {
    try {
    if (!taskId) {
        console.error('fetchActiveEmployeesWithoutStateUpdate: Brak ID zadania');
        return null;
      }
      
      // 1. Pobierz aktywnych pracowników w zadaniu bezpośrednio z bazy danych
      const activeEmployeesList = await getActiveEmployeesForTask(taskId);
      
      // 2. Pobierz licznik aktywnych pracowników (using our local function instead)
      const activeCount = await updateActiveEmployeesCount();
      
      return { activeEmployeesList, activeCount };
    } catch (error) {
      console.error('fetchActiveEmployeesWithoutStateUpdate: Nieoczekiwany błąd:', error);
      return null;
    }
  };

  // Create a version of checkCurrentEmployeeStatus that only runs once
  const checkCurrentEmployeeStatusOnce = async () => {
    // Sprawdzamy tylko czy task istnieje, nie sprawdzamy jego statusu
    if (!task) {
      setShouldShowJoinButton(false);
      setCurrentEmployeeData(null);
        return;
      }
      
    try {
      // Pobierz aktualnego użytkownika
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setShouldShowJoinButton(false);
        setCurrentEmployeeData(null);
        return;
      }
      
      // Pobierz dane pracownika
      let employeeId: string | null = null;
      
      try {
        // First try looking up by user's ID directly
        const { data: directMatch } = await supabase
          .from('employees')
          .select('id')
          .eq('id', user.id);
        
        if (directMatch && directMatch.length > 0) {
          employeeId = directMatch[0].id;
          console.log(`User ID ${user.id} is directly used as employee ID`);
        }
      } catch (error) {
        // If that fails, we'll try alternative methods
        console.log(`Failed to look up employee by ID=${user.id}, trying alternatives`);
      }
      
      if (!employeeId) {
        // Try alternative method - look up by company_id
        const companyId = user?.user_metadata?.company_id;
        if (companyId) {
          try {
            const { data: companyMatch } = await supabase
              .from('employees')
              .select('id')
              .eq('company_id', companyId)
              .limit(1);
            
            if (companyMatch && companyMatch.length > 0) {
              employeeId = companyMatch[0].id;
              console.log(`Found employee ID ${employeeId} using company_id=${companyId}`);
            }
          } catch (error) {
            console.log(`Failed to look up employee by company_id=${companyId}`);
          }
        }
      }
      
      // If all lookups fail, use the user ID as fallback
      if (!employeeId) {
        employeeId = user.id;
        console.log(`No employee found for user ${user.id}, using user ID directly as employee ID`);
      }
      
      if (employeeId) {
        // Sprawdź czy pracownik jest przypisany i czy jest aktywny
        const isAssigned = task?.assigned_employees?.includes(employeeId) || false;
        
        // FIX: Check task_activities properly without the user_id column
        // Instead, query only by employee_id and task_id
        const { data: taskActivities, error: taskActivitiesError } = await supabase
          .from('task_activities')
          .select('id')
          .eq('task_id', taskId)
          .eq('status', 'active')
          .eq('employee_id', employeeId);
        
        if (taskActivitiesError) {
          console.error('Błąd sprawdzania aktywnych rekordów task_activities:', taskActivitiesError);
        }
        
        // Set whether employee is active based on database records
        const isActiveInDB = taskActivities && taskActivities.length > 0;
        
        // Also check in our local state
        const isActiveInState = activeEmployees.includes(employeeId);
        
        // Consider employee active if they have an active record in DB or in local state
        const isActive = isActiveInDB || isActiveInState;
        
        console.log(`checkCurrentEmployeeStatusOnce: Employee ${employeeId}:
          - isAssigned: ${isAssigned}
          - isActiveInDB: ${isActiveInDB} (found ${taskActivities?.length || 0} records)
          - isActiveInState: ${isActiveInState}
          - finalIsActive: ${isActive}
        `);
        
        // Aktualizujemy stan currentEmployeeData
        setCurrentEmployeeData({ id: employeeId, isActive, isAssigned });
        
        // Update activeEmployees and activeEmployeeIds if needed
        if (isActive && !activeEmployees.includes(employeeId)) {
          setActiveEmployees(prev => [...prev, employeeId]);
          setActiveEmployeeIds(prev => ({ ...prev, [employeeId]: true }));
          console.log(`Added ${employeeId} to activeEmployees list`);
        }
        
        // Button visibility depends on task status and whether employee is assigned
        const taskInProgress = task?.status === 'in_progress';
        const shouldShow = isAssigned && taskInProgress;
        setShouldShowJoinButton(shouldShow);
        
        console.log(`checkCurrentEmployeeStatusOnce: Set shouldShowJoinButton=${shouldShow}`);
      } else {
        setCurrentEmployeeData(null);
        setShouldShowJoinButton(false);
      }
    } catch (error) {
      console.error('Błąd sprawdzania statusu pracownika:', error);
      setCurrentEmployeeData(null);
      setShouldShowJoinButton(false);
    }
  };

  // Hook do kontroli statusu pracownika
  useEffect(() => {
    checkCurrentEmployeeStatus();
  }, [task, activeEmployees]);

  // Add debugging for button state
  useEffect(() => {
    if (task?.status === 'in_progress') {
      const isButtonDisabled = statusLoading || !currentEmployeeData || !currentEmployeeData.isActive;
      console.log('Complete Task button state:', {
        statusLoading,
        hasEmployeeData: !!currentEmployeeData,
        isEmployeeActive: currentEmployeeData?.isActive,
        isButtonDisabled
      });
      
      // Monitorowanie warunkow renderowania przycisku "Dołącz do zadania"
      if (currentEmployeeData) {
        console.log('Warunki renderowania przycisku "Dołącz do zadania":', {
          currentEmployeeData,
          isActive: currentEmployeeData?.isActive,
          isAssigned: currentEmployeeData?.isAssigned,
          shouldShowJoinButton,
          shouldBeVisible: currentEmployeeData && !currentEmployeeData.isActive && task.assigned_employees.includes(currentEmployeeData.id)
        });
      }
    }
  }, [task?.status, statusLoading, currentEmployeeData, shouldShowJoinButton]);

  // Function to determine if the Complete Task button should be disabled
  // Usunięto funkcję isCompleteTaskButtonDisabled jako niepotrzebną 

  // Usunięto funkcję renderCompleteTaskButton jako niepotrzebną

  /**
   * Funkcja aktualizująca liczbę aktywnych pracowników dla zadania
   * @returns Liczba aktywnych pracowników lub -1 w przypadku błędu
   */
  const updateActiveEmployeesCount = async (): Promise<number> => {
    try {
      if (!taskId) {
        console.error('updateActiveEmployeesCount: Brak ID zadania');
        return -1;
      }
      
      // Use task_activities instead of work_sessions to count active employees
      const { data: activeActivities, error: countError } = await supabase
        .from('task_activities')
        .select('id')
        .eq('task_id', taskId)
        .eq('status', 'active');
      
      if (countError) {
        console.error('updateActiveEmployeesCount: Błąd pobierania aktywnych aktywności:', countError);
        return -1;
      }
      
      // Get the count of active employees
      const activeCount = activeActivities?.length || 0;
      console.log(`updateActiveEmployeesCount: Znaleziono ${activeCount} aktywnych pracowników w zadaniu`);
      
      // Update the task record with the new count
      const { error: updateCountError } = await supabase
        .from('tasks')
          .update({
          active_employees_count: activeCount
          })
          .eq('id', taskId);
        
      if (updateCountError) {
        console.error('updateActiveEmployeesCount: Błąd aktualizacji licznika:', updateCountError);
        return -1;
      }
      
      console.log('updateActiveEmployeesCount: Zaktualizowano liczbę aktywnych pracowników na:', activeCount);
      
      // Return the active count
      return activeCount;
    } catch (error) {
      console.error('updateActiveEmployeesCount: Nieoczekiwany błąd:', error);
      return -1;
    }
  };

  // Modify the fetchActiveEmployees function to be more reliable
  const fetchActiveEmployees = async () => {
    try {
      console.log(`fetchActiveEmployees: Pobieranie aktywnych pracowników dla zadania ${taskId}`);
      
      // 1. Pobierz listę aktywnych pracowników z bazy danych
      const activeEmployeesList = await getActiveEmployeesForTask(taskId);
      
      // Log the result
      console.log(`fetchActiveEmployees: Znaleziono ${activeEmployeesList.length} aktywnych pracowników: `, activeEmployeesList);
      
      // 2. Sprawdź czy aktualny pracownik jest w tej liście
      if (currentEmployeeData && activeEmployeesList.includes(currentEmployeeData.id)) {
        console.log(`Aktualny pracownik ${currentEmployeeData.id} jest aktywny w zadaniu ${taskId}`);
        
        // Update local state to match database state
        if (!currentEmployeeData.isActive) {
          setCurrentEmployeeData(prev => {
            if (!prev) return null;
            return { ...prev, isActive: true };
          });
          console.log(`Ustawiono isActive=true dla pracownika ${currentEmployeeData.id}`);
        }
      } else if (currentEmployeeData && !activeEmployeesList.includes(currentEmployeeData.id)) {
        console.log(`Aktualny pracownik ${currentEmployeeData.id} NIE jest aktywny w zadaniu ${taskId}`);
        
        // Also update if the employee is not active
        if (currentEmployeeData.isActive) {
          setCurrentEmployeeData(prev => {
            if (!prev) return null;
            return { ...prev, isActive: false };
          });
          console.log(`Ustawiono isActive=false dla pracownika ${currentEmployeeData.id}`);
        }
      }
      
      // 3. Aktualizuj licznik aktywnych pracowników dla zadania
      // Use the correct function to get active employee count
      const activeCount = await updateActiveEmployeesCount();
      console.log(`Zliczono ${activeCount} aktywnych pracowników dla zadania ${taskId}`);
      
      // Update task's count if necessary - ensure we're comparing numbers
      if (task && typeof activeCount === 'number' && activeCount >= 0 && 
          task.active_employees_count !== activeCount) {
        setTask(prev => {
          if (!prev) return null;
          // Ensure active_employees_count is a number
          return { ...prev, active_employees_count: activeCount };
        });
      }
      
      // 4. Ustaw stan activeEmployees
      setActiveEmployees(activeEmployeesList);
      
      // 5. Aktualizuj też activeEmployeeIds dla renderowania
      const newActiveEmployeeIds: Record<string, boolean> = {};
      for (const empId of activeEmployeesList) {
        newActiveEmployeeIds[empId] = true;
      }
      setActiveEmployeeIds(newActiveEmployeeIds);
      
      // Force UI update with new timestamp
      setRefreshTimestamp(Date.now());
      
      // Log the current active employee state for debugging
      console.log('Current active employee state after fetchActiveEmployees:', {
        activeEmployees: activeEmployeesList,
        activeEmployeeIds: newActiveEmployeeIds,
        currentEmployeeData,
        shouldShowJoinButton
      });
    } catch (error) {
      console.error('fetchActiveEmployees: Nieoczekiwany błąd:', error);
    }
  };

  // Dodajmy dodatkowe informacje debugowania dla aktywnych pracowników
  useEffect(() => {
    console.log('DEBUG - aktualizacja activeEmployees:', {
      activeEmployees, 
      currentEmployeeData,
      isCurrentEmployeeInActiveEmployees: currentEmployeeData?.id ? activeEmployees.includes(currentEmployeeData.id) : false,
      shouldShowJoinButton,
      taskStatus: task?.status
    });
    
    // Zapewnijmy, że currentEmployeeData jest zgodne z activeEmployees
    if (currentEmployeeData && currentEmployeeData.id) {
      const isActive = activeEmployees.includes(currentEmployeeData.id);
      if (isActive !== currentEmployeeData.isActive) {
        console.log(`Aktualizacja isActive dla pracownika ${currentEmployeeData.id} z ${currentEmployeeData.isActive} na ${isActive}`);
        setCurrentEmployeeData(prev => {
          if (!prev) return null;
          return { ...prev, isActive };
        });
      }
    }
  }, [activeEmployees, currentEmployeeData, shouldShowJoinButton, task?.status]);

  // Dodaj useEffect, który będzie odświeżał listę aktywnych pracowników gdy zmieni się status zadania
  useEffect(() => {
    if (task?.status === 'in_progress') {
      console.log('Zadanie jest w trakcie - odświeżam listę aktywnych pracowników');
      fetchActiveEmployees();
    }
  }, [task?.status]);

  // Dodajmy dodatkowe odświeżanie po każdej akcji
  const refreshAfterAction = async () => {
    console.log('Odświeżanie danych po akcji');
    try {
      // Najpierw odśwież podstawowe dane
      const taskDetailsResult = await fetchTaskDetailsWithoutStateUpdate();
      if (taskDetailsResult) {
        setTask(taskDetailsResult.task);
        setTaskEmployees(taskDetailsResult.employees);
      }
      
      // Odśwież dane o aktywności pracowników
      await fetchActiveEmployees();
      await fetchEmployeeActivityData();
      
      // Aktualizuj status bieżącego pracownika
      await checkCurrentEmployeeStatus();
      
      // Ustaw timestamp odświeżenia, żeby wymusić rerenderowanie
      setRefreshTimestamp(Date.now());
    } catch (error) {
      console.error('Błąd podczas odświeżania danych po akcji:', error);
    }
  };

  // Modify the joinEmployeeToTask function to ensure it properly updates the UI
  const joinEmployeeToTask = async (employeeId: string) => {
    if (statusLoading) return;
    setStatusLoading(true);
    
    console.log('PRZYCISK: Kliknięto "Dołącz do zadania" dla pracownika:', employeeId);
    
    try {
      // Pobierz dane użytkownika
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      
      if (!user) {
        console.error('Brak zalogowanego użytkownika');
        setStatusLoading(false);
        return;
      }
      
      // Pobierz company_id z metadanych użytkownika
      const companyId = user?.user_metadata?.company_id;
      if (!companyId) {
        console.error('Brak company_id dla użytkownika');
        setStatusLoading(false);
        return;
      }
      
      console.log(`Tworzę aktywność zadaniową dla pracownika ${employeeId} w zadaniu ${taskId} dla firmy ${companyId}`);
      
      // Rozpocznij aktywność zadaniową dla wybranego pracownika
      const taskActivity = await startTaskActivity(employeeId, taskId, companyId);
      
      if (!taskActivity) {
        throw new Error('Nie udało się dołączyć do zadania');
      }
      
      console.log('Dołączono pracownika do zadania:', taskActivity);
      
      // IMMEDIATELY update ALL state to show this employee is active
      
      // 1. Update currentEmployeeData if this is the current employee
      if (currentEmployeeData && currentEmployeeData.id === employeeId) {
        console.log(`This is the current employee (${employeeId}), setting isActive=true`);
        setCurrentEmployeeData(prev => {
          if (!prev) return null;
          return { ...prev, isActive: true };
        });
      }
      
      // 2. Add to activeEmployees list
      setActiveEmployees(prev => {
        if (prev.includes(employeeId)) return prev;
        console.log(`Adding ${employeeId} to activeEmployees array`);
        return [...prev, employeeId];
      });
      
      // 3. Add to activeEmployeeIds
      setActiveEmployeeIds(prev => {
        console.log(`Adding ${employeeId} to activeEmployeeIds object`);
        return { ...prev, [employeeId]: true };
      });
      
      // 4. Force refresh timestamp to trigger re-render
      setRefreshTimestamp(Date.now());
      
      // 5. Update task count
      if (task) {
        setTask(prev => {
          if (!prev) return null;
          const newCount = (prev.active_employees_count || 0) + 1;
          console.log(`Updating task active_employees_count to ${newCount}`);
          return { ...prev, active_employees_count: newCount };
        });
      }
      
      // 6. Set shouldShowJoinButton based on currentEmployeeData if needed
      if (currentEmployeeData && currentEmployeeData.id === employeeId) {
        console.log(`Setting shouldShowJoinButton for current employee ${employeeId}`);
        // We still show the button, but it will be disabled due to isActive=true
        setShouldShowJoinButton(true);
      }
      
      setStatusLoading(false);
      Alert.alert('Sukces', 'Dołączono do zadania');
    } catch (error) {
      console.error('Błąd podczas dołączania do zadania:', error);
      setStatusLoading(false);
      Alert.alert('Błąd', 'Nie udało się dołączyć do zadania. Spróbuj ponownie.');
    }
  };

  // Modify the renderJoinTaskButton function to handle the state correctly
  const renderJoinTaskButton = () => {
    // Only render if running on the client side and we have task data
    if (typeof window === 'undefined' || !task) return null;
    
    // Use currentEmployeeData to determine if the current user is active in the task
    if (!currentEmployeeData) {
      console.log('renderJoinTaskButton: No currentEmployeeData available');
      return null;
    }
    
    const employeeId = currentEmployeeData.id;
    
    // Check if the current employee is assigned to this task
    const isAssigned = task.assigned_employees?.includes(employeeId) || false;
    
    // Task must be in progress
    const taskInProgress = task.status === 'in_progress';
    
    // For button visibility: only show for assigned employees when task is in progress
    const shouldShowButton = isAssigned && taskInProgress;
    
    // Now determine if the employee is active based on multiple sources
    const isActiveInLocalState = activeEmployees.includes(employeeId);
    const isActiveInEmployeeIds = activeEmployeeIds[employeeId] === true;
    const isActiveInCurrentData = currentEmployeeData.isActive === true;
    
    // Consider employee active if ANY check indicates they are active
    const isActive = isActiveInLocalState || isActiveInEmployeeIds || isActiveInCurrentData;
    
    // Enhanced logging for debugging
    console.log(`=== Join Task Button State (ts:${refreshTimestamp}) ===`);
    console.log(`- Employee ID: ${employeeId}`);
    console.log(`- Assigned to task: ${isAssigned}`);
    console.log(`- Task in progress: ${taskInProgress}`);
    console.log(`- Active checks:`);
    console.log(`  - In activeEmployees array: ${isActiveInLocalState}`);
    console.log(`  - In activeEmployeeIds object: ${isActiveInEmployeeIds}`);
    console.log(`  - In currentEmployeeData: ${isActiveInCurrentData}`);
    console.log(`- Final Active Status: ${isActive}`);
    console.log(`- Should Show Button: ${shouldShowButton}`);

    // Return the button if it should be shown
    // The button will be enabled or disabled based on the employee's active status
    if (shouldShowButton) {
      return (
        <TouchableOpacity
          style={[
            styles.joinTaskButton,
            isActive && styles.joinTaskButtonDisabled
          ]}
          onPress={() => {
            if (!isActive) {
              joinEmployeeToTask(employeeId);
            } else {
              console.log('PRZYCISK: Przycisk wyłączony, pracownik już aktywny');
            }
          }}
          disabled={isActive}
        >
          <Ionicons 
            name="play-circle-outline" 
            size={20} 
            color={isActive ? "#9ca3af" : "white"} 
            style={{ marginRight: 8 }} 
          />
          <Text style={[
            styles.joinTaskButtonText,
            isActive && styles.joinTaskButtonTextDisabled
          ]}>
            {isActive ? i18n.t('activeEmployee') : i18n.t('startTask')}
          </Text>
        </TouchableOpacity>
      );
    }
    
    return null;
  };

  // Disable the useEffect that sets up refresh intervals
  useEffect(() => {
    // Commented out to disable automatic refreshing
    // const interval = setInterval(() => {
    //   if (task?.status === 'in_progress') {
    //     console.log('Odświeżanie aktywnych pracowników (interwał)');
    //     fetchActiveEmployees();
    //   }
    // }, 5000); // 5 sekund
    
    // return () => clearInterval(interval);
    
    // Log that automatic refreshing is disabled
    console.log('Automatyczne odświeżanie danych zostało wyłączone');
    
    return () => {};
  }, [task?.status, taskId]);

  // Disable the useEffect that refreshes data when task status changes
  useEffect(() => {
    // Commented out to disable automatic refreshing
    // if (task?.status === 'in_progress') {
    //   console.log('Zadanie jest w trakcie - odświeżam listę aktywnych pracowników');
    //   fetchActiveEmployees();
    // }
    
    console.log('Odświeżanie przy zmianie statusu zadania zostało wyłączone');
  }, [task?.status]);

  // Disable the useEffect that refreshes data when activeEmployees changes
  useEffect(() => {
    // Commented out to disable automatic refreshing
    // console.log('DEBUG - aktualizacja activeEmployees:', {
    //   activeEmployees, 
    //   currentEmployeeData,
    //   isCurrentEmployeeInActiveEmployees: currentEmployeeData?.id ? activeEmployees.includes(currentEmployeeData.id) : false,
    //   shouldShowJoinButton,
    //   taskStatus: task?.status
    // });
    
    // // Zapewnijmy, że currentEmployeeData jest zgodne z activeEmployees
    // if (currentEmployeeData && currentEmployeeData.id) {
    //   const isActive = activeEmployees.includes(currentEmployeeData.id);
    //   if (isActive !== currentEmployeeData.isActive) {
    //     console.log(`Aktualizacja isActive dla pracownika ${currentEmployeeData.id} z ${currentEmployeeData.isActive} na ${isActive}`);
    //     setCurrentEmployeeData(prev => {
    //       if (!prev) return null;
    //       return { ...prev, isActive };
    //     });
    //   }
    // }
    
    console.log('Odświeżanie przy zmianie activeEmployees zostało wyłączone');
  }, [activeEmployees, currentEmployeeData, shouldShowJoinButton, task?.status]);

  // Disable the useEffect that refreshes data when taskId changes
  useEffect(() => {
    if (taskId) {
      console.log(`TaskDetails: taskId zmienione na ${taskId}, odświeżanie danych wyłączone`);
      // Commented out to disable automatic refreshing
      // const refresh = async () => {
      //   await fetchTaskDetails();
      //   await fetchTaskTimes();
      //   await fetchActiveEmployees();
      // };
      // refresh();
      
      // Instead, fetch data only once when component mounts
      const initialFetch = async () => {
        await fetchTaskDetailsWithoutStateUpdate();
        await fetchTaskTimesWithoutStateUpdate();
        await fetchActiveEmployeesWithoutStateUpdate();
      };
      initialFetch();
    }
  }, [taskId]);

  // Restore the missing functions
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    
      const date = new Date(dateString);
      return date.toLocaleDateString('pl-PL', {
      day: '2-digit',
        month: '2-digit',
      year: 'numeric'
    });
  };
  
  const formatTime = (timeString?: string) => {
    if (!timeString) return '';
    
    // If it's just time in HH:MM format
    if (timeString.length <= 5 && timeString.includes(':')) {
      return timeString;
    }
    
    try {
      // Handle different date-time formats
        const date = new Date(timeString);
      
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.log('Invalid date format:', timeString);
        return timeString; // Return the original string if we can't parse it
      }
      
    return date.toLocaleTimeString('pl-PL', {
          hour: '2-digit',
          minute: '2-digit'
        });
    } catch (error) {
      console.log('Error formatting time:', error, timeString);
      return timeString; // Return the original string if there's an error
    }
  };
  
  // Helper function to format duration in HH:MM format
  const formatDuration = (minutes: number | null) => {
    if (minutes === null || minutes === undefined) return '00:00';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };
  
  // Calculate total task time spent
  const getTotalTaskTimeSpent = () => {
    let totalMinutes = 0;
    
    taskTimes.forEach(time => {
      if (time.duration_minutes) {
        totalMinutes += time.duration_minutes;
      } else if (time.start_time && !time.end_time) {
        // For active task times, calculate current duration
        const start = new Date(time.start_time);
        const now = new Date();
        const diffMinutes = Math.floor((now.getTime() - start.getTime()) / 60000);
        totalMinutes += diffMinutes;
      }
    });
    
    return formatDuration(totalMinutes);
  };
  
  // Calculate total task duration based on status changes
  const getTaskDuration = () => {
    if (!task) return '00:00';
    
    // If task is completed and has duration recorded, return it
    if (task.status === 'completed' && task.task_duration_minutes) {
      return formatDuration(task.task_duration_minutes);
    }
    
    // Jeśli zadanie jest zakończone, ale nie ma task_duration_minutes, oblicz czas trwania na podstawie started_at i completed_at
    if (task.status === 'completed' && task.started_at && task.completed_at) {
      const startTime = new Date(task.started_at);
      const endTime = new Date(task.completed_at);
      const diffMinutes = Math.floor((endTime.getTime() - startTime.getTime()) / 60000);
      console.log(`Obliczony czas trwania zadania zakończonego: ${diffMinutes} minut`);
      return formatDuration(diffMinutes);
    }
    
    // If task is in progress and has started_at, calculate current duration
    if (task.status === 'in_progress' && task.started_at) {
      const startTime = new Date(task.started_at);
      const now = new Date();
      const diffMinutes = Math.floor((now.getTime() - startTime.getTime()) / 60000);
      return formatDuration(diffMinutes);
    }
    // Fallback to status_changed_at if started_at is not available
    else if (task.status === 'in_progress' && task.status_changed_at) {
      const startTime = new Date(task.status_changed_at);
      const now = new Date();
      const diffMinutes = Math.floor((now.getTime() - startTime.getTime()) / 60000);
      return formatDuration(diffMinutes);
    }
    
    return '00:00';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return { bg: '#FEF3C7', text: '#92400E' };
      case 'in_progress':
        return { bg: '#DBEAFE', text: '#1E40AF' };
      case 'completed':
        return { bg: '#D1FAE5', text: '#065F46' };
      default:
        return { bg: '#E5E7EB', text: '#374151' };
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return i18n.t('pending');
      case 'in_progress':
        return i18n.t('inProgress');
      case 'completed':
        return i18n.t('completed');
      default:
        return i18n.t('unknownStatus');
    }
  };
  
  // Check if an employee is active
  const isEmployeeActive = async (employeeId: string): Promise<boolean> => {
    if (!employeeId || !taskId) {
      console.error('isEmployeeActive: Brak ID pracownika lub zadania');
      return false;
    }

    console.log('isEmployeeActive: Sprawdzanie statusu dla pracownika', employeeId);
    
    try {
      const isActive = await isEmployeeActiveInTask(employeeId, taskId);
      console.log('isEmployeeActive: Wynik sprawdzania dla', employeeId, ':', isActive);
      return isActive;
    } catch (error) {
      console.error('isEmployeeActive: Błąd podczas sprawdzania statusu:', error);
      return false;
    }
  };

  // Fix the checkIfUserActiveInTask function
  const checkIfUserActiveInTask = async (userId: string, taskId: string): Promise<boolean> => {
    try {
      console.log(`Checking if user/employee ${userId} has active task activity for task ${taskId}`);
      
      // Use select() without single() to get an array response instead of requiring exactly one row
      const { data, error } = await supabase
        .from('task_activities')
        .select('id')
        .eq('employee_id', userId)
        .eq('task_id', taskId)
        .eq('status', 'active');
        
      if (error) {
        console.error('Error checking if user is active in task:', error);
        return false;
      }
      
      // If we found any results, user is active
      const isActive = data && data.length > 0;
      console.log(`User/employee ${userId} active in task ${taskId}: ${isActive}`);
      return isActive;
    } catch (error) {
      console.error('Unexpected error checking if user is active in task:', error);
      return false;
    }
  };
  
  // Fix the refreshActiveStatus function
  const refreshActiveStatus = async () => {
    console.log('Periodic refresh: Checking active employee status');
    
    if (!currentEmployeeData) return;
    
    const employeeId = currentEmployeeData.id;
    
    // Check directly in the database using the employee ID
    const isActive = await checkIfUserActiveInTask(employeeId, taskId);
    
    // Update local state if needed
    if (isActive !== currentEmployeeData.isActive) {
      console.log(`Updating active status for employee ${employeeId} from ${currentEmployeeData.isActive} to ${isActive}`);
      
      setCurrentEmployeeData(prev => {
        if (!prev) return null;
        return { ...prev, isActive };
      });
      
      // Also update the activeEmployees array and activeEmployeeIds object
      if (isActive) {
        setActiveEmployees(prev => {
          if (prev.includes(employeeId)) return prev;
          return [...prev, employeeId];
        });
        
        setActiveEmployeeIds(prev => ({
          ...prev,
          [employeeId]: true
        }));
      }
    }
    
    // Also refresh the full list of active employees
    await fetchActiveEmployees();
  };

  // Fix references to checkCurrentEmployeeStatus
  const checkCurrentEmployeeStatus = checkCurrentEmployeeStatusOnce;

  // Add back the startTask function that was removed
  const startTask = async () => {
    if (statusLoading) return;
    setStatusLoading(true);
    
    console.log('Starting task with initial status:', task?.status);
    
    try {
      // Pobierz lokalizację początkową
      const startLocation = await getCurrentLocation();
      console.log('Start location for work session:', startLocation);
      
      // Pobierz dane użytkownika
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      
      if (!user) {
        console.error('Brak zalogowanego użytkownika');
        setStatusLoading(false);
        return;
      }
      
      // Pobierz company_id z metadanych użytkownika
      const companyId = user?.user_metadata?.company_id;
      if (!companyId) {
        console.error('Brak company_id dla użytkownika');
        setStatusLoading(false);
        return;
      }
      
      const now = new Date().toISOString();
      
      // 1. Aktualizuj status zadania na "in_progress" jeśli nie jest już w trakcie
      if (task?.status !== 'in_progress') {
      console.log('Aktualizacja statusu zadania na "in_progress"');
      const { error: updateError } = await supabase
        .from('tasks')
        .update({
          status: 'in_progress',
            status_changed_at: now,
            started_at: now
        })
        .eq('id', taskId);
      
      if (updateError) throw updateError;
      
        // Immediately update local task state
        console.log('Updating local task state from "pending" to "in_progress"');
        setTask(prev => {
          if (!prev) return null;
          const updatedTask = {
            ...prev,
            status: 'in_progress',
            status_changed_at: now,
            started_at: now
          };
          console.log('New task state:', updatedTask);
          return updatedTask;
        });
      }
      
      // 2. Rozpocznij aktywność zadaniową, przekazując lokalizację
      const taskActivity = await startTaskActivity(user.id, taskId, companyId, startLocation);
      
      if (!taskActivity) {
        throw new Error('Nie udało się rozpocząć aktywności zadaniowej');
      }
      
      console.log('Rozpoczęto aktywność zadaniową:', taskActivity);
      
      // 3. Update local state to reflect changes
      // Add the user to active employees list
      setActiveEmployees(prev => {
        if (prev.includes(user.id)) return prev;
        console.log(`Adding user ${user.id} to active employees list`);
        return [...prev, user.id];
      });
      
      // Update activeEmployeeIds
      setActiveEmployeeIds(prev => {
        console.log(`Adding user ${user.id} to activeEmployeeIds`);
        return {
          ...prev,
          [user.id]: true
        };
      });
      
      // Update the task's active_employees_count
      setTask(prev => {
        if (!prev) return null;
        const newCount = (prev.active_employees_count || 0) + 1;
        console.log(`Updating task active_employees_count to ${newCount}`);
        return {
          ...prev,
          active_employees_count: newCount,
          status: 'in_progress' // Ensure status is set to in_progress
        };
      });
      
      // Update currentEmployeeData if it exists
      if (currentEmployeeData) {
        setCurrentEmployeeData(prev => {
          if (!prev) return null;
          console.log(`Setting currentEmployeeData.isActive to true for employee ${prev.id}`);
          return { ...prev, isActive: true };
        });
      }
      
      // 4. Force UI refresh immediately
      setRefreshTimestamp(Date.now());
      
      // 5. Hide the "Join Task" button
      setShouldShowJoinButton(false);
      
      // 6. Re-fetch the task details to ensure we have the latest state
      console.log('Explicitly fetching updated task details');
      const { data: updatedTaskData, error: fetchError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();
        
      if (fetchError) {
        console.error('Error fetching updated task data:', fetchError);
      } else if (updatedTaskData) {
        console.log('Fetched updated task data, status:', updatedTaskData.status);
        setTask(updatedTaskData);
      }
      
      // 7. Explicitly call fetchActiveEmployees to update the list of active employees
      console.log('Refreshing active employees list');
      await fetchActiveEmployees();
      
      setStatusLoading(false);
      Alert.alert('Sukces', 'Zadanie zostało rozpoczęte. Jesteś teraz oznaczony jako aktywny w tym zadaniu.');
    } catch (error) {
      console.error('Błąd podczas rozpoczynania zadania:', error);
      setStatusLoading(false);
      Alert.alert('Błąd', 'Nie udało się rozpocząć zadania. Spróbuj ponownie.');
    }
  };
  
  // Add a useEffect to periodically refresh the active status
  useEffect(() => {
    // Only run if we have task data and it's in progress
    if (!task || task.status !== 'in_progress') return;
    
    console.log('Setting up periodic refresh of active employee status');
    
    // Set up interval to refresh every 5 seconds
    const intervalId = setInterval(refreshActiveStatus, 5000);
    
    // Clear interval on cleanup
    return () => {
      console.log('Clearing periodic refresh interval');
      clearInterval(intervalId);
    };
  }, [task, currentEmployeeData, taskId]);

  // Add a new function to handle completing a task activity
  const completeEmployeeTaskActivity = async () => {
    if (statusLoading) return;
    setStatusLoading(true);
    
    try {
      // Pobierz lokalizację końcową
      const endLocation = await getCurrentLocation();
      console.log('End location for work session:', endLocation);
      
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      
      if (!user) {
        console.error('Brak zalogowanego użytkownika');
        setStatusLoading(false);
            return;
          }
          
      const companyId = user?.user_metadata?.company_id;
      if (!companyId) {
        console.error('Brak company_id dla użytkownika');
        setStatusLoading(false);
        return;
      }
      
      // Zakończ aktywność zadaniową z przekazaniem lokalizacji końcowej
      const result = await stopTaskActivity(user.id, taskId, companyId, endLocation);
      
      if (!result) {
        throw new Error('Nie udało się zakończyć aktywności zadaniowej');
      }
      
      console.log('Zakończono aktywność zadaniową:', result);
      
      // Update activeEmployees
      setActiveEmployees(prev => prev.filter(id => id !== user.id));
      
      // Update activeEmployeeIds
      setActiveEmployeeIds(prev => {
        const newIds = { ...prev };
        delete newIds[user.id];
        return newIds;
      });
      
      // Update the task's active_employees_count
          setTask(prev => {
            if (!prev) return null;
        const newCount = Math.max(0, (prev.active_employees_count || 0) - 1);
            return { 
              ...prev, 
          active_employees_count: newCount
        };
      });
      
      if (currentEmployeeData) {
        setCurrentEmployeeData(prev => {
          if (!prev) return null;
          return { ...prev, isActive: false };
          });
      }
      
      // Force UI refresh immediately
      setRefreshTimestamp(Date.now());
      
      // Show the "Join Task" button again
      setShouldShowJoinButton(true);
      
      // Explicit update of the "current session" markers
      // setCurrentSession(null); // Usuwam tę linię, ponieważ nie mamy funkcji setCurrentSession
      
      // Re-fetch active employees
      await fetchActiveEmployees();
      
      setStatusLoading(false);
      Alert.alert('Sukces', 'Zakończono Twoją aktywność w zadaniu.');
    } catch (error) {
      console.error('Błąd podczas zakańczania aktywności:', error);
      setStatusLoading(false);
      Alert.alert('Błąd', 'Nie udało się zakończyć aktywności w zadaniu. Spróbuj ponownie.');
    }
  };

  // Add a function to render the Complete Task button
  const renderCompleteTaskButton = () => {
    // Only render if we have task data and currentEmployeeData
    if (typeof window === 'undefined' || !task || !currentEmployeeData) return null;
    
    // Get the current state of the employee
    const employeeId = currentEmployeeData.id;
    const isActive = currentEmployeeData.isActive;
    
    // Button should only be visible when employee is active in the task
    if (!isActive) {
      console.log('Complete Task button not shown - employee is not active');
      return null;
    }
    
    // Log state for debugging
      console.log('Complete Task button state:', {
        statusLoading,
        hasEmployeeData: !!currentEmployeeData,
      isEmployeeActive: isActive,
      isButtonDisabled: statusLoading
    });
    
    // Return the button
    return (
      <TouchableOpacity
        style={styles.completeButton}
        onPress={completeEmployeeTaskActivity}
        disabled={statusLoading}
      >
        <Ionicons 
          name="checkmark-circle-outline" 
          size={20} 
          color="white" 
          style={{ marginRight: 8 }} 
        />
        <Text style={styles.completeButtonText}>
          {i18n.t('completeTask')}
        </Text>
      </TouchableOpacity>
    );
  };

  // Update the render function to include the new button where appropriate
  // Find the section with the task actions (around line ~1500-1600) and add:

  const renderTaskActions = () => {
    // Don't render anything if task is not in progress
    if (!task || task.status !== 'in_progress') return null;
    
    // Get buttons to render
    const joinButton = renderJoinTaskButton();
    const completeButton = renderCompleteTaskButton();
    
    // Only render the container if we have at least one button
    if (!joinButton && !completeButton) return null;
    
    return (
      <View style={styles.actionsContainer}>
        {joinButton}
        {completeButton}
      </View>
    );
  };

  // Pobieranie danych o aktywności pracowników w zadaniu
  const fetchEmployeeActivityData = async () => {
    if (!taskId || !taskEmployees.length) return;
    
    try {
      // Pobierz dane aktywności dla wszystkich pracowników przypisanych do zadania
      const employeeIds = taskEmployees.map(emp => emp.id);
      
      const { data, error } = await supabase
        .from('task_activities')
        .select('*')
        .eq('task_id', taskId)
        .in('employee_id', employeeIds);
      
      if (error) {
        console.error('Błąd pobierania danych o aktywności pracowników:', error);
        return;
      }
      
      if (!data || data.length === 0) {
        console.log('Brak danych o aktywności pracowników dla tego zadania');
        return;
      }
      
      // Aktualizuj dane pracowników dodając informacje o ich aktywności
      const updatedEmployees = taskEmployees.map(employee => {
        // Znajdź najnowszą aktywność dla danego pracownika
        const employeeActivities = data.filter(act => act.employee_id === employee.id);
        
        if (employeeActivities.length > 0) {
          // Sortuj według czasu rozpoczęcia (od najnowszych)
          employeeActivities.sort((a, b) => 
            new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
          );
          
          const latestActivity = employeeActivities[0];
          
          return {
            ...employee,
            taskActivity: {
              start_time: latestActivity.start_time,
              end_time: latestActivity.end_time,
              duration_minutes: latestActivity.duration_minutes,
              status: latestActivity.status
            }
          };
        }
        
        return employee;
      });
      
      setTaskEmployees(updatedEmployees);
      
    } catch (error) {
      console.error('Nieoczekiwany błąd podczas pobierania danych o aktywności:', error);
    }
  };
  
  // Wywołaj funkcję po załadowaniu danych pracowników
  useEffect(() => {
    if (taskEmployees.length > 0) {
      fetchEmployeeActivityData();
    }
  }, [taskEmployees.length]);

  // Funkcja do nawigacji do formularza edycji zadania
  const handleEditTask = () => {
    console.log(`Editing task ${taskId}`);
    if (onEditTask) {
      onEditTask(taskId);
    } else {
      // Fallback jeśli onEditTask nie jest dostępne
      Alert.alert(i18n.t('editTask'), i18n.t('featureInDevelopment'));
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary.main} />
          <Text style={styles.loadingText}>{i18n.t('loadingTask')}</Text>
        </View>
      </View>
    );
  }

  if (!task) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#EF4444" />
          <Text style={styles.errorText}>{i18n.t('failedToLoadTask')}</Text>
          <TouchableOpacity style={styles.backButtonLarge} onPress={onBack}>
            <Text style={styles.backButtonText}>{i18n.t('back')}</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Modal do powiększania zdjęć */}
      <Modal
        visible={photoModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closePhotoModal}
      >
        <View style={styles.photoModalOverlay}>
          <TouchableOpacity 
            style={styles.photoModalCloseButton}
            onPress={closePhotoModal}
          >
            <Ionicons name="close-circle" size={32} color="#FFFFFF" />
          </TouchableOpacity>
          {selectedImage && (
            <Image 
              source={{ uri: selectedImage }} 
              style={styles.photoModalImage} 
              resizeMode="contain"
            />
          )}
        </View>
      </Modal>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={22} color="#1A1A1A" />
          </TouchableOpacity>
          
          <Text style={styles.pageTitle}>{i18n.t('taskDetails')}</Text>
          
          {/* Przycisk edycji zadania - widoczny tylko dla odpowiednich użytkowników */}
          {(userRole === 'company' || userRole === 'coordinator') && (
            <TouchableOpacity 
              style={styles.editButton} 
              onPress={handleEditTask}
            >
              <Ionicons name="create-outline" size={20} color="white" />
            </TouchableOpacity>
          )}
        </View>
        
        {/* Task Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{i18n.t('basicInfo')}</Text>
          <View style={styles.taskCard}>
            <View style={styles.taskHeader}>
              <Text style={styles.clientName}>{task.client_name}</Text>
              {/* Task status */}
              <View style={styles.statusContainer}>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(task.status).bg },
                  ]}
                >
                  <Text
                    style={[
                      styles.statusText,
                      { color: getStatusColor(task.status).text },
                    ]}
                  >
                    {getStatusText(task.status)}
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="location-outline" size={18} color="#6B7280" />
              <Text style={styles.infoText}>{task.address}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="construct-outline" size={18} color="#6B7280" />
              <Text style={styles.infoLabel}>{i18n.t('workScope')}:</Text>
              <Text style={styles.infoText}>{task.work_scope}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="calendar-outline" size={18} color="#6B7280" />
              <Text style={styles.infoLabel}>{i18n.t('date')}:</Text>
              <Text style={styles.infoText}>{formatDate(task.date)}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Ionicons name="time-outline" size={18} color="#6B7280" />
              <Text style={styles.infoLabel}>{i18n.t('startTime')}:</Text>
              <Text style={styles.infoText}>{formatTime(task.start_time)}</Text>
            </View>
            
            {/* Godzina rozpoczęcia zadania - aktualizacja użycia started_at */}
            {task.status !== 'pending' && (task.started_at || task.status_changed_at) && (
              <View style={styles.infoRow}>
                <Ionicons name="play-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>{i18n.t('startedAt')}:</Text>
                <Text style={styles.infoText}>
                  {`${formatDate(task.started_at || task.status_changed_at)} ${formatTime(task.started_at || task.status_changed_at)}`}
                </Text>
              </View>
            )}
            
            {/* Godzina zakończenia zadania */}
            {task.status === 'completed' && task.completed_at && (
              <View style={styles.infoRow}>
                <Ionicons name="flag-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>{i18n.t('finishedAt')}:</Text>
                <Text style={styles.infoText}>
                  {`${formatDate(task.completed_at)} ${formatTime(task.completed_at)}`}
                </Text>
              </View>
            )}
            
            {task.additional_info && (
              <View style={styles.additionalInfo}>
                <Text style={styles.infoLabel}>{i18n.t('additionalInfo')}:</Text>
                <Text style={styles.additionalInfoText}>{task.additional_info}</Text>
              </View>
            )}
            
            {/* Task Duration */}
            <View style={styles.infoRow}>
              <Ionicons name="hourglass-outline" size={18} color="#6B7280" />
              <Text style={styles.infoLabel}>{i18n.t('taskDuration')}:</Text>
              <Text style={styles.totalTaskTimeText}>{getTaskDuration()}</Text>
            </View>

            {/* Task Status Controls */}
            <View style={styles.taskActions}>
              {task && task.status === 'pending' && (
                <TouchableOpacity 
                  style={[styles.actionButton, statusLoading && styles.buttonDisabled]} 
                  onPress={() => {
                    console.log('Start task button pressed');
                    startTask();
                  }}
                  disabled={statusLoading}
                >
                  {statusLoading ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <>
                      <Ionicons name="play-circle-outline" size={20} color="white" style={{ marginRight: 8 }} />
                      <Text style={styles.actionButtonText}>{i18n.t('startTask')}</Text>
                    </>
                  )}
                </TouchableOpacity>
              )}

              {task && task.status === 'in_progress' && (
                <View style={styles.taskActionsRow}>
                  {renderTaskActions()}
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Assigned Employees */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{i18n.t('assignedEmployees')}</Text>
          {taskEmployees.length > 0 ? (
            <View style={styles.employeesList}>
              {taskEmployees.map((employee) => {
                // Use the state value instead of calling the async function directly
                const isActive = activeEmployeeIds[employee.id] || false;
                console.log(`Rendering employee ${employee.full_name} (${employee.id}), active: ${isActive}`);
                
                return (
                  <View key={employee.id} style={[
                    styles.employeeCard,
                    isActive && styles.activeEmployeeCard
                  ]}>
                    <View style={styles.employeeHeader}>
                      <Ionicons 
                        name={isActive ? "person" : "person-outline"} 
                        size={24} 
                        color={isActive ? "#10B981" : theme.colors.primary.main} 
                      />
                      <View style={styles.employeeNameContainer}>
                        {isActive && (
                          <View style={styles.greenDot} />
                        )}
                        <Text style={[
                          styles.employeeName,
                          isActive && styles.activeEmployeeName
                        ]}>
                          {employee.full_name}
                        </Text>
                      </View>
                    </View>
                    {employee.email && (
                      <View style={styles.infoRow}>
                        <Ionicons name="mail-outline" size={18} color="#6B7280" />
                        <Text style={styles.infoText}>{employee.email}</Text>
                      </View>
                    )}
                    {employee.phone && (
                      <View style={styles.infoRow}>
                        <Ionicons name="call-outline" size={18} color="#6B7280" />
                        <Text style={styles.infoText}>{employee.phone}</Text>
                      </View>
                    )}
            
                    {employee.taskActivity && (
                      <View style={styles.employeeActivityInfo}>
                        <View style={styles.activityHeader}>
                          <Ionicons 
                            name="time-outline" 
                            size={18} 
                            color={employee.taskActivity.status === 'active' ? "#10B981" : "#6B7280"} 
                          />
                          <Text style={styles.activityTitle}>
                            {employee.taskActivity.status === 'active' ? i18n.t('activityInProgress') : i18n.t('completedActivity')}
                          </Text>
                        </View>
                        
                        {employee.taskActivity.start_time && (
                          <View style={styles.infoRow}>
                            <Ionicons name="play-outline" size={16} color="#6B7280" />
                            <Text style={styles.infoLabel}>{i18n.t('startedAt')}:</Text>
                            <Text style={styles.infoText}>
                              {formatTime(employee.taskActivity.start_time)}
                            </Text>
                          </View>
                        )}
                        
                        {employee.taskActivity.end_time && (
                          <View style={styles.infoRow}>
                            <Ionicons name="stop-outline" size={16} color="#6B7280" />
                            <Text style={styles.infoLabel}>{i18n.t('finishedAt')}:</Text>
                            <Text style={styles.infoText}>
                              {formatTime(employee.taskActivity.end_time)}
                            </Text>
                          </View>
                        )}
                        
                        {employee.taskActivity.duration_minutes !== null && employee.taskActivity.duration_minutes !== undefined ? (
                          <View style={styles.infoRow}>
                            <Ionicons name="hourglass-outline" size={16} color="#6B7280" />
                            <Text style={styles.infoLabel}>{i18n.t('duration')}:</Text>
                            <Text style={styles.infoText}>
                              {formatDuration(employee.taskActivity.duration_minutes)}
                            </Text>
                          </View>
                        ) : null}
                      </View>
                    )}
                  </View>
                );
              })}
            </View>
          ) : (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataText}>{i18n.t('noEmployees')}</Text>
            </View>
          )}
        </View>

        {/* Photos Before Work */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{i18n.t('photosBeforeWork')}</Text>
          {photosBefore && photosBefore.length > 0 ? (
            <View style={styles.photosGrid}>
              {photosBefore.map((photo, index) => (
                <TouchableOpacity 
                  key={index}
                  style={styles.photoWrapper}
                  onPress={() => openPhotoModal(photo)}
                >
                  <Image 
                    source={{ uri: photo }} 
                    style={styles.photo}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataText}>{i18n.t('noData')}</Text>
            </View>
          )}
        </View>

        {/* Photos After Work */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{i18n.t('photosAfterWork')}</Text>
          {photosAfter && photosAfter.length > 0 ? (
            <View style={styles.photosGrid}>
              {photosAfter.map((photo, index) => (
                <TouchableOpacity 
                  key={index}
                  style={styles.photoWrapper}
                  onPress={() => openPhotoModal(photo)}
                >
                  <Image 
                    source={{ uri: photo }} 
                    style={styles.photo}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.noDataContainer}>
              <Text style={styles.noDataText}>{i18n.t('noData')}</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingTop: 16,
    paddingBottom: 32,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 20,
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginLeft: 8,
    display: 'none', // Ukrywamy tekst "Powrót"
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    marginTop: 12,
    fontSize: 18,
    color: '#4B5563',
    marginBottom: 24,
  },
  backButtonLarge: {
    backgroundColor: theme.colors.primary.main,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  section: {
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  taskCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginLeft: 8,
    marginRight: 4,
  },
  infoText: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1,
    marginLeft: 8,
  },
  additionalInfo: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  additionalInfoText: {
    fontSize: 14,
    color: '#4B5563',
    marginTop: 4,
  },
  taskActions: {
    marginTop: 20,
    marginBottom: 16,
  },
  taskActionsRow: {
    width: '100%',
  },
  actionButton: {
    backgroundColor: theme.colors.primary.main,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  buttonDisabled: {
    backgroundColor: '#9CA3AF',
    opacity: 0.7,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  employeesList: {
    marginTop: 4,
  },
  employeeCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  employeeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  employeeNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  greenDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#10B981', // Green color
    marginRight: 6,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: 10,
  },
  noEmployeesContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noEmployeesText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  totalTaskTimeText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary.main,
    marginLeft: 8,
  },
  photosContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  photoWrapper: {
    marginRight: 12,
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 8,
  },
  completeButton: {
    backgroundColor: '#4CAF50', // Green color for completion
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  completeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  activeEmployeeCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#10B981',
  },
  activeEmployeeName: {
    color: '#10B981',
    fontWeight: '700',
  },
  activeEmployeeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
    width: '100%',
  },
  activeEmployeeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  activeEmployeeText: {
    fontSize: 12,
    color: '#10B981',
    fontWeight: '600',
  },
  employeeActions: {
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  inactiveEmployeeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(156, 163, 175, 0.1)',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  inactiveEmployeeText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '600',
  },
  joinTaskButton: {
    backgroundColor: theme.colors.primary.main,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    width: '100%',
  },
  joinTaskButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  activateButton: {
    backgroundColor: theme.colors.primary.main,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activateButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  joinTaskButtonDisabled: {
    backgroundColor: '#e5e7eb', // Light gray background for disabled state
    opacity: 0.7,
  },
  joinTaskButtonTextDisabled: {
    color: '#9ca3af', // Gray text for disabled state
  },
  actionsContainer: {
    flexDirection: 'column',
    gap: 10,
    marginTop: 16,
    marginBottom: 16,
    width: '100%',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  employeeActivityInfo: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    borderLeftWidth: 2,
    borderLeftColor: '#6B7280',
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4B5563',
    marginLeft: 4,
  },
  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2563EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
    marginLeft: 6,
    display: 'none', // Ukrywamy tekst "Edytuj zadanie"
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  noDataText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
  },
  // Style do obsługi powiększania zdjęć
  photoModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  photoModalCloseButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    backgroundColor: 'transparent',
    padding: 8,
  },
  photoModalImage: {
    width: '90%',
    height: '80%',
    borderRadius: 8,
  },
});

export default TaskDetails; 