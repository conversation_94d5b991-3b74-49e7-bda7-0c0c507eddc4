-- NAPRAWA FUNKCJI can_use_verification_code

-- <PERSON><PERSON><PERSON> starą funkcję jeśli istnieje
DROP FUNCTION IF EXISTS can_use_verification_code(TEXT, UUID);

-- Utw<PERSON><PERSON> nową funkcję
CREATE OR REPLACE FUNCTION can_use_verification_code(
  p_code TEXT,
  p_company_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  code_exists BOOLEAN;
  active_employees_count INTEGER;
  employee_limit INTEGER;
  has_active_subscription BOOLEAN;
BEGIN
  -- Spra<PERSON><PERSON><PERSON> czy kod istnieje i nie jest użyty
  SELECT EXISTS(
    SELECT 1 FROM verification_codes 
    WHERE code = p_code 
    AND company_id = p_company_id 
    AND used = false
  ) INTO code_exists;
  
  -- <PERSON><PERSON><PERSON> kod nie istnieje lub jest już użyty, zwróć false
  IF NOT code_exists THEN
    RAISE NOTICE 'Code does not exist or is already used: %', p_code;
    RETURN FALSE;
  END IF;
  
  -- <PERSON><PERSON><PERSON><PERSON><PERSON> czy firma ma aktywną subskrypcję
  SELECT EXISTS(
    SELECT 1 FROM company_subscriptions 
    WHERE company_id = p_company_id 
    AND status = 'active'
    AND (current_period_end IS NULL OR current_period_end > NOW())
  ) INTO has_active_subscription;
  
  -- Pobierz limit pracowników
  SELECT verification_code_limit INTO employee_limit
  FROM companies WHERE id = p_company_id;
  
  -- Pobierz aktualną liczbę aktywnych pracowników
  SELECT COUNT(*) INTO active_employees_count
  FROM employees 
  WHERE company_id = p_company_id 
  AND subscription_status = 'ACTIVE';
  
  RAISE NOTICE 'Company: %, Active subscription: %, Employee limit: %, Active employees: %', 
    p_company_id, has_active_subscription, employee_limit, active_employees_count;
  
  -- Jeśli firma ma aktywną subskrypcję premium, pozwól na użycie kodu
  IF has_active_subscription THEN
    RAISE NOTICE 'Company has active subscription, allowing code usage';
    RETURN TRUE;
  END IF;
  
  -- Jeśli nie ma aktywnej subskrypcji, sprawdź limit darmowego konta
  IF active_employees_count < employee_limit THEN
    RAISE NOTICE 'Free account limit not exceeded, allowing code usage';
    RETURN TRUE;
  ELSE
    RAISE NOTICE 'Free account limit exceeded, blocking code usage';
    RETURN FALSE;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Przetestuj funkcję
SELECT can_use_verification_code(
  'POGBJK', 
  (SELECT company_id FROM verification_codes WHERE code = 'POGBJK')
) as test_result;
