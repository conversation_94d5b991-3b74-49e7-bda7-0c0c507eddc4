-- Funkcja do "usuwania" pracownika - przekształcenie w niezależnego pracownika
-- Zamiast usuwać pracownika, od<PERSON><PERSON><PERSON><PERSON><PERSON> go od firmy i zwalniamy kod weryfikacyjny

CREATE OR REPLACE FUNCTION remove_employee_from_company(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_employee_name TEXT;
  v_verification_code TEXT;
  v_employee_email TEXT;
BEGIN
  -- Sprawdź czy pracownik należy do tej firmy
  IF NOT EXISTS (
    SELECT 1 FROM employees
    WHERE id = p_employee_id
    AND company_id = p_company_id
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie należy do tej firmy',
      'error_type', 'invalid_employee'
    );
  END IF;

  -- Po<PERSON>rz dane pracownika (bez email jeśli kolumna nie istnieje)
  SELECT full_name, verification_code
  INTO v_employee_name, v_verification_code
  FROM employees
  WHERE id = p_employee_id;

  -- Ustaw email na NULL jeśli kolumna nie istnieje
  v_employee_email := NULL;

  -- 1. Usuń powiązanie kodu weryfikacyjnego z pracownikiem
  IF v_verification_code IS NOT NULL THEN
    -- Usuń powiązanie w tabeli verification_codes
    UPDATE verification_codes
    SET used_by_employee_id = NULL,
        used_at = NULL,
        is_used = false
    WHERE code = v_verification_code
    AND company_id = p_company_id;
    
    RAISE NOTICE 'Zwolniono kod weryfikacyjny % dla pracownika %', v_verification_code, v_employee_name;
  END IF;

  -- 2. Przekształć pracownika w niezależnego
  UPDATE employees
  SET 
    company_id = NULL,                    -- Usuń powiązanie z firmą
    verification_code = NULL,             -- Usuń kod weryfikacyjny
    subscription_status = 'ACTIVE',       -- Niezależni pracownicy są aktywni
    last_status_change = NOW(),
    last_manual_status_change = NULL,     -- Wyzeruj ograniczenia czasowe
    employee_type = 'INDEPENDENT'         -- Oznacz jako niezależnego (jeśli kolumna istnieje)
  WHERE id = p_employee_id;

  -- 3. Dodaj wpis do historii (opcjonalnie)
  INSERT INTO employee_history (
    employee_id,
    action_type,
    action_description,
    performed_by,
    performed_at,
    old_company_id,
    new_company_id
  ) VALUES (
    p_employee_id,
    'REMOVED_FROM_COMPANY',
    'Pracownik został usunięty z firmy i przekształcony w niezależnego',
    'ADMIN',
    NOW(),
    p_company_id,
    NULL
  );

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został usunięty z firmy i przekształcony w niezależnego pracownika',
    'alert_type', 'success',
    'employee_name', v_employee_name,
    'employee_email', v_employee_email,
    'freed_verification_code', v_verification_code
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Błąd podczas usuwania pracownika: ' || SQLERRM,
      'error_type', 'database_error'
    );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzania czy pracownik jest niezależny
CREATE OR REPLACE FUNCTION is_independent_employee(
  p_employee_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM employees
    WHERE id = p_employee_id
    AND company_id IS NULL
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do pobierania niezależnych pracowników (dla debugowania)
CREATE OR REPLACE FUNCTION get_independent_employees()
RETURNS TABLE (
  id UUID,
  full_name TEXT,
  subscription_status TEXT,
  created_at TIMESTAMPTZ,
  last_status_change TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    e.id,
    e.full_name,
    e.subscription_status,
    e.created_at,
    e.last_status_change
  FROM employees e
  WHERE e.company_id IS NULL
  ORDER BY e.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Dodaj kolumnę employee_type jeśli nie istnieje (opcjonalnie)
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS employee_type TEXT DEFAULT 'COMPANY' 
CHECK (employee_type IN ('COMPANY', 'INDEPENDENT'));

-- Utwórz tabelę historii pracowników jeśli nie istnieje (opcjonalnie)
CREATE TABLE IF NOT EXISTS employee_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL,
  action_description TEXT,
  performed_by TEXT,
  performed_at TIMESTAMPTZ DEFAULT NOW(),
  old_company_id UUID,
  new_company_id UUID,
  metadata JSONB
);

-- Indeksy dla wydajności
CREATE INDEX IF NOT EXISTS idx_employees_company_id_null ON employees(id) WHERE company_id IS NULL;
CREATE INDEX IF NOT EXISTS idx_employees_employee_type ON employees(employee_type);
CREATE INDEX IF NOT EXISTS idx_employee_history_employee_id ON employee_history(employee_id);

-- Test funkcji (zakomentowane - odkomentuj do testowania)
/*
-- Sprawdź aktualny stan
SELECT 
  c.name as company_name,
  COUNT(e.id) as total_employees,
  COUNT(CASE WHEN e.company_id IS NULL THEN 1 END) as independent_employees
FROM companies c
LEFT JOIN employees e ON c.id = e.company_id
GROUP BY c.id, c.name
ORDER BY c.name;

-- Sprawdź niezależnych pracowników
SELECT * FROM get_independent_employees();
*/
