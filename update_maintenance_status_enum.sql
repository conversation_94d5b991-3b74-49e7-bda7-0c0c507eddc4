-- SQL code to update the maintenance_status enum type to include 'pending' and 'to_check' values

-- Method 1: For PostgreSQL 9.1+ - Create a new type, update the columns, drop the old type
DO $$
BEGIN
    -- Add the new values to the existing type
    -- This works in PostgreSQL 9.1+ (adding to an enum without recreating it)
    ALTER TYPE maintenance_status ADD VALUE IF NOT EXISTS 'pending' AFTER 'in_progress';
    ALTER TYPE maintenance_status ADD VALUE IF NOT EXISTS 'to_check' AFTER 'pending';
EXCEPTION
    WHEN others THEN
        -- If the simple approach fails, use the more complex approach
        -- Create a new enum type with all the values
        CREATE TYPE maintenance_status_new AS ENUM ('reported', 'in_progress', 'pending', 'to_check', 'resolved', 'canceled');
        
        -- Update the columns to use the new type (may need to adapt the table name and column if different)
        ALTER TABLE maintenance_reports 
            ALTER COLUMN status TYPE maintenance_status_new 
            USING status::text::maintenance_status_new;
        
        -- Drop the old type
        DROP TYPE maintenance_status;
        
        -- Rename the new type to the old name
        ALTER TYPE maintenance_status_new RENAME TO maintenance_status;
END$$;

-- Check if the altered type was successful
SELECT DISTINCT status FROM maintenance_reports; 