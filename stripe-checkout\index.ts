import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';
import { corsHeaders } from '../_shared/cors.ts';

// Konfiguracja Stripe
const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
const stripe = new Stripe(stripeApiKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient(),
});

// Funkcja do tworzenia sesji płatności w Stripe
serve(async (req) => {
  // Obsługa CORS dla zapytań preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Sprawdź metodę HTTP
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Metoda nie jest dozwolona' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Parsowanie danych z żądania
    const { companyId, planId, successUrl, cancelUrl } = await req.json();

    // Walidacja danych wejściowych
    if (!companyId || !planId || !successUrl || !cancelUrl) {
      return new Response(JSON.stringify({ error: 'Brakujące wymagane parametry' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Pobierz dane planu subskrypcji z Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Brakujące zmienne środowiskowe Supabase');
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${supabaseServiceKey}`,
      'apikey': supabaseServiceKey
    };
    
    // Pobierz dane planu subskrypcji
    const planResponse = await fetch(
      `${supabaseUrl}/rest/v1/subscription_plans?id=eq.${planId}&select=*`,
      { headers }
    );
    
    const plans = await planResponse.json();
    
    if (!plans || plans.length === 0) {
      return new Response(JSON.stringify({ error: 'Nie znaleziono planu subskrypcji' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    const plan = plans[0];
    
    // Pobierz dane firmy
    const companyResponse = await fetch(
      `${supabaseUrl}/rest/v1/companies?id=eq.${companyId}&select=name,email`,
      { headers }
    );
    
    const companies = await companyResponse.json();
    
    if (!companies || companies.length === 0) {
      return new Response(JSON.stringify({ error: 'Nie znaleziono firmy' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    const company = companies[0];
    
    // Sprawdź czy firma ma już klienta w Stripe
    let customerId;
    const customerResponse = await fetch(
      `${supabaseUrl}/rest/v1/company_subscriptions?company_id=eq.${companyId}&select=stripe_customer_id`,
      { headers }
    );
    
    const existingSubscriptions = await customerResponse.json();
    
    if (existingSubscriptions && existingSubscriptions.length > 0 && existingSubscriptions[0].stripe_customer_id) {
      customerId = existingSubscriptions[0].stripe_customer_id;
    } else {
      // Utwórz nowego klienta w Stripe
      const customer = await stripe.customers.create({
        email: company.email || '<EMAIL>',
        name: company.name || `Company ${companyId}`,
        metadata: {
          company_id: companyId
        }
      });
      
      customerId = customer.id;
    }
    
    // Utwórz sesję płatności w Stripe
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: plan.stripe_price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      subscription_data: {
        metadata: {
          company_id: companyId,
          plan_id: planId
        }
      }
    });
    
    // Zwróć URL do sesji płatności
    return new Response(JSON.stringify({ sessionUrl: session.url }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Błąd tworzenia sesji płatności:', error);
    return new Response(JSON.stringify({ error: `Błąd serwera: ${error.message}` }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}); 