import React, { useEffect, useState } from 'react';
import { View, LogBox, StyleSheet, Platform, StatusBar, Text } from 'react-native';
import { Session } from '@supabase/supabase-js';
import Dashboard from './Dashboard';
import { patchConsoleLog, unpatchConsoleLog } from './TextNodePatch';
import { ErrorBoundary } from './ErrorBoundary';
import { supabase } from '../services/supabaseClient';

// Custom error handler to intercept React errors
const customErrorHandler = (error: Error, errorInfo: React.ErrorInfo) => {
  console.log('🚨 CUSTOM ERROR CAUGHT:', error.message);
  console.log('ErrorInfo:', errorInfo.componentStack);
  
  // Check specifically for useInsertionEffect errors
  if (error.message && error.message.includes('useInsertionEffect')) {
    console.log('⚠️ DETECTED useInsertionEffect ERROR - this is likely caused by nested animation components');
  }
};

// Aggressively ignore all text node warnings
LogBox.ignoreLogs(['Unexpected text node']);

// App color scheme
const COLORS = {
  primary: '#1e6edf', // Exact match for the top bar blue
  background: '#f5f7fa',
  quickBar: '#f8fafc' // Exact match for the quick bar
};

// Calculate status bar height based on platform
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;
// Use an estimated height for the bottom safe area on iOS
const BOTTOM_SAFE_AREA_HEIGHT = Platform.OS === 'ios' ? 34 : 0;

interface DashboardWrapperProps {
  session: Session;
  onBack: () => void;
}

export const DashboardWrapper = ({ session, onBack }: DashboardWrapperProps) => {
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    StatusBar.setBackgroundColor('#1e6edf');
  }, []);

  // Handle network reconnection after returning from Stripe
  useEffect(() => {
    const handleAppStateChange = () => {
      console.log('App state changed - checking network connectivity');

      // Force refresh of Supabase connection
      setTimeout(() => {
        console.log('Refreshing dashboard after app state change');
        setRefreshKey(prev => prev + 1);
      }, 1000);
    };

    // Listen for app focus events (when returning from external browser)
    if (Platform.OS === 'web') {
      window.addEventListener('focus', handleAppStateChange);
      return () => {
        window.removeEventListener('focus', handleAppStateChange);
      };
    } else {
      // For mobile, we'll rely on the deep link handler in App.tsx
      // This is just a backup refresh mechanism
      const interval = setInterval(() => {
        // Check if we have network connectivity by testing Supabase connection
        supabase.from('companies').select('id').limit(1).then(() => {
          // Connection is working
        }).catch((error) => {
          console.log('Network connectivity issue detected:', error);
          // Force refresh
          setRefreshKey(prev => prev + 1);
        });
      }, 5000);

      return () => clearInterval(interval);
    }
  }, []);

  // Apply our console.log patches to prevent text node errors
  useEffect(() => {
    // Apply the patch when component mounts
    patchConsoleLog();
    
    // Restore original console functions on unmount
    return () => {
      unpatchConsoleLog();
    };
  }, []);

  // Custom fallback component for the ErrorBoundary
  const ErrorFallback = (
    <View style={styles.rootContainer}>
      <StatusBar barStyle="light-content" backgroundColor="#1e6edf" />
      
      {/* Top safe area - manually handled */}
      <View style={[styles.topSafeArea, { height: STATUSBAR_HEIGHT }]} />
      
      {/* Error message content area */}
      <View style={styles.innerContainer}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>SafeArea Error Detected</Text>
          <Text style={styles.errorMessage}>
            We encountered an issue with the SafeAreaProvider. We've applied a fix to keep the app running.
          </Text>
        </View>
      </View>
      
      {/* Bottom safe area - manually handled */}
      <View style={[styles.bottomSafeArea, { height: BOTTOM_SAFE_AREA_HEIGHT }]} />
    </View>
  );

  return (
    <View style={styles.rootContainer}>
      <StatusBar barStyle="light-content" backgroundColor="#1e6edf" />
      
      {/* Top safe area - manually handled */}
      <View style={[styles.topSafeArea, { height: STATUSBAR_HEIGHT }]} />
      
      {/* Main content area */}
      <View style={styles.innerContainer}>
        <ErrorBoundary
          fallbackComponent={ErrorFallback}
          onError={customErrorHandler}
        >
          <Dashboard
            key={refreshKey}
            session={session}
            onBack={onBack}
            hasParentSafeArea={false}
          />
        </ErrorBoundary>
      </View>
      
      {/* Bottom safe area - manually handled */}
      <View style={[styles.bottomSafeArea, { height: BOTTOM_SAFE_AREA_HEIGHT }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  topSafeArea: {
    backgroundColor: '#1e6edf',
    width: '100%'
  },
  innerContainer: {
    flex: 1,
  },
  bottomSafeArea: {
    backgroundColor: '#f8fafc',
    width: '100%'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  errorMessage: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  }
}); 