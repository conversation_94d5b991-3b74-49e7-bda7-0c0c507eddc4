-- Funkcja do aktualizacji statusu firmy na podstawie planu subskrypcji
CREATE OR REPLACE FUNCTION update_company_premium(
  company_uuid TEXT,
  plan_id_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
  plan_name TEXT;
  verification_limit INTEGER;
BEGIN
  -- <PERSON><PERSON><PERSON> nie podano plan_id, pob<PERSON>z pier<PERSON>zy dostępny plan
  IF plan_id_param IS NULL THEN
    SELECT name, 
           CASE
             WHEN name = 'Basic' OR name = 'Basic Yearly' THEN 5
             WHEN name = 'Pro' OR name = 'Pro Yearly' THEN 20
             WHEN name = 'Business' OR name = 'Business Yearly' THEN 999999 -- praktycznie nieograniczone
             ELSE 5 -- domyślny limit
           END INTO plan_name, verification_limit
    FROM subscription_plans 
    LIMIT 1;
  ELSE
    -- Pobierz dane planu na podstawie ID
    SELECT name,
           CASE
             WHEN name = 'Basic' OR name = 'Basic Yearly' THEN 5
             WHEN name = 'Pro' OR name = 'Pro Yearly' THEN 20
             WHEN name = 'Business' OR name = 'Business Yearly' THEN 999999 -- prakty<PERSON>nie nieograniczone
             ELSE 5 -- domyślny limit
           END INTO plan_name, verification_limit
    FROM subscription_plans 
    WHERE id = plan_id_param::UUID;
  END IF;
  
  -- Zaktualizuj status firmy i limit kodów weryfikacyjnych
  UPDATE companies 
  SET account_type = plan_name,
      verification_code_limit = verification_limit
  WHERE id = company_uuid::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funkcja do tworzenia subskrypcji
CREATE OR REPLACE FUNCTION create_subscription(
  company_uuid TEXT,
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  plan_id_param TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
  plan_id UUID;
  now_time TIMESTAMPTZ := now();
  period_end TIMESTAMPTZ;
  billing_period TEXT;
BEGIN
  -- Pobierz plan subskrypcji
  IF plan_id_param IS NULL THEN
    -- Jeśli nie podano plan_id, pobierz pierwszy dostępny plan
    SELECT id, billing_period INTO plan_id, billing_period 
    FROM subscription_plans 
    LIMIT 1;
  ELSE
    -- Pobierz plan na podstawie ID
    SELECT id, billing_period INTO plan_id, billing_period 
    FROM subscription_plans 
    WHERE id = plan_id_param::UUID;
  END IF;
  
  -- Ustaw datę końca okresu na podstawie typu planu
  IF billing_period = 'yearly' THEN
    period_end := now_time + INTERVAL '1 year';
  ELSE
    period_end := now_time + INTERVAL '1 month';
  END IF;
  
  -- Utwórz wpis w tabeli company_subscriptions
  INSERT INTO company_subscriptions (
    company_id, 
    plan_id, 
    stripe_subscription_id, 
    stripe_customer_id, 
    status, 
    current_period_start, 
    current_period_end, 
    cancel_at_period_end
  ) VALUES (
    company_uuid::UUID,
    plan_id,
    COALESCE(stripe_subscription_id, 'sub_' || gen_random_uuid()),
    COALESCE(stripe_customer_id, 'cus_' || gen_random_uuid()),
    'active',
    now_time,
    period_end,
    false
  );
  
  -- Dodaj wpis do historii płatności
  INSERT INTO payment_history (
    company_id,
    subscription_id,
    amount,
    currency,
    status,
    description
  ) VALUES (
    company_uuid::UUID,
    (SELECT id FROM company_subscriptions WHERE company_id = company_uuid::UUID ORDER BY created_at DESC LIMIT 1),
    (SELECT price FROM subscription_plans WHERE id = plan_id),
    'usd',
    'succeeded',
    'Subskrypcja ' || (SELECT name FROM subscription_plans WHERE id = plan_id)
  );
  
  -- Aktualizuj status firmy i limit kodów weryfikacyjnych
  PERFORM update_company_premium(company_uuid, plan_id::TEXT);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funkcja do zapisywania zdarzeń webhook
CREATE OR REPLACE FUNCTION log_webhook_event(
  event_type TEXT,
  event_id TEXT,
  company_uuid TEXT,
  event_data JSONB
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO webhook_logs (
    event_type,
    event_id,
    company_id,
    data
  ) VALUES (
    event_type,
    event_id,
    company_uuid,
    event_data
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 