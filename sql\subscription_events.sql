-- Tabela do przechowywania historii zdarzeń subskrypcji
create table subscription_events (
  id uuid default uuid_generate_v4() primary key,
  company_id uuid references companies(id) on delete cascade,
  subscription_id uuid references company_subscriptions(id) on delete cascade,
  event_type text not null,
  event_data jsonb default '{}',
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Indeksy
create index subscription_events_company_id_idx on subscription_events(company_id);
create index subscription_events_subscription_id_idx on subscription_events(subscription_id);
create index subscription_events_event_type_idx on subscription_events(event_type);
create index subscription_events_created_at_idx on subscription_events(created_at);

-- RLS policies
alter table subscription_events enable row level security;

-- Polityka dla company_managers - mogą widzieć wszystkie zdarzenia swojej firmy
create policy "Company managers can view their company's subscription events"
  on subscription_events for select
  to authenticated
  using (
    exists (
      select 1 from employees e
      where e.company_id = subscription_events.company_id
      and e.id = auth.uid()
      and e.role = 'company_manager'
    )
  );

-- Polityka dla pracowników - nie mogą widzieć zdarzeń subskrypcji
create policy "Employees cannot view subscription events"
  on subscription_events for select
  to authenticated
  using (false);

-- Trigger do aktualizacji updated_at
create trigger set_subscription_events_updated_at
  before update on subscription_events
  for each row
  execute function update_updated_at_column(); 