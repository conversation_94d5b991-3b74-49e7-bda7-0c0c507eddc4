-- Kod SQL do dodania kolumny photos do tabeli purchases
-- Wykonaj ten kod w SQL Editor w konsoli Supabase

-- Dodanie kolumny photos jako tablicy tekstowej (array of text) do przechowywania URLi zdjęć
ALTER TABLE purchases 
ADD COLUMN IF NOT EXISTS photos TEXT[] DEFAULT '{}';

-- Dodajemy komentarz do kolumny, aby wyja<PERSON><PERSON> jej przeznaczenie
COMMENT ON COLUMN purchases.photos IS 'Tablica URLi do zdjęć awarii związanych z wnioskiem zakupowym';

-- <PERSON>pra<PERSON><PERSON><PERSON><PERSON> czy kolumna została pomyślnie dodana
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'purchases' AND column_name = 'photos'
    ) THEN
        RAISE NOTICE 'Kolumna photos została pomyślnie dodana do tabeli purchases.';
    ELSE
        RAISE NOTICE 'Uwaga: Nie udało się dodać kolumny photos do tabeli purchases!';
    END IF;
END $$;

-- Dodajmy indeks dla nowej kolumny, aby prz<PERSON><PERSON><PERSON>ć wyszukiwanie
CREATE INDEX IF NOT EXISTS purchases_photos_idx ON purchases USING gin(photos);

-- Wyświetl strukturę tabeli po zmianach
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'purchases' 
ORDER BY ordinal_position; 