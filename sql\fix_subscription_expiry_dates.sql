-- Poprawka: W<PERSON><PERSON><PERSON><PERSON> daty ostatniej zmiany przy automatycznej dezaktywacji z powodu wygaśnięcia subskrypcji
-- D<PERSON>ęki temu pracownicy będą mogli być ręcznie aktywowani w ramach darmowego planu

-- 1. Zaktualizu<PERSON> funkcję automatycznej dezaktywacji przy wygaśnięciu subskrypcji
CREATE OR REPLACE FUNCTION deactivate_company_employees_on_expiry(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Dezaktywuj pracowników - WYZERUJ last_status_change i last_manual_status_change
  -- aby umo<PERSON><PERSON><PERSON><PERSON> r<PERSON> aktywację w ramach darmowego planu
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NULL,
      last_manual_status_change = NULL
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE'
  AND NOT EXISTS (
    SELECT 1 FROM company_subscriptions
    WHERE company_id = p_company_id
    AND status = 'active'
    AND current_period_end > NOW()
  );
  
  RAISE NOTICE 'Dezaktywowano pracowników firmy % z wyzerowanymi datami', p_company_id;
END;
$$ LANGUAGE plpgsql;

-- 2. Zaktualizuj funkcję ogólnej dezaktywacji (używaną w webhook)
CREATE OR REPLACE FUNCTION deactivate_company_employees(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Dezaktywuj pracowników - WYZERUJ daty aby umożliwić ręczną aktywację
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NULL,
      last_manual_status_change = NULL
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';
  
  RAISE NOTICE 'Dezaktywowano wszystkich pracowników firmy % z wyzerowanymi datami', p_company_id;
END;
$$ LANGUAGE plpgsql;

-- 3. Wyzeruj daty dla już zdezaktywowanych pracowników (jednorazowa poprawka)
UPDATE employees
SET last_status_change = NULL,
    last_manual_status_change = NULL
WHERE subscription_status = 'SUBSCRIPTION_EXPIRED'
AND company_id IN (
  SELECT c.id 
  FROM companies c
  WHERE c.account_type = 'free'
  OR NOT EXISTS (
    SELECT 1 FROM company_subscriptions cs
    WHERE cs.company_id = c.id
    AND cs.status = 'active'
    AND cs.current_period_end > NOW()
  )
);

-- 4. Dodaj funkcję do sprawdzania czy firma ma aktywną subskrypcję Premium
CREATE OR REPLACE FUNCTION company_has_active_premium_subscription(
  p_company_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM company_subscriptions
    WHERE company_id = p_company_id
    AND status = 'active'
    AND current_period_end > NOW()
  );
END;
$$ LANGUAGE plpgsql;

-- 5. Zaktualizuj funkcję sprawdzania ograniczeń czasowych
CREATE OR REPLACE FUNCTION can_change_employee_status(
  p_employee_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  v_last_change TIMESTAMPTZ;
  v_company_id UUID;
  v_has_premium BOOLEAN;
BEGIN
  -- Pobierz company_id i datę ostatniej ręcznej zmiany
  SELECT company_id, last_manual_status_change
  INTO v_company_id, v_last_change
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź czy firma ma aktywną subskrypcję Premium
  SELECT company_has_active_premium_subscription(v_company_id) INTO v_has_premium;

  -- Jeśli firma ma Premium, nie ma ograniczeń czasowych
  IF v_has_premium THEN
    RETURN TRUE;
  END IF;

  -- Jeśli nie ma daty ostatniej zmiany, można zmienić
  IF v_last_change IS NULL THEN
    RETURN TRUE;
  END IF;

  -- Sprawdź czy minęło 7 dni od ostatniej RĘCZNEJ zmiany
  RETURN v_last_change <= NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- 6. Zaktualizuj funkcję aktywacji pracownika (ręczna zmiana)
CREATE OR REPLACE FUNCTION activate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_subscription_limit INTEGER;
  v_active_count INTEGER;
  v_employee_name TEXT;
  v_has_premium BOOLEAN;
BEGIN
  -- Sprawdź czy pracownik należy do firmy
  IF NOT EXISTS (
    SELECT 1 FROM employees
    WHERE id = p_employee_id
    AND company_id = p_company_id
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie należy do tej firmy',
      'error_type', 'invalid_employee'
    );
  END IF;

  -- Pobierz imię pracownika
  SELECT full_name INTO v_employee_name
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź czy firma ma Premium
  SELECT company_has_active_premium_subscription(p_company_id) INTO v_has_premium;

  -- Sprawdź ograniczenia czasowe (tylko dla firm bez Premium)
  IF NOT v_has_premium AND NOT can_change_employee_status(p_employee_id) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" może być aktywowany dopiero za kilka dni. Ostatnia zmiana statusu była zbyt niedawno.',
      'error_type', 'time_limit',
      'show_upgrade', true
    );
  END IF;

  -- Pobierz limit aktywnych pracowników
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz aktualną liczbę aktywnych pracowników
  SELECT COUNT(*) INTO v_active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Sprawdź limit pracowników (tylko dla firm bez Premium)
  IF NOT v_has_premium AND v_active_count >= v_subscription_limit THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Osiągnięto limit aktywnych pracowników (' || v_subscription_limit || ') dla darmowego planu. Aktualnie aktywnych: ' || v_active_count,
      'error_type', 'employee_limit',
      'show_upgrade', true
    );
  END IF;

  -- Aktywuj pracownika z ustawieniem dat ręcznej zmiany
  UPDATE employees
  SET subscription_status = 'ACTIVE',
      last_status_change = NOW(),
      last_manual_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został aktywowany',
    'show_upgrade', false,
    'alert_type', 'success'
  );
END;
$$ LANGUAGE plpgsql;

-- 7. Zaktualizuj funkcję dezaktywacji pracownika (ręczna zmiana)
CREATE OR REPLACE FUNCTION deactivate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_employee_name TEXT;
  v_has_premium BOOLEAN;
BEGIN
  -- Sprawdź czy pracownik należy do firmy
  IF NOT EXISTS (
    SELECT 1 FROM employees
    WHERE id = p_employee_id
    AND company_id = p_company_id
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie należy do tej firmy',
      'error_type', 'invalid_employee'
    );
  END IF;

  -- Pobierz imię pracownika
  SELECT full_name INTO v_employee_name
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź czy firma ma Premium
  SELECT company_has_active_premium_subscription(p_company_id) INTO v_has_premium;

  -- Sprawdź ograniczenia czasowe (tylko dla firm bez Premium)
  IF NOT v_has_premium AND NOT can_change_employee_status(p_employee_id) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" może być dezaktywowany dopiero za kilka dni. Ostatnia zmiana statusu była zbyt niedawno.',
      'error_type', 'time_limit',
      'show_upgrade', true
    );
  END IF;

  -- Dezaktywuj pracownika z ustawieniem dat ręcznej zmiany
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW(),
      last_manual_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został dezaktywowany',
    'show_upgrade', false,
    'alert_type', 'success'
  );
END;
$$ LANGUAGE plpgsql;

-- 8. Dodaj funkcję specjalnie do automatycznej dezaktywacji przy wygaśnięciu (bez ustawiania dat)
CREATE OR REPLACE FUNCTION auto_deactivate_company_employees_on_expiry(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Automatyczna dezaktywacja - NIE ustawiaj żadnych dat
  -- aby umożliwić natychmiastową ręczną aktywację w darmowym planie
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED'
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  RAISE NOTICE 'Automatycznie dezaktywowano pracowników firmy % bez ustawiania dat', p_company_id;
END;
$$ LANGUAGE plpgsql;

-- 9. Zaktualizuj trigger do używania nowej funkcji
CREATE OR REPLACE FUNCTION update_employees_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Reaktywuj pracowników przy aktywacji subskrypcji
  IF NEW.status = 'active' AND (OLD.status IS NULL OR OLD.status != 'active') THEN
    PERFORM reactivate_company_employees(NEW.company_id);
  END IF;

  -- Dezaktywuj pracowników przy wygaśnięciu - używaj funkcji bez ustawiania dat
  IF NEW.status = 'expired' AND OLD.status = 'active' THEN
    PERFORM auto_deactivate_company_employees_on_expiry(NEW.company_id);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 10. Zaktualizuj webhook handler do używania nowej funkcji
-- Ta funkcja powinna być używana w webhook zamiast deactivate_company_employees
CREATE OR REPLACE FUNCTION webhook_deactivate_company_employees(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Webhook dezaktywacja - NIE ustawiaj dat aby umożliwić ręczną aktywację
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED'
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  RAISE NOTICE 'Webhook: Dezaktywowano pracowników firmy % bez ustawiania dat', p_company_id;
END;
$$ LANGUAGE plpgsql;

-- 11. INSTRUKCJE DLA WEBHOOK HANDLERÓW:
-- W plikach webhook handlerów zamień wywołania:
--
-- STARE: supabase.rpc('deactivate_company_employees', { p_company_id: companyId })
-- NOWE: supabase.rpc('webhook_deactivate_company_employees', { p_company_id: companyId })
--
-- Pliki do zaktualizowania:
-- - supabase/functions/stripe-webhook/index.ts (linia ~252)
-- - supabase/functions/smooth-handler/index.ts (linie ~979, ~1060)
--
-- Lub zostaw stare wywołania - funkcja deactivate_company_employees została już zaktualizowana
-- aby nie ustawiać dat, więc będzie działać poprawnie.

-- 12. Wyzeruj daty dla wszystkich obecnie zdezaktywowanych pracowników (jednorazowa poprawka)
UPDATE employees
SET last_status_change = NULL,
    last_manual_status_change = NULL
WHERE subscription_status = 'SUBSCRIPTION_EXPIRED';

-- 13. Sprawdź wyniki
SELECT
  c.name as company_name,
  c.account_type,
  COUNT(e.id) as total_employees,
  COUNT(CASE WHEN e.subscription_status = 'ACTIVE' THEN 1 END) as active_employees,
  COUNT(CASE WHEN e.subscription_status = 'SUBSCRIPTION_EXPIRED' THEN 1 END) as expired_employees,
  COUNT(CASE WHEN e.last_status_change IS NULL THEN 1 END) as employees_with_null_dates
FROM companies c
LEFT JOIN employees e ON c.id = e.company_id
GROUP BY c.id, c.name, c.account_type
ORDER BY c.name;
