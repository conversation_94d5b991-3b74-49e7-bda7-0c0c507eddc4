-- Funkcja specjalna dla webhook handlerów do dezaktywacji pracowników
-- NIE ustawia last_status_change ani last_manual_status_change
-- <PERSON><PERSON><PERSON><PERSON> temu administrator może natychmiast aktywować pracowników w ramach darmowego planu

CREATE OR REPLACE FUNCTION webhook_deactivate_company_employees(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Webhook dezaktywacja - WYZERUJ daty aby umożliwić ręczną aktywację
  UPDATE employees
  SET
    subscription_status = 'SUBSCRIPTION_EXPIRED',
    last_status_change = NULL,
    last_manual_status_change = NULL
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  RAISE NOTICE 'Webhook: Dezaktywowano pracowników firmy % i wyzerowano daty', p_company_id;
END;
$$ LANGUAGE plpgsql;

-- Komentarz: Ta funkcja jest używana w webhook handlerach gdy subskrypcja wygasa.
-- W przeciwieństwie do deactivate_company_employees, nie ustawia last_status_change
-- ani last_manual_status_change, co pozwala administratorowi na natychmiastową
-- aktywację pracowników w ramach limitów darmowego planu.
