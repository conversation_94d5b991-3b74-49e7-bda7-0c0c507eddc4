-- Kod SQL do utworzenia bucketu product-images w Supabase Storage
-- Wykonaj ten kod w SQL Editor w konsoli Supabase

-- Ze względu na ograniczenia SQL, potrzebujemy użyć funkcji pl/pgsql do utworzenia bucketu
DO $$
DECLARE
  bucket_exists BOOLEAN;
BEGIN
  -- Sprawdź czy bucket już istnieje
  SELECT EXISTS (
    SELECT 1 FROM storage.buckets WHERE name = 'product-images'
  ) INTO bucket_exists;
  
  -- Jeśli bucket nie istnieje, utwórz go
  IF NOT bucket_exists THEN
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('product-images', 'product-images', TRUE);
    
    RAISE NOTICE 'Bucket product-images został pomyślnie utworzony.';
  ELSE
    RAISE NOTICE 'Bucket product-images już istnieje.';
  END IF;
  
  -- Utwórz politykę dla bucketu, aby zezwoli<PERSON> na dodawanie plików przez każdego uwierzytelnionego użytkownika
  -- Najpierw usuń istniejącą politykę, jeśli istnieje
  BEGIN
    DELETE FROM storage.policies 
    WHERE bucket_id = 'product-images' AND name = 'Allow authenticated users to upload files';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Error removing existing policy: %', SQLERRM;
  END;
  
  -- Utwórz politykę INSERT
  INSERT INTO storage.policies (bucket_id, name, definition, allow_insert)
  VALUES (
    'product-images',
    'Allow authenticated users to upload files',
    '(auth.role() = ''authenticated'')',
    TRUE
  );
  
  -- Utwórz politykę SELECT aby każdy uwierzytelniony użytkownik mógł odczytywać pliki
  BEGIN
    DELETE FROM storage.policies 
    WHERE bucket_id = 'product-images' AND name = 'Allow authenticated users to view files';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Error removing existing policy: %', SQLERRM;
  END;
  
  INSERT INTO storage.policies (bucket_id, name, definition, allow_select)
  VALUES (
    'product-images',
    'Allow authenticated users to view files',
    '(auth.role() = ''authenticated'')',
    TRUE
  );
  
  -- Utwórz politykę dla publicznego dostępu (odczyt)
  BEGIN
    DELETE FROM storage.policies 
    WHERE bucket_id = 'product-images' AND name = 'Allow public access to files';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Error removing existing policy: %', SQLERRM;
  END;
  
  INSERT INTO storage.policies (bucket_id, name, definition, allow_select)
  VALUES (
    'product-images',
    'Allow public access to files',
    'true',
    TRUE
  );
  
  RAISE NOTICE 'Polityki dostępu dla bucketu product-images zostały zaktualizowane.';
END $$; 