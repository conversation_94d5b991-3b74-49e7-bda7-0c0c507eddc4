// <PERSON>ript to execute SQL directly in the database
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase credentials
const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// SQL to create webhook_logs table
const createWebhookLogsTableSQL = `
-- Tabela do logowania zdarzeń webhook ze Stripe
CREATE TABLE IF NOT EXISTS webhook_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type TEXT NOT NULL,
  event_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Polityka bezpieczeństwa
ALTER TABLE webhook_logs ENABLE ROW LEVEL SECURITY;

-- Tylko administratorzy mogą widzieć logi webhooków
CREATE POLICY "Tylko administratorzy mogą widzieć logi webhooków" 
  ON webhook_logs FOR SELECT 
  USING (auth.role() = 'service_role' OR 
         EXISTS (SELECT 1 FROM companies WHERE companies.id = webhook_logs.company_id AND companies.owner_id = auth.uid()));
`;

async function executeSQL(sql) {
  try {
    // Execute SQL using REST API directly
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/execute_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({ sql })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error executing SQL: ${response.status} - ${errorText}`);
      return false;
    }

    console.log('SQL executed successfully');
    return true;
  } catch (err) {
    console.error('Error executing SQL:', err);
    return false;
  }
}

async function main() {
  console.log('Creating webhook_logs table...');
  
  // Split SQL into separate statements
  const statements = createWebhookLogsTableSQL
    .split(';')
    .filter(stmt => stmt.trim().length > 0)
    .map(stmt => stmt.trim() + ';');
  
  for (const statement of statements) {
    console.log(`Executing: ${statement.substring(0, 50)}...`);
    await executeSQL(statement);
  }
  
  console.log('Done!');
}

main(); 