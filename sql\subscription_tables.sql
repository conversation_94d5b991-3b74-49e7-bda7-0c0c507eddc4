-- Tabela planów subskrypcji
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  price INTEGER NOT NULL, -- cena w naj<PERSON><PERSON><PERSON><PERSON><PERSON> jednostkach waluty (np. grosze)
  billing_period TEXT NOT NULL CHECK (billing_period IN ('monthly', 'yearly')),
  features JSON<PERSON>,
  stripe_price_id TEXT,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Tabela subskrypcji firm
CREATE TABLE IF NOT EXISTS company_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  plan_id UUID NOT NULL REFERENCES subscription_plans(id),
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'past_due', 'incomplete', 'incomplete_expired', 'trialing', 'unpaid')),
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  cancel_at_period_end BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Historia płatności
CREATE TABLE IF NOT EXISTS payment_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES company_subscriptions(id),
  stripe_payment_intent_id TEXT,
  stripe_invoice_id TEXT,
  amount INTEGER NOT NULL, -- kwota w najmniejszych jednostkach waluty
  currency TEXT DEFAULT 'pln',
  status TEXT NOT NULL CHECK (status IN ('succeeded', 'pending', 'failed')),
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Metody płatności
CREATE TABLE IF NOT EXISTS payment_methods (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  stripe_payment_method_id TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('card', 'bank_account')),
  card_brand TEXT,
  card_last4 TEXT,
  card_exp_month INTEGER,
  card_exp_year INTEGER,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Dodanie funkcji wyzwalacza do aktualizacji updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Wyzwalacze do aktualizacji updated_at
CREATE TRIGGER update_subscription_plans_updated_at
BEFORE UPDATE ON subscription_plans
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_company_subscriptions_updated_at
BEFORE UPDATE ON company_subscriptions
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at
BEFORE UPDATE ON payment_methods
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Przykładowe plany subskrypcji
INSERT INTO subscription_plans (name, description, price, billing_period, features, stripe_price_id, active)
VALUES
  ('Basic', 'Plan podstawowy z 5 kodami weryfikacyjnymi', 1990, 'monthly',
   '["5 kodów weryfikacyjnych", "Podstawowe wsparcie", "1 konto administratora"]',
   'price_basic_monthly', true),
  ('Pro', 'Plan rozszerzony z 20 kodami weryfikacyjnymi i dodatkowymi funkcjami', 3990, 'monthly',
   '["20 kodów weryfikacyjnych", "Priorytetowe wsparcie", "3 konta administratorów", "Rozszerzone raporty"]',
   'price_pro_monthly', true),
  ('Business', 'Plan biznesowy z nieograniczoną liczbą kodów weryfikacyjnych', 8990, 'monthly',
   '["Nieograniczona liczba kodów weryfikacyjnych", "Dedykowany opiekun klienta", "10 kont administratorów", "Własne logo", "Zaawansowane raporty i analizy"]',
   'price_business_monthly', true),
  ('Basic Yearly', 'Plan podstawowy z 5 kodami weryfikacyjnymi (płatność roczna)', 19900, 'yearly',
   '["5 kodów weryfikacyjnych", "Podstawowe wsparcie", "1 konto administratora", "Rabat za płatność roczną"]',
   'price_basic_yearly', true),
  ('Pro Yearly', 'Plan rozszerzony z 20 kodami weryfikacyjnymi i dodatkowymi funkcjami (płatność roczna)', 39900, 'yearly',
   '["20 kodów weryfikacyjnych", "Priorytetowe wsparcie", "3 konta administratorów", "Rozszerzone raporty", "Rabat za płatność roczną"]',
   'price_pro_yearly', true),
  ('Business Yearly', 'Plan biznesowy z nieograniczoną liczbą kodów weryfikacyjnych (płatność roczna)', 89900, 'yearly',
   '["Nieograniczona liczba kodów weryfikacyjnych", "Dedykowany opiekun klienta", "10 kont administratorów", "Własne logo", "Zaawansowane raporty i analizy", "Rabat za płatność roczną"]',
   'price_business_yearly', true);

-- Dodanie kolumny do tabeli company, aby wskazać typ planu (free/premium)
ALTER TABLE IF EXISTS companies
  ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'free' CHECK (account_type IN ('free', 'premium')),
  ADD COLUMN IF NOT EXISTS verification_code_limit INTEGER DEFAULT 2;

-- Funkcje bezpieczeństwa na poziomie wierszy (RLS)
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;

-- Polityki bezpieczeństwa
CREATE POLICY "Plany subskrypcji są publiczne dla wszystkich" 
  ON subscription_plans FOR SELECT 
  USING (active = true);

CREATE POLICY "Tylko firma może widzieć swoje subskrypcje" 
  ON company_subscriptions FOR SELECT 
  USING (company_id = auth.uid() OR 
         EXISTS (SELECT 1 FROM employees WHERE employees.company_id = company_subscriptions.company_id AND employees.id = auth.uid() AND role = 'admin'));

CREATE POLICY "Tylko firma może widzieć swoją historię płatności" 
  ON payment_history FOR SELECT 
  USING (company_id = auth.uid() OR 
         EXISTS (SELECT 1 FROM employees WHERE employees.company_id = payment_history.company_id AND employees.id = auth.uid() AND role = 'admin'));

CREATE POLICY "Tylko firma może widzieć swoje metody płatności" 
  ON payment_methods FOR SELECT 
  USING (company_id = auth.uid() OR 
         EXISTS (SELECT 1 FROM employees WHERE employees.company_id = payment_methods.company_id AND employees.id = auth.uid() AND role = 'admin'));

-- Funkcja do sprawdzania czy firma ma aktywną subskrypcję premium
CREATE OR REPLACE FUNCTION has_active_subscription(p_company_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  has_sub BOOLEAN;
BEGIN
  SELECT EXISTS(
    SELECT 1 FROM company_subscriptions 
    WHERE company_id = p_company_id 
      AND status = 'active' 
      AND current_period_end > now()
  ) INTO has_sub;
  
  RETURN has_sub;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzania limitu kodów weryfikacyjnych
CREATE OR REPLACE FUNCTION check_verification_code_limit(p_company_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  used_codes INTEGER;
  company_limit INTEGER;
  is_premium BOOLEAN;
BEGIN
  -- Sprawdź czy firma ma aktywną subskrypcję premium
  is_premium := has_active_subscription(p_company_id);

  -- Pobierz limit kodów dla firmy
  SELECT verification_code_limit INTO company_limit
  FROM companies WHERE id = p_company_id;

  -- Jeśli firma ma premium, nie ma limitu
  IF is_premium THEN
    RETURN TRUE;
  END IF;

  -- Jeśli nie ma premium, sprawdź limit
  SELECT COUNT(*) INTO used_codes
  FROM verification_codes
  WHERE company_id = p_company_id;

  RETURN used_codes < company_limit;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do zarządzania statusami pracowników przy zmianach subskrypcji
CREATE OR REPLACE FUNCTION manage_employee_status_on_subscription_change(
  p_company_id UUID,
  p_subscription_status TEXT
)
RETURNS VOID AS $$
BEGIN
  -- Jeśli subskrypcja jest aktywna, reaktywuj wszystkich zawieszonych pracowników
  IF p_subscription_status = 'active' THEN
    UPDATE employees
    SET status = 'active',
        updated_at = now()
    WHERE company_id = p_company_id
      AND status = 'suspended';

    RAISE NOTICE 'Reaktywowano pracowników dla firmy %', p_company_id;

  -- Jeśli subskrypcja została anulowana/wygasła, zawieś pracowników
  ELSIF p_subscription_status IN ('canceled', 'past_due', 'incomplete_expired', 'unpaid') THEN
    UPDATE employees
    SET status = 'suspended',
        updated_at = now()
    WHERE company_id = p_company_id
      AND status = 'active';

    RAISE NOTICE 'Zawieszono pracowników dla firmy %', p_company_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Wyzwalacz do sprawdzania limitu przed dodaniem kodu weryfikacyjnego
CREATE OR REPLACE FUNCTION check_verification_code_limit_trigger()
RETURNS TRIGGER AS $$
BEGIN
  IF NOT check_verification_code_limit(NEW.company_id) THEN
    RAISE EXCEPTION 'Osiągnięto limit kodów weryfikacyjnych dla darmowego konta';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_verification_code_limit_before_insert
BEFORE INSERT ON verification_codes
FOR EACH ROW EXECUTE PROCEDURE check_verification_code_limit_trigger(); 