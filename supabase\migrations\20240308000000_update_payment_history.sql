-- Aktualizacja tabeli payment_history
ALTER TABLE payment_history
ADD COLUMN IF NOT EXISTS billing_period_start TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS billing_period_end TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS payment_date TIMESTAMP WITH TIME ZONE;

-- <PERSON><PERSON><PERSON> indeksów dla optymalizacji zapytań (jeśli nie istnieją)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_payment_history_company_id') THEN
        CREATE INDEX idx_payment_history_company_id ON payment_history(company_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_payment_history_payment_date') THEN
        CREATE INDEX idx_payment_history_payment_date ON payment_history(payment_date);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_payment_history_status') THEN
        CREATE INDEX idx_payment_history_status ON payment_history(status);
    END IF;
END $$;

-- <PERSON><PERSON>ie ograniczenia (jeśli nie istnieje)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'payment_history_status_check') THEN
        ALTER TABLE payment_history
        ADD CONSTRAINT payment_history_status_check 
        CHECK (status IN ('pending', 'succeeded', 'failed', 'refunded', 'canceled'));
    END IF;
END $$;

-- Dodanie komentarzy do kolumn
COMMENT ON COLUMN payment_history.billing_period_start IS 'Początek okresu rozliczeniowego';
COMMENT ON COLUMN payment_history.billing_period_end IS 'Koniec okresu rozliczeniowego';
COMMENT ON COLUMN payment_history.payment_date IS 'Data realizacji płatności';
COMMENT ON COLUMN payment_history.status IS 'Status płatności: pending, succeeded, failed, refunded, canceled';

-- Upewnienie się, że mamy wszystkie potrzebne kolumny
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'payment_history' AND column_name = 'stripe_invoice_id') THEN
        ALTER TABLE payment_history ADD COLUMN stripe_invoice_id VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'payment_history' AND column_name = 'stripe_payment_intent_id') THEN
        ALTER TABLE payment_history ADD COLUMN stripe_payment_intent_id VARCHAR(255);
    END IF;
END $$; 