-- Aktualizacja limitów kodów weryfikacyjnych w planach subskrypcji
-- Nowe limity: free=2, basic=5, pro=20, business=unlimited

-- 1. Zaktualizuj opisy planów w tabeli subscription_plans
UPDATE subscription_plans 
SET 
  description = 'Plan podstawowy z 5 kodami weryfikacyjnymi',
  features = '["5 kodów weryfikacyjnych", "Podstawowe wsparcie", "1 konto administratora"]'
WHERE name = 'Basic';

UPDATE subscription_plans 
SET 
  description = 'Plan rozszerzony z 20 kodami weryfikacyjnymi i dodatkowymi funkcjami',
  features = '["20 kodów weryfikacyjnych", "Priorytetowe wsparcie", "3 konta administratorów", "Rozszerzone raporty"]'
WHERE name = 'Pro';

UPDATE subscription_plans 
SET 
  description = 'Plan podstawowy z 5 kodami weryfikacyjnymi (płatność roczna)',
  features = '["5 kodów weryfikacyjnych", "Podstawowe wsparcie", "1 konto administratora", "Rabat za płatność roczną"]'
WHERE name = 'Basic Yearly';

UPDATE subscription_plans 
SET 
  description = 'Plan rozszerzony z 20 kodami weryfikacyjnymi i dodatkowymi funkcjami (płatność roczna)',
  features = '["20 kodów weryfikacyjnych", "Priorytetowe wsparcie", "3 konta administratorów", "Rozszerzone raporty", "Rabat za płatność roczną"]'
WHERE name = 'Pro Yearly';

-- 2. Zaktualizuj limity dla istniejących firm z aktywnymi subskrypcjami
UPDATE companies 
SET verification_code_limit = 5
WHERE account_type LIKE '%Basic%' OR account_type = 'basic';

UPDATE companies 
SET verification_code_limit = 20
WHERE account_type LIKE '%Pro%' OR account_type = 'pro';

UPDATE companies 
SET verification_code_limit = 999999
WHERE account_type LIKE '%Business%' OR account_type = 'business';

-- 3. Upewnij się, że firmy z planem free mają limit 2
UPDATE companies 
SET verification_code_limit = 2
WHERE account_type = 'free';

-- 4. Sprawdź wyniki
SELECT 
  account_type,
  COUNT(*) as company_count,
  verification_code_limit
FROM companies 
GROUP BY account_type, verification_code_limit
ORDER BY account_type;

-- 5. Wyświetl zaktualizowane plany
SELECT 
  name,
  description,
  features,
  billing_period
FROM subscription_plans 
WHERE active = true
ORDER BY 
  CASE 
    WHEN name LIKE '%Basic%' THEN 1
    WHEN name LIKE '%Pro%' THEN 2
    WHEN name LIKE '%Business%' THEN 3
    ELSE 4
  END,
  billing_period;
