import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';

// Ustaw swoje dane konfiguracyjne
const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET') || '';
const stripe = new Stripe(stripeApiKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient(),
});

// Mapowanie ID produktów Stripe na nazwy planów
const stripePlanMapping: Record<string, string> = {
  'prod_PUvVGpVvbdcYGn': 'Basic',
  'prod_PUvWlcBSdSPMbw': 'Pro',
  'prod_PUvXOXMGcMTAuc': 'Business',
  // Dodaj więcej mapowań w razie potrzeby
};

// Funkcja pomocnicza do logowania zdarzeń webhook
async function logWebhookEvent(event: any) {
  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
  
  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('Brakujące zmienne środowiskowe Supabase');
    return;
  }
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseServiceKey}`,
    'apikey': supabaseServiceKey
  };
  
  try {
    const logData = {
      event_type: event.type,
      data: event,
      created_at: new Date().toISOString()
    };
    
    const response = await fetch(
      `${supabaseUrl}/rest/v1/webhook_logs`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(logData)
      }
    );
    
    if (!response.ok) {
      console.error('Błąd podczas logowania zdarzenia webhook:', await response.text());
    }
  } catch (error) {
    console.error('Błąd podczas logowania zdarzenia webhook:', error);
  }
}

// Funkcja pomocnicza do aktualizacji subskrypcji w Supabase
async function updateSubscriptionInSupabase(subscription: any, event: string) {
  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Brakujące zmienne środowiskowe Supabase');
  }
  
  // Pobierz metadane subskrypcji, które zawierają company_id
  const companyId = subscription.metadata?.company_id;
  
  if (!companyId) {
    throw new Error('Nie znaleziono company_id w metadanych subskrypcji');
  }
  
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseServiceKey}`,
    'apikey': supabaseServiceKey
  };
  
  try {
    // Pobierz informacje o produkcie, aby określić plan
    let planName = 'Basic'; // Domyślnie Basic
    let planId = null;
    
    if (subscription.items && subscription.items.data && subscription.items.data.length > 0) {
      const item = subscription.items.data[0];
      if (item.price && item.price.product) {
        const productId = typeof item.price.product === 'string' 
          ? item.price.product 
          : item.price.product.id;
        
        // Użyj mapowania do określenia nazwy planu
        planName = stripePlanMapping[productId] || 'Basic';
        
        // Pobierz ID planu z Supabase
        const planResponse = await fetch(
          `${supabaseUrl}/rest/v1/subscription_plans?select=id&name=eq.${planName}`,
          { headers }
        );
        
        const plans = await planResponse.json();
        if (plans && plans.length > 0) {
          planId = plans[0].id;
        }
      }
    }
    
    // Znajdź istniejącą subskrypcję
    const getResponse = await fetch(
      `${supabaseUrl}/rest/v1/company_subscriptions?company_id=eq.${companyId}&stripe_subscription_id=eq.${subscription.id}`,
      { headers }
    );
    
    const existingSubscriptions = await getResponse.json();
    const exists = existingSubscriptions && existingSubscriptions.length > 0;
    
    // Przygotuj dane subskrypcji
    const subscriptionData = {
      company_id: companyId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer,
      plan_id: planId,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
      cancel_at_period_end: subscription.cancel_at_period_end,
      updated_at: new Date().toISOString()
    };
    
    let response;
    
    if (exists) {
      // Aktualizuj istniejącą subskrypcję
      response = await fetch(
        `${supabaseUrl}/rest/v1/company_subscriptions?stripe_subscription_id=eq.${subscription.id}`,
        {
          method: 'PATCH',
          headers,
          body: JSON.stringify(subscriptionData)
        }
      );
    } else {
      // Utwórz nową subskrypcję
      subscriptionData.created_at = new Date().toISOString();
      response = await fetch(
        `${supabaseUrl}/rest/v1/company_subscriptions`,
        {
          method: 'POST',
          headers,
          body: JSON.stringify(subscriptionData)
        }
      );
    }
    
    if (response.ok) {
      console.log(`Subskrypcja zaktualizowana pomyślnie: ${subscription.id}, event: ${event}`);
      
      // Jeśli subskrypcja jest aktywna, zaktualizuj status firmy na odpowiedni plan
      if (subscription.status === 'active') {
        // Najpierw spróbuj użyć funkcji RPC do aktualizacji
        try {
          const rpcResponse = await fetch(
            `${supabaseUrl}/rest/v1/rpc/update_company_account_type_rpc`,
            {
              method: 'POST',
              headers,
              body: JSON.stringify({
                p_company_id: companyId,
                p_plan_name: planName
              })
            }
          );
          
          if (!rpcResponse.ok) {
            throw new Error('Funkcja RPC nie powiodła się');
          }
        } catch (rpcError) {
          console.error('Błąd podczas wywoływania funkcji RPC:', rpcError);
          
          // Jeśli funkcja RPC nie zadziała, użyj bezpośredniego zapytania SQL
          const verificationLimit = planName.includes('Basic') ? 5 :
                                   planName.includes('Pro') ? 20 :
                                   planName.includes('Business') ? 999999 : 5;
          
          await fetch(
            `${supabaseUrl}/rest/v1/companies?id=eq.${companyId}`,
            {
              method: 'PATCH',
              headers,
              body: JSON.stringify({
                account_type: planName,
                verification_code_limit: verificationLimit
              })
            }
          );
        }
      }
      
      // Jeśli subskrypcja wygasła, zaktualizuj status firmy na 'free'
      if (['canceled', 'unpaid', 'incomplete_expired'].includes(subscription.status)) {
        // Najpierw spróbuj użyć funkcji RPC do aktualizacji
        try {
          const rpcResponse = await fetch(
            `${supabaseUrl}/rest/v1/rpc/update_company_account_type_rpc`,
            {
              method: 'POST',
              headers,
              body: JSON.stringify({
                p_company_id: companyId,
                p_plan_name: 'free'
              })
            }
          );

          if (!rpcResponse.ok) {
            throw new Error('Funkcja RPC nie powiodła się');
          }
        } catch (rpcError) {
          console.error('Błąd podczas wywoływania funkcji RPC dla anulowanej subskrypcji:', rpcError);

          // Jeśli funkcja RPC nie zadziała, użyj bezpośredniego zapytania SQL
          await fetch(
            `${supabaseUrl}/rest/v1/companies?id=eq.${companyId}`,
            {
              method: 'PATCH',
              headers,
              body: JSON.stringify({
                account_type: 'free',
                verification_code_limit: 2
              })
            }
          );
        }
      }
    } else {
      console.error('Błąd podczas zapisywania danych subskrypcji:', await response.text());
    }
  } catch (error) {
    console.error('Błąd przetwarzania subskrypcji:', error);
    throw new Error(`Błąd aktualizacji subskrypcji: ${error.message}`);
  }
}

// Funkcja pomocnicza do zapisywania historii płatności
async function savePaymentRecord(invoice: any) {
  const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Brakujące zmienne środowiskowe Supabase');
  }
  
  // Pobierz subskrypcję powiązaną z fakturą
  const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
  const companyId = subscription.metadata?.company_id;
  
  if (!companyId) {
    throw new Error('Nie znaleziono company_id w metadanych subskrypcji');
  }
  
  // Znajdź id subskrypcji w Supabase
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseServiceKey}`,
    'apikey': supabaseServiceKey
  };
  
  const getResponse = await fetch(
    `${supabaseUrl}/rest/v1/company_subscriptions?select=id&company_id=eq.${companyId}&stripe_subscription_id=eq.${invoice.subscription}`,
    { headers }
  );
  
  const subscriptions = await getResponse.json();
  const subscriptionId = subscriptions && subscriptions.length > 0 ? subscriptions[0].id : null;
  
  // Przygotuj dane płatności
  const paymentData = {
    company_id: companyId,
    subscription_id: subscriptionId,
    stripe_payment_intent_id: invoice.payment_intent,
    stripe_invoice_id: invoice.id,
    amount: invoice.total,
    currency: invoice.currency,
    status: invoice.status,
    description: `Faktura ${invoice.number} za subskrypcję`,
    created_at: new Date().toISOString()
  };
  
  // Zapisz historię płatności
  try {
    const response = await fetch(
      `${supabaseUrl}/rest/v1/payment_history`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify(paymentData)
      }
    );
    
    if (response.ok) {
      console.log('Płatność zapisana pomyślnie:', invoice.id);
    } else {
      console.error('Błąd podczas zapisywania płatności:', await response.text());
    }
  } catch (error) {
    console.error('Błąd zapisu płatności:', error);
    throw new Error(`Błąd zapisu płatności: ${error.message}`);
  }
}

// Funkcja webhook
serve(async (req) => {
  const signature = req.headers.get('stripe-signature');
  
  try {
    // Jeśli to żądanie z naszej aplikacji (nie z Stripe)
  if (!signature) {
      const body = await req.json();
      
      // Obsługa żądania anulowania subskrypcji
      if (body.type === 'cancel_subscription') {
        const subscription = await stripe.subscriptions.update(
          body.subscription_id,
          {
            cancel_at_period_end: body.cancel_at_period_end
          }
        );
        
        return new Response(JSON.stringify({ success: true, subscription }), {
          headers: { 'Content-Type': 'application/json' },
          status: 200
        });
      }
      
      return new Response('Nieznany typ żądania', { status: 400 });
  }
  
    // Obsługa webhooków ze Stripe
    const body = await req.text();
    const event = stripe.webhooks.constructEvent(body, signature, stripeWebhookSecret);
    
    console.log(`Otrzymano zdarzenie Stripe: ${event.type}`);
    
    // Zaloguj zdarzenie
    await logWebhookEvent(event);
    
    // Obsługa zdarzeń Stripe
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.deleted':
        await updateSubscriptionInSupabase(event.data.object, event.type);
        break;
        
      case 'customer.subscription.updated':
        const subscription = event.data.object;
        // Najpierw aktualizujemy subskrypcję w bazie
        await updateSubscriptionInSupabase(subscription, event.type);
        
        // Jeśli subskrypcja została oznaczona do anulowania, zapisz zdarzenie
        if (subscription.cancel_at_period_end) {
          const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
          const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
          
          const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseServiceKey}`,
            'apikey': supabaseServiceKey
          };

          const companyId = subscription.metadata?.company_id;
          if (companyId) {
            await fetch(
              `${supabaseUrl}/rest/v1/subscription_events`,
              {
                method: 'POST',
                headers,
                body: JSON.stringify({
                  company_id: companyId,
                  event_type: 'subscription_canceled',
                  stripe_subscription_id: subscription.id,
                  metadata: {
                    cancel_at_period_end: true,
                    canceled_at: subscription.canceled_at,
                    cancel_at: subscription.cancel_at
                  }
                })
              }
            );
          }
        }
        break;
        
      case 'invoice.paid':
      case 'invoice.payment_failed':
        await savePaymentRecord(event.data.object);
        break;
        
      default:
        console.log(`Nieobsługiwany typ zdarzenia: ${event.type}`);
    }
    
    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200
    });
  } catch (err) {
    console.error(`Błąd przetwarzania webhooka: ${err.message}`);
    return new Response(`Webhook error: ${err.message}`, { status: 400 });
  }
}); 