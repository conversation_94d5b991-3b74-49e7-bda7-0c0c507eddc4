// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Upraszczamy aliasy dla modułów node.js
config.resolver.extraNodeModules = {
  'stream': path.resolve(__dirname, 'stream.js'),
  'http': path.resolve(__dirname, 'http.js'),
  'https': path.resolve(__dirname, 'https.js'),
  'net': path.resolve(__dirname, 'net.js'),
  'tls': path.resolve(__dirname, 'tls.js'),
  'zlib': path.resolve(__dirname, 'zlib.js'),
  'crypto': require.resolve('crypto-browserify'),
  'path': require.resolve('path-browserify'),
  'fs': false, // <PERSON>żna wył<PERSON>czyć moduły, które nie są potrzebne
  'buffer': require.resolve('buffer/'),
  'process': require.resolve('process/browser'),
  // Blokujemy dostęp do innych podstawowych modułów Node.js, które mogą powodować problemy
  'dgram': false,
  'dns': false,
  'os': false,
  'timers': false,
  'constants': false
};

// Zwiększamy czas oczekiwania na bundling w inny sposób
config.server = {
  ...config.server,
  // Usunięto timeoutForBundling, które powodowało błąd
};

// Wyłączamy sourcemapy na iOS, aby zmniejszyć rozmiar bundle'a
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    compress: {
      ...config.transformer.minifierConfig?.compress,
      drop_console: false,  // Zachowujemy console.log aby widzieć komunikaty debugowania
    },
  },
};

module.exports = config; 