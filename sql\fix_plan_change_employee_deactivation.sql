-- Skrypt do naprawy problemu z dezaktywacją pracowników podczas zmiany planu
-- Wykonaj w Supabase SQL Editor

-- 1. Funkcja do reaktywacji pracowników po niepotrzebnej dezaktywacji
CREATE OR REPLACE FUNCTION reactivate_employees_after_plan_change(
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_active_subscription_count INTEGER;
  v_reactivated_count INTEGER := 0;
BEGIN
  -- Sprawdź czy firma ma aktywną subskrypcję
  SELECT COUNT(*)
  INTO v_active_subscription_count
  FROM company_subscriptions
  WHERE company_id = p_company_id
  AND status = 'active';

  -- Jeśli firma ma aktywną subskrypcję, reaktywuj pracowników
  IF v_active_subscription_count > 0 THEN
    -- Reaktywuj pracowników którzy zostali dezaktywowani w ciągu ostatnich 24 godzin
    -- (prawdopodobnie przez błąd podczas zmiany planu)
    UPDATE employees
    SET 
      subscription_status = 'ACTIVE',
      last_status_change = NOW()
    WHERE company_id = p_company_id
    AND subscription_status = 'SUBSCRIPTION_EXPIRED'
    AND last_status_change > NOW() - INTERVAL '24 hours';

    GET DIAGNOSTICS v_reactivated_count = ROW_COUNT;

    RETURN jsonb_build_object(
      'success', true,
      'message', 'Reaktywowano ' || v_reactivated_count || ' pracowników',
      'reactivated_count', v_reactivated_count,
      'company_id', p_company_id
    );
  ELSE
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Firma nie ma aktywnej subskrypcji',
      'company_id', p_company_id
    );
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Błąd podczas reaktywacji pracowników: ' || SQLERRM,
      'error_type', 'database_error'
    );
END;
$$ LANGUAGE plpgsql;

-- 2. Funkcja do sprawdzenia statusu pracowników firmy
CREATE OR REPLACE FUNCTION check_company_employees_status(
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_total_employees INTEGER;
  v_active_employees INTEGER;
  v_inactive_employees INTEGER;
  v_subscription_status TEXT;
  v_subscription_plan TEXT;
BEGIN
  -- Pobierz informacje o subskrypcji
  SELECT cs.status, sp.name
  INTO v_subscription_status, v_subscription_plan
  FROM company_subscriptions cs
  LEFT JOIN subscription_plans sp ON cs.stripe_price_id = sp.stripe_price_id
  WHERE cs.company_id = p_company_id
  AND cs.status = 'active'
  ORDER BY cs.created_at DESC
  LIMIT 1;

  -- Policz pracowników
  SELECT 
    COUNT(*),
    COUNT(CASE WHEN subscription_status = 'ACTIVE' THEN 1 END),
    COUNT(CASE WHEN subscription_status != 'ACTIVE' THEN 1 END)
  INTO v_total_employees, v_active_employees, v_inactive_employees
  FROM employees
  WHERE company_id = p_company_id;

  RETURN jsonb_build_object(
    'company_id', p_company_id,
    'subscription_status', COALESCE(v_subscription_status, 'no_active_subscription'),
    'subscription_plan', COALESCE(v_subscription_plan, 'unknown'),
    'total_employees', v_total_employees,
    'active_employees', v_active_employees,
    'inactive_employees', v_inactive_employees,
    'has_active_subscription', v_subscription_status = 'active'
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'error', 'Błąd podczas sprawdzania statusu: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql;

-- 3. Przykłady użycia:

-- Sprawdź status pracowników firmy (zastąp UUID swoim company_id)
-- SELECT check_company_employees_status('your-company-id-here');

-- Reaktywuj pracowników po niepotrzebnej dezaktywacji (zastąp UUID swoim company_id)
-- SELECT reactivate_employees_after_plan_change('your-company-id-here');

-- 4. Zapytanie do znalezienia firm z nieaktywnymi pracownikami mimo aktywnej subskrypcji
/*
SELECT 
  c.id as company_id,
  c.name as company_name,
  cs.status as subscription_status,
  sp.name as plan_name,
  COUNT(e.id) as total_employees,
  COUNT(CASE WHEN e.subscription_status = 'ACTIVE' THEN 1 END) as active_employees,
  COUNT(CASE WHEN e.subscription_status != 'ACTIVE' THEN 1 END) as inactive_employees
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id AND cs.status = 'active'
LEFT JOIN subscription_plans sp ON cs.stripe_price_id = sp.stripe_price_id
LEFT JOIN employees e ON c.id = e.company_id
WHERE cs.status = 'active'
GROUP BY c.id, c.name, cs.status, sp.name
HAVING COUNT(CASE WHEN e.subscription_status != 'ACTIVE' THEN 1 END) > 0
ORDER BY inactive_employees DESC;
*/
