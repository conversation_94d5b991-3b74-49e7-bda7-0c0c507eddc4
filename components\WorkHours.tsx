import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ScrollView, Modal, Platform, Dimensions } from 'react-native';
import { supabase } from '../services/supabaseClient';
import { Ionicons } from '@expo/vector-icons';
import FilterDrawer from './FilterDrawer';
import DatePicker from './DatePicker';
import { 
  startWorkDay, 
  endWorkDay, 
  WorkSession,
  getActiveTaskActivities
} from '../services/timeTrackingService';
import { i18n } from '../utils/localization';

type SortField = 'date' | 'job_order' | 'duration';
type SortOrder = 'asc' | 'desc';

interface WorkHoursProps {
  onWorkSessionEnd?: () => void;
  onRowClick?: (sessionId: string, date: string) => void;
}

// Funkcja do konwersji współrzędnych GPS na adres
const reverseGeocode = async (latitude: number, longitude: number): Promise<{ shortAddress: string, fullAddress: string }> => {
  try {
    // Najpierw sprawdź, czy mamy współrzędne
    if (!latitude || !longitude) {
      return { shortAddress: 'Brak danych lokalizacji', fullAddress: 'Brak danych lokalizacji' };
    }

    // Używamy API Nominatim (OpenStreetMap) do reverse geocoding
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`,
      {
        headers: {
          'Accept-Language': 'pl', // Preferowany język odpowiedzi
          'User-Agent': 'PrismApp' // Wymagane przez Nominatim
        }
      }
    );

    if (!response.ok) {
      throw new Error('Błąd przy pobieraniu adresu');
    }

    const data = await response.json();
    
    // Pełny adres
    const fullAddress = data.display_name || 'Adres nieznany';
    
    // Tworzymy skrócony adres - tylko ulica i numer
    let shortAddress = 'Adres nieznany';
    
    if (data.address) {
      const addressParts = [];
      
      // Ulica
      if (data.address.road || data.address.street || data.address.pedestrian) {
        addressParts.push(data.address.road || data.address.street || data.address.pedestrian);
      }
      
      // Numer budynku
      if (data.address.house_number) {
        addressParts.push(data.address.house_number);
      }
      
      // Jeśli nie ma ulicy ani numeru, dodaj chociaż miejscowość
      if (addressParts.length === 0 && data.address.city) {
        addressParts.push(data.address.city);
      }
      
      // Jeśli nadal nie ma danych, spróbuj dodać dzielnicę lub przedmieście
      if (addressParts.length === 0 && (data.address.suburb || data.address.neighbourhood)) {
        addressParts.push(data.address.suburb || data.address.neighbourhood);
      }
      
      if (addressParts.length > 0) {
        shortAddress = addressParts.join(' ');
      }
    }
    
    return { shortAddress, fullAddress };
  } catch (error) {
    console.error('Błąd podczas geokodowania:', error);
    return { shortAddress: 'Nie udało się pobrać adresu', fullAddress: 'Nie udało się pobrać adresu' };
  }
};

// Komponent do wyświetlania adresu na podstawie współrzędnych
const LocationAddress = ({ location }: { location: any }) => {
  const [shortAddress, setShortAddress] = useState<string>(i18n.t('loadingAddress'));
  const [fullAddress, setFullAddress] = useState<string>('');
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    const getAddress = async () => {
      if (!location) {
        setShortAddress('Brak danych lokalizacji');
        return;
      }

      try {
        const result = await reverseGeocode(location.latitude, location.longitude);
        setShortAddress(result.shortAddress);
        setFullAddress(result.fullAddress);
      } catch (error) {
        console.error('Błąd podczas pobierania adresu:', error);
        setShortAddress('Nie udało się pobrać adresu');
      }
    };

    getAddress();
  }, [location]);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  if (!fullAddress || fullAddress === shortAddress) {
    return (
      <Text style={styles.locationText}>
        {shortAddress}
      </Text>
    );
  }

  return (
    <TouchableOpacity onPress={toggleExpanded} style={styles.locationContainer}>
      <Text style={styles.locationText}>
        {expanded ? fullAddress : shortAddress}
      </Text>
      <Text style={styles.expandText}>
        {expanded ? i18n.t('collapse') : i18n.t('more')}
      </Text>
    </TouchableOpacity>
  );
};

const WorkHours = ({ onWorkSessionEnd, onRowClick }: WorkHoursProps) => {
  const [isWorking, setIsWorking] = useState(false);
  const [activeSession, setActiveSession] = useState<WorkSession | null>(null);
  const [workHistory, setWorkHistory] = useState<WorkSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [companyId, setCompanyId] = useState<string | null>(null);
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filterJobOrder, setFilterJobOrder] = useState('');
  
  // Dodajemy stan do śledzenia czasu trwania aktywnej sesji
  const [activeSessionDuration, setActiveSessionDuration] = useState('0h 0m');
  // Stan dla animacji kropki
  const [blinkVisible, setBlinkVisible] = useState(true);
  
  // Get the first and last day of the current month
  const firstDayOfMonth = React.useMemo(() => {
    const today = new Date();
    // Create date at local midnight to avoid timezone issues
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    // Format as YYYY-MM-DD ensuring we use local date
    const year = firstDay.getFullYear();
    const month = String(firstDay.getMonth() + 1).padStart(2, '0');
    const day = String(firstDay.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }, []);

  // Get the last day of the current month
  const lastDayOfMonth = React.useMemo(() => {
    const today = new Date();
    // Last day of current month (0th day of next month is the last day of current month)
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    // Format as YYYY-MM-DD ensuring we use local date
    const year = lastDay.getFullYear();
    const month = String(lastDay.getMonth() + 1).padStart(2, '0');
    const day = String(lastDay.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }, []);
  
  const [filterDateFrom, setFilterDateFrom] = useState(firstDayOfMonth);
  const [filterDateTo, setFilterDateTo] = useState(lastDayOfMonth);
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filterButtonPosition, setFilterButtonPosition] = useState({ top: 60, left: 10, width: 300 });
  const filterButtonRef = React.useRef<View>(null);
  
  // Dodaj stany dla własnego modalu
  const [showEndTaskModal, setShowEndTaskModal] = useState(false);
  const [sessionToEnd, setSessionToEnd] = useState<WorkSession | null>(null);
  const [endTimeValue, setEndTimeValue] = useState<string>('');
  const [durationMinutesValue, setDurationMinutesValue] = useState<number>(0);

  // Dodanie stanu do kontrolowania dialogu potwierdzenia
  const [showCompletionDialog, setShowCompletionDialog] = useState(false);
  const [confirmationMessage, setConfirmationMessage] = useState('');
  const [confirmCallback, setConfirmCallback] = useState<() => void>(() => {});

  useEffect(() => {
    fetchUserData();
  }, []);

  // Dodajemy efekt, który będzie aktualizował czas trwania aktywnej sesji co sekundę
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    
    if (isWorking && activeSession) {
      // Funkcja aktualizująca czas trwania
      const updateDuration = () => {
        const startTime = new Date(activeSession.start_time).getTime();
        const now = new Date().getTime();
        const durationMinutes = Math.floor((now - startTime) / (1000 * 60));
        const hours = Math.floor(durationMinutes / 60);
        const minutes = durationMinutes % 60;
        setActiveSessionDuration(`${hours}h ${minutes}m`);
      };

      // Aktualizuj czas od razu
      updateDuration();
      
      // Ustawiamy interwał na co sekundę
      intervalId = setInterval(updateDuration, 1000);
    }
    
    // Czyszczenie interwału przy odmontowaniu komponentu
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isWorking, activeSession]);

  // Efekt dla animacji migającej kropki
  useEffect(() => {
    const blinkInterval = setInterval(() => {
      setBlinkVisible(prev => !prev);
    }, 800); // Miganie co 800ms
    
    return () => clearInterval(blinkInterval);
  }, []);
  
  const fetchUserData = async () => {
    try {
      setLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setLoading(false);
        return;
      }
      
      // Get company_id from user metadata
      const companyId = user.user_metadata?.company_id;
      if (companyId) {
        setCompanyId(companyId);
      }
      
      // Check if user has an active work session
      await checkActiveSession(user.id, companyId);
      
      // Get work history
      await fetchWorkHistory(user.id, companyId);
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching user data:', error);
      setLoading(false);
    }
  };

  const checkActiveSession = async (userId: string, companyId: string) => {
    const { data, error } = await supabase
      .from('work_sessions')
      .select('*')
      .eq('employee_id', userId)
      .eq('is_main_session', true)
      .is('end_time', null)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking active session:', error);
      return;
    }

    if (data) {
      setIsWorking(true);
      setActiveSession(data as WorkSession);
      
      // Check for active tasks
      const activeTaskActivities = await getActiveTaskActivities(userId);
      if (activeTaskActivities.length > 0) {
        console.log(`User has ${activeTaskActivities.length} active task activities:`, activeTaskActivities);
      }
    }
  };

  const fetchWorkHistory = async (userId: string, companyId: string) => {
    const { data, error } = await supabase
      .from('work_sessions')
      .select('*')
      .eq('employee_id', userId)
      .eq('company_id', companyId)
      .not('end_time', 'is', null)
      .order('start_time', { ascending: false });

    if (error) {
      console.error('Error fetching work history:', error);
      return;
    }

    setWorkHistory(data || []);
  };

  // Funkcja pomocnicza do pobierania lokalizacji
  const getCurrentLocation = async (): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (Platform.OS === 'web') {
        if ('geolocation' in navigator) {
          navigator.geolocation.getCurrentPosition(
            (position: GeolocationPosition) => {
              const locationData = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                timestamp: new Date().toISOString()
              };
              resolve(locationData);
            },
            (error: GeolocationPositionError) => {
              console.error('Błąd podczas pobierania lokalizacji na web:', error);
              resolve(null); // Rozwiązujemy z null, aby nie blokować głównej funkcjonalności
            },
            { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
          );
        } else {
          console.log('Geolokalizacja nie jest dostępna w przeglądarce');
          resolve(null);
        }
      } else {
        // Dla React Native na urządzeniach mobilnych
        try {
          // React Native Geolocation API
          require('react-native').Geolocation.getCurrentPosition(
            (position: any) => {
              const locationData = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
                accuracy: position.coords.accuracy,
                timestamp: new Date().toISOString()
              };
              resolve(locationData);
            },
            (error: any) => {
              console.error('Błąd podczas pobierania lokalizacji na mobile:', error);
              resolve(null);
            },
            { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
          );
        } catch (error) {
          console.error('Błąd podczas wywołania API geolokalizacji:', error);
          resolve(null);
        }
      }
    });
  };

  const handleStopWork = async () => {
    console.log('handleStopWork: Zakończenie pracy');
    try {
      setLoading(true);

      // Pobierz aktualnego użytkownika
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      
      if (!user) {
        console.error('Brak zalogowanego użytkownika');
        Alert.alert('Błąd', 'Użytkownik nie jest zalogowany');
        setLoading(false);
        return;
      }

      // Check for active task activities
      const activeTaskActivities = await getActiveTaskActivities(user.id);
      
      // Enhanced message with task information
      let message = 'Czy na pewno chcesz zakończyć pracę?';
      
      if (activeTaskActivities.length > 0) {
        // Get a list of task activities with their task IDs to check
        const taskIds = [...new Set(activeTaskActivities.map(a => a.task_id))];
        
        // For each task, check if you're the last active employee
        const tasksWithLastActivity = [];
        for (const taskId of taskIds) {
          const { data: activities } = await supabase
            .from('task_activities')
            .select('id')
            .eq('task_id', taskId)
            .eq('status', 'active');
            
          if (activities && activities.length === 1) {
            // If only one active employee (you), this task will be marked as completed
            tasksWithLastActivity.push(taskId);
          }
        }
        
        // Prepare message based on the checks
        if (tasksWithLastActivity.length > 0) {
          message = `Czy chcesz zakończyć pracę? Zakończy to Twoje aktywności w ${activeTaskActivities.length} zadaniach.\n\nUWAGA: Jesteś ostatnim aktywnym pracownikiem w ${tasksWithLastActivity.length} zadaniu/zadaniach - zostaną one automatycznie oznaczone jako zakończone.`;
        } else {
          message = `Czy chcesz zakończyć pracę? Zakończy to Twoje aktywności w ${activeTaskActivities.length} zadaniach.`;
        }
      }
      
      showConfirmationModal(message, () => finishStoppingWork(user.id));
    } catch (error) {
      console.error('Błąd podczas kończenia pracy:', error);
      Alert.alert('Błąd', 'Wystąpił problem podczas kończenia pracy');
    } finally {
      setLoading(false);
    }
  };

  const finishStoppingWork = async (employeeId: string) => {
    try {
      setLoading(true);

      // Get active task activities before ending work day (for reporting)
      const activeTaskActivities = await getActiveTaskActivities(employeeId);
      const taskCount = [...new Set(activeTaskActivities.map(a => a.task_id))].length;
      
      // Pobierz aktualną lokalizację
      const locationData = await getCurrentLocation();
      console.log('Current location for end work:', locationData);
      
      // End work day using our new service with location data
      const success = await endWorkDay(employeeId, locationData);
      
      if (!success) {
        throw new Error('Nie udało się zakończyć dnia pracy');
      }
      
      // Reset local state
      setIsWorking(false);
      setActiveSession(null);
      
      // Refresh history
      if (companyId) {
        await fetchWorkHistory(employeeId, companyId);
      }
      
      // Call callback for UI refresh
      if (onWorkSessionEnd) {
        onWorkSessionEnd();
      }
      
      // Enhanced success message with task info
      if (activeTaskActivities.length > 0) {
        Alert.alert(
          i18n.t('success'), 
          i18n.t('workSessionEndedWithTasks', { count: taskCount })
        );
      } else {
        Alert.alert(i18n.t('success'), i18n.t('workSessionEnded'));
      }
    } catch (error) {
      console.error('Błąd podczas finalizowania zakończenia pracy:', error);
      Alert.alert('Błąd', 'Wystąpił problem podczas kończenia pracy');
    } finally {
      setLoading(false);
      setShowCompletionDialog(false);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('pl-PL', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(2);
    return `${day}.${month}.${year}`;
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return 'chevron-down';
    return sortOrder === 'asc' ? 'chevron-up' : 'chevron-down';
  };

  const sortedAndFilteredHistory = React.useMemo(() => {
    let filtered = [...workHistory];

    // Apply filters
    if (filterJobOrder) {
      filtered = filtered.filter(session => 
        (session.job_order || '').toLowerCase().includes(filterJobOrder.toLowerCase())
      );
    }

    if (filterDateFrom) {
      filtered = filtered.filter(session => 
        new Date(session.start_time) >= new Date(filterDateFrom)
      );
    }

    if (filterDateTo) {
      filtered = filtered.filter(session => 
        new Date(session.start_time) <= new Date(filterDateTo)
      );
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      const multiplier = sortOrder === 'asc' ? 1 : -1;
      
      switch (sortField) {
        case 'date':
          return (new Date(a.start_time).getTime() - new Date(b.start_time).getTime()) * multiplier;
        case 'job_order':
          return ((a.job_order || '').localeCompare(b.job_order || '')) * multiplier;
        case 'duration':
          return ((a.duration_minutes || 0) - (b.duration_minutes || 0)) * multiplier;
        default:
          return 0;
      }
    });
  }, [workHistory, sortField, sortOrder, filterJobOrder, filterDateFrom, filterDateTo]);

  const handleClearFilters = () => {
    setFilterJobOrder('');
    setFilterDateFrom(firstDayOfMonth);
    setFilterDateTo(lastDayOfMonth);
  };

  // Funkcja do wyświetlania modala potwierdzenia
  const showConfirmationModal = (message: string, callback: () => void) => {
    setConfirmationMessage(message);
    setConfirmCallback(() => callback);
    setShowCompletionDialog(true);
  };

  // Funkcja do obsługi anulowania potwierdzenia
  const handleCancelConfirmation = () => {
    setShowCompletionDialog(false);
  };

  // Funkcja do obsługi potwierdzenia
  const handleConfirmAction = () => {
    setShowCompletionDialog(false);
    confirmCallback();
  };

  // Add a function to calculate total duration
  const calculateTotalDuration = (sessions: WorkSession[]) => {
    return sessions.reduce((total, session) => total + (session.duration_minutes || 0), 0);
  };

  // Function to generate PDF from work time data
  const exportToPDF = useCallback(() => {
    if (Platform.OS !== 'web') return;
    
    // We're on the web platform, so we can access the window object
    const loadScript = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
      });
    };

    const generatePDF = async () => {
      try {
        // Load jsPDF and jsPDF-AutoTable scripts if not already loaded
        if (!(window as any).jspdf) {
          await loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
        }
        if (!(window as any).jspdf?.jsPDF) {
          console.error('jsPDF not properly loaded');
          alert('Nie można załadować biblioteki PDF. Odśwież stronę i spróbuj ponownie.');
          return;
        }
        
        if (!(window as any).jspdf.autoTable) {
          await loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js');
        }

        // Create new PDF document
        const { jsPDF } = (window as any).jspdf;
        const doc = new jsPDF();
        
        // Add title
        doc.setFontSize(18);
        doc.text('Raport czasu pracy', 14, 22);
        
        // Add date range
        doc.setFontSize(12);
        doc.text(`Okres: ${formatDate(filterDateFrom)} - ${formatDate(filterDateTo)}`, 14, 30);
        
        // Add filter info if job order filter is active
        if (filterJobOrder) {
          doc.text(`Filtr zlecenia: ${filterJobOrder}`, 14, 38);
        }
        
        // Add total duration
        const totalDuration = calculateTotalDuration(sortedAndFilteredHistory);
        doc.text(`${i18n.t('totalWorkTime')} ${formatDuration(totalDuration)}`, 14, filterJobOrder ? 46 : 38);
        
        // Create table with work session data
        const tableColumn = ["Data", "Zlecenie", "Czas rozpoczęcia", "Czas zakończenia", "Czas trwania"];
        const tableRows = sortedAndFilteredHistory.map(session => [
          formatDate(session.start_time),
          session.job_order,
          formatDateTime(session.start_time),
          formatDateTime(session.end_time || ''),
          formatDuration(session.duration_minutes || 0)
        ]);
        
        // Add table to document
        doc.autoTable({
          head: [tableColumn],
          body: tableRows,
          startY: filterJobOrder ? 50 : 42,
          styles: { fontSize: 10, cellPadding: 3 },
          headStyles: { fillColor: [37, 99, 235] },
        });
        
        // Save PDF
        doc.save('raport-czasu-pracy.pdf');
      } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Wystąpił błąd podczas generowania PDF: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    };

    generatePDF();
  }, [sortedAndFilteredHistory, filterDateFrom, filterDateTo, filterJobOrder]);

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={{ width: '100%', padding: 0 }}
      >
        {isWorking && activeSession && (
          <View style={styles.activeSessionContainer}>
            <View style={styles.activeSessionHeader}>
              <View style={styles.activeSessionTitleContainer}>
                {/* Migająca zielona kropka */}
                <View style={[styles.statusDot, !blinkVisible && styles.statusDotBlink]} />
                <Text style={styles.activeSessionTitle}>{i18n.t('activeSessionTitle')}</Text>
              </View>
              
              {/* Wyświetlamy czas trwania sesji */}
              <View style={styles.durationBadge}>
                <Text style={styles.durationBadgeText}>{activeSessionDuration}</Text>
              </View>
            </View>
            
            <Text style={styles.activeSessionText}>
              {`${i18n.t('jobOrderLabel')} ${activeSession.job_order}`}
            </Text>
            <Text style={styles.activeSessionText}>
              {`${i18n.t('startTimeLabel')} ${formatDate(activeSession.start_time)} ${formatDateTime(activeSession.start_time)}`}
            </Text>
            
            {/* Przycisk "Zakończ pracę" */}
            <TouchableOpacity 
              style={styles.stopWorkButton}
              onPress={handleStopWork}
              disabled={loading}
            >
              <Ionicons name="stop-circle-outline" size={16} color="#DC2626" style={styles.stopWorkIcon} />
              <Text style={styles.stopWorkText}>{i18n.t('stopWork')}</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Pasek wyszukiwania ze zmodyfikowanym układem */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder={i18n.t('searchEmployee')}
              value={filterJobOrder}
              onChangeText={setFilterJobOrder}
              placeholderTextColor="#9CA3AF"
            />
            {filterJobOrder.length > 0 && (
              <TouchableOpacity style={styles.clearButton} onPress={() => setFilterJobOrder('')}>
                <Ionicons name="close-circle" size={20} color="#6B7280" />
              </TouchableOpacity>
            )}
            <TouchableOpacity 
              style={styles.dateButton}
              onPress={() => setShowDateFilter(true)}
            >
              <Ionicons name="calendar-outline" size={18} color="#4B5563" />
              <Text style={styles.dateButtonText}>{i18n.t('date')}</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        {/* Przyciski filtrów - wyśrodkowane */}
        <View style={styles.filterTabsContainer}>
          <ScrollView 
            horizontal={true} 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.filterTabsContent}
          >
            <TouchableOpacity 
              style={[
                styles.filterTab, 
                !filterDateFrom && !filterDateTo ? styles.activeFilterTab : null
              ]}
              onPress={handleClearFilters}
            >
              <Text style={!filterDateFrom && !filterDateTo ? styles.activeFilterTabText : styles.filterTabText}>
                {i18n.t('all')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[
                styles.filterTab, 
                filterDateFrom === firstDayOfMonth && filterDateTo === lastDayOfMonth ? styles.activeFilterTab : null
              ]}
              onPress={() => {
                setFilterDateFrom(firstDayOfMonth);
                setFilterDateTo(lastDayOfMonth);
              }}
            >
              <Text style={filterDateFrom === firstDayOfMonth && filterDateTo === lastDayOfMonth ? styles.activeFilterTabText : styles.filterTabText}>
                {i18n.t('thisMonth')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
              style={[
                styles.filterTab,
                filterDateFrom.startsWith(new Date().getFullYear() + '-' + String(new Date().getMonth()).padStart(2, '0')) ? styles.activeFilterTab : null
              ]}
              onPress={() => {
                // Poprzedni miesiąc
                const today = new Date();
                const previousMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const lastDayOfPreviousMonth = new Date(today.getFullYear(), today.getMonth(), 0);
                
                const firstDay = previousMonth.toISOString().split('T')[0];
                const lastDay = lastDayOfPreviousMonth.toISOString().split('T')[0];
                
                setFilterDateFrom(firstDay);
                setFilterDateTo(lastDay);
              }}
            >
              <Text style={filterDateFrom.startsWith(new Date().getFullYear() + '-' + String(new Date().getMonth()).padStart(2, '0')) ? styles.activeFilterTabText : styles.filterTabText}>
                {i18n.t('previousMonth')}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        <View style={styles.totalDurationContainer}>
          <Text style={styles.totalDurationLabel}>{i18n.t('totalWorkTime')}</Text>
          <View style={styles.totalDurationControls}>
            <Text style={styles.totalDurationValue}>{formatDuration(calculateTotalDuration(sortedAndFilteredHistory))}</Text>
            {Platform.OS === 'web' && (
              <TouchableOpacity style={styles.exportButton} onPress={exportToPDF}>
                <Ionicons name="download-outline" size={18} color="#FFFFFF" style={{ marginRight: 6 }} />
                <Text style={styles.exportButtonText}>{i18n.t('exportToPdf')}</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Modal do filtrowania daty */}
        <Modal
          visible={showDateFilter}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDateFilter(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{i18n.t('selectDateRange')}</Text>
                <TouchableOpacity onPress={() => setShowDateFilter(false)}>
                  <Ionicons name="close" size={24} color="#6B7280" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.dateFilterContainer}>
                <Text style={styles.dateFilterLabel}>{i18n.t('dateFrom')}</Text>
                <DatePicker
                  label={i18n.t('dateFrom')}
                  date={filterDateFrom}
                  onDateChange={setFilterDateFrom}
                  placeholder={i18n.t('selectStartDate')}
                />
              </View>
              
              <View style={styles.dateFilterContainer}>
                <Text style={styles.dateFilterLabel}>{i18n.t('dateTo')}</Text>
                <DatePicker
                  label={i18n.t('dateTo')}
                  date={filterDateTo}
                  onDateChange={setFilterDateTo}
                  placeholder={i18n.t('selectEndDate')}
                />
              </View>
              
              <View style={styles.modalActions}>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.modalButtonOutline]} 
                  onPress={handleClearFilters}
                >
                  <Text style={styles.modalButtonOutlineText}>{i18n.t('clear')}</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.modalButton, styles.modalButtonFilled]} 
                  onPress={() => setShowDateFilter(false)}
                >
                  <Text style={styles.modalButtonFilledText}>{i18n.t('apply')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        <View style={styles.tableContainer}>
          <View style={styles.tableHeader}>
            <TouchableOpacity 
              style={[styles.headerCell, { flex: 1 }]}
              onPress={() => handleSort('date')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('date')}</Text>
                {sortField === 'date' && (
                  <Ionicons
                    name={getSortIcon('date')}
                    size={16}
                    color="#4B5563"
                  />
                )}
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.headerCell, { flex: 1.5 }]}
              onPress={() => handleSort('job_order')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('jobOrder')}</Text>
                {sortField === 'job_order' && (
                  <Ionicons
                    name={getSortIcon('job_order')}
                    size={16}
                    color="#4B5563"
                  />
                )}
              </View>
            </TouchableOpacity>
            
            <View style={[styles.headerCell, { flex: 1.5 }]}>
              <Text style={styles.headerText}>{i18n.t('workTime')}</Text>
            </View>
            
            <TouchableOpacity 
              style={[styles.headerCell, { flex: 1 }]}
              onPress={() => handleSort('duration')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('duration')}</Text>
                {sortField === 'duration' && (
                  <Ionicons
                    name={getSortIcon('duration')}
                    size={16}
                    color="#4B5563"
                  />
                )}
              </View>
            </TouchableOpacity>
          </View>
          
          <ScrollView 
            style={styles.tableBody}
            contentContainerStyle={{ width: '100%', padding: 0 }}
          >
            {sortedAndFilteredHistory.map((session) => (
              <TouchableOpacity 
                key={session.id} 
                style={[styles.tableRow, styles.clickableRow]}
                activeOpacity={0.7}
                onPress={() => onRowClick && onRowClick(
                  session.id, 
                  formatDate(session.start_time)
                )}
              >
                <Text style={[styles.cell, { flex: 1 }]}>
                  {formatDate(session.start_time)}
                </Text>
                <Text style={[styles.cell, { flex: 1.5 }]} numberOfLines={1}>
                  {session.job_order}
                </Text>
                <Text style={[styles.cell, { flex: 1.5 }]}>
                  {formatDateTime(session.start_time)} - {formatDateTime(session.end_time || '')}
                </Text>
                <Text style={[styles.cell, { flex: 1, color: '#2563EB', fontWeight: '500' }]}>
                  {formatDuration(session.duration_minutes || 0)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Modal dla potwierdzenia zakończenia pracy */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showCompletionDialog}
        onRequestClose={handleCancelConfirmation}
      >
        <View style={styles.centeredView}>
          <View style={styles.modalView}>
            <Text style={styles.modalTitle}>Potwierdź operację</Text>
            <Text style={styles.modalText}>{confirmationMessage}</Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.buttonCancel}
                onPress={handleCancelConfirmation}
              >
                <Text style={styles.textStyle}>Anuluj</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.buttonConfirm}
                onPress={handleConfirmAction}
              >
                <Text style={styles.textStyle}>Potwierdź</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F6FA',
    width: '100%',
  },
  content: {
    flex: 1,
    width: '100%',
    padding: 0,
  },
  activeSessionContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    paddingVertical: 16,
    borderRadius: 0,
    marginTop: 0,
    marginHorizontal: 0,
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
    borderLeftWidth: 0,
    borderLeftColor: 'transparent',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  activeSessionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  activeSessionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#22C55E',
    marginRight: 8,
  },
  statusDotBlink: {
    opacity: 0.4,
  },
  activeSessionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  durationBadge: {
    backgroundColor: '#DCFCE7',
    paddingHorizontal: 10,
    paddingVertical: 3,
    borderRadius: 100,
  },
  durationBadgeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#166534',
  },
  activeSessionText: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 4,
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingLeft: 12,
    paddingRight: 5,
    height: 46,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    height: 46,
    padding: 0,
  },
  clearButton: {
    padding: 6,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 5,
    marginRight: 5,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginLeft: 4,
  },
  filterTabsContainer: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    paddingVertical: 8,
  },
  filterTabsContent: {
    paddingHorizontal: 16,
    gap: 8,
    justifyContent: 'center',
    flexGrow: 1,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  activeFilterTab: {
    backgroundColor: '#EBF5FF',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
  },
  activeFilterTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2563EB',
  },
  tableContainer: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 0,
    overflow: 'hidden',
    borderWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    borderColor: '#E5E7EB',
    marginHorizontal: 0,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerCell: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingRight: 8,
  },
  tableBody: {
    flex: 1,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cell: {
    fontSize: 14,
    color: '#1F2937',
    textAlign: 'center',
  },
  totalDurationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  totalDurationLabel: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1F2937',
  },
  totalDurationValue: {
    fontSize: 15,
    fontWeight: '600',
    color: '#2563EB',
    marginRight: 16,
  },
  totalDurationControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2563EB',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  exportButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  clickableRow: {
    backgroundColor: 'white',
    cursor: 'pointer',
    position: 'relative',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 8,
    width: '80%',
    maxWidth: 400,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  modalText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#555',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  buttonCancel: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#e0e0e0',
    marginRight: 10,
  },
  buttonConfirm: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  locationContainer: {
    flex: 1,
    paddingHorizontal: 4,
    paddingVertical: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  locationText: {
    fontSize: 12,
    color: '#4B5563',
    textAlign: 'center',
  },
  expandText: {
    fontSize: 11,
    color: '#3B82F6',
    fontWeight: '500',
    marginTop: 2,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  dateFilterContainer: {
    marginBottom: 16,
  },
  dateFilterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 4,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginLeft: 8,
  },
  modalButtonOutline: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
  },
  modalButtonFilled: {
    backgroundColor: '#2563EB',
  },
  modalButtonOutlineText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  modalButtonFilledText: {
    color: 'white',
    fontWeight: '500',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  headerText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
  },
  stopWorkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FEE2E2',
    alignSelf: 'center',
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  stopWorkIcon: {
    marginRight: 6,
  },
  stopWorkText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#DC2626',
  },
});

export default WorkHours; 