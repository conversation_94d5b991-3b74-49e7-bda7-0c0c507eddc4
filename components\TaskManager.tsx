import React, { useState, useEffect, forwardRef, useImperative<PERSON><PERSON>le, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  Platform,
  Alert,
  ActivityIndicator,
  Image,
  SafeAreaView,
  KeyboardAvoidingView,
  Dimensions,
  Pressable,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { decode } from 'base64-arraybuffer';
import { supabase } from '../services/supabaseClient';
import FilterDrawer from './FilterDrawer';
import DatePicker from './DatePicker';
import TimePicker from './TimePicker';
import { TopBar } from './TopBar';
import { i18n } from '../utils/localization';

// Interface for custom alert buttons
interface AlertButton {
  text: string;
  onPress: () => void;
  style?: 'cancel' | 'default';
}

interface Employee {
  id: string;
  full_name: string;
  email: string;
  status: string;
}

interface TaskForm {
  date: Date;
  clientName: string;
  address: string;
  workScope: string;
  startTime: Date;
  selectedEmployees: string[];
  additionalInfo: string;
  status: 'pending' | 'in_progress' | 'completed';
  photosBefore: string[];
  photosAfter: string[];
}

interface Task {
  id: string;
  date: string;
  client_name: string;
  address: string;
  work_scope: string;
  start_time: string;
  started_at?: string;
  completed_at?: string;
  status: 'pending' | 'in_progress' | 'completed';
  assigned_employees: string[];
}

type SortField = 'date' | 'client' | 'address' | 'time' | 'status';
type SortOrder = 'asc' | 'desc';

interface TaskManagerProps {
  companyId: string;
  activeTab: 'add' | 'list';
  userType: 'company' | 'employee' | 'coordinator';
  userId: string;
  onTaskSelect?: (taskId: string) => void;
  onTabChange?: (tab: 'add' | 'list') => void;
}

export interface TaskManagerRef {
  fetchTasks: () => Promise<void>;
}

const TaskManager = forwardRef<TaskManagerRef, TaskManagerProps>(({ companyId, activeTab, userType, userId, onTaskSelect, onTabChange }, ref) => {
  const [form, setForm] = useState<TaskForm>({
    date: new Date(),
    clientName: '',
    address: '',
    workScope: '',
    startTime: new Date(),
    selectedEmployees: [],
    additionalInfo: '',
    status: 'pending',
    photosBefore: [],
    photosAfter: []
  });

  const [employees, setEmployees] = useState<Employee[]>([]);
  const [showEmployeeModal, setShowEmployeeModal] = useState(false);
  const [uploadingPhotos, setUploadingPhotos] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filterClient, setFilterClient] = useState('');
  const [filterAddress, setFilterAddress] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterDateFrom, setFilterDateFrom] = useState('');
  const [filterDateTo, setFilterDateTo] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filterButtonPosition, setFilterButtonPosition] = useState({ top: 60, left: 10, width: 300 });
  const filterButtonRef = React.useRef<View>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // Custom Alert state - used for mobile
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [alertButtons, setAlertButtons] = useState<AlertButton[]>([]);
  
  // Check if we're on web platform
  const isWeb = Platform.OS === 'web';

  // For web: create alert container once when component mounts
  useEffect(() => {
    if (isWeb) {
      // Remove any existing alert container
      const existingContainer = document.getElementById('task-alert-container');
      if (existingContainer) {
        document.body.removeChild(existingContainer);
      }

      // Create a new alert container
      const alertContainer = document.createElement('div');
      alertContainer.id = 'task-alert-container';
      alertContainer.style.position = 'fixed';
      alertContainer.style.top = '0';
      alertContainer.style.left = '0';
      alertContainer.style.width = '100%';
      alertContainer.style.height = '100%';
      alertContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      alertContainer.style.zIndex = '9999';
      alertContainer.style.display = 'flex';
      alertContainer.style.justifyContent = 'center';
      alertContainer.style.alignItems = 'center';
      alertContainer.style.visibility = 'hidden';
      
      document.body.appendChild(alertContainer);
      
      // Clean up on unmount
      return () => {
        if (alertContainer && alertContainer.parentNode) {
          alertContainer.parentNode.removeChild(alertContainer);
        }
      };
    }
  }, [isWeb]);
  
  // Show web alert (direct DOM manipulation)
  const showWebAlert = useCallback((title: string, message: string, buttons: AlertButton[]) => {
    if (!isWeb) return;
    
    const container = document.getElementById('task-alert-container');
    if (!container) return;
    
    // Clear previous content
    container.innerHTML = '';
    container.style.visibility = 'visible';
    
    // Create alert dialog
    const alertBox = document.createElement('div');
    alertBox.style.backgroundColor = 'white';
    alertBox.style.borderRadius = '8px';
    alertBox.style.width = '90%';
    alertBox.style.maxWidth = '400px';
    alertBox.style.overflow = 'hidden';
    // Apply global font for all text inside the alert
    alertBox.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
    
    // Title
    const titleDiv = document.createElement('div');
    titleDiv.style.padding = '16px';
    titleDiv.style.textAlign = 'center';
    
    const titleText = document.createElement('h3');
    titleText.textContent = title;
    titleText.style.margin = '0';
    titleText.style.fontSize = '18px';
    titleText.style.fontWeight = 'bold';
    titleText.style.color = 'black';
    
    titleDiv.appendChild(titleText);
    alertBox.appendChild(titleDiv);
    
    // Message
    const messageDiv = document.createElement('div');
    messageDiv.style.padding = '0 16px 16px 16px';
    messageDiv.style.textAlign = 'center';
    
    const messageText = document.createElement('p');
    messageText.textContent = message;
    messageText.style.margin = '0';
    messageText.style.fontSize = '16px';
    messageText.style.color = 'black';
    messageText.style.lineHeight = '22px';
    
    messageDiv.appendChild(messageText);
    alertBox.appendChild(messageDiv);
    
    // Buttons
    const buttonDiv = document.createElement('div');
    buttonDiv.style.display = 'flex';
    buttonDiv.style.padding = '16px';
    buttonDiv.style.justifyContent = 'center';
    
    // Add buttons
    buttons.forEach((button, index) => {
      const btnElement = document.createElement('button');
      btnElement.textContent = button.text;
      btnElement.style.flex = '1';
      btnElement.style.padding = '12px';
      btnElement.style.borderRadius = '4px';
      btnElement.style.border = 'none';
      btnElement.style.margin = '0 4px';
      btnElement.style.color = 'white';
      btnElement.style.fontSize = '16px';
      btnElement.style.fontWeight = '500';
      btnElement.style.cursor = 'pointer';
      btnElement.style.fontFamily = 'inherit'; // Inherit font family from parent
      
      // Set button color based on style
      if (button.style === 'cancel') {
        btnElement.style.backgroundColor = '#6B7280';
      } else {
        btnElement.style.backgroundColor = '#2563EB';
      }
      
      // Add click handler
      btnElement.addEventListener('click', () => {
        container.style.visibility = 'hidden';
        if (button.onPress) {
          setTimeout(() => button.onPress(), 10);
        }
      });
      
      buttonDiv.appendChild(btnElement);
    });
    
    alertBox.appendChild(buttonDiv);
    container.appendChild(alertBox);
    
  }, [isWeb]);
  
  // Alert function that works differently based on platform
  const showAlert = useCallback((
    title: string, 
    message: string, 
    buttons: AlertButton[]
  ) => {
    // For web, use custom web alert
    if (isWeb) {
      showWebAlert(title, message, buttons);
    } 
    // For mobile, use our custom React Native alert
    else {
      setAlertTitle(title);
      setAlertMessage(message);
      setAlertButtons(buttons);
      setAlertVisible(true);
    }
  }, [isWeb, showWebAlert]);

  const hideCustomAlert = useCallback(() => {
    setAlertVisible(false);
  }, []);
  
  // Optimized custom alert component with React.memo
  const CustomAlert = React.memo(() => (
    <Modal
      transparent={true}
      visible={alertVisible}
      onRequestClose={hideCustomAlert}
      animationType="none"
    >
      <View style={styles.modalOverlay}>
        <View style={styles.alertContainer}>
          <View style={styles.alertTitleContainer}>
            <Text style={styles.alertTitle}>{alertTitle}</Text>
          </View>
          
          <View style={styles.alertBodyContainer}>
            <Text style={styles.alertMessage}>{alertMessage}</Text>
          </View>
          
          <View style={styles.alertButtonsContainer}>
            {alertButtons.map((button, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.alertButton,
                  button.style === 'cancel' ? styles.cancelButton : styles.confirmButton,
                  alertButtons.length > 1 && index === 0 ? { marginRight: 8 } : {}
                ]}
                onPress={() => {
                  hideCustomAlert();
                  button.onPress();
                }}
              >
                <Text 
                  style={[
                    styles.alertButtonText,
                    button.style === 'cancel' ? {} : {}
                  ]}
                >
                  {button.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  ));

  useEffect(() => {
    fetchEmployees();
    requestMediaLibraryPermissions();
    if (activeTab === 'list') {
      fetchTasks();
    }
  }, [activeTab]);

  useImperativeHandle(ref, () => ({
    fetchTasks
  }));

  const requestMediaLibraryPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        showAlert(
          'Błąd', 
          'Potrzebujemy dostępu do galerii, aby dodawać zdjęcia.',
          [{ text: 'OK', onPress: () => {}, style: 'default' }]
        );
      }
    }
  };

  const fetchEmployees = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('company_id', companyId)
        .eq('subscription_status', 'ACTIVE'); // Tylko aktywni pracownicy

      if (error) {
        console.error('Error fetching employees:', error);
        showAlert(
          'Błąd',
          'Nie udało się pobrać listy pracowników',
          [{ text: 'OK', onPress: () => {}, style: 'default' }]
        );
        return;
      }

      setEmployees(data || []);
    } catch (error) {
      console.error('Exception in fetchEmployees:', error);
      showAlert(
        'Błąd',
        'Wystąpił nieoczekiwany błąd',
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
    }
  };

  const fetchTasks = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('company_id', companyId)
        .order('date', { ascending: false });

      // Jeśli użytkownik jest pracownikiem, pobierz tylko przypisane zlecenia
      if (userType === 'employee') {
        query = query.contains('assigned_employees', [userId]);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tasks:', error);
        showAlert(
          'Błąd', 
          'Nie udało się pobrać listy zleceń',
          [{ text: 'OK', onPress: () => {}, style: 'default' }]
        );
        return;
      }

      setTasks(data || []);
    } catch (error) {
      console.error('Exception in fetchTasks:', error);
      showAlert(
        'Błąd', 
        'Wystąpił nieoczekiwany błąd',
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const uploadImage = async (base64Image: string): Promise<string | null> => {
    try {
      const fileName = `${Date.now()}.jpg`;
      const filePath = `task-photos/${companyId}/${fileName}`;
      const contentType = 'image/jpeg';

      const { data, error } = await supabase.storage
        .from('tasks')
        .upload(filePath, decode(base64Image), {
          contentType,
          upsert: false
        });

      if (error) {
        throw error;
      }

      const { data: { publicUrl } } = supabase.storage
        .from('tasks')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      return null;
    }
  };

  const handleImagePick = async (type: 'before' | 'after') => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true,
      });

      if (!result.canceled && result.assets[0].base64) {
        setUploadingPhotos(true);
        const uploadedUrl = await uploadImage(result.assets[0].base64);
        
        if (uploadedUrl) {
          setForm(prev => ({
            ...prev,
            [type === 'before' ? 'photosBefore' : 'photosAfter']: [
              ...prev[type === 'before' ? 'photosBefore' : 'photosAfter'],
              uploadedUrl
            ]
          }));
        }
        setUploadingPhotos(false);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      showAlert(
        'Błąd', 
        'Nie udało się dodać zdjęcia',
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
      setUploadingPhotos(false);
    }
  };

  const removeImage = (type: 'before' | 'after', index: number) => {
    setForm(prev => ({
      ...prev,
      [type === 'before' ? 'photosBefore' : 'photosAfter']: prev[type === 'before' ? 'photosBefore' : 'photosAfter'].filter((_, i) => i !== index)
    }));
  };

  const handleCreateTask = async () => {
    console.log('handleCreateTask called');
    
    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    try {
      console.log('Form validation passed, showing confirmation dialog');
      
      showAlert(
        'Potwierdź utworzenie zlecenia',
        'Czy na pewno chcesz utworzyć to zlecenie?',
        [
          { text: 'Anuluj', onPress: () => console.log('Task creation cancelled'), style: 'cancel' },
          { text: 'Utwórz', onPress: createTask, style: 'default' }
        ]
      );
    } catch (error) {
      console.error('Exception in handleCreateTask:', error);
      showAlert(
        'Błąd',
        'Wystąpił nieoczekiwany błąd podczas tworzenia zlecenia',
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
    }
  };

  const createTask = async () => {
    try {
      console.log('Creating task...');
      
      // Format date as YYYY-MM-DD
      const formattedDate = form.date.toISOString().split('T')[0];
      
      // Format time as HH:mm:ss
      const formattedTime = form.startTime.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      // Ensure selectedEmployees is an array
      const employeesToAssign = Array.isArray(form.selectedEmployees) 
        ? [...form.selectedEmployees] // Make a copy to avoid reference issues
        : [];
      
      console.log('Selected employees (original):', form.selectedEmployees);
      console.log('Employees to assign (processed):', employeesToAssign);
      console.log('Selected employees type:', typeof form.selectedEmployees);
      console.log('Selected employees is array:', Array.isArray(form.selectedEmployees));
      console.log('Selected employees length:', form.selectedEmployees.length);
      
      // Prepare task data
      const taskData = {
        company_id: companyId,
        date: formattedDate,
        client_name: form.clientName,
        address: form.address,
        work_scope: form.workScope,
        start_time: formattedTime,
        assigned_employees: employeesToAssign, // Make sure we're using the processed array
        additional_info: form.additionalInfo,
        status: form.status,
        photos_before: form.photosBefore,
        photos_after: form.photosAfter
      };
      
      console.log('Submitting task to Supabase with data:', taskData);
      console.log('assigned_employees field (stringified):', JSON.stringify(taskData.assigned_employees));

      const { data, error } = await supabase
        .from('tasks')
        .insert([taskData])
        .select();

      if (error) {
        console.error('Error creating task:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        showAlert(
          'Błąd',
          `Nie udało się utworzyć zlecenia. ${error.message}`,
          [{ text: 'OK', onPress: () => {}, style: 'default' }]
        );
        return;
      }

      console.log('Task created successfully:', data);
      showAlert(
        'Sukces',
        'Zlecenie zostało utworzone',
        [{ text: 'OK', onPress: () => {
          // Navigate to task details if a task was created and onTaskSelect is provided
          if (data && data.length > 0 && onTaskSelect) {
            console.log('Navigating to task details for task ID:', data[0].id);
            onTaskSelect(data[0].id);
          }
          resetForm();
        }, style: 'default' }]
      );
      
    } catch (error) {
      console.error('Exception in createTask:', error);
      showAlert(
        'Błąd',
        `Wystąpił nieoczekiwany błąd. ${error instanceof Error ? error.message : 'Unknown error'}`,
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
    }
  };

  const validateForm = () => {
    console.log('Running form validation...');
    
    const errors = [];
    
    if (!form.clientName.trim()) {
      errors.push('Wprowadź nazwę zleceniodawcy');
    }
    if (!form.address.trim()) {
      errors.push('Wprowadź adres zlecenia');
    }
    if (!form.workScope.trim()) {
      errors.push('Wprowadź zakres prac');
    }
    if (form.selectedEmployees.length === 0) {
      errors.push('Wybierz przynajmniej jednego pracownika');
    }
    
    if (errors.length > 0) {
      const errorMessage = `Formularz zawiera błędy:\n\n${errors.map(e => `• ${e}`).join('\n')}`;
      console.error('Validation errors:', errors);
      
      showAlert(
        'Uzupełnij wymagane pola',
        errorMessage,
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
      return false;
    }
    
    console.log('Form validation passed');
    return true;
  };

  const resetForm = () => {
    setForm({
      date: new Date(),
      clientName: '',
      address: '',
      workScope: '',
      startTime: new Date(),
      selectedEmployees: [],
      additionalInfo: '',
      status: 'pending',
      photosBefore: [],
      photosAfter: []
    });
  };

  const toggleEmployeeSelection = (employeeId: string) => {
    setForm(prev => ({
      ...prev,
      selectedEmployees: prev.selectedEmployees.includes(employeeId)
        ? prev.selectedEmployees.filter(id => id !== employeeId)
        : [...prev.selectedEmployees, employeeId]
    }));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    // Format as dd.mm.rr
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2); // Pobierz tylko ostatnie 2 cyfry roku
    
    return `${day}.${month}.${year}`;
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('pl-PL', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Oczekujące';
      case 'in_progress':
        return 'W realizacji';
      case 'completed':
        return 'Zakończone';
      default:
        return status;
    }
  };

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'pending':
        return styles.statusPending;
      case 'in_progress':
        return styles.statusInProgress;
      case 'completed':
        return styles.statusCompleted;
      default:
        return {};
    }
  };

  const handleTaskClick = async (task: Task) => {
    try {
      console.log('=== Starting handleTaskClick ===');
      console.log('Task details:', {
        taskId: task.id,
        companyId,
        userId,
        userType,
        taskStatus: task.status,
        assignedEmployees: task.assigned_employees
      });

      if (userType !== 'employee') {
        console.log('Rejected: User is not an employee');
        return;
      }

      if (task.status !== 'pending') {
        console.log('Rejected: Task is not pending, current status:', task.status);
        showAlert(
          'Informacja', 
          'Możesz rozpocząć tylko oczekujące zlecenia.',
          [{ text: 'OK', onPress: () => {}, style: 'default' }]
        );
        return;
      }

      if (!task.assigned_employees.includes(userId)) {
        console.log('Rejected: User is not assigned to task');
        console.log('Assigned employees:', task.assigned_employees);
        console.log('User ID:', userId);
        showAlert(
          'Informacja', 
          'Nie jesteś przypisany do tego zlecenia.',
          [{ text: 'OK', onPress: () => {}, style: 'default' }]
        );
        return;
      }

      console.log('Showing confirmation alert...');
      
      showAlert(
        'Rozpocznij pracę',
        `Czy chcesz rozpocząć pracę nad zleceniem: ${task.client_name}?`,
        [
          { text: 'Anuluj', onPress: () => console.log('User cancelled starting work'), style: 'cancel' },
          { text: 'Rozpocznij', onPress: startWork, style: 'default' }
        ]
      );

      async function startWork() {
        try {
          console.log('User confirmed starting work');

          // 1. Check for active session
          console.log('Checking for active sessions...');
          const { data: activeSession, error: activeSessionError } = await supabase
            .from('work_sessions')
            .select('*')
            .eq('employee_id', userId)
            .eq('company_id', companyId)
            .is('end_time', null)
            .single();

          console.log('Active session check result:', { activeSession, activeSessionError });

          if (activeSessionError && activeSessionError.code !== 'PGRST116') {
            console.error('Error checking active session:', activeSessionError);
            throw new Error(`Błąd sprawdzania aktywnej sesji: ${activeSessionError.message}`);
          }

          if (activeSession) {
            console.log('Found active session:', activeSession);
            showAlert(
              'Informacja', 
              'Masz już aktywną sesję pracy. Zakończ ją przed rozpoczęciem nowej.',
              [{ text: 'OK', onPress: () => {}, style: 'default' }]
            );
            return;
          }

          // 2. Update task status
          console.log('Updating task status to in_progress...');
          const { data: updatedTask, error: taskError } = await supabase
            .from('tasks')
            .update({ 
              status: 'in_progress',
              updated_at: new Date().toISOString()
            })
            .match({ 
              id: task.id,
              company_id: companyId,
              status: 'pending'
            })
            .select()
            .single();

          if (taskError) {
            console.error('Task update error:', taskError);
            console.error('Task update details:', {
              taskId: task.id,
              companyId,
              userId,
              currentStatus: task.status
            });
            throw new Error(`Błąd aktualizacji zlecenia: ${taskError.message}`);
          }

          if (!updatedTask) {
            throw new Error('Nie udało się zaktualizować statusu zlecenia. Zadanie mogło zostać już rozpoczęte przez innego pracownika.');
          }

          console.log('Task updated successfully:', updatedTask);

          // 3. Create work session
          const sessionData = {
            employee_id: userId,
            company_id: companyId,
            task_id: task.id,
            job_order: `${task.client_name} - ${task.address}`,
            start_time: new Date().toISOString(),
            status: 'active',
            duration_minutes: 0,
            is_working: true
          };

          console.log('Creating work session with data:', sessionData);

          const { data: newSession, error: sessionError } = await supabase
            .from('work_sessions')
            .insert([sessionData])
            .select()
            .single();

          if (sessionError) {
            console.error('Session creation error:', sessionError);
            console.error('Session error details:', {
              message: sessionError.message,
              details: sessionError.details,
              hint: sessionError.hint,
              code: sessionError.code
            });
            
            // Rollback task status
            console.log('Rolling back task status due to session creation error...');
            const { error: revertError } = await supabase
              .from('tasks')
              .update({ status: 'pending' })
              .eq('id', task.id)
              .eq('company_id', companyId);

            if (revertError) {
              console.error('Error reverting task status:', revertError);
            }

            throw new Error(`Błąd tworzenia sesji: ${sessionError.message}`);
          }

          console.log('Work session created successfully:', newSession);
          showAlert(
            'Sukces', 
            'Rozpoczęto pracę nad zleceniem',
            [{ text: 'OK', onPress: () => fetchTasks(), style: 'default' }]
          );

        } catch (error) {
          console.error('Error in start work process:', error);
          showAlert(
            'Błąd', 
            error instanceof Error 
              ? `Nie udało się rozpocząć pracy: ${error.message}` 
              : 'Nie udało się rozpocząć pracy. Sprawdź konsolę po więcej szczegółów.',
            [{ text: 'OK', onPress: () => {}, style: 'default' }]
          );
        }
      }

    } catch (error) {
      console.error('Unexpected error in handleTaskClick:', error);
      showAlert(
        'Błąd', 
        'Wystąpił nieoczekiwany błąd. Spróbuj ponownie.',
        [{ text: 'OK', onPress: () => {}, style: 'default' }]
      );
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const sortedAndFilteredTasks = React.useMemo(() => {
    let filtered = [...tasks];

    // Apply filters
    if (filterClient) {
      filtered = filtered.filter(task => 
        task.client_name.toLowerCase().includes(filterClient.toLowerCase())
      );
    }

    if (filterAddress) {
      filtered = filtered.filter(task => 
        task.address.toLowerCase().includes(filterAddress.toLowerCase())
      );
    }

    if (filterStatus !== 'all') {
      filtered = filtered.filter(task => task.status === filterStatus);
    }

    // Filtrowanie według pola wyszukiwania
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(task => 
        (task.client_name && task.client_name.toLowerCase().includes(query)) ||
        (task.address && task.address.toLowerCase().includes(query)) ||
        (task.work_scope && task.work_scope.toLowerCase().includes(query))
      );
    }

    // Apply date filters
    if (filterDateFrom) {
      filtered = filtered.filter(task => 
        new Date(task.date) >= new Date(filterDateFrom)
      );
    }

    if (filterDateTo) {
      filtered = filtered.filter(task => 
        new Date(task.date) <= new Date(filterDateTo)
      );
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      const multiplier = sortOrder === 'asc' ? 1 : -1;
      
      switch (sortField) {
        case 'date':
          return (new Date(a.date).getTime() - new Date(b.date).getTime()) * multiplier;
        case 'client':
          return a.client_name.localeCompare(b.client_name) * multiplier;
        case 'address':
          return a.address.localeCompare(b.address) * multiplier;
        case 'time':
          return a.start_time.localeCompare(b.start_time) * multiplier;
        case 'status': {
          const statusOrder = { pending: 0, in_progress: 1, completed: 2 };
          return (statusOrder[a.status] - statusOrder[b.status]) * multiplier;
        }
        default:
          return 0;
      }
    });
  }, [tasks, sortField, sortOrder, filterClient, filterAddress, filterStatus, filterDateFrom, filterDateTo, searchQuery]);

  const handleClearFilters = () => {
    setFilterClient('');
    setFilterAddress('');
    setFilterDateFrom('');
    setFilterDateTo('');
    setSearchQuery('');
  };

  const renderTaskList = () => {
    return (
      <View style={styles.container}>
        <ScrollView 
          style={styles.content} 
          contentContainerStyle={{ width: '100%', padding: 0 }}
        >
          {loading ? (
            <ActivityIndicator size="large" color="#2563EB" style={styles.loader} />
          ) : tasks.length === 0 ? (
            <Text style={styles.noTasks}>
              {userType === 'company' 
                ? 'Brak zleceń do wyświetlenia'
                : 'Nie masz przypisanych zleceń'}
            </Text>
          ) : (
            <>
              <View style={styles.filtersContainer}>
                <View style={styles.searchBarContainer}>
                  <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder={i18n.t('searchTasks')}
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholderTextColor="#9CA3AF"
                  />
                  {searchQuery.length > 0 && (
                    <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearSearchButton}>
                      <Ionicons name="close-circle" size={20} color="#6B7280" />
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity
                    style={[
                      styles.dateButton,
                      (filterDateFrom || filterDateTo) && styles.dateButtonActive,
                    ]}
                    onPress={() => setShowFilters(true)}
                  >
                    <Ionicons
                      name="calendar-outline"
                      size={18}
                      color={(filterDateFrom || filterDateTo) ? '#2563EB' : '#4B5563'}
                    />
                    <Text
                      style={[
                        styles.dateButtonText,
                        (filterDateFrom || filterDateTo) && styles.dateButtonTextActive,
                      ]}
                    >
                      {i18n.t('date')}
                    </Text>
                  </TouchableOpacity>
                </View>
                
                {(searchQuery.length > 0 || filterStatus !== 'all' || filterDateFrom || filterDateTo) && (
                  <TouchableOpacity 
                    onPress={handleClearFilters}
                    style={styles.clearFiltersButton}
                  >
                    <Ionicons name="close-circle-outline" size={16} color="#4B5563" />
                    <Text style={styles.clearFiltersText}>{i18n.t('clearAllFilters')}</Text>
                  </TouchableOpacity>
                )}
                
                <View style={styles.statusFilter}>
                  <TouchableOpacity
                    style={[
                      styles.statusFilterButton,
                      filterStatus === 'all' && styles.statusFilterButtonActive
                    ]}
                    onPress={() => setFilterStatus('all')}
                  >
                    <Text style={[
                      styles.statusFilterText,
                      filterStatus === 'all' && styles.statusFilterTextActive
                    ]}>{i18n.t('all')}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.statusFilterButton,
                      filterStatus === 'pending' && styles.statusFilterButtonActive
                    ]}
                    onPress={() => setFilterStatus('pending')}
                  >
                    <Text style={[
                      styles.statusFilterText,
                      filterStatus === 'pending' && styles.statusFilterTextActive
                    ]}>{i18n.t('pending')}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.statusFilterButton,
                      filterStatus === 'in_progress' && styles.statusFilterButtonActive
                    ]}
                    onPress={() => setFilterStatus('in_progress')}
                  >
                    <Text style={[
                      styles.statusFilterText,
                      filterStatus === 'in_progress' && styles.statusFilterTextActive
                    ]}>{i18n.t('inProgress')}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.statusFilterButton,
                      filterStatus === 'completed' && styles.statusFilterButtonActive
                    ]}
                    onPress={() => setFilterStatus('completed')}
                  >
                    <Text style={[
                      styles.statusFilterText,
                      filterStatus === 'completed' && styles.statusFilterTextActive
                    ]}>{i18n.t('completed')}</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <Modal
                visible={showFilters}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setShowFilters(false)}
              >
                <View style={styles.modalOverlay}>
                  <View style={styles.dateModalContent}>
                    <View style={styles.dateModalHeader}>
                      <Text style={styles.dateModalTitle}>{i18n.t('dateRangeSelect')}</Text>
                      <TouchableOpacity onPress={() => setShowFilters(false)}>
                        <Ionicons name="close" size={24} color="#4B5563" />
                      </TouchableOpacity>
                    </View>
                    
                    <View style={styles.dateModalBody}>
                      <DatePicker
                        label={i18n.t('dateFrom')}
                        date={filterDateFrom}
                        onDateChange={(date: string) => setFilterDateFrom(date)}
                        placeholder={i18n.t('selectStartDate')}
                      />
                      <View style={{ height: 16 }} />
                      <DatePicker
                        label={i18n.t('dateTo')}
                        date={filterDateTo}
                        onDateChange={(date: string) => setFilterDateTo(date)}
                        placeholder={i18n.t('selectEndDate')}
                      />
                      
                      <View style={styles.modalButtons}>
                        <TouchableOpacity 
                          style={styles.modalCancelButton}
                          onPress={() => {
                            setFilterDateFrom('');
                            setFilterDateTo('');
                            setShowFilters(false);
                          }}
                        >
                          <Text style={styles.modalCancelButtonText}>{i18n.t('clear')}</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={styles.modalConfirmButton}
                          onPress={() => setShowFilters(false)}
                        >
                          <Text style={styles.modalConfirmButtonText}>{i18n.t('apply')}</Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </View>
              </Modal>

              <View style={styles.tableContainer}>
                <View style={styles.tableHeader}>
                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 1 }]}
                    onPress={() => handleSort('date')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('date')}</Text>
                      {sortField === 'date' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 2.5 }]}
                    onPress={() => handleSort('client')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('client')}</Text>
                      {sortField === 'client' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 2.5 }]}
                    onPress={() => handleSort('address')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('address')}</Text>
                      {sortField === 'address' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 1 }]}
                    onPress={() => handleSort('status')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('status')}</Text>
                      {sortField === 'status' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                </View>
                
                <ScrollView style={styles.tableBody}>
                  {sortedAndFilteredTasks.map((task) => {
                    return (
                      <TouchableOpacity 
                        key={task.id} 
                        style={[
                          styles.tableRow
                        ]}
                        onPress={() => {
                          if (onTaskSelect) {
                            onTaskSelect(task.id);
                          }
                        }}
                      >
                        <Text style={[styles.cell, { flex: 1 }]}>
                          {formatDate(task.date)}
                        </Text>
                        <Text style={[styles.cell, { flex: 2.5 }]} numberOfLines={1}>
                          {task.client_name}
                        </Text>
                        <Text style={[styles.cell, { flex: 2.5 }]} numberOfLines={1}>
                          {task.address}
                        </Text>
                        <View style={[styles.statusContainer, { flex: 1 }]}>
                          <Text style={[styles.statusBadge, getStatusStyle(task.status)]}>
                            {getStatusLabel(task.status)}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </ScrollView>
              </View>
            </>
          )}
        </ScrollView>
      </View>
    );
  };

  const renderAddTask = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => {
            if (activeTab === 'add') {
              if (onTabChange) {
                onTabChange('list');
              }
            }
          }}
        >
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('addTask')}</Text>
        <View style={styles.placeholderView} />
      </View>
      
      <ScrollView 
        style={styles.content} 
        contentContainerStyle={styles.formScrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {/* Data zlecenia */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('date')}</Text>
          <DatePicker
            date={form.date.toISOString().split('T')[0]}
            onDateChange={(value) => {
              if (value) {
                const newDate = new Date(value);
                setForm(prev => ({ ...prev, date: newDate }));
              }
            }}
            placeholder={i18n.t('selectStartDate')}
          />
        </View>

        {/* Zleceniodawca */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('clientNameLabel')}</Text>
          <TextInput
            style={styles.formInput}
            value={form.clientName}
            onChangeText={(text) => setForm(prev => ({ ...prev, clientName: text }))}
            placeholder={i18n.t('clientName')}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Adres */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('taskAddressLabel')}</Text>
          <TextInput
            style={styles.formInput}
            value={form.address}
            onChangeText={(text) => setForm(prev => ({ ...prev, address: text }))}
            placeholder={i18n.t('address')}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Zakres prac */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('workScopeLabel')}</Text>
          <TextInput
            style={[styles.formInput, styles.formTextArea]}
            value={form.workScope}
            onChangeText={(text) => setForm(prev => ({ ...prev, workScope: text }))}
            placeholder={i18n.t('workScope')}
            placeholderTextColor="#9CA3AF"
            multiline
            numberOfLines={4}
          />
        </View>

        {/* Godzina rozpoczęcia */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('startTime')}</Text>
          <TimePicker
            time={form.startTime}
            onTimeChange={(newTime) => {
              setForm(prev => ({ ...prev, startTime: newTime }));
            }}
            placeholder={i18n.t('startTime')}
          />
        </View>

        {/* Wybór pracowników */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('assignedEmployees')}*</Text>
          <TouchableOpacity 
            style={styles.photoButton} 
            onPress={() => setShowEmployeeModal(true)}
          >
            <View style={styles.photoButtonContent}>
              <Ionicons name="people-outline" size={20} color="#2563EB" />
              <Text style={styles.photoButtonText}>
                {form.selectedEmployees.length > 0 
                  ? `${i18n.t('selectEmployee')} (${form.selectedEmployees.length})` 
                  : i18n.t('selectEmployee')}
              </Text>
            </View>
          </TouchableOpacity>
          
          {form.selectedEmployees.length > 0 && (
            <View style={styles.selectedEmployeeChips}>
              {employees
                .filter(emp => form.selectedEmployees.includes(emp.id))
                .map(emp => (
                  <View key={emp.id} style={styles.employeeChip}>
                    <Text style={styles.employeeChipText}>{emp.full_name}</Text>
                    <TouchableOpacity 
                      onPress={() => toggleEmployeeSelection(emp.id)}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <Ionicons name="close-circle" size={18} color="#4B5563" />
                    </TouchableOpacity>
                  </View>
                ))}
            </View>
          )}
        </View>

        {/* Dodatkowe informacje */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('additionalInfoLabel')}</Text>
          <TextInput
            style={[styles.formInput, styles.formTextArea]}
            value={form.additionalInfo}
            onChangeText={(text) => setForm(prev => ({ ...prev, additionalInfo: text }))}
            placeholder={i18n.t('additionalInfo')}
            placeholderTextColor="#9CA3AF"
            multiline
            numberOfLines={4}
          />
        </View>

        {/* Sekcja zdjęć przed */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('photosBefore')}</Text>
          <TouchableOpacity 
            style={styles.photoButton} 
            onPress={() => handleImagePick('before')}
            disabled={uploadingPhotos}
          >
            <Ionicons name="camera-outline" size={24} color="#2563EB" />
            <Text style={styles.photoButtonText}>
              {uploadingPhotos ? i18n.t('loading') : i18n.t('photos')}
            </Text>
          </TouchableOpacity>
          
          {form.photosBefore.length > 0 && (
            <View style={styles.photosList}>
              {form.photosBefore.map((photo, index) => (
                <View key={index} style={styles.photoContainer}>
                  <Image source={{ uri: photo }} style={styles.photoThumbnail} />
                  <TouchableOpacity 
                    style={styles.removePhotoButton}
                    onPress={() => removeImage('before', index)}
                  >
                    <Ionicons name="close-circle" size={24} color="#DC2626" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
          <Text style={styles.formFieldNote}>
            Zdjęcia zostaną zapisane jako linki w bazie danych
          </Text>
        </View>

        {/* Informacja o polach wymaganych */}
        <Text style={styles.formRequiredFieldsInfo}>{i18n.t('requiredFields')}</Text>

        {/* Przycisk utworzenia zadania */}
        <TouchableOpacity 
          style={[
            styles.formSubmitButton, 
            areRequiredFieldsEmpty() ? styles.formSubmitButtonDisabled : {}
          ]} 
          onPress={handleCreateTask}
          disabled={areRequiredFieldsEmpty() || uploadingPhotos}
        >
          <Text style={styles.submitButtonText}>{i18n.t('createTask')}</Text>
        </TouchableOpacity>

        {/* Modal wyboru pracowników */}
        <Modal
          visible={showEmployeeModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowEmployeeModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{i18n.t('selectEmployees')}</Text>
                <TouchableOpacity onPress={() => setShowEmployeeModal(false)}>
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.modalBody}>
                {employees.map((employee) => (
                  <TouchableOpacity
                    key={employee.id}
                    style={styles.employeeItem}
                    onPress={() => toggleEmployeeSelection(employee.id)}
                  >
                    <View style={styles.employeeInfo}>
                      <Text style={styles.employeeName}>{employee.full_name}</Text>
                      <Text style={styles.employeeEmail}>{employee.email}</Text>
                    </View>
                    <View style={[
                      styles.checkbox,
                      form.selectedEmployees.includes(employee.id) && styles.checkboxSelected
                    ]}>
                      {form.selectedEmployees.includes(employee.id) && (
                        <Ionicons name="checkmark" size={16} color="white" />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>
      </ScrollView>
    </View>
  );

  const areRequiredFieldsEmpty = () => {
    return !form.clientName.trim() || 
           !form.address.trim() || 
           !form.workScope.trim() || 
           form.selectedEmployees.length === 0;
  };

  return (
    <View style={styles.container}>
      {activeTab === 'list' ? renderTaskList() : renderAddTask()}
      {/* Render CustomAlert only on mobile platforms */}
      {!isWeb && <CustomAlert />}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
    display: 'none', // Ukrywamy stary nagłówek
  },
  backText: {
    fontSize: 16,
    marginLeft: 8,
    color: '#1F2937',
    display: 'none', // Ukrywamy tekst "Powrót"
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
    paddingHorizontal: 16,
    display: 'none', // Ukrywamy tytuł, bo jest w headerze
  },
  content: {
    flex: 1,
    width: '100%',
    padding: 0,
    backgroundColor: 'white',
  },
  scrollContent: {
    padding: 20,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  comingSoon: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
    fontSize: 16,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 10,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  employeesButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    marginBottom: 10,
  },
  employeeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  employeesButtonText: {
    color: '#1F2937',
    fontSize: 14,
    marginLeft: 8,
  },
  createButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  createButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '90%',
    maxWidth: 500,
    maxHeight: '80%',
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    padding: 20,
  },
  employeeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
  },
  employeeEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#2563EB',
    borderColor: '#2563EB',
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'transparent',
  },
  photoButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  photoButtonText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  photosList: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  photoContainer: {
    marginRight: 10,
    position: 'relative',
  },
  photoThumbnail: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  loader: {
    marginTop: 20,
  },
  noTasks: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
    fontSize: 16,
  },
  tableContainer: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 0,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginHorizontal: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    marginTop: 16,
    marginBottom: 60,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  headerCell: {
    paddingHorizontal: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    paddingHorizontal: 2,
  },
  headerText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4B5563',
    textAlign: 'center',
  },
  sortIcon: {
    marginLeft: 4,
  },
  tableBody: {
    flex: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  cell: {
    fontSize: 13,
    color: '#1F2937',
    paddingHorizontal: 2,
    textAlign: 'center',
  },
  statusContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 4,
    borderRadius: 12,
    fontSize: 10,
    fontWeight: '500',
    overflow: 'hidden',
    width: 75,
    textAlign: 'center',
    marginHorizontal: 2,
  },
  statusPending: {
    backgroundColor: '#FEF3C7',
    color: '#92400E',
  },
  statusInProgress: {
    backgroundColor: '#DBEAFE',
    color: '#1E40AF',
  },
  statusCompleted: {
    backgroundColor: '#D1FAE5',
    color: '#065F46',
  },
  filterActions: {
    width: '100%',
    marginBottom: 16,
    paddingHorizontal: 0,
    gap: 10,
    backgroundColor: 'white',
    paddingVertical: 12,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  filterButtonText: {
    marginHorizontal: 8,
    fontSize: 14,
    color: '#1F2937',
  },
  filterContent: {
    gap: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
  },
  filterInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
  },
  statusFilter: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    marginTop: 8,
    marginBottom: 8,
    flexWrap: 'wrap',
    justifyContent: 'center',
    backgroundColor: 'white',
    width: '100%',
    rowGap: 8,
    gap: 8,
  },
  statusFilterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    marginHorizontal: 2,
  },
  statusFilterButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  statusFilterText: {
    fontSize: 14,
    color: '#374151',
  },
  statusFilterTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  inputRequired: {
    borderColor: '#E5E7EB',
    borderWidth: 1,
  },
  selectedEmployeeChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  employeeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
  },
  employeeChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    width: '90%',
    maxWidth: 400,
    overflow: 'hidden',
  },
  alertTitleContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  alertTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'black',
    textAlign: 'center',
  },
  alertBodyContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  alertMessage: {
    fontSize: 16,
    color: 'black',
    textAlign: 'center',
    lineHeight: 22,
  },
  alertButtonsContainer: {
    flexDirection: 'row',
    padding: 16,
    justifyContent: 'center',
  },
  alertButton: {
    flex: 1,
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#6B7280',
  },
  confirmButton: {
    backgroundColor: '#2563EB',
  },
  alertButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  filtersContainer: {
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingBottom: 0,
    width: '100%',
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 8,
    marginTop: 16,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    padding: 8,
  },
  clearSearchButton: {
    padding: 8,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginLeft: 8,
  },
  dateButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  dateButtonText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 4,
  },
  dateButtonTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalCancelButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    marginRight: 8,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  modalConfirmButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#2563EB',
    borderRadius: 6,
    alignItems: 'center',
  },
  modalConfirmButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  dateModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  dateModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  dateModalBody: {
    padding: 20,
  },
  dateModalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    marginBottom: 4,
    padding: 6,
  },
  clearFiltersText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 4,
  },
  formScrollContent: {
    padding: 20,
    paddingHorizontal: 24,
    paddingBottom: 40,
    backgroundColor: 'white',
  },
  formInputGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 10,
  },
  formInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#1F2937',
  },
  formTextArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  formFieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  formRequiredFieldsInfo: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TaskManager; 