-- Najpierw usuń istniejące ograniczenie
ALTER TABLE companies DROP CONSTRAINT IF EXISTS companies_account_type_check;

-- Zaktualizuj istniejące dane do poprawnego formatu
UPDATE companies
SET account_type = CASE 
    WHEN LOWER(account_type) LIKE '%basic%' THEN 'basic'
    WHEN LOWER(account_type) LIKE '%pro%' THEN 'pro'
    WHEN LOWER(account_type) LIKE '%business%' THEN 'business'
    ELSE 'free'
END;

-- Dodaj nowe ograniczenie
ALTER TABLE companies 
ADD CONSTRAINT companies_account_type_check 
CHECK (account_type IN ('free', 'basic', 'pro', 'business'));

-- Zak<PERSON><PERSON><PERSON><PERSON> funkcję RPC
CREATE OR REPLACE FUNCTION update_company_account_type_rpc(
  p_company_id UUID,
  p_plan_name TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  verification_limit INTEGER;
  normalized_plan_name TEXT;
BEGIN
  -- Normalizuj nazwę planu do podstawowej wersji
  normalized_plan_name := CASE 
    WHEN LOWER(p_plan_name) LIKE '%basic%' THEN 'basic'
    WHEN LOWER(p_plan_name) LIKE '%pro%' THEN 'pro'
    WHEN LOWER(p_plan_name) LIKE '%business%' THEN 'business'
    ELSE 'free'
  END;
  
  -- Ustaw limit kodów weryfikacyjnych na podstawie planu
  verification_limit := CASE
    WHEN normalized_plan_name = 'basic' THEN 5
    WHEN normalized_plan_name = 'pro' THEN 20
    WHEN normalized_plan_name = 'business' THEN 999999
    ELSE 2 -- dla planu free
  END;
  
  -- Aktualizuj firmę
  UPDATE companies
  SET account_type = normalized_plan_name,
      verification_code_limit = verification_limit
  WHERE id = p_company_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER;

-- Nadaj uprawnienia do funkcji
GRANT EXECUTE ON FUNCTION update_company_account_type_rpc TO service_role;
GRANT EXECUTE ON FUNCTION update_company_account_type_rpc TO authenticated;

-- Wyświetl aktualne wartości dla weryfikacji
SELECT DISTINCT account_type FROM companies; 