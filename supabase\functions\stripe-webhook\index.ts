import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

const stripe = Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient()
});

const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    const body = await req.text();
    const event = JSON.parse(body);
    console.log('Processing webhook event:', event.type);

    // Log webhook event
    try {
      await supabase
        .from('webhook_logs')
        .insert({
          event_type: event.type,
          event_id: event.id,
          company_id: event.data.object.client_reference_id,
          data: event.data.object
        });
    } catch (logError) {
      console.error('Failed to log webhook event:', logError);
    }

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        const companyId = session.client_reference_id;
        
        console.log('Full session object:', JSON.stringify(session, null, 2));
        
        // Retrieve the session with line items
        const expandedSession = await stripe.checkout.sessions.retrieve(session.id, {
          expand: ['line_items']
        });
        
        console.log('Expanded session line items:', JSON.stringify(expandedSession.line_items, null, 2));
        
        const priceId = expandedSession.line_items?.data[0]?.price?.id;

        if (!companyId) {
          throw new Error('No company ID found in session');
        }

        if (!priceId) {
          throw new Error('No price ID found in expanded session');
        }

        console.log('Processing subscription for company:', companyId, 'with price ID:', priceId);

        // Get plan details from subscription_plans table
        const { data: plan, error: planError } = await supabase
          .from('subscription_plans')
          .select('*')
          .eq('stripe_price_id', priceId)
          .single();

        if (planError || !plan) {
          console.error('Plan not found for price ID:', priceId);
          throw new Error('Plan not found for price ID: ' + priceId);
        }

        console.log('Found plan:', plan.name);

        // Create subscription record
        const { error: subscriptionError } = await supabase
          .from('company_subscriptions')
          .insert({
            company_id: companyId,
            plan_id: plan.id,
            stripe_subscription_id: session.subscription,
            stripe_customer_id: session.customer,
            status: 'active',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancel_at_period_end: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (subscriptionError) {
          console.error('Failed to create subscription record:', subscriptionError);
          throw new Error('Failed to create subscription record: ' + subscriptionError.message);
        }

        console.log('Created subscription record');

        // Update company account type using RPC function
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('update_company_account_type_rpc', {
            p_company_id: companyId,
            p_plan_name: plan.name.toLowerCase()
          });

        if (rpcError) {
          console.error('Failed to update company via RPC:', rpcError);
          throw new Error('Failed to update company via RPC: ' + rpcError.message);
        }

        console.log('Updated company account type via RPC');

        // Create payment history record using RPC function
        try {
          const { data: paymentData, error: paymentError } = await supabase
            .rpc('process_stripe_payment', {
              p_company_id: companyId,
              p_subscription_id: session.subscription,
              p_amount: session.amount_total,
              p_currency: 'usd',
              p_payment_intent_id: session.payment_intent,
              p_invoice_id: session.invoice,
              p_description: `Subscription ${plan.name}`
            });

          if (paymentError) {
            console.error('Failed to create payment record:', paymentError);
            throw new Error('Failed to create payment record: ' + paymentError.message);
          }

          console.log('Created payment history record');
        } catch (paymentErr) {
          console.error('Error in payment history creation:', paymentErr);
        }
        break;
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        console.log('Subscription updated:', {
          id: subscription.id,
          status: subscription.status,
          cancel_at_period_end: subscription.cancel_at_period_end,
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
        });
        
        // Find company subscription
        const { data: companySubscription, error: findError } = await supabase
          .from('company_subscriptions')
          .select('id, company_id, plan_id')
          .eq('stripe_subscription_id', subscription.id)
          .single();

        if (findError || !companySubscription) {
          console.error('Could not find subscription:', subscription.id);
          throw new Error('Could not find subscription');
        }

        console.log('Found company subscription:', companySubscription);

        // Update subscription status
        const { error: updateError } = await supabase
          .from('company_subscriptions')
          .update({
            status: subscription.status,
            current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            cancel_at_period_end: subscription.cancel_at_period_end,
            updated_at: new Date().toISOString()
          })
          .eq('id', companySubscription.id);

        if (updateError) {
          console.error('Failed to update subscription:', updateError);
          throw new Error('Failed to update subscription');
        }

        console.log('Successfully updated subscription status');

        // Jeśli subskrypcja została anulowana (cancel_at_period_end = true)
        if (subscription.cancel_at_period_end) {
          console.log('Subscription marked for cancellation at period end');
          
          // Możemy dodać wpis do historii lub wysłać powiadomienie
          try {
            await supabase
              .from('subscription_events')
              .insert({
                company_id: companySubscription.company_id,
                subscription_id: companySubscription.id,
                event_type: 'cancellation_scheduled',
                event_data: {
                  cancel_at: subscription.cancel_at,
                  current_period_end: subscription.current_period_end
                }
              });
            console.log('Added cancellation event to history');
          } catch (eventError) {
            console.error('Failed to log cancellation event:', eventError);
          }
        }

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        console.log('Subscription deleted:', subscription.id);
        
        // Find company subscription
        const { data: companySubscription, error: findError } = await supabase
          .from('company_subscriptions')
          .select('id, company_id')
          .eq('stripe_subscription_id', subscription.id)
          .single();

        if (findError || !companySubscription) {
          console.error('Could not find subscription:', subscription.id);
          throw new Error('Could not find subscription');
        }

        console.log('Found company subscription:', companySubscription);

        // Update subscription status
        const { error: updateError } = await supabase
          .from('company_subscriptions')
          .update({
            status: 'canceled',
            updated_at: new Date().toISOString()
          })
          .eq('id', companySubscription.id);

        if (updateError) {
          console.error('Failed to update subscription:', updateError);
          throw new Error('Failed to update subscription');
        }

        console.log('Successfully marked subscription as canceled');

        // Dezaktywuj wszystkich pracowników firmy
        const { error: deactivateError } = await supabase.rpc(
          'deactivate_company_employees',
          { p_company_id: companySubscription.company_id }
        );

        if (deactivateError) {
          console.error('Failed to deactivate employees:', deactivateError);
          // Nie przerywamy procesu, kontynuujemy z pozostałymi operacjami
        } else {
          console.log('Successfully deactivated all company employees');
        }

        // Reset company to free plan using RPC function
        const { error: rpcError } = await supabase
          .rpc('update_company_account_type_rpc', {
            p_company_id: companySubscription.company_id,
            p_plan_name: 'free'
          });

        if (rpcError) {
          console.error('Failed to update company account type:', rpcError);
          throw new Error('Failed to update company via RPC');
        }

        console.log('Successfully reset company to free plan');

        // Dodaj wpis o całkowitym anulowaniu subskrypcji
        try {
          await supabase
            .from('subscription_events')
            .insert({
              company_id: companySubscription.company_id,
              subscription_id: companySubscription.id,
              event_type: 'subscription_canceled',
              event_data: {
                canceled_at: new Date().toISOString(),
                reason: subscription.cancellation_details?.reason || 'unknown'
              }
            });
          console.log('Added cancellation event to history');
        } catch (eventError) {
          console.error('Failed to log cancellation event:', eventError);
        }

        break;
      }
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400
    });
  }
});