import { registerRootComponent } from 'expo';
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.23.0';
import { corsHeaders } from '../_shared/cors.ts';

import App from './App';

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);

// Funkcja pomocnicza do obsługi CORS preflight
function handleCors(req: Request) {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  return null;
}

// Konfiguracja Stripe
const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
const stripe = new Stripe(stripeApiKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient(),
});

serve(async (req) => {
  // Obsługa CORS dla zapytań preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Sprawdź metodę HTTP
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Metoda nie jest dozwolona' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Pobierz dane z żądania
    const body = await req.text();
    
    // Parsuj dane JSON zamiast weryfikować podpis (aby uniknąć błędu SubtleCryptoProvider)
    let event;
    try {
      event = JSON.parse(body);
      console.log("Webhook event parsed successfully:", event.type);
    } catch (err) {
      console.error(`Webhook parsing failed: ${err.message}`);
      return new Response(JSON.stringify({ error: `Webhook Error: ${err.message}` }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Inicjalizacja klienta Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Brakujące zmienne środowiskowe Supabase');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Obsługa różnych typów zdarzeń
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        
        // Pobierz dane klienta i subskrypcji
        const customerId = session.customer;
        const subscriptionId = session.subscription;
        const clientReferenceId = session.client_reference_id; // ID firmy
        
        if (!clientReferenceId) {
          console.error('Brak client_reference_id w sesji Stripe');
          break;
        }
        
        // Pobierz szczegóły subskrypcji
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        const priceId = subscription.items.data[0].price.id;
        
        // Znajdź plan w bazie danych na podstawie stripe_price_id
        const { data: plans, error: plansError } = await supabase
          .from('subscription_plans')
          .select('*')
          .eq('stripe_price_id', priceId)
          .limit(1);
        
        if (plansError || !plans || plans.length === 0) {
          console.error('Nie znaleziono planu subskrypcji:', plansError);
          break;
        }
        
        const planId = plans[0].id;
        
        // Utwórz wpis w tabeli company_subscriptions
        const { error: subscriptionError } = await supabase
          .from('company_subscriptions')
          .insert([{
            company_id: clientReferenceId,
            plan_id: planId,
            stripe_subscription_id: subscriptionId,
            stripe_customer_id: customerId,
            status: subscription.status,
            current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            cancel_at_period_end: subscription.cancel_at_period_end,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }]);
        
        if (subscriptionError) {
          console.error('Błąd zapisywania subskrypcji:', subscriptionError);
          break;
        }
        
        // Zaktualizuj status firmy
        await supabase
          .from('companies')
          .update({
            account_type: 'premium'
          })
          .eq('id', clientReferenceId);
        
        // Dodaj wpis do historii płatności
        await supabase
          .from('payment_history')
          .insert([{
            company_id: clientReferenceId,
            amount: subscription.items.data[0].price.unit_amount,
            currency: subscription.items.data[0].price.currency,
            status: 'succeeded',
            type: 'subscription',
            stripe_payment_id: session.payment_intent,
            created_at: new Date().toISOString()
          }]);
        
        break;
      }
      
      case 'invoice.payment_succeeded': {
        const invoice = event.data.object;
        const subscriptionId = invoice.subscription;
        
        if (!subscriptionId) break;
        
        // Pobierz subskrypcję
        const subscription = await stripe.subscriptions.retrieve(subscriptionId);
        const customerId = subscription.customer;
        
        // Znajdź firmę na podstawie stripe_customer_id
        const { data: subscriptions, error: subscriptionsError } = await supabase
          .from('company_subscriptions')
          .select('company_id')
          .eq('stripe_customer_id', customerId)
          .limit(1);
        
        if (subscriptionsError || !subscriptions || subscriptions.length === 0) {
          console.error('Nie znaleziono subskrypcji firmy:', subscriptionsError);
          break;
        }
        
        const companyId = subscriptions[0].company_id;
        
        // Zaktualizuj okres subskrypcji
        await supabase
          .from('company_subscriptions')
          .update({
            current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('stripe_subscription_id', subscriptionId);
        
        // Dodaj wpis do historii płatności
        await supabase
          .from('payment_history')
          .insert([{
            company_id: companyId,
            amount: invoice.amount_paid,
            currency: invoice.currency,
            status: 'succeeded',
            type: 'renewal',
            stripe_payment_id: invoice.payment_intent,
            created_at: new Date().toISOString()
          }]);
        
        break;
      }
      
      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        const subscriptionId = subscription.id;
        
        // Zaktualizuj status subskrypcji w bazie danych
        await supabase
          .from('company_subscriptions')
          .update({
            status: 'canceled',
            updated_at: new Date().toISOString()
          })
          .eq('stripe_subscription_id', subscriptionId);
        
        // Znajdź firmę na podstawie stripe_subscription_id
        const { data: subscriptions, error: subscriptionsError } = await supabase
          .from('company_subscriptions')
          .select('company_id')
          .eq('stripe_subscription_id', subscriptionId)
          .limit(1);
        
        if (subscriptionsError || !subscriptions || subscriptions.length === 0) {
          console.error('Nie znaleziono subskrypcji firmy:', subscriptionsError);
          break;
        }
        
        const companyId = subscriptions[0].company_id;
        
        // Zaktualizuj status firmy używając funkcji RPC
        const { error: rpcError } = await supabase
          .rpc('update_company_account_type_rpc', {
            p_company_id: companyId,
            p_plan_name: 'free'
          });

        if (rpcError) {
          console.error('Błąd podczas aktualizacji typu konta firmy:', rpcError);
          // Fallback do bezpośredniej aktualizacji
          await supabase
            .from('companies')
            .update({
              account_type: 'free',
              verification_code_limit: 2
            })
            .eq('id', companyId);
        }
        
        break;
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Błąd przetwarzania webhook:', error);
    const errorMessage = error instanceof Error ? error.message : 'Nieznany błąd';
    return new Response(JSON.stringify({ error: `Błąd serwera: ${errorMessage}` }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
