import { supabase } from './supabaseClient';

export interface EmployeeStatusCheck {
  company_id: string;
  subscription_status: string;
  subscription_plan: string;
  total_employees: number;
  active_employees: number;
  inactive_employees: number;
  has_active_subscription: boolean;
}

export interface EmployeeReactivationResult {
  success: boolean;
  message: string;
  reactivated_count?: number;
  company_id: string;
  error_type?: string;
}

/**
 * Sprawdza status pracowników firmy
 */
export const checkCompanyEmployeesStatus = async (companyId: string): Promise<EmployeeStatusCheck | null> => {
  try {
    const { data, error } = await supabase.rpc('check_company_employees_status', {
      p_company_id: companyId
    });

    if (error) {
      console.error('Error checking employees status:', error);
      return null;
    }

    return data as EmployeeStatusCheck;
  } catch (error) {
    console.error('Error in checkCompanyEmployeesStatus:', error);
    return null;
  }
};

/**
 * Reaktywuje pracowników po niepotrzebnej dezaktywacji (np. podczas zmiany planu)
 */
export const reactivateEmployeesAfterPlanChange = async (companyId: string): Promise<EmployeeReactivationResult | null> => {
  try {
    const { data, error } = await supabase.rpc('reactivate_employees_after_plan_change', {
      p_company_id: companyId
    });

    if (error) {
      console.error('Error reactivating employees:', error);
      return null;
    }

    return data as EmployeeReactivationResult;
  } catch (error) {
    console.error('Error in reactivateEmployeesAfterPlanChange:', error);
    return null;
  }
};

/**
 * Sprawdza czy firma potrzebuje naprawy statusu pracowników
 */
export const checkIfEmployeeRecoveryNeeded = async (companyId: string): Promise<boolean> => {
  const status = await checkCompanyEmployeesStatus(companyId);
  
  if (!status) {
    return false;
  }

  // Jeśli firma ma aktywną subskrypcję ale nieaktywnych pracowników, może potrzebować naprawy
  return status.has_active_subscription && status.inactive_employees > 0;
};

/**
 * Automatycznie naprawia status pracowników jeśli to potrzebne
 */
export const autoFixEmployeeStatus = async (companyId: string): Promise<{
  needed: boolean;
  result?: EmployeeReactivationResult;
  status?: EmployeeStatusCheck;
}> => {
  const status = await checkCompanyEmployeesStatus(companyId);
  
  if (!status) {
    return { needed: false };
  }

  const recoveryNeeded = status.has_active_subscription && status.inactive_employees > 0;
  
  if (!recoveryNeeded) {
    return { needed: false, status };
  }

  const result = await reactivateEmployeesAfterPlanChange(companyId);
  
  return {
    needed: true,
    result: result || undefined,
    status
  };
};
