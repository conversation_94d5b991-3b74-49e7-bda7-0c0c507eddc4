// Skrypt do sprawdzenia stanu bazy danych
const { createClient } = require('@supabase/supabase-js');

// Supabase credentials
const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Funkcja do sprawdzenia czy tabela istnieje
async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      console.error(`<PERSON><PERSON><PERSON><PERSON> podczas sprawdzania tabeli ${tableName}:`, error);
      return false;
    }
    
    console.log(`✅ Tabela ${tableName} istnieje`);
    return true;
  } catch (err) {
    console.error(`Wyjątek podczas sprawdzania tabeli ${tableName}:`, err);
    return false;
  }
}

// Funkcja do sprawdzenia czy funkcja istnieje
async function checkFunctionExists(functionName) {
  try {
    // Próba wywołania funkcji z minimalnymi parametrami
    const { data, error } = await supabase.rpc(functionName);
    
    if (error && error.message.includes('does not exist')) {
      console.error(`❌ Funkcja ${functionName} nie istnieje`);
      return false;
    } else {
      console.log(`✅ Funkcja ${functionName} istnieje`);
      return true;
    }
  } catch (err) {
    // Jeśli błąd nie dotyczy nieistniejącej funkcji, to prawdopodobnie funkcja istnieje
    // ale potrzebuje parametrów
    console.log(`✅ Funkcja ${functionName} prawdopodobnie istnieje (wymaga parametrów)`);
    return true;
  }
}

// Funkcja do sprawdzenia zawartości tabeli
async function checkTableContents(tableName, limit = 5) {
  try {
    const { data, error, count } = await supabase
      .from(tableName)
      .select('*', { count: 'exact' })
      .limit(limit);
    
    if (error) {
      console.error(`Błąd podczas pobierania danych z tabeli ${tableName}:`, error);
      return;
    }
    
    console.log(`📊 Tabela ${tableName} zawiera ${count} rekordów`);
    if (data && data.length > 0) {
      console.log(`Przykładowe dane z tabeli ${tableName}:`);
      console.log(JSON.stringify(data, null, 2));
    } else {
      console.log(`Tabela ${tableName} jest pusta`);
    }
  } catch (err) {
    console.error(`Wyjątek podczas pobierania danych z tabeli ${tableName}:`, err);
  }
}

// Funkcja do sprawdzenia statusu firmy
async function checkCompanyStatus(companyId) {
  try {
    const { data, error } = await supabase
      .from('companies')
      .select('id, name, account_type, verification_code_limit')
      .eq('id', companyId)
      .single();
    
    if (error) {
      console.error(`Błąd podczas pobierania informacji o firmie ${companyId}:`, error);
      return;
    }
    
    console.log(`📋 Informacje o firmie ${companyId}:`);
    console.log(JSON.stringify(data, null, 2));
  } catch (err) {
    console.error(`Wyjątek podczas pobierania informacji o firmie ${companyId}:`, err);
  }
}

// Funkcja do sprawdzenia subskrypcji firmy
async function checkCompanySubscription(companyId) {
  try {
    const { data, error } = await supabase
      .from('company_subscriptions')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (error) {
      console.error(`Błąd podczas pobierania subskrypcji firmy ${companyId}:`, error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log(`📊 Subskrypcja firmy ${companyId}:`);
      console.log(JSON.stringify(data[0], null, 2));
    } else {
      console.log(`❌ Firma ${companyId} nie ma subskrypcji`);
    }
  } catch (err) {
    console.error(`Wyjątek podczas pobierania subskrypcji firmy ${companyId}:`, err);
  }
}

// Funkcja do sprawdzenia logów webhook
async function checkWebhookLogs(limit = 5) {
  try {
    const { data, error, count } = await supabase
      .from('webhook_logs')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) {
      console.error(`Błąd podczas pobierania logów webhook:`, error);
      return;
    }
    
    console.log(`📊 Tabela webhook_logs zawiera ${count} rekordów`);
    if (data && data.length > 0) {
      console.log(`Ostatnie logi webhook:`);
      data.forEach(log => {
        console.log(`- Event: ${log.event_type}, ID: ${log.event_id}, Company: ${log.company_id}, Time: ${log.created_at}`);
      });
    } else {
      console.log(`Tabela webhook_logs jest pusta`);
    }
  } catch (err) {
    console.error(`Wyjątek podczas pobierania logów webhook:`, err);
  }
}

// Główna funkcja sprawdzająca
async function checkDatabase() {
  console.log('🔍 Sprawdzanie stanu bazy danych...\n');
  
  console.log('=== TABELE ===');
  await checkTableExists('companies');
  await checkTableExists('subscription_plans');
  await checkTableExists('company_subscriptions');
  await checkTableExists('payment_history');
  await checkTableExists('webhook_logs');
  
  console.log('\n=== FUNKCJE ===');
  await checkFunctionExists('update_company_premium');
  await checkFunctionExists('create_subscription');
  await checkFunctionExists('log_webhook_event');
  
  console.log('\n=== ZAWARTOŚĆ TABEL ===');
  await checkTableContents('subscription_plans');
  await checkTableContents('webhook_logs');
  
  // Sprawdź status firmy i subskrypcji - podaj ID firmy, która dokonała płatności
  const companyId = 'daf867f8-4139-4933-916d-e117002cb5a2'; // Zmień na właściwe ID firmy
  console.log('\n=== STATUS FIRMY ===');
  await checkCompanyStatus(companyId);
  
  console.log('\n=== SUBSKRYPCJA FIRMY ===');
  await checkCompanySubscription(companyId);
  
  console.log('\n=== LOGI WEBHOOK ===');
  await checkWebhookLogs();
  
  console.log('\n🏁 Sprawdzanie zakończone');
}

// Uruchom sprawdzanie
checkDatabase().catch(console.error); 