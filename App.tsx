import './stream';
import './polyfills';

// Importuję naszą łatkę na problem z useInsertionEffect oraz AutoWrapText
import InsertionEffectPatch, { patchConsoleLog, AutoWrapText } from './components/TextNodePatch';

import React, { useState, useEffect } from 'react';
import { StyleSheet, View, ActivityIndicator, Alert, Text, StatusBar, Platform } from 'react-native';
import { supabase } from './services/supabaseClient';
import { Session } from '@supabase/supabase-js';
import { DashboardWrapper } from './components/DashboardWrapper';
import Login from './components/Login';
import AccountTypeSelection from './components/AccountTypeSelection';
import CompanyRegistration from './components/CompanyRegistration';
import EmployeeRegistration from './components/EmployeeRegistration';
import { ErrorBoundary } from './components/ErrorBoundary';
import { LogBox } from 'react-native';
import { setLocale } from './store/settingsStore';
import { i18n } from './utils/localization';

// Ignore specific warnings - tutaj dodajemy ignorowanie błędów związanych z tekstem
LogBox.ignoreLogs([
  'Unexpected text node', 
  'Text strings must be rendered within a <Text> component',
  'useInsertionEffect must not schedule updates'
]);

// Włączamy patcha dla konsoli przy starcie aplikacji
patchConsoleLog();

// App color scheme
const COLORS = {
  primary: '#1e6edf', // Exact match for the top bar blue
  background: '#F5F6FA',
  quickBar: '#f8fafc' // Exact match for the quick bar
};

// Calculate status bar height based on platform
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;
// Use an estimated height for the bottom safe area on iOS
const BOTTOM_SAFE_AREA_HEIGHT = Platform.OS === 'ios' ? 34 : 0;

type ScreenType = 'login' | 'register' | 'dashboard' | 'accountTypeSelection' | 'companyRegistration' | 'employeeRegistration';
type AccountType = 'company' | 'employee' | null;

export default function App() {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentScreen, setCurrentScreen] = useState<ScreenType>('login');
  const [accountType, setAccountType] = useState<AccountType>(null);

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session: userSession } }) => {
      setSession(userSession);
      if (userSession) setCurrentScreen('dashboard');
      setLoading(false);
    });

    supabase.auth.onAuthStateChange((_event: any, authSession: Session | null) => {
      setSession(authSession);
      if (authSession) setCurrentScreen('dashboard');
    });
  }, []);

  // TODO: Add deep linking handler later
  // Currently disabled due to configuration issues

  useEffect(() => {
    const loadUserSettings = async () => {
      try {
        const { data: settings } = await supabase
          .from('user_settings')
          .select('preferred_language')
          .single();

        if (settings?.preferred_language) {
          setLocale(settings.preferred_language);
        }
      } catch (error) {
        console.error('Error loading user settings:', error);
      }
    };

    loadUserSettings();
  }, []);

  const handleLogout = async () => {
    await supabase.auth.signOut();
    setCurrentScreen('login');
    setSession(null);
  };

  const handleLoginSuccess = () => {
    setCurrentScreen('dashboard');
  };

  const handleRegisterPress = () => {
    setCurrentScreen('accountTypeSelection');
  };

  const handleBackToLogin = () => {
    setCurrentScreen('login');
  };

  const handleAccountTypeSelect = (type: AccountType) => {
    setAccountType(type);
    setCurrentScreen(type === 'company' ? 'companyRegistration' : 'employeeRegistration');
  };

  const handleForgotPassword = () => {
    console.log('Forgot password clicked');
  };

  const renderAuthScreen = (content: React.ReactNode) => {
    return (
      <View style={styles.rootContainer}>
        {/* Top safe area - blue color */}
        <View style={[styles.topSafeArea, { height: STATUSBAR_HEIGHT }]} />
        
        {/* Main content area */}
        <View style={styles.screenContainer}>
          {content}
        </View>
        
        {/* Bottom safe area - light color */}
        <View style={[styles.bottomSafeArea, { height: BOTTOM_SAFE_AREA_HEIGHT }]} />
      </View>
    );
  };

  const renderContent = () => {
    if (loading) {
      return renderAuthScreen(
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
        </View>
      );
    }

    if (session && currentScreen === 'dashboard') {
      return <DashboardWrapper session={session} onBack={handleLogout} />;
    }

    switch (currentScreen) {
      case 'login':
        return renderAuthScreen(
          <Login
            onLoginSuccess={handleLoginSuccess}
            onRegisterPress={handleRegisterPress}
            onForgotPassword={handleForgotPassword}
          />
        );
      case 'accountTypeSelection':
        return renderAuthScreen(
          <AccountTypeSelection 
            onSelectType={handleAccountTypeSelect} 
            onBack={handleBackToLogin} 
          />
        );
      case 'companyRegistration':
        return renderAuthScreen(
          <CompanyRegistration onBack={handleBackToLogin} />
        );
      case 'employeeRegistration':
        return renderAuthScreen(
          <EmployeeRegistration onBack={handleBackToLogin} />
        );
      default:
        return <Text>Error: Unknown screen</Text>;
    }
  };

  // Wrap everything in an ErrorBoundary to catch unexpected errors
  return (
    <View style={{ flex: 1 }}>
      {/* Używamy naszej zaktualizowanej łatki na problem z renderowaniem tekstu i useInsertionEffect */}
      <InsertionEffectPatch />
      
      <StatusBar
        backgroundColor={COLORS.primary}
        barStyle="light-content" 
      />
      <ErrorBoundary>
        {renderContent()}
      </ErrorBoundary>
    </View>
  );
}

const styles = StyleSheet.create({
  rootContainer: {
    flex: 1,
  },
  topSafeArea: {
    backgroundColor: COLORS.primary,
    width: '100%',
  },
  bottomSafeArea: {
    backgroundColor: COLORS.quickBar,
    width: '100%',
  },
  screenContainer: {
    flex: 1,
    width: '100%',
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
