-- Ten skrypt aktualizuje typ konta (account_type) w tabeli companies na podstawie subskrypcji
-- Wykonaj ten skrypt w konsoli SQL Supabase

-- Najpierw wyświetl bieżące dane
SELECT c.id, c.name, c.account_type, c.verification_code_limit, 
       cs.id as subscription_id, sp.name as plan_name
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE cs.status = 'active'
ORDER BY c.created_at DESC;

-- Aktualizuj typ konta na podstawie planu subskrypcji
DO $$
DECLARE
    company_rec RECORD;
BEGIN
    FOR company_rec IN 
        SELECT c.id as company_id, sp.name as plan_name,
               CASE
                   WHEN sp.name LIKE '%Basic%' THEN 5
                   WHEN sp.name LIKE '%Pro%' THEN 20
                   WHEN sp.name LIKE '%Business%' THEN 999999
                   ELSE 5
               END as code_limit
        FROM companies c
        JOIN company_subscriptions cs ON c.id = cs.company_id
        JOIN subscription_plans sp ON cs.plan_id = sp.id
        WHERE cs.status = 'active' AND c.account_type = 'premium'
    LOOP
        -- Aktualizuj typ konta i limit kodów weryfikacyjnych
        UPDATE companies
        SET account_type = company_rec.plan_name,
            verification_code_limit = company_rec.code_limit
        WHERE id = company_rec.company_id;
        
        RAISE NOTICE 'Zaktualizowano firmę % na plan % z limitem %', 
            company_rec.company_id, company_rec.plan_name, company_rec.code_limit;
    END LOOP;
END $$;

-- Sprawdź wyniki po aktualizacji
SELECT c.id, c.name, c.account_type, c.verification_code_limit, 
       cs.id as subscription_id, sp.name as plan_name
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE cs.status = 'active'
ORDER BY c.created_at DESC; 