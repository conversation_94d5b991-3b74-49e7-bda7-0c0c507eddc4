-- Dodanie kolumn związanych z anulowaniem subskrypcji
-- Te kolumny są potrzebne do prawidłowego działania webhook'ów Stripe

-- Dodaj kolumny canceled_at i cancel_at do tabeli company_subscriptions
ALTER TABLE company_subscriptions 
ADD COLUMN IF NOT EXISTS canceled_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS cancel_at TIMESTAMPTZ;

-- Dodaj komentarze do kolumn
COMMENT ON COLUMN company_subscriptions.canceled_at IS 'Data faktycznego anulowania subskrypcji';
COMMENT ON COLUMN company_subscriptions.cancel_at IS 'Data zaplanowanego anulowania subskrypcji (gdy cancel_at_period_end = true)';

-- Sprawdź czy kolumny zostały dodane
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'company_subscriptions' 
  AND column_name IN ('canceled_at', 'cancel_at')
ORDER BY column_name;
