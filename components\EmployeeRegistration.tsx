import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, Platform, ScrollView } from 'react-native';
import { supabase } from '../services/supabaseClient';
import { Ionicons } from '@expo/vector-icons';
import PrismIcon from './PrismIcon';

interface EmployeeRegistrationProps {
  onBack: () => void;
}

const EmployeeRegistration = ({ onBack }: EmployeeRegistrationProps) => {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    if (!fullName.trim()) {
      Alert.alert('Error', 'Please enter your full name');
      return false;
    }
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter email address');
      return false;
    }

    // Walidacja formatu email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      Alert.alert('Error', 'Please enter a valid email address');
      return false;
    }
    if (!verificationCode.trim()) {
      Alert.alert('Error', 'Please enter verification code');
      return false;
    }
    if (!password) {
      Alert.alert('Error', 'Please enter password');
      return false;
    }
    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return false;
    }
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    return true;
  };

  const verifyCode = async () => {
    try {
      console.log('Verifying code:', verificationCode);

      // First, get the verification code
      const { data: codeData, error: codeError } = await supabase
        .from('verification_codes')
        .select('*')
        .eq('code', verificationCode.toUpperCase())
        .eq('used', false)
        .single();

      console.log('Verification code data:', codeData);

      if (codeError) {
        console.error('Verification code error:', codeError);
        Alert.alert('Error', 'Invalid or expired verification code');
        return null;
      }

      if (!codeData) {
        Alert.alert('Error', 'Verification code not found');
        return null;
      }

      // Then, get the company details first
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('id, name')
        .eq('id', codeData.company_id)
        .single();

      console.log('Company data:', companyData);

      if (companyError) {
        console.error('Company fetch error:', companyError);
        Alert.alert('Error', 'Could not verify company information');
        return null;
      }

      if (!companyData) {
        console.error('No company found for ID:', codeData.company_id);
        Alert.alert('Error', 'Company not found');
        return null;
      }

      // Check if the verification code can be used (respects subscription limits)
      const { data: canUseResult, error: canUseError } = await supabase.rpc(
        'can_use_verification_code',
        {
          p_code: verificationCode.toUpperCase(),
          p_company_id: codeData.company_id
        }
      );

      if (canUseError) {
        console.error('Error checking code usage:', canUseError);
        Alert.alert('Error', 'Could not verify code availability');
        return null;
      }

      if (!canUseResult) {
        // Zapytaj użytkownika czy chce utworzyć konto niezależne
        const createIndependent = await new Promise((resolve) => {
          if (Platform.OS === 'web') {
            const result = confirm(
              `Firma "${companyData.name}" osiągnęła limit aktywnych pracowników dla swojego planu subskrypcji.\n\n` +
              'Czy chcesz utworzyć niezależne konto pracownika?\n\n' +
              '• Będziesz miał ograniczone funkcje\n' +
              '• Pole kodu weryfikacyjnego zostanie wyczyszczone\n' +
              '• Możesz być później przypisany do firmy z aktywną subskrypcją\n\n' +
              'Kliknij OK aby utworzyć niezależne konto, lub Anuluj aby przerwać rejestrację.'
            );
            resolve(result);
          } else {
            Alert.alert(
              'Limit osiągnięty',
              `Firma "${companyData.name}" osiągnęła limit aktywnych pracowników dla swojego planu subskrypcji.\n\nCzy chcesz utworzyć niezależne konto pracownika?\n\n• Będziesz miał ograniczone funkcje\n• Pole kodu zostanie wyczyszczone\n• Możesz być później przypisany do firmy`,
              [
                { text: 'Anuluj', style: 'cancel', onPress: () => resolve(false) },
                { text: 'Utwórz niezależne konto', onPress: () => resolve(true) }
              ]
            );
          }
        });

        if (!createIndependent) {
          return null;
        }

        // Zwróć specjalny obiekt oznaczający niezależne konto
        return {
          companyId: codeData.company_id,
          companyName: companyData.name,
          isIndependent: true
        };
      }



      return {
        companyId: companyData.id,
        companyName: companyData.name
      };
    } catch (error) {
      console.error('Exception in verifyCode:', error);
      Alert.alert('Error', 'An unexpected error occurred while verifying the code');
      return null;
    }
  };

  const signInWithEmail = async () => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Sign in error:', error);
      throw error;
    }

    return data;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      console.log('Starting registration process...');
      
      // 1. Verify the code first
      const companyData = await verifyCode();
      if (!companyData) {
        setIsLoading(false);
        return;
      }

      // 2. Register user
      console.log('Attempting to register user with email:', email);
      console.log('Email length:', email.length);
      console.log('Email trimmed:', email.trim());
      console.log('Is independent:', companyData.isIndependent);

      const signUpData = {
        email: email.trim().toLowerCase(), // Normalizuj email
        password,
        options: {
          data: {
            full_name: fullName,
            user_type: companyData.isIndependent ? 'independent_employee' : 'employee',
            company_id: companyData.isIndependent ? null : companyData.companyId,
          },
        },
      };

      console.log('SignUp data:', signUpData);

      const { data: authData, error: authError } = await supabase.auth.signUp(signUpData);

      if (authError) {
        console.error('Auth error:', authError);

        // Obsługa specjalnego przypadku gdy użytkownik już istnieje
        if (authError.message.includes('User already registered') || authError.message.includes('already registered')) {
          Alert.alert(
            'Konto już istnieje',
            'Użytkownik z tym adresem email już istnieje w systemie. Czy chcesz się zalogować zamiast rejestrować?',
            [
              { text: 'Anuluj', style: 'cancel' },
              {
                text: 'Przejdź do logowania',
                onPress: () => {
                  // Wyczyść formularz i wróć do ekranu logowania
                  setEmail('');
                  setPassword('');
                  setConfirmPassword('');
                  setFullName('');
                  setVerificationCode('');
                  onBack();
                }
              }
            ]
          );
        } else {
          Alert.alert('Błąd rejestracji', authError.message);
        }
        return;
      }

      if (!authData.user) {
        console.error('No user data received');
        Alert.alert('Error', 'Failed to create user account');
        return;
      }

      console.log('User created successfully:', authData.user.id);

      // 3. Mark verification code as used (tylko dla normalnych kont, nie dla niezależnych)
      if (!companyData.isIndependent) {
        const { error: codeError } = await supabase
          .from('verification_codes')
          .update({
            used: true,
            used_by: authData.user.id,
            used_at: new Date().toISOString()
          })
          .eq('code', verificationCode.toUpperCase())
          .select();

        if (codeError) {
          console.error('Error updating verification code:', codeError);
          Alert.alert('Error', 'Failed to update verification code status');
          return;
        }
      }

      // 4. Create employee record
      try {
        console.log('Creating employee record with data:', {
          id: authData.user.id,
          company_id: companyData.companyId,
          full_name: fullName,
          isIndependent: companyData.isIndependent
        });

        const employeeRecord = {
          id: authData.user.id,
          full_name: fullName,
          status: 'active',
          subscription_status: companyData.isIndependent ? 'SUBSCRIPTION_EXPIRED' : 'ACTIVE'
        };

        // Dodaj company_id i verification_code tylko jeśli nie jest to niezależne konto
        if (!companyData.isIndependent) {
          employeeRecord.company_id = companyData.companyId;
          employeeRecord.verification_code = verificationCode.toUpperCase();
          console.log('Adding company_id and verification_code for regular employee');
        } else {
          console.log('Creating independent employee - no company_id or verification_code');
        }

        console.log('Final employee record to insert:', employeeRecord);

        const { data: employeeData, error: employeeError } = await supabase
          .from('employees')
          .insert([employeeRecord])
          .select();

        if (employeeError) {
          console.error('Error creating employee record:', employeeError);
          console.error('Employee error details:', JSON.stringify(employeeError, null, 2));
          Alert.alert('Error', 'Failed to create employee record. Error: ' + employeeError.message);
          return;
        }

        console.log('Employee record created successfully:', employeeData);

        // 5. Update company employee count (tylko dla pracowników przypisanych do firmy)
        if (!companyData.isIndependent) {
          const { error: countError } = await supabase.rpc('increment_employee_count', {
            company_id: companyData.companyId
          });

          if (countError) {
            console.error('Error updating employee count:', countError);
            Alert.alert('Warning', 'Employee record created but failed to update company employee count');
          }
        }

      } catch (error) {
        console.error('Exception in employee creation:', error);
        Alert.alert('Error', 'An unexpected error occurred while creating employee record');
        return;
      }

      // 6. Sign in the user automatically
      try {
        await signInWithEmail();
        console.log('User signed in successfully');

        const successMessage = companyData.isIndependent
          ? `Twoje niezależne konto zostało utworzone. Masz ograniczone funkcje do czasu przypisania do firmy z aktywną subskrypcją.\n\nPole kodu weryfikacyjnego zostanie wyczyszczone.`
          : 'Twoje konto zostało utworzone i zostałeś zalogowany.';

        Alert.alert(
          'Sukces',
          successMessage,
          [{
            text: 'OK',
            onPress: () => {
              // Wyczyść pole kodu weryfikacyjnego dla niezależnych kont
              if (companyData.isIndependent) {
                setVerificationCode('');
              }
              onBack();
            }
          }]
        );
      } catch (signInError) {
        console.error('Error signing in:', signInError);
        Alert.alert(
          'Account Created',
          'Your account has been created successfully, but we could not sign you in automatically. Please try signing in manually.',
          [{
            text: 'OK',
            onPress: () => {
              // Wyczyść pole kodu weryfikacyjnego dla niezależnych kont
              if (companyData.isIndependent) {
                setVerificationCode('');
              }
              onBack();
            }
          }]
        );
      }

    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Error', 'An unexpected error occurred during registration');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView 
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.logoContainer}>
        <PrismIcon />
      </View>

      <View style={styles.formWrapper}>
        <Text style={styles.title}>Join Your Company</Text>
        <Text style={styles.subtitle}>Create your employee account using the verification code from your employer</Text>

        <View style={styles.form}>
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={styles.input}
            value={fullName}
            onChangeText={setFullName}
            placeholder="Enter your full name"
            editable={!isLoading}
          />

          <Text style={styles.label}>Email Address</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
            editable={!isLoading}
          />

          <Text style={styles.label}>Verification Code</Text>
          <TextInput
            style={styles.input}
            value={verificationCode}
            onChangeText={setVerificationCode}
            placeholder="Enter company verification code"
            autoCapitalize="characters"
            editable={!isLoading}
          />

          <Text style={styles.label}>Password</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter password"
            secureTextEntry
            editable={!isLoading}
          />

          <Text style={styles.label}>Confirm Password</Text>
          <TextInput
            style={styles.input}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            placeholder="Confirm password"
            secureTextEntry
            editable={!isLoading}
          />

          <TouchableOpacity 
            style={[styles.registerButton, isLoading && styles.registerButtonDisabled]}
            onPress={handleRegister}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.registerButtonText}>Create Account</Text>
            )}
          </TouchableOpacity>
          
          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <TouchableOpacity onPress={onBack}>
              <Text style={styles.signInLink}>Sign in</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E6EDF',
  },
  contentContainer: {
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 10 : 20,
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    marginBottom: 32,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  label: {
    fontSize: 14,
    color: 'white',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 0,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  registerButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  registerButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  registerButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'center',
  },
  footerText: {
    color: 'white',
    fontSize: 16,
  },
  signInLink: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default EmployeeRegistration; 