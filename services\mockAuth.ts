import { AuthResponse, User, Session, AuthTokenResponse } from '@supabase/supabase-js';

// Symulowana odpowiedź uwierzytelniania
export const mockAuth = {
  signInWithPassword: async (credentials: { email: string; password: string }): Promise<AuthResponse> => {
    console.log('Mock auth: Logging in with', credentials.email);
    
    // Zawsze udane logowanie dla testów
    const mockUser: User = {
      id: 'mock-user-id',
      app_metadata: {},
      user_metadata: { 
        full_name: 'Test User',
        type: 'company' // lub 'employee' w zależności od potrzeb testowych
      },
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      email: credentials.email,
      role: 'authenticated',
      updated_at: new Date().toISOString()
    };

    const mockSession: Session = {
      access_token: 'mock-access-token',
      expires_at: Math.floor(Date.now() / 1000) + 3600,
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
      user: mockUser
    };

    return {
      data: { session: mockSession, user: mockUser },
      error: null
    };
  },

  getSession: async (): Promise<AuthTokenResponse> => {
    console.log('Mock auth: Getting session');
    
    // Symuluj posiadanie aktywnej sesji
    const mockUser: User = {
      id: 'mock-user-id',
      app_metadata: {},
      user_metadata: { 
        full_name: 'Test User',
        type: 'company'
      },
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      email: '<EMAIL>',
      role: 'authenticated',
      updated_at: new Date().toISOString()
    };

    const mockSession: Session = {
      access_token: 'mock-access-token',
      expires_at: Math.floor(Date.now() / 1000) + 3600,
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
      user: mockUser
    };

    return {
      data: { session: mockSession, user: mockUser },
      error: null
    };
  },

  getUser: async () => {
    console.log('Mock auth: Getting user');
    
    const mockUser: User = {
      id: 'mock-user-id',
      app_metadata: {},
      user_metadata: { 
        full_name: 'Test User',
        type: 'company'
      },
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      email: '<EMAIL>',
      role: 'authenticated',
      updated_at: new Date().toISOString()
    };
    
    return {
      data: { user: mockUser },
      error: null
    };
  },

  onAuthStateChange: (callback: (event: string, session: Session | null) => void) => {
    console.log('Mock auth: Setting up auth state change listener');
    
    // Wywołaj callback z aktywną sesją
    const mockUser: User = {
      id: 'mock-user-id',
      app_metadata: {},
      user_metadata: { 
        full_name: 'Test User',
        type: 'company'
      },
      aud: 'authenticated',
      created_at: new Date().toISOString(),
      email: '<EMAIL>',
      role: 'authenticated',
      updated_at: new Date().toISOString()
    };

    const mockSession: Session = {
      access_token: 'mock-access-token',
      expires_at: Math.floor(Date.now() / 1000) + 3600,
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
      user: mockUser
    };

    // Symuluj event SIGNED_IN
    setTimeout(() => {
      callback('SIGNED_IN', mockSession);
    }, 100);

    // Zwróć pustą subskrypcję
    return {
      data: {
        subscription: {
          unsubscribe: () => {
            console.log('Mock auth: Unsubscribing from auth state changes');
          }
        }
      }
    };
  },

  signOut: async () => {
    console.log('Mock auth: Signing out');
    return { error: null };
  }
}; 