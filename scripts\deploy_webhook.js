// Script to deploy the webhook function
const { execSync } = require('child_process');
const path = require('path');

async function main() {
  try {
    console.log('Deploying webhook function...');
    
    // Set the current working directory to the project root
    const projectRoot = path.join(__dirname, '..');
    process.chdir(projectRoot);
    
    // Deploy the webhook function
    console.log('Deploying smooth-handler function...');
    try {
      execSync('npx supabase functions deploy smooth-handler --no-verify-jwt', { stdio: 'inherit' });
      console.log('smooth-handler function deployed successfully');
    } catch (err) {
      console.error('Error deploying smooth-handler function:', err);
      console.log('\nYou may need to run the following commands manually:');
      console.log('1. supabase login');
      console.log('2. npx supabase functions deploy smooth-handler --no-verify-jwt');
    }
    
    console.log('\nDone!');
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

main(); 