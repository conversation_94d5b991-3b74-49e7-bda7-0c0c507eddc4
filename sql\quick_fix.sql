-- SZYBKA NAPRAWA - wykonaj w Supabase SQL Editor

-- <PERSON><PERSON><PERSON> istniejące funkcje aby uniknąć konfliktów
DROP FUNCTION IF EXISTS activate_employee(UUID, UUID);
DROP FUNCTION IF EXISTS deactivate_employee(UUID, UUID);
DROP FUNCTION IF EXISTS deactivate_company_employees_on_expiry(UUID);
DROP FUNCTION IF EXISTS reactivate_company_employees(UUID);

-- 1. <PERSON><PERSON><PERSON> kolumny jeśli nie istnieją i zmodyfikuj company_id
ALTER TABLE employees
ADD COLUMN IF NOT EXISTS subscription_status TEXT NOT NULL DEFAULT 'ACTIVE'
  CHECK (subscription_status IN ('ACTIVE', 'SUBSCRIPTION_EXPIRED')),
ADD COLUMN IF NOT EXISTS last_status_change TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS verification_code TEXT,
ADD COLUMN IF NOT EXISTS last_manual_status_change TIMESTAMPTZ;

-- Pozwól na NULL w company_id dla niezależnych pracowników
ALTER TABLE employees
ALTER COLUMN company_id DROP NOT NULL;

-- 2. Zaktualizuj istniejących pracowników
UPDATE employees
SET subscription_status = 'ACTIVE',
    last_status_change = NOW(),
    last_manual_status_change = NULL  -- NULL oznacza brak ręcznych zmian
WHERE subscription_status IS NULL;

-- 3. Funkcja do aktywacji pracownika (z ręcznym śledzeniem)
CREATE OR REPLACE FUNCTION activate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_subscription_limit INTEGER;
  v_active_count INTEGER;
  v_last_manual_change TIMESTAMPTZ;
  v_has_active_subscription BOOLEAN;
  v_employee_name TEXT;
BEGIN
  -- Pobierz imię pracownika dla komunikatów
  SELECT full_name INTO v_employee_name
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź czy firma ma aktywną subskrypcję premium
  SELECT EXISTS(
    SELECT 1 FROM company_subscriptions
    WHERE company_id = p_company_id
    AND status = 'active'
    AND (current_period_end IS NULL OR current_period_end > NOW())
  ) INTO v_has_active_subscription;

  -- OGRANICZENIE CZASOWE TYLKO DLA DARMOWYCH KONT
  -- Jeśli firma NIE ma aktywnej subskrypcji premium, sprawdź ograniczenie czasowe
  IF NOT v_has_active_subscription THEN
    SELECT last_manual_status_change INTO v_last_manual_change
    FROM employees
    WHERE id = p_employee_id;

    IF v_last_manual_change IS NOT NULL AND (NOW() - v_last_manual_change) < INTERVAL '7 days' THEN
      RETURN jsonb_build_object(
        'success', false,
        'message', 'Nie można ręcznie zmienić statusu pracownika "' || COALESCE(v_employee_name, 'Nieznany') || '" częściej niż raz na tydzień. Ostatnia zmiana: ' || to_char(v_last_manual_change, 'DD.MM.YYYY HH24:MI') || '. Wykup Premium aby usunąć to ograniczenie.',
        'show_upgrade', true,
        'alert_type', 'time_limit'
      );
    END IF;
  END IF;

  -- Pobierz limit aktywnych pracowników
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz aktualną liczbę aktywnych pracowników
  SELECT COUNT(*) INTO v_active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Jeśli firma ma aktywną subskrypcję premium, pozwól na aktywację
  IF v_has_active_subscription THEN
    UPDATE employees
    SET subscription_status = 'ACTIVE',
        last_status_change = NOW(),
        last_manual_status_change = NOW()
    WHERE id = p_employee_id
    AND company_id = p_company_id;

    RETURN jsonb_build_object(
      'success', true,
      'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został aktywowany',
      'show_upgrade', false,
      'alert_type', 'success'
    );
  END IF;

  -- Sprawdź, czy nie przekroczono limitu dla darmowego konta
  IF v_active_count >= v_subscription_limit THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Osiągnięto limit aktywnych pracowników (' || v_active_count || '/' || v_subscription_limit || ') dla darmowego planu. Nie można aktywować pracownika "' || COALESCE(v_employee_name, 'Nieznany') || '".',
      'show_upgrade', true,
      'alert_type', 'employee_limit',
      'current_count', v_active_count,
      'limit', v_subscription_limit
    );
  END IF;

  -- Aktualizuj status pracownika
  UPDATE employees
  SET subscription_status = 'ACTIVE',
      last_status_change = NOW(),
      last_manual_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został aktywowany',
    'show_upgrade', false,
    'alert_type', 'success'
  );
END;
$$ LANGUAGE plpgsql;

-- 4. Funkcja do dezaktywacji pracownika (z ręcznym śledzeniem)
CREATE OR REPLACE FUNCTION deactivate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_last_manual_change TIMESTAMPTZ;
  v_employee_name TEXT;
  v_has_active_subscription BOOLEAN;
BEGIN
  -- Pobierz imię pracownika dla komunikatów
  SELECT full_name INTO v_employee_name
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź czy firma ma aktywną subskrypcję premium
  SELECT EXISTS(
    SELECT 1 FROM company_subscriptions
    WHERE company_id = p_company_id
    AND status = 'active'
    AND (current_period_end IS NULL OR current_period_end > NOW())
  ) INTO v_has_active_subscription;

  -- OGRANICZENIE CZASOWE TYLKO DLA DARMOWYCH KONT
  -- Jeśli firma NIE ma aktywnej subskrypcji premium, sprawdź ograniczenie czasowe
  IF NOT v_has_active_subscription THEN
    SELECT last_manual_status_change INTO v_last_manual_change
    FROM employees
    WHERE id = p_employee_id;

    IF v_last_manual_change IS NOT NULL AND (NOW() - v_last_manual_change) < INTERVAL '7 days' THEN
      RETURN jsonb_build_object(
        'success', false,
        'message', 'Nie można ręcznie zmienić statusu pracownika "' || COALESCE(v_employee_name, 'Nieznany') || '" częściej niż raz na tydzień. Ostatnia zmiana: ' || to_char(v_last_manual_change, 'DD.MM.YYYY HH24:MI') || '. Wykup Premium aby usunąć to ograniczenie.',
        'show_upgrade', true,
        'alert_type', 'time_limit'
      );
    END IF;
  END IF;

  -- Dezaktywuj pracownika
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW(),
      last_manual_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został dezaktywowany',
    'show_upgrade', false,
    'alert_type', 'success'
  );
END;
$$ LANGUAGE plpgsql;

-- 5. Funkcje automatyczne (bez ręcznego śledzenia)
CREATE OR REPLACE FUNCTION deactivate_company_employees_on_expiry(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Dezaktywuj pracowników - NIE aktualizuj last_manual_status_change
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW()
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE'
  AND NOT EXISTS (
    SELECT 1 FROM company_subscriptions
    WHERE company_id = p_company_id
    AND status = 'active'
    AND current_period_end > NOW()
  );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION reactivate_company_employees(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Reaktywuj pracowników - NIE aktualizuj last_manual_status_change
  UPDATE employees
  SET subscription_status = 'ACTIVE',
      last_status_change = NOW()
  WHERE company_id = p_company_id
  AND subscription_status = 'SUBSCRIPTION_EXPIRED';
END;
$$ LANGUAGE plpgsql;

-- 6. Funkcja do sprawdzania czy kod może być użyty
CREATE OR REPLACE FUNCTION can_use_verification_code(
  p_code TEXT,
  p_company_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  code_exists BOOLEAN;
  active_employees_count INTEGER;
  employee_limit INTEGER;
BEGIN
  -- Sprawdź czy kod istnieje i nie jest użyty
  SELECT EXISTS(
    SELECT 1 FROM verification_codes
    WHERE code = p_code
    AND company_id = p_company_id
    AND used = false
  ) INTO code_exists;

  IF NOT code_exists THEN
    RETURN FALSE;
  END IF;

  -- Pobierz limit pracowników i aktualną liczbę
  SELECT verification_code_limit INTO employee_limit
  FROM companies WHERE id = p_company_id;

  SELECT COUNT(*) INTO active_employees_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Sprawdź limit
  RETURN active_employees_count < employee_limit;
END;
$$ LANGUAGE plpgsql;
