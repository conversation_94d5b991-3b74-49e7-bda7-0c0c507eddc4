import React from 'react';
import { View, Text } from 'react-native';
import PurchaseDetails from './PurchaseDetails';

interface PurchaseDetailsWrapperProps {
  purchase_id?: string;
  purchaseId?: string;
  onBack: () => void;
  onMenuPress?: () => void;
  onLogoPress?: () => void;
}

/**
 * Wrapper component for PurchaseDetails that serves as a compatibility layer
 * to resolve import issues in the Dashboard component.
 */
const PurchaseDetailsWrapper: React.FC<PurchaseDetailsWrapperProps> = (props) => {
  // Mapuj właściwości - przyjmij purchase_id lub purchaseId, przeka<PERSON>jąc je jako purchaseId
  const purchaseId = props.purchaseId || props.purchase_id;
  
  // Jeśli brak purchaseId, wyświetl informację o błędzie
  if (!purchaseId) {
    console.error('PurchaseDetailsWrapper: Brak wymaganego parametru purchaseId lub purchase_id');
    return (
      <View style={{ padding: 20 }}>
        <Text style={{ color: 'red' }}>
          Błąd: Brak wymaganego identyfikatora zakupu
        </Text>
      </View>
    );
  }
  
  return (
    <PurchaseDetails 
      purchaseId={purchaseId} 
      onBack={props.onBack}
      onMenuPress={props.onMenuPress}
      onLogoPress={props.onLogoPress}
    />
  );
};

export default PurchaseDetailsWrapper; 