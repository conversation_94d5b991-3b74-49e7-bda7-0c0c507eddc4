const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function convertCurrencyToUSD() {
  try {
    console.log('=== Converting Currency from PLN to USD ===\n');
    
    // Kurs wymiany PLN -> USD (przybliżony: 1 USD = 4 PLN)
    const PLN_TO_USD_RATE = 0.25; // 1 PLN = 0.25 USD
    
    // Pobierz wszystkie plany
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    if (plansError) {
      console.error('Error fetching plans:', plansError);
      return;
    }

    console.log('Current plans (PLN):');
    plans.forEach(plan => {
      console.log(`${plan.name}: ${plan.price/100} PLN`);
    });

    // Konwertuj ceny na USD
    console.log('\n=== Converting Prices to USD ===\n');
    
    const priceUpdates = [];
    
    for (const plan of plans) {
      const currentPricePLN = plan.price / 100; // Cena w PLN
      const newPriceUSD = Math.round(currentPricePLN * PLN_TO_USD_RATE * 100); // Cena w centach USD
      
      console.log(`${plan.name}:`);
      console.log(`  Current: ${currentPricePLN} PLN`);
      console.log(`  New: ${newPriceUSD/100} USD`);
      
      priceUpdates.push({
        id: plan.id,
        name: plan.name,
        oldPrice: plan.price,
        newPrice: newPriceUSD
      });
    }

    // Aktualizuj ceny w bazie danych
    console.log('\n=== Updating Prices in Database ===\n');
    
    for (const update of priceUpdates) {
      const { error: updateError } = await supabase
        .from('subscription_plans')
        .update({ price: update.newPrice })
        .eq('id', update.id);

      if (updateError) {
        console.log(`❌ Error updating ${update.name}: ${updateError.message}`);
      } else {
        console.log(`✅ Updated ${update.name}: ${update.oldPrice/100} PLN → ${update.newPrice/100} USD`);
      }
    }

    // Aktualizuj domyślną walutę w payment_history
    console.log('\n=== Updating Default Currency in Payment History ===\n');
    
    const { error: currencyError } = await supabase
      .rpc('execute_sql', {
        sql: "ALTER TABLE payment_history ALTER COLUMN currency SET DEFAULT 'usd'"
      });

    if (currencyError) {
      console.log('Note: Could not update payment_history default currency via RPC');
      console.log('You may need to update this manually in the database');
    } else {
      console.log('✅ Updated payment_history default currency to USD');
    }

    // Sprawdź zaktualizowane ceny
    console.log('\n=== Verification ===\n');
    
    const { data: updatedPlans, error: verifyError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    if (verifyError) {
      console.error('Error verifying updates:', verifyError);
      return;
    }

    console.log('Updated plans (USD):');
    updatedPlans.forEach(plan => {
      console.log(`${plan.name}: $${plan.price/100} USD`);
    });

    // Porównanie oszczędności dla planów rocznych
    console.log('\n=== Yearly Plan Savings (USD) ===\n');
    
    const monthlyPlans = updatedPlans.filter(p => p.billing_period === 'monthly');
    const yearlyPlans = updatedPlans.filter(p => p.billing_period === 'yearly');

    for (const monthlyPlan of monthlyPlans) {
      const yearlyEquivalent = yearlyPlans.find(y => 
        y.name.toLowerCase().includes(monthlyPlan.name.toLowerCase())
      );
      
      if (yearlyEquivalent) {
        const monthlyYearlyCost = monthlyPlan.price * 12;
        const actualYearlyCost = yearlyEquivalent.price;
        const savings = monthlyYearlyCost - actualYearlyCost;
        const savingsPercent = Math.round((savings / monthlyYearlyCost) * 100);
        
        console.log(`${monthlyPlan.name}:`);
        console.log(`  Monthly: $${monthlyPlan.price/100}/month ($${monthlyYearlyCost/100}/year)`);
        console.log(`  Yearly: $${actualYearlyCost/100}/year`);
        console.log(`  Savings: $${savings/100} (${savingsPercent}%)`);
      }
    }

    console.log('\n🎉 Currency conversion completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Update frontend formatPrice functions to use USD');
    console.log('2. Update locale settings to use en-US');
    console.log('3. Update Stripe Price IDs if needed for USD');
    console.log('4. Test the application with new USD pricing');

  } catch (error) {
    console.error('Error in convertCurrencyToUSD:', error);
  }
}

convertCurrencyToUSD();
