{"name": "workflow", "license": "0BSD", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@babel/core": "^7.26.9", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@stripe/stripe-js": "^7.3.1", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-js": "^2.70.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "base64-arraybuffer": "^1.0.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "expo": "^53.0.7", "expo-blur": "~14.1.4", "expo-crypto": "^14.1.4", "expo-dev-client": "~5.1.8", "expo-file-system": "~18.1.9", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-location": "~18.1.5", "expo-permissions": "^14.4.0", "expo-status-bar": "~2.2.3", "https-browserify": "^1.0.0", "i18n-js": "^4.5.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "metro": "^0.82.2", "metro-cache": "^0.82.2", "metro-config": "^0.82.2", "metro-core": "^0.82.2", "metro-react-native-babel-preset": "^0.77.0", "metro-resolver": "^0.82.2", "metro-runtime": "^0.82.2", "metro-source-map": "^0.82.2", "node-libs-browser": "^2.2.1", "node-libs-react-native": "^1.2.1", "process": "^0.11.10", "react": "19.0.0", "react-dom": "19.0.0", "react-error-boundary": "^6.0.0", "react-native": "0.79.2", "react-native-calendars": "^1.1309.1", "react-native-crypto": "^2.2.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-randombytes": "^3.6.1", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tamagui": "^1.125.14", "text-encoding": "^0.7.0", "typescript": "^5.7.3", "url": "^0.11.4", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^4.3.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "^19.1.4", "@types/react-native": "^0.72.8", "metro-react-native-babel-preset": "^0.77.0", "typescript": "^5.3.3"}, "private": true}