import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

interface TimePickerProps {
  time: Date;
  onTimeChange: (time: Date) => void;
  placeholder?: string;
  label?: string;
}

const TimePicker = ({
  time,
  onTimeChange,
  placeholder = 'Wybierz godzinę',
  label,
}: TimePickerProps) => {
  const [isPickerVisible, setPickerVisible] = useState(false);
  const isWeb = Platform.OS === 'web';

  const formatTime = (date: Date) => {
    try {
      return date.toLocaleTimeString('pl-PL', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      console.error('Error formatting time:', error);
      return '00:00';
    }
  };

  const formatTimeForInput = (date: Date) => {
    try {
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    } catch (error) {
      console.error('Error formatting time for input:', error);
      return '00:00';
    }
  };

  const createNewDateWithTime = (originalDate: Date, hours: number, minutes: number) => {
    try {
      const year = originalDate.getFullYear();
      const month = originalDate.getMonth();
      const day = originalDate.getDate();
      
      const newDate = new Date(year, month, day, hours, minutes, 0, 0);
      
      // Sprawdź czy data jest prawidłowa
      if (isNaN(newDate.getTime())) {
        console.error('Invalid date created');
        return originalDate;
      }
      
      return newDate;
    } catch (error) {
      console.error('Error creating new date:', error);
      return originalDate;
    }
  };

  const handleConfirm = (date: Date) => {
    try {
      setPickerVisible(false);
      const newDate = createNewDateWithTime(time, date.getHours(), date.getMinutes());
      onTimeChange(newDate);
    } catch (error) {
      console.error('Error in handleConfirm:', error);
    }
  };

  const handleWebTimeChange = (timeString: string) => {
    try {
      const [hoursStr, minutesStr] = timeString.split(':');
      const hours = parseInt(hoursStr, 10);
      const minutes = parseInt(minutesStr, 10);
      
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        console.error('Invalid time values:', { hours, minutes });
        return;
      }

      const newDate = createNewDateWithTime(time, hours, minutes);
      onTimeChange(newDate);
    } catch (error) {
      console.error('Error in handleWebTimeChange:', error);
    }
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      {isWeb ? (
        <View style={styles.webInputWrapper}>
          <input
            type="time"
            value={formatTimeForInput(time)}
            onChange={(e) => handleWebTimeChange(e.target.value)}
            className="time-input"
            style={{
              width: '100%',
              padding: '10px 32px 10px 12px',
              fontSize: '16px',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
              backgroundColor: '#F9FAFB',
              color: '#1A1A1A',
              outline: 'none',
              appearance: 'none',
              WebkitAppearance: 'none',
              MozAppearance: 'none',
              cursor: 'pointer',
              boxSizing: 'border-box'
            }}
          />
          <style dangerouslySetInnerHTML={{
            __html: `
              .time-input::-webkit-calendar-picker-indicator {
                background-position: right center;
                padding-right: 10px;
                position: absolute;
                right: 0;
                top: 0;
                height: 100%;
                width: 20px;
                opacity: 0;
              }
            `
          }} />
          <Ionicons name="time-outline" size={20} color="#666" style={styles.webIcon} />
        </View>
      ) : (
        <>
          <TouchableOpacity
            style={styles.pickerButton}
            onPress={() => setPickerVisible(true)}
          >
            <Text style={styles.pickerText}>{formatTime(time)}</Text>
            <Ionicons name="time-outline" size={20} color="#666" />
          </TouchableOpacity>

          <DateTimePickerModal
            isVisible={isPickerVisible}
            mode="time"
            onConfirm={handleConfirm}
            onCancel={() => setPickerVisible(false)}
            date={time}
            locale="pl"
            is24Hour={true}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    width: '100%',
  },
  label: {
    fontSize: 14,
    color: '#1A1A1A',
    marginBottom: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  pickerText: {
    fontSize: 16,
    color: '#1A1A1A',
  },
  webInputWrapper: {
    position: 'relative',
    width: '100%',
  },
  webIcon: {
    position: 'absolute',
    right: 12,
    top: '50%',
    transform: [{translateY: -10}],
    pointerEvents: 'none',
  },
});

export default TimePicker; 