import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Switch, Animated, Dimensions, Alert, Platform, Linking } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';
import { TopBar } from './TopBar';
import {
  getSubscriptionPlans,
  getActiveSubscription,
  createCheckoutSession,
  cancelSubscription,
  getPaymentHistory,
  updateSubscriptionAfterPayment,
  SubscriptionPlan
} from '../services/stripeService';
import PaymentSuccess from './PaymentSuccess';

interface SubscriptionManagementProps {
  companyId: string;
  onClose: () => void;
  isModal?: boolean;
  onNavigate?: (menuItem: 'dashboard') => void;
  onMenuPress?: () => void;
  hideTopBar?: boolean;
}

const SubscriptionManagement: React.FC<SubscriptionManagementProps> = ({ 
  companyId, 
  onClose,
  isModal = false,
  onNavigate,
  onMenuPress,
  hideTopBar = false
}) => {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [activeSubscription, setActiveSubscription] = useState<any>(null);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [paymentPlanId, setPaymentPlanId] = useState<string | null>(null);
  const [isYearlyBilling, setIsYearlyBilling] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  
  // Stan dla własnego menu bocznego
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const translateX = React.useRef(new Animated.Value(-300)).current;
  const overlayOpacity = React.useRef(new Animated.Value(0)).current;
  const windowHeight = Dimensions.get('window').height;

  useEffect(() => {
    loadData();
    checkPaymentParams();
  }, []);

  // Obsługa menu bocznego
  useEffect(() => {
    if (isDrawerVisible) {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0.5,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: -300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isDrawerVisible]);

  // Funkcja do obsługi przycisku menu
  const handleLocalMenuPress = () => {
    if (onMenuPress) {
      onMenuPress();
    } else {
      setIsDrawerVisible(true);
    }
  };

  // Funkcja do zamknięcia menu
  const closeDrawer = () => {
    setIsDrawerVisible(false);
  };

  // Funkcja nawigacji do dashboardu
  const navigateToDashboard = () => {
    if (onNavigate) {
      onNavigate('dashboard');
    }
  };

  const checkPaymentParams = () => {
    // Sprawdź, czy jesteśmy na stronie po przekierowaniu z płatności
    if (typeof window !== 'undefined' && window.location && window.location.search) {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const successParam = urlParams.get('payment_success');
        const planIdParam = urlParams.get('planId');

        if (successParam === 'true' && planIdParam) {
          setShowPaymentSuccess(true);
          setPaymentPlanId(planIdParam);

          // Usuń parametry z URL, aby uniknąć ponownego przetworzenia płatności po odświeżeniu
          if (window.history && window.location.pathname) {
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
          }
        }
      } catch (error) {
        console.log('Error checking payment params (likely mobile environment):', error);
      }
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      // Pobierz dostępne plany
      const availablePlans = await getSubscriptionPlans();
      setPlans(availablePlans);

      // Pobierz aktywną subskrypcję
      const subscription = await getActiveSubscription(companyId);
      setActiveSubscription(subscription);

      // Pobierz historię płatności
      const history = await getPaymentHistory(companyId);
      setPaymentHistory(history);

      // Ustaw domyślnie wybrany plan
      if (availablePlans.length > 0 && !subscription) {
        setSelectedPlanId(availablePlans[0].id);
      }
    } catch (error) {
      console.error('Error loading subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async () => {
    if (!selectedPlanId) return;

    try {
      // URL do przekierowania po zakończeniu płatności
      let successUrl = '';
      let cancelUrl = '';

      if (typeof window !== 'undefined' && window.location && window.location.origin) {
        // Web - użyj obecnego URL z parametrami
        successUrl = window.location.origin + window.location.pathname + '?payment_success=true&companyId=' + companyId + '&planId=' + selectedPlanId;
        cancelUrl = window.location.origin + window.location.pathname + '?payment_canceled=true';
      } else {
        // Mobile - użyj custom URL scheme
        successUrl = `workflow://payment_success?companyId=${companyId}&planId=${selectedPlanId}`;
        cancelUrl = `workflow://payment_canceled`;
      }

      // Utwórz sesję płatności
      const checkoutUrl = await createCheckoutSession(
        companyId,
        selectedPlanId,
        successUrl,
        cancelUrl
      );

      if (checkoutUrl) {
        // Przekieruj do strony płatności Stripe
        if (Platform.OS === 'web' && typeof window !== 'undefined' && window.location) {
          // Web - użyj window.location
          window.location.href = checkoutUrl;
        } else {
          // Mobile (iOS/Android) - użyj Linking.openURL
          try {
            await Linking.openURL(checkoutUrl);
          } catch (error) {
            console.error('Error opening checkout URL:', error);
            Alert.alert(
              'Błąd',
              'Nie można otworzyć strony płatności. Spróbuj ponownie.',
              [{ text: 'OK' }]
            );
          }
        }
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
    }
  };

  // Funkcja pomocnicza dla alertów cross-platform
  const showAlert = (title: string, message: string, buttons: any[]) => {
    if (Platform.OS === 'web') {
      // Dla web używamy window.confirm
      const confirmed = window.confirm(`${title}\n\n${message}`);
      if (confirmed) {
        // Znajdź przycisk z onPress i wywołaj go
        const confirmButton = buttons.find(btn => btn.onPress && btn.style === 'destructive');
        if (confirmButton && confirmButton.onPress) {
          confirmButton.onPress();
        }
      }
    } else {
      // Dla mobile używamy Alert
      Alert.alert(title, message, buttons);
    }
  };

  const showSuccessAlert = (title: string, message: string) => {
    if (Platform.OS === 'web') {
      window.alert(`${title}\n\n${message}`);
    } else {
      Alert.alert(title, message, [{ text: 'OK' }]);
    }
  };

  const handleCancel = async () => {
    if (!activeSubscription) return;

    const performCancel = async () => {
      setIsCancelling(true);
      try {
        const success = await cancelSubscription(
          activeSubscription.stripe_subscription_id,
          true
        ); // true = anuluj na koniec okresu

        if (success) {
          // Odśwież dane subskrypcji
          const updatedSubscription = await getActiveSubscription(companyId);
          setActiveSubscription(updatedSubscription);

          showSuccessAlert(
            'Sukces',
            i18n.t('subscriptionCancelledSuccess')
          );
        } else {
          showSuccessAlert(
            'Błąd',
            i18n.t('subscriptionCancelledError')
          );
        }
      } catch (error) {
        console.error('Error canceling subscription:', error);
        showSuccessAlert(
          'Błąd',
          i18n.t('subscriptionCancelledError')
        );
      } finally {
        setIsCancelling(false);
      }
    };

    // Pokaż dialog potwierdzenia
    showAlert(
      'Anuluj subskrypcję',
      i18n.t('confirmCancelSubscription'),
      [
        {
          text: 'Anuluj',
          style: 'cancel',
        },
        {
          text: 'Potwierdź',
          style: 'destructive',
          onPress: performCancel,
        },
      ]
    );
  };

  const handlePaymentSuccessClose = () => {
    setShowPaymentSuccess(false);
    loadData(); // Odśwież dane po udanej płatności
    onClose(); // Zamknij modal po udanej płatności
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatPrice = (price: number) => {
    return '$' + (price / 100).toFixed(2);
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return i18n.t('active');
      case 'canceled': return i18n.t('canceled');
      case 'past_due': return i18n.t('pastDue');
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#10B981';
      case 'canceled': return '#DC2626';
      case 'past_due': return '#F59E0B';
      default: return '#6B7280';
    }
  };

  const renderBillingToggle = () => (
    <View style={styles.billingToggle}>
      <Text style={[styles.billingOption, !isYearlyBilling && styles.billingOptionActive]}>
        {i18n.t('monthly')}
      </Text>
      <Switch
        value={isYearlyBilling}
        onValueChange={setIsYearlyBilling}
        trackColor={{ false: '#CBD5E1', true: '#4F46E5' }}
        thumbColor={isYearlyBilling ? '#fff' : '#fff'}
        style={{ marginHorizontal: 12 }}
      />
      <View>
        <Text style={[styles.billingOption, isYearlyBilling && styles.billingOptionActive]}>
          {i18n.t('yearly')}
        </Text>
        <Text style={styles.savingsText}>{i18n.t('save20Percent')}</Text>
      </View>
    </View>
  );

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isActive = activeSubscription && activeSubscription.plan_id === plan.id;
    const isSelected = selectedPlanId === plan.id;
    const isYearlyPlan = plan.billing_period === 'yearly';

    // Pokaż tylko plany zgodne z wybranym okresem rozliczeniowym
    if ((isYearlyBilling && !isYearlyPlan) || (!isYearlyBilling && isYearlyPlan)) {
      return null;
    }

    return (
      <TouchableOpacity
        key={plan.id}
        style={[
          styles.planCard,
          isActive && styles.activePlanCard,
          isSelected && !isActive && styles.selectedPlanCard
        ]}
        onPress={() => !isActive && setSelectedPlanId(plan.id)}
        disabled={isActive}
      >
        <Text style={styles.planName}>{plan.name}</Text>
        <Text style={styles.planPrice}>
          {formatPrice(plan.price)}
          <Text style={styles.planPeriod}>
            /{isYearlyBilling ? i18n.t('year') : i18n.t('month')}
          </Text>
        </Text>
        <Text style={styles.planDescription}>{plan.description}</Text>
        
        {isActive && (
          <View style={styles.activeBadge}>
            <Text style={styles.activeBadgeText}>{i18n.t('currentPlan')}</Text>
          </View>
        )}
        
        {plan.features && (
          <View style={styles.featuresList}>
            {plan.features.map((feature: string, index: number) => (
              <View key={index} style={styles.featureItem}>
                <Ionicons name="checkmark-circle" size={16} color="#10B981" />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Dodaj przycisk zmiany planu */}
        {activeSubscription && !isActive && !activeSubscription.cancel_at_period_end && (
          <TouchableOpacity
            style={styles.changePlanButton}
            onPress={() => handleSubscribe()}
          >
            <Text style={styles.changePlanButtonText}>{i18n.t('changePlan')}</Text>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };

  const renderPaymentHistory = () => {
    if (paymentHistory.length === 0) return null;

    return (
      <View style={styles.historySection}>
        <Text style={styles.sectionTitleText}>{i18n.t('paymentHistory')}</Text>
        {paymentHistory.map((payment, index) => (
          <View key={index} style={styles.historyItem}>
            <View style={styles.historyItemDetails}>
              <Text style={styles.historyItemDate}>{formatDate(payment.created_at)}</Text>
              <Text style={styles.historyItemAmount}>{formatPrice(payment.amount)}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(payment.status) }]}>
              <Text style={styles.statusText}>{getStatusText(payment.status)}</Text>
            </View>
          </View>
        ))}
      </View>
    );
  };

  // Renderowanie zawartości menu
  const renderDrawerContent = () => (
    <View style={styles.drawerContent}>
      <View style={styles.drawerHeader}>
        <Text style={styles.drawerHeaderText}>{i18n.t('menu')}</Text>
      </View>
      <TouchableOpacity style={styles.menuItem} onPress={navigateToDashboard}>
        <Ionicons name="home-outline" size={24} color="#666" />
        <Text style={styles.menuItemText}>{i18n.t('dashboard')}</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuItem} onPress={closeDrawer}>
        <Ionicons name="close-outline" size={24} color="#666" />
        <Text style={styles.menuItemText}>{i18n.t('close')}</Text>
      </TouchableOpacity>
    </View>
  );

  // Jeśli jesteśmy po udanej płatności, pokaż ekran sukcesu
  if (showPaymentSuccess && paymentPlanId) {
    return (
      <PaymentSuccess 
        companyId={companyId} 
        planId={paymentPlanId} 
        onClose={handlePaymentSuccessClose} 
      />
    );
  }

  const renderActiveSubscription = () => {
    if (!activeSubscription) return null;

    const isActive = activeSubscription.status === 'active';
    const isCanceled = activeSubscription.cancel_at_period_end;
    const endDate = new Date(activeSubscription.current_period_end);
    const startDate = new Date(activeSubscription.current_period_start);
    const formattedEndDate = endDate.toLocaleDateString();
    const formattedStartDate = startDate.toLocaleDateString();
    
    // Znajdź aktualny plan - używamy danych z zagnieżdżonego obiektu plan lub wyszukujemy w tablicy plans
    const currentPlan = activeSubscription.plan || plans.find(plan => plan.id === activeSubscription.plan_id);
    const billingPeriod = currentPlan?.billing_period === 'yearly' ? i18n.t('perYear') : i18n.t('perMonth');
    
    // Sprawdź, czy mamy plan do wyświetlenia
    if (!currentPlan) {
      console.error('Nie znaleziono danych planu dla subskrypcji:', activeSubscription.id);
      return null;
    }
    
    return (
      <View style={styles.activeSubscriptionContainer}>
        <Text style={styles.sectionTitle}>{i18n.t('activeSubscription')}</Text>
        <View style={styles.subscriptionDetails}>
          <View style={styles.subscriptionInfo}>
            <Text style={styles.subscriptionLabel}>{i18n.t('plan')}:</Text>
            <Text style={styles.subscriptionValue}>{currentPlan.name || '-'}</Text>
            
            <Text style={styles.subscriptionLabel}>{i18n.t('price')}:</Text>
            <Text style={styles.subscriptionValue}>
              {currentPlan ? `${formatPrice(currentPlan.price)}${billingPeriod}` : '-'}
            </Text>
            
            <Text style={styles.subscriptionLabel}>{i18n.t('periodStart')}:</Text>
            <Text style={styles.subscriptionValue}>{formattedStartDate}</Text>
            
            <Text style={styles.subscriptionLabel}>{i18n.t('periodEnd')}:</Text>
            <Text style={styles.subscriptionValue}>{formattedEndDate}</Text>
            
            <Text style={[
              styles.statusText,
              { color: isCanceled ? '#DC2626' : '#10B981', marginTop: 8 }
            ]}>
              {isCanceled 
                ? i18n.t('subscriptionCancelledEndDate', { date: formattedEndDate })
                : i18n.t('subscriptionActive')
              }
            </Text>
          </View>
          {isActive && !isCanceled && (
            <TouchableOpacity 
              style={[styles.cancelButton, isCancelling && styles.cancelButtonDisabled]}
              onPress={handleCancel}
              disabled={isCancelling}
            >
              <Text style={[
                styles.cancelButtonText,
                isCancelling && styles.cancelButtonTextDisabled
              ]}>
                {isCancelling ? i18n.t('cancelling') : i18n.t('cancelSubscription')}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {!hideTopBar && (
        <TopBar 
          onMenuPress={handleLocalMenuPress}
          onLogoPress={navigateToDashboard}
        />
      )}
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.backButton}>
          <Ionicons name="arrow-back" size={22} color="#1A1A1A" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('subscriptionManagement')}</Text>
        <View style={styles.placeholderView} />
      </View>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Najpierw pokazujemy informacje o aktywnej subskrypcji */}
        {renderActiveSubscription()}
        
        <Text style={styles.title}>{i18n.t('choosePlan')}</Text>
        
        {renderBillingToggle()}

        <View style={styles.plansContainer}>
          {plans.map(renderPlanCard)}
        </View>

        {selectedPlanId && !activeSubscription && (
          <TouchableOpacity
            style={styles.subscribeButton}
            onPress={handleSubscribe}
          >
            <Text style={styles.subscribeButtonText}>{i18n.t('subscribe')}</Text>
          </TouchableOpacity>
        )}

        {renderPaymentHistory()}
      </ScrollView>

      {/* Menu boczne */}
      {isDrawerVisible && (
        <View style={styles.drawerContainer}>
          <Animated.View 
            style={[
              styles.overlay,
              { opacity: overlayOpacity }
            ]}
          >
            <TouchableOpacity
              style={styles.overlayTouch}
              activeOpacity={1}
              onPress={closeDrawer}
            />
          </Animated.View>
          
          <Animated.View
            style={[
              styles.drawer,
              {
                transform: [{ translateX }],
                height: windowHeight,
              },
            ]}
          >
            {renderDrawerContent()}
          </Animated.View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  activeSubscriptionContainer: {
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#BFDBFE',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
  },
  subscriptionDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  subscriptionInfo: {
    flexDirection: 'column',
  },
  subscriptionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 4,
  },
  subscriptionValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  cancelButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#DC2626',
  },
  cancelButtonDisabled: {
    backgroundColor: '#E5E7EB',
    borderColor: '#9CA3AF',
  },
  cancelButtonText: {
    color: '#DC2626',
    fontSize: 14,
    fontWeight: '500',
  },
  cancelButtonTextDisabled: {
    color: '#6B7280',
  },
  billingToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 24,
    paddingHorizontal: 16,
  },
  billingOption: {
    fontSize: 16,
    color: '#6B7280',
  },
  billingOptionActive: {
    color: '#4F46E5',
    fontWeight: '600',
  },
  savingsText: {
    fontSize: 12,
    color: '#10B981',
    marginTop: 4,
  },
  plansContainer: {
    gap: 16,
  },
  planCard: {
    width: '100%',
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activePlanCard: {
    borderColor: '#10B981',
    borderWidth: 2,
  },
  selectedPlanCard: {
    borderColor: '#3B82F6',
    borderWidth: 2,
  },
  planName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  planPrice: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  planPeriod: {
    fontSize: 14,
    fontWeight: '400',
    color: '#6B7280',
  },
  planDescription: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 16,
  },
  activeBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  activeBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  featuresList: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4B5563',
  },
  subscribeButton: {
    backgroundColor: '#3B82F6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 24,
  },
  subscribeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  historySection: {
    marginBottom: 24,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  historyItemDetails: {
    flex: 1,
  },
  historyItemDate: {
    fontSize: 14,
    marginBottom: 4,
  },
  historyItemAmount: {
    fontSize: 16,
    fontWeight: '500',
  },
  sectionTitleText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#1F2937',
  },
  // Style dla menu bocznego
  drawerContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 9999,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayTouch: {
    flex: 1,
  },
  drawer: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: 300,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  drawerContent: {
    flex: 1,
    backgroundColor: 'white',
    paddingTop: 20,
  },
  drawerHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  drawerHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  menuItemText: {
    marginLeft: 16,
    fontSize: 16,
    color: '#666',
  },
  changePlanButton: {
    backgroundColor: '#4F46E5',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 16,
  },
  changePlanButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 24,
    marginBottom: 8,
    textAlign: 'center',
  },
});

export default SubscriptionManagement;