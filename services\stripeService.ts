import { supabase } from '../services/supabaseClient';
import { loadStripe } from '@stripe/stripe-js';

// Interfejsy dla typów danych
export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  billing_period: 'monthly' | 'yearly';
  features: string[];
  stripe_price_id: string;
  active: boolean;
}

export interface CompanySubscription {
  id: string;
  company_id: string;
  plan_id: string;
  stripe_subscription_id: string;
  stripe_customer_id: string;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'incomplete_expired' | 'trialing' | 'unpaid';
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
  plan?: SubscriptionPlan; // Dodane pole dla relacji z planem
}

export interface PaymentMethod {
  id: string;
  customer_id: string;
  type: 'card' | 'bank_account';
  card_brand?: string;
  card_last4?: string;
  card_exp_month?: number;
  card_exp_year?: number;
  is_default: boolean;
}

// Konfiguracja Stripe - w produkcji można użyć zmiennych środowiskowych
const STRIPE_PUBLIC_KEY = 'pk_test_51RVGsvPaKRxqYgSx0OiJcyyfr01WnXheefhFRMG77dpUu3sKFx2HbeRIu9U3OJxzhgpiKlfCzeG218iZzqRCn4Dq003kS2Q0B6';

// Inicjalizacja Stripe
const stripePromise = loadStripe(STRIPE_PUBLIC_KEY);

// URL do zewnętrznego serwisu Stripe Checkout
const STRIPE_CHECKOUT_URL = 'https://checkout.stripe.com/c/pay/';

/**
 * Inicjuje proces płatności w Stripe dla nowej subskrypcji
 */
export const createCheckoutSession = async (
  companyId: string,
  planId: string,
  successUrl: string,
  cancelUrl: string
): Promise<string | null> => {
  try {
    console.log('Tworzenie sesji płatności dla:', { companyId, planId });

    // Pobierz dane planu subskrypcji
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();

    if (planError || !plan) {
      console.error('Nie znaleziono planu subskrypcji:', planError);
      return null;
    }

    // Sprawdź czy plan ma Stripe Price ID
    if (!plan.stripe_price_id) {
      console.error('Plan nie ma ustawionego Stripe Price ID:', plan.name);
      return null;
    }

    console.log('Tworzenie sesji Stripe Checkout dla planu:', {
      planName: plan.name,
      stripePriceId: plan.stripe_price_id,
      billingPeriod: plan.billing_period
    });

    // Użyj smooth-handler do tworzenia sesji checkout
    const { data, error } = await supabase.functions.invoke('smooth-handler', {
      body: {
        type: 'create_checkout_session',
        companyId,
        planId,
        successUrl,
        cancelUrl
      }
    });

    if (error) {
      console.error('Błąd podczas tworzenia sesji Stripe:', error);

      // Fallback do Payment Links jeśli smooth-handler nie działa
      console.log('Próba użycia Payment Links jako fallback...');
      return createPaymentLinkFallback(plan, companyId);
    }

    if (data?.url) {
      console.log('Sesja Stripe utworzona pomyślnie przez smooth-handler:', data.url);
      return data.url;
    }

    if (data?.sessionUrl) {
      console.log('Sesja Stripe utworzona pomyślnie przez smooth-handler:', data.sessionUrl);
      return data.sessionUrl;
    }

    console.log('Brak URL w odpowiedzi, próba fallback do Payment Links...');
    return createPaymentLinkFallback(plan, companyId);
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return null;
  }
};

/**
 * Fallback do Payment Links gdy Stripe Checkout nie działa
 */
const createPaymentLinkFallback = (plan: any, companyId: string): string | null => {
  // Mapowanie Stripe Price ID do Payment Links
  const stripePaymentLinks: Record<string, string> = {
    // Plany miesięczne
    'price_1RVH3EPaKRxqYgSxOEuTVBhE': 'https://buy.stripe.com/test_8x24gB8NMglSeGQdyf5EY00', // Basic
    'price_1RVH4nPaKRxqYgSxRL6Wofu4': 'https://buy.stripe.com/test_00wfZj1lk5He7eo8dV5EY01', // Pro
    'price_1RVH5mPaKRxqYgSxgh62XnY0': 'https://buy.stripe.com/test_cNi3cxe864DabuEam35EY02', // Business

    // Plany roczne - te linki muszą być utworzone w Stripe Dashboard
    'price_1RXnK2PaKRxqYgSxSpZzOpRc': 'https://buy.stripe.com/test_basic_yearly',     // Basic Yearly
    'price_1RXnJXPaKRxqYgSxYwuVFMS2': 'https://buy.stripe.com/test_pro_yearly',       // Pro Yearly
    'price_1RXnIvPaKRxqYgSx9ZwLw4R0': 'https://buy.stripe.com/test_business_yearly'   // Business Yearly
  };

  const paymentLink = stripePaymentLinks[plan.stripe_price_id];

  if (!paymentLink) {
    console.error('Brak Payment Link dla Price ID:', plan.stripe_price_id);
    // Fallback do podstawowego planu
    return stripePaymentLinks['price_1RVH3EPaKRxqYgSxOEuTVBhE'] + `?client_reference_id=${companyId}`;
  }

  // Dodaj parametry do Payment Link
  const checkoutUrl = `${paymentLink}?client_reference_id=${companyId}&prefilled_email=<EMAIL>`;

  console.log('Używam Payment Link jako fallback:', checkoutUrl);
  return checkoutUrl;
};

/**
 * Pobiera dostępne plany subskrypcji z bazy danych
 */
export const getSubscriptionPlans = async (): Promise<SubscriptionPlan[]> => {
  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('price', { ascending: true });

    if (error) {
      console.error('Error fetching subscription plans:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getSubscriptionPlans:', error);
    return [];
  }
};

/**
 * Pobiera aktywną subskrypcję dla danej firmy
 */
export const getActiveSubscription = async (companyId: string): Promise<CompanySubscription | null> => {
  try {
    // Pobierz aktywną subskrypcję wraz z danymi planu
    const { data, error } = await supabase
      .from('company_subscriptions')
      .select(`
        *,
        plan:plan_id (*)
      `)
      .eq('company_id', companyId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error fetching active subscription:', error);
      return null;
    }

    // Jeśli nie znaleziono subskrypcji, sprawdź account_type w tabeli companies
    if (!data || data.length === 0) {
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('account_type')
        .eq('id', companyId)
        .single();

      if (companyError || !company) {
        console.error('Error fetching company data:', companyError);
        return null;
      }

      // Jeśli firma ma ustawiony account_type inny niż 'free', próbuj znaleźć odpowiedni plan
      if (company.account_type !== 'free') {
        // Pobierz wszystkie plany i przefiltruj ręcznie po nazwie
        const { data: plans, error: plansError } = await supabase
          .from('subscription_plans')
          .select('*');

        if (!plansError && plans && plans.length > 0) {
          // Ręczne filtrowanie planów, które zawierają account_type (case-insensitive)
          const matchingPlans = plans.filter(plan => 
            plan.name.toLowerCase().includes(company.account_type.toLowerCase())
          );
          
          if (matchingPlans.length > 0) {
            // Stwórz tymczasowy obiekt subskrypcji na podstawie danych firmy
            const now = new Date();
            const endDate = new Date();
            endDate.setMonth(endDate.getMonth() + 1);

            return {
              id: 'temp_' + Date.now(),
              company_id: companyId,
              plan_id: matchingPlans[0].id,
              stripe_subscription_id: 'temp_sub_' + Date.now(),
              stripe_customer_id: 'temp_cus_' + Date.now(),
              status: 'active',
              current_period_start: now.toISOString(),
              current_period_end: endDate.toISOString(),
              cancel_at_period_end: false,
              created_at: now.toISOString(),
              updated_at: now.toISOString(),
              plan: matchingPlans[0]
            };
          }
        }
      }
      
      return null;
    }

    return data[0];
  } catch (error) {
    console.error('Exception in getActiveSubscription:', error);
    return null;
  }
};

/**
 * Anuluje aktywną subskrypcję firmy
 */
export const cancelSubscription = async (
  subscriptionId: string,
  atPeriodEnd: boolean = true
): Promise<boolean> => {
  try {
    const session = await supabase.auth.getSession();
    
    const requestBody = { 
      type: 'cancel_subscription',
      subscription_id: subscriptionId,
      cancel_at_period_end: atPeriodEnd 
    };
    
    console.log('Sending cancel request:', {
      requestBody,
      hasSession: !!session.data.session,
      sessionToken: session.data.session?.access_token?.slice(0, 10) + '...'
    });
    
    // Wywołaj Edge Function do anulowania subskrypcji w Stripe
    const { data, error } = await supabase.functions.invoke<{ success: boolean; subscription: any }>(
      'smooth-handler',
      {
        method: 'POST',
        body: requestBody, // Supabase automatycznie serializuje body do JSON
        headers: {
          Authorization: `Bearer ${session.data.session?.access_token}`
        }
      }
    );

    if (error) {
      console.error('Error from Edge Function:', {
        message: error.message,
        name: error.name,
        context: error.context
      });
      return false;
    }

    console.log('Response from Edge Function:', data);
    return data?.success ?? false;
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return false;
  }
};

/**
 * Sprawdza, czy firma ma aktywną subskrypcję premium
 */
export const hasActiveSubscription = async (companyId: string): Promise<boolean> => {
  const subscription = await getActiveSubscription(companyId);
  return subscription !== null;
};

/**
 * Pobiera historię płatności dla firmy
 */
export const getPaymentHistory = async (companyId: string): Promise<any[]> => {
  try {
    const { data, error } = await supabase
      .from('payment_history')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching payment history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getPaymentHistory:', error);
    return [];
  }
};

/**
 * Pobiera informacje o wykorzystaniu zasobów przez firmę
 */
export const getResourceUsage = async (companyId: string): Promise<any> => {
  try {
    // Pobierz liczbę kodów weryfikacyjnych
    const { data: verificationCodes, error: verificationError } = await supabase
      .from('verification_codes')
      .select('id, used')
      .eq('company_id', companyId);

    if (verificationError) {
      console.error('Error fetching verification codes:', verificationError);
      return null;
    }

    // Zlicz kody według statusu used (true/false)
    const total = verificationCodes ? verificationCodes.length : 0;
    const used = verificationCodes ? verificationCodes.filter(code => code.used === true).length : 0;
    const available = verificationCodes ? verificationCodes.filter(code => code.used === false).length : 0;

    return {
      verificationCodes: {
        total,
        used,
        available
      }
    };
  } catch (error) {
    console.error('Exception in getResourceUsage:', error);
    return null;
  }
};

/**
 * Aktualizuje bazę danych po udanej płatności
 * Ta funkcja powinna być wywołana po przekierowaniu użytkownika z powrotem do aplikacji
 */
export const updateSubscriptionAfterPayment = async (
  companyId: string,
  planId: string
): Promise<boolean> => {
  try {
    console.log('Aktualizacja subskrypcji po płatności:', { companyId, planId });
    
    // Pobierz dane planu subskrypcji
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();
    
    if (planError || !plan) {
      console.error('Nie znaleziono planu subskrypcji:', planError);
      return false;
    }
    
    // Utwórz wpis w tabeli company_subscriptions
    const now = new Date();
    const periodEnd = new Date();
    periodEnd.setMonth(periodEnd.getMonth() + (plan.billing_period === 'monthly' ? 1 : 12));
    
    const { error: subscriptionError } = await supabase
      .from('company_subscriptions')
      .insert([{
        company_id: companyId,
        plan_id: planId,
        stripe_subscription_id: `manual_${Date.now()}`, // Tymczasowy identyfikator
        stripe_customer_id: `customer_${companyId}`, // Tymczasowy identyfikator
        status: 'active',
        current_period_start: now.toISOString(),
        current_period_end: periodEnd.toISOString(),
        cancel_at_period_end: false,
        created_at: now.toISOString(),
        updated_at: now.toISOString()
      }]);
    
    if (subscriptionError) {
      console.error('Błąd zapisywania subskrypcji:', subscriptionError);
      return false;
    }
    
    // Zaktualizuj status firmy używając funkcji RPC
    const planName = plan.name.toLowerCase().replace(/\s+/g, '').replace('yearly', '');
    const { data: rpcResult, error: companyError } = await supabase
      .rpc('update_company_account_type_rpc', {
        p_company_id: companyId,
        p_plan_name: planName
      });
    
    if (companyError) {
      console.error('Błąd aktualizacji statusu firmy:', companyError);
      return false;
    }
    
    // Dodaj wpis do historii płatności
    const { error: paymentError } = await supabase
      .from('payment_history')
      .insert([{
        company_id: companyId,
        amount: plan.price,
        currency: 'pln',
        status: 'succeeded',
        type: 'subscription',
        stripe_payment_id: `payment_${Date.now()}`, // Tymczasowy identyfikator
        created_at: now.toISOString()
      }]);
    
    if (paymentError) {
      console.error('Błąd zapisywania historii płatności:', paymentError);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error updating subscription after payment:', error);
    return false;
  }
}; 