import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';

const PrismIcon = () => {
  return (
    <View style={styles.container}>
      <Image 
        source={require('../assets/images/logo.png')}
        style={styles.logoImage}
        resizeMode="contain"
      />
      <Text style={styles.logoText}>WorkFlow</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoImage: {
    width: 270,
    height: 270,
  },
  logoText: {
    fontSize: 63,
    fontWeight: 'bold',
    color: 'white',
    marginTop: -20,
  }
});

export default PrismIcon; 