-- Ten skrypt aktualizuje schemat tabeli companies, aby umo<PERSON><PERSON>wić różne typy kont
-- Wykonaj ten skrypt w konsoli SQL Supabase

-- Najpierw sprawdź obecny schemat tabeli companies
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default, 
  character_maximum_length
FROM 
  information_schema.columns 
WHERE 
  table_name = 'companies' 
ORDER BY 
  ordinal_position;

-- Sprawdź obecne ograniczenie CHECK dla kolumny account_type
SELECT
  conname AS constraint_name,
  pg_get_constraintdef(oid) AS constraint_definition
FROM
  pg_constraint
WHERE
  conrelid = 'companies'::regclass
  AND contype = 'c'
  AND pg_get_constraintdef(oid) LIKE '%account_type%';

-- <PERSON><PERSON><PERSON> istniejące ograniczenie CHECK dla kolumny account_type
ALTER TABLE companies DROP CONSTRAINT IF EXISTS companies_account_type_check;

-- Dodaj nowe ograniczenie CHECK z rozszerzoną listą typów kont
ALTER TABLE companies ADD CONSTRAINT companies_account_type_check 
  CHECK (account_type IN ('free', 'premium', 'Basic', 'Pro', 'Business', 'Basic Yearly', 'Pro Yearly', 'Business Yearly'));

-- Sprawdź, czy ograniczenie zostało zaktualizowane
SELECT
  conname AS constraint_name,
  pg_get_constraintdef(oid) AS constraint_definition
FROM
  pg_constraint
WHERE
  conrelid = 'companies'::regclass
  AND contype = 'c'
  AND pg_get_constraintdef(oid) LIKE '%account_type%';

-- Zaktualizuj firmę z ID z obrazka (repap) na plan Basic
UPDATE companies
SET account_type = 'Basic',
    verification_code_limit = 5
WHERE id = '90c35057-24c0-432e-8d06-68ae46ce3979';

-- Sprawdź wyniki po aktualizacji
SELECT id, name, account_type, verification_code_limit
FROM companies
WHERE id = '90c35057-24c0-432e-8d06-68ae46ce3979'; 