-- Napraw trigger'y dla automatycznej aktualizacji firm przy zmianie subskrypcji

-- Naj<PERSON>rw usuń stare trigger'y jeś<PERSON> istnieją
DROP TRIGGER IF EXISTS update_company_account_type_trigger ON company_subscriptions;
DROP TRIGGER IF EXISTS reset_company_account_type_trigger ON company_subscriptions;

-- Usuń stare funkcje jeśli istnieją
DROP FUNCTION IF EXISTS update_company_account_type();
DROP FUNCTION IF EXISTS reset_company_account_type();

-- <PERSON><PERSON><PERSON> do aktualizacji typu konta firmy na podstawie aktywnej subskrypcji
CREATE OR REPLACE FUNCTION update_company_account_type()
RETURNS TRIGGER AS $$
DECLARE
  plan_name TEXT;
  verification_limit INTEGER;
BEGIN
  -- <PERSON><PERSON>rz nazwę planu z tabeli subscription_plans
  SELECT sp.name INTO plan_name
  FROM subscription_plans sp
  WHERE sp.id = NEW.plan_id;
  
  -- <PERSON><PERSON><PERSON> nie znaleziono planu, u<PERSON><PERSON>j do<PERSON>ślnej wartości
  IF plan_name IS NULL THEN
    plan_name := 'Basic';
  END IF;
  
  -- Normalizuj nazwę planu do małych liter
  plan_name := lower(plan_name);
  
  -- Ustaw limit kodów weryfikacyjnych na podstawie planu
  verification_limit := CASE
    WHEN plan_name LIKE '%basic%' THEN 5
    WHEN plan_name LIKE '%pro%' THEN 20
    WHEN plan_name LIKE '%business%' THEN 999999
    ELSE 5
  END;
  
  -- Aktualizuj firmę (bez updated_at, bo kolumna nie istnieje)
  UPDATE companies
  SET account_type = plan_name,
      verification_code_limit = verification_limit
  WHERE id = NEW.company_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do resetowania typu konta firmy na darmowy, gdy subskrypcja wygasa
CREATE OR REPLACE FUNCTION reset_company_account_type()
RETURNS TRIGGER AS $$
BEGIN
  -- Sprawdź, czy firma ma inne aktywne subskrypcje
  IF NOT EXISTS (
    SELECT 1 FROM company_subscriptions 
    WHERE company_id = OLD.company_id 
      AND status = 'active' 
      AND id != OLD.id
  ) THEN
    -- Jeśli nie ma innych aktywnych subskrypcji, przywróć darmowy plan
    UPDATE companies
    SET account_type = 'free',
        verification_code_limit = 2
    WHERE id = OLD.company_id;
    
    RAISE NOTICE 'Company % reset to free plan', OLD.company_id;
  ELSE
    RAISE NOTICE 'Company % has other active subscriptions, not resetting', OLD.company_id;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Utwórz trigger dla aktywnych subskrypcji
CREATE TRIGGER update_company_account_type_trigger
AFTER INSERT OR UPDATE OF status ON company_subscriptions
FOR EACH ROW
WHEN (NEW.status = 'active')
EXECUTE FUNCTION update_company_account_type();

-- Utwórz trigger dla anulowanych subskrypcji
CREATE TRIGGER reset_company_account_type_trigger
AFTER UPDATE OF status ON company_subscriptions
FOR EACH ROW
WHEN (OLD.status = 'active' AND NEW.status IN ('canceled', 'unpaid', 'incomplete_expired'))
EXECUTE FUNCTION reset_company_account_type();

-- Sprawdź czy trigger'y zostały utworzone
SELECT 
  trigger_name, 
  event_manipulation, 
  event_object_table, 
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'company_subscriptions'
ORDER BY trigger_name;
