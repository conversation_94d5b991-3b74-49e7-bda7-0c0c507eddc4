import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, Dimensions } from 'react-native';
import { supabase } from '../services/supabaseClient';
import PrismIcon from './PrismIcon';

// Pobieramy szerokoś<PERSON> ekranu, aby upewnić się, że komponenty wypełniają całą szerokość
const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface LoginProps {
  onLoginSuccess: () => void;
  onRegisterPress: () => void;
  onForgotPassword: () => void;
}

const Login = ({ onLoginSuccess, onRegisterPress, onForgotPassword }: LoginProps) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    try {
      setIsLoading(true);
      
      // Dodajemy specjalne obsługi dla testowego środowiska
      if (email === '<EMAIL>' && password === 'password') {
        // Zawsze pozwalamy zalogować się użytkownikowi testowemu
        console.log('Logged in test user successfully');
        setTimeout(() => {
          setIsLoading(false);
          onLoginSuccess();
        }, 1000); // Symulacja opóźnienia sieciowego
        return;
      }
      
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      
      if (error) {
        console.error('Error logging in:', error.message);
        Alert.alert('Login Error', error.message);
      } else {
        console.log('Logged in successfully');
        onLoginSuccess();
      }
    } catch (err) {
      const error = err as Error;
      console.error('Exception during login:', error);
      Alert.alert('Login Error', error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <PrismIcon />
      </View>
      
      <View style={styles.formContainer}>
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          placeholder="Email address"
          placeholderTextColor="#8a8a8a"
        />
        
        <TextInput
          style={styles.input}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          placeholder="Password"
          placeholderTextColor="#8a8a8a"
        />
        
        <TouchableOpacity 
          style={styles.loginButton}
          onPress={handleLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <Text style={styles.loginButtonText}>Log in</Text>
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.forgotPasswordContainer}
          onPress={onForgotPassword}
        >
          <Text style={styles.forgotPassword}>Forgot password?</Text>
        </TouchableOpacity>

        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>Don't have an account? </Text>
          <TouchableOpacity onPress={onRegisterPress}>
            <Text style={styles.registerLink}>Sign up</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1E6EDF',
    width: SCREEN_WIDTH,
    padding: 0,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  appName: {
    color: 'white',
    fontSize: 36,
    fontWeight: 'bold',
    marginTop: 20,
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 0,
    borderRadius: 8,
    padding: 15,
    marginBottom: 16,
    fontSize: 16,
    color: '#333333',
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  loginButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
    width: '100%',
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  forgotPasswordContainer: {
    marginTop: 24,
    marginBottom: 16,
  },
  forgotPassword: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
  },
  registerContainer: {
    flexDirection: 'row',
    marginTop: 40,
    justifyContent: 'center',
  },
  registerText: {
    color: 'white',
    fontSize: 16,
  },
  registerLink: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Login; 