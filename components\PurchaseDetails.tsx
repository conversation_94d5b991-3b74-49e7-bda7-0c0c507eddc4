import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert, ActivityIndicator, Dimensions, Platform, Image as RNImage } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { TopBar } from './TopBar';
import { MaterialIcons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';
import { getLocale } from '../utils/localization';
import SafeText from './SafeText';

interface PurchaseDetailsProps {
  purchaseId: string;
  onBack: () => void;
  onMenuPress?: () => void;
  onLogoPress?: () => void;
}

interface Purchase {
  id: string;
  title: string;
  description: string;
  category: string;
  price_estimate: number;
  actual_price?: number;
  status: 'pending' | 'approved' | 'ordered' | 'delivered' | 'canceled';
  requested_by: string;
  requested_by_name: string;
  requested_at: string;
  approved_by?: string;
  approved_by_name?: string;
  approved_at?: string;
  ordered_at?: string;
  delivered_at?: string;
  canceled_at?: string;
  canceled_by?: string;
  canceled_by_name?: string;
  supplier?: string;
  invoice_number?: string;
  invoice_date?: string;
  warranty_end_date?: string;
  attachments?: any[];
  location?: string;
  notes?: string;
  priority: string;
  company_id: string;
  photos?: string[];
  photo_urls?: string[];
  quantity?: number;
}

// Proste komponenty bez memoizacji czy efektów
function Image({ uri, style }: { uri: string, style?: any }) {
  // Używamy zwykłego komponentu Image bez animacji
  return (
    <RNImage 
      source={{ uri }} 
      style={style || { width: 100, height: 100 }}
      resizeMode="cover"
    />
  );
}

const PurchaseDetails: React.FC<PurchaseDetailsProps> = ({
  purchaseId,
  onBack,
  onMenuPress,
  onLogoPress,
}) => {
  const [purchase, setPurchase] = useState<Purchase | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isApproving, setIsApproving] = useState<boolean>(false);
  const [isCanceling, setIsCanceling] = useState<boolean>(false);
  const [isOrdering, setIsOrdering] = useState<boolean>(false);
  const [isDelivering, setIsDelivering] = useState<boolean>(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Stałe dla często używanych komunikatów
  const successMessage = i18n.t('success');
  const errorMessage = i18n.t('error');
  const statusChangedMessage = getLocale() === 'pl' 
    ? 'Status zgłoszenia został zmieniony' 
    : getLocale() === 'uk' 
      ? 'Статус запиту було змінено' 
      : 'Request status has been changed';
  const saveErrorMessage = i18n.t('purchasesSaveError');

  // Ustalamy które zdjęcia wyświetlić - photo_urls mają pierwszeństwo nad photos
  const imagesToDisplay = purchase?.photo_urls?.length 
    ? purchase.photo_urls 
    : purchase?.photos || [];

  // Funkcje obsługi podglądu zdjęć - uproszczona wersja bez modalu
  const handleImagePress = (url: string) => {
    // Zamiast otwierać modal, po prostu pokazujemy wybrane zdjęcie w większym formacie
    if (selectedImageUrl === url) {
      setSelectedImageUrl(null); // Zamykamy podgląd jeśli klikamy to samo zdjęcie
    } else {
      setSelectedImageUrl(url);
    }
  };

  useEffect(() => {
    if (purchaseId) {
      fetchPurchaseDetails();
    }
  }, [purchaseId]);
    
  const fetchPurchaseDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const { data, error } = await supabase
        .from('purchases')
        .select('*')
        .eq('id', purchaseId)
        .single();
        
      if (error) {
        console.error('Error fetching purchase details:', error);
        setError(error.message || i18n.t('error'));
      } else {
        setPurchase(data);
      }
    } catch (error) {
      console.error('Error in fetchPurchaseDetails:', error);
      setError(error instanceof Error ? error.message : i18n.t('error'));
    } finally {
      setLoading(false);
    }
  };

  const handleMenuPress = () => {
    if (onMenuPress) {
      onMenuPress();
    }
  };

  const handleLogoPress = () => {
    if (onLogoPress) {
      onLogoPress();
    }
  };

  const handleApprove = async () => {
    if (!purchase) return;
    
    setIsApproving(true);
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Error getting user session:', sessionError);
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        return;
      }
      
      if (!sessionData.session) {
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        return;
      }
      
      const currentUser = sessionData.session.user;
      const now = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('purchases')
        .update({
          status: 'approved',
          approved_by: currentUser.id,
          approved_by_name: currentUser.email,
          approved_at: now,
        })
        .eq('id', purchase.id)
        .select();
      
      if (error) {
        console.error('Error approving purchase:', error);
        Alert.alert(errorMessage, saveErrorMessage);
        return;
      }
      
      console.log('Purchase approved successfully:', data);
      
      // Refresh purchase details
      fetchPurchaseDetails();
      
      Alert.alert(successMessage, statusChangedMessage);
    } catch (error) {
      console.error('Unexpected error during purchase approval:', error);
      Alert.alert(errorMessage, saveErrorMessage);
    } finally {
      setIsApproving(false);
    }
  };
  
  const handleCancel = async () => {
    if (!purchase) return;
    
    setIsCanceling(true);
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Error getting user session:', sessionError);
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        return;
      }
      
      if (!sessionData.session) {
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        return;
      }
      
      const currentUser = sessionData.session.user;
      const now = new Date().toISOString();
      
      const { data, error } = await supabase
        .from('purchases')
        .update({
          status: 'canceled',
          canceled_by: currentUser.id,
          canceled_by_name: currentUser.email,
          canceled_at: now,
        })
        .eq('id', purchase.id)
        .select();
      
      if (error) {
        console.error('Error canceling purchase:', error);
        Alert.alert(errorMessage, saveErrorMessage);
        return;
      }
      
      console.log('Purchase canceled successfully:', data);
      
      // Refresh purchase details
      fetchPurchaseDetails();
      
      Alert.alert(successMessage, statusChangedMessage);
    } catch (error) {
      console.error('Unexpected error during purchase cancellation:', error);
      Alert.alert(errorMessage, saveErrorMessage);
      } finally {
      setIsCanceling(false);
    }
  };

  const handleOrder = async () => {
    if (!purchase) {
      console.error("Nie można oznaczyć jako zamówione - brak danych zakupu");
      return;
    }
    
    console.log("Rozpoczynam oznaczanie zakupu jako zamówiony, ID:", purchase.id);
    
    // Zabezpieczenie przed wielokrotnym kliknięciem
    if (isOrdering) {
      console.log("Operacja już w toku, pomijam...");
      return;
    }
    
    setIsOrdering(true);
    
    try {
      // Pobierz sesję użytkownika
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Error getting user session:', sessionError);
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        setIsOrdering(false);
        return;
      }
      
      if (!sessionData.session) {
        console.error('No active user session');
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        setIsOrdering(false);
        return;
      }
      
      const now = new Date().toISOString();
      console.log("Aktualizuję status zakupu w bazie danych na 'ordered', ID:", purchase.id);
      
      // Aktualizuj status zakupu
      const { data, error } = await supabase
        .from('purchases')
        .update({
          status: 'ordered',
          ordered_at: now,
        })
        .eq('id', purchase.id)
        .select();
      
      if (error) {
        console.error('Error marking purchase as ordered:', error);
        
        // Sprawdź czy błąd dotyczy braku wartości 'ordered' w enum
        if (error.code === '22P02' && 
            error.message && 
            error.message.includes('invalid input value for enum purchase_status')) {
          
          // Próba aktualizacji bez zmiany statusu - tylko dodanie timestamp
          console.log("Próba aktualizacji zakupu z pominięciem statusu...");
          const { data: timestampData, error: timestampError } = await supabase
            .from('purchases')
            .update({
              ordered_at: now,
            })
            .eq('id', purchase.id)
            .select();
            
          if (timestampError) {
            console.error('Failed to update timestamp:', timestampError);
            Alert.alert(
              errorMessage, 
              i18n.t('purchasesStatusUpdateFailed'),
              [
                { text: 'OK' }
              ]
            );
          } else {
            // Aktualizacja timestampa się powiodła
            Alert.alert(
              successMessage, 
              i18n.t('purchasesOrderedPartially'),
              [
                { text: 'OK' }
              ]
            );
            
            // Aktualizuj lokalny stan, aby poprawnie pokazać timestamp
            setPurchase({
              ...purchase,
              ordered_at: now
            });
            
            // Odśwież dane
            await fetchPurchaseDetails();
          }
        } else {
          // Inny błąd niż problem z enum
          Alert.alert(errorMessage, saveErrorMessage);
        }
        
        setIsOrdering(false);
        return;
      }
      
      if (!data || data.length === 0) {
        console.error('No data returned after update operation');
        Alert.alert(errorMessage, i18n.t('purchasesNoDataReturned'));
        setIsOrdering(false);
        return;
      }
      
      console.log('Purchase marked as ordered successfully:', data);
      
      // Aktualizuj lokalny stan, aby szybciej pokazać zmiany użytkownikowi
      setPurchase({
        ...purchase,
        status: 'ordered',
        ordered_at: now
      });
      
      // Odśwież dane zakupu z serwera
      console.log("Odświeżam dane zakupu z serwera...");
      await fetchPurchaseDetails();
      
      Alert.alert(successMessage, statusChangedMessage);
    } catch (error) {
      console.error('Unexpected error during marking purchase as ordered:', error);
      Alert.alert(errorMessage, saveErrorMessage);
    } finally {
      setIsOrdering(false);
    }
  };

  const handleDeliver = async () => {
    if (!purchase) {
      console.error("Nie można oznaczyć jako dostarczone - brak danych zakupu");
      return;
    }
    
    console.log("Rozpoczynam oznaczanie zakupu jako dostarczony, ID:", purchase.id);
    
    // Zabezpieczenie przed wielokrotnym kliknięciem
    if (isDelivering) {
      console.log("Operacja już w toku, pomijam...");
      return;
    }
    
    setIsDelivering(true);
    
    try {
      // Pobierz sesję użytkownika
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.error('Error getting user session:', sessionError);
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        setIsDelivering(false);
        return;
      }
      
      if (!sessionData.session) {
        console.error('No active user session');
        Alert.alert(errorMessage, i18n.t('purchasesNoPermissions'));
        setIsDelivering(false);
        return;
      }
      
      const now = new Date().toISOString();
      console.log("Aktualizuję status zakupu w bazie danych na 'delivered', ID:", purchase.id);
      
      // Aktualizuj status zakupu
      const { data, error } = await supabase
        .from('purchases')
        .update({
          status: 'delivered',
          delivered_at: now,
        })
        .eq('id', purchase.id)
        .select();
      
      if (error) {
        console.error('Error marking purchase as delivered:', error);
        
        // Sprawdź czy błąd dotyczy braku wartości 'delivered' w enum
        if (error.code === '22P02' && 
            error.message && 
            error.message.includes('invalid input value for enum purchase_status')) {
          
          // Próba aktualizacji bez zmiany statusu - tylko dodanie timestamp
          console.log("Próba aktualizacji zakupu z pominięciem statusu...");
          const { data: timestampData, error: timestampError } = await supabase
            .from('purchases')
            .update({
              delivered_at: now,
            })
            .eq('id', purchase.id)
            .select();
            
          if (timestampError) {
            console.error('Failed to update timestamp:', timestampError);
            Alert.alert(
              errorMessage, 
              i18n.t('purchasesStatusUpdateFailed'),
              [
                { text: 'OK' }
              ]
            );
          } else {
            // Aktualizacja timestampa się powiodła
            Alert.alert(
              successMessage, 
              i18n.t('purchasesDeliveredPartially'),
              [
                { text: 'OK' }
              ]
            );
            
            // Aktualizuj lokalny stan, aby poprawnie pokazać timestamp
            setPurchase({
              ...purchase,
              delivered_at: now
            });
            
            // Odśwież dane
            await fetchPurchaseDetails();
          }
        } else {
          // Inny błąd niż problem z enum
          Alert.alert(errorMessage, saveErrorMessage);
        }
        
        setIsDelivering(false);
        return;
      }
      
      if (!data || data.length === 0) {
        console.error('No data returned after update operation');
        Alert.alert(errorMessage, i18n.t('purchasesNoDataReturned'));
        setIsDelivering(false);
        return;
      }
      
      console.log('Purchase marked as delivered successfully:', data);
      
      // Aktualizuj lokalny stan, aby szybciej pokazać zmiany użytkownikowi
      setPurchase({
        ...purchase,
        status: 'delivered',
        delivered_at: now
      });
      
      // Odśwież dane zakupu z serwera
      console.log("Odświeżam dane zakupu z serwera...");
      await fetchPurchaseDetails();
      
      Alert.alert(successMessage, statusChangedMessage);
    } catch (error) {
      console.error('Unexpected error during marking purchase as delivered:', error);
      Alert.alert(errorMessage, saveErrorMessage);
    } finally {
      setIsDelivering(false);
    }
  };

  // Helper functions for formatting and display
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear().toString().slice(-2)}`;
  };

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  };

  const formatPrice = (price?: number) => {
    if (price === undefined || price === null) return '-';
    return '$' + price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return i18n.t('purchaseStatusPending');
      case 'approved': return i18n.t('purchaseStatusApproved');
      case 'ordered': return i18n.t('purchaseStatusOrdered');
      case 'delivered': return i18n.t('purchaseStatusDelivered');
      case 'canceled': return i18n.t('purchaseStatusCanceled');
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#F59E0B'; // Amber
      case 'approved': return '#3B82F6'; // Blue
      case 'ordered': return '#8B5CF6'; // Purple
      case 'delivered': return '#10B981'; // Green
      case 'canceled': return '#6B7280'; // Gray
      default: return '#F59E0B';
    }
  };

  const getStatusBackgroundColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FEF3C7'; // Light amber
      case 'approved': return '#DBEAFE'; // Light blue
      case 'ordered': return '#EDE9FE'; // Light purple
      case 'delivered': return '#D1FAE5'; // Light green
      case 'canceled': return '#F3F4F6'; // Light gray
      default: return '#FEF3C7';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low': return i18n.t('priorityLow');
      case 'medium': return i18n.t('priorityMedium');
      case 'high': return i18n.t('priorityHigh');
      case 'critical': return i18n.t('priorityCritical');
      default: return i18n.t('priorityMedium');
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return '#065F46'; // Green text
      case 'medium': return '#92400E'; // Amber text
      case 'high': return '#991B1B'; // Red text
      case 'critical': return '#7F1D1D'; // Dark red text
      default: return '#92400E';
    }
  };

  const getPriorityBackgroundColor = (priority: string) => {
    switch (priority) {
      case 'low': return '#D1FAE5'; // Light green
      case 'medium': return '#FEF3C7'; // Light amber
      case 'high': return '#FEE2E2'; // Light red
      case 'critical': return '#FECACA'; // Light red, slightly different
      default: return '#FEF3C7';
    }
  };

  // Uproszczona wersja renderowania zdjęć
  const renderPhotoSection = () => {
    if (imagesToDisplay.length === 0) return null;

    return (
      <View style={styles.photoSection}>
        <Text style={styles.sectionTitle}>{i18n.t('purchasesPhotos')}</Text>
        
        {/* Wyświetlamy duże zdjęcie, jeśli jest wybrane */}
        {selectedImageUrl && (
          <View style={styles.selectedImageContainer}>
            <TouchableOpacity 
              onPress={() => setSelectedImageUrl(null)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#000" />
            </TouchableOpacity>
            <Image 
              uri={selectedImageUrl} 
              style={styles.selectedImage} 
            />
          </View>
        )}
        
        {/* Lista miniatur zdjęć */}
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={selectedImageUrl ? { marginTop: 8 } : undefined}
        >
          {imagesToDisplay.map((photo, index) => (
            <TouchableOpacity
              key={index}
              style={styles.photoContainer}
              onPress={() => handleImagePress(photo)}
            >
              <Image 
                uri={photo} 
                style={styles.photo} 
              />
              <View style={styles.photoIndexBadge}>
                <Text style={styles.photoIndexText}>{index + 1}/{imagesToDisplay.length}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <SafeText style={styles.loadingText}>{i18n.t('loading')}</SafeText>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#EF4444" />
          <SafeText style={styles.errorText}>{error}</SafeText>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={22} color="#1F2937" />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  if (!purchase) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#EF4444" />
          <SafeText style={styles.errorText}>{i18n.t('error')}</SafeText>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={22} color="#1F2937" />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Renderuj główny widok
  return (
    <View style={styles.container}>
      <View style={styles.headerActions}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={22} color="#1A1A1A" />
        </TouchableOpacity>
        <SafeText style={styles.headerTitle}>{i18n.t('purchasesDetailsHeader')}</SafeText>
        <View style={styles.placeholderView} />
      </View>
      
      <ScrollView style={styles.contentContainer}>
        <View style={styles.contentWrapper}>
          <SafeText style={styles.title}>{purchase.title}</SafeText>
          
          <View style={styles.statusBadgeContainer}>
            <SafeText style={[styles.statusBadge, { backgroundColor: getStatusBackgroundColor(purchase.status) }]}>
              {getStatusText(purchase.status)}
            </SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="alert-circle-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('purchasesPriority')}:</SafeText>
            <SafeText style={[styles.priorityValue, { color: getPriorityColor(purchase.priority) }]}>
              {getPriorityText(purchase.priority)}
            </SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="person-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('purchasesRequesterLabel')}</SafeText>
            <SafeText style={styles.infoValue}>{purchase.requested_by_name || i18n.t('purchasesUnknownUser')}</SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="pricetag-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('purchasesCategoryLabel')}</SafeText>
            <SafeText style={styles.infoValue}>{purchase.category || "-"}</SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="calendar-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('purchasesRequestDateLabel')}</SafeText>
            <SafeText style={styles.infoValue}>{formatDate(purchase.requested_at)}</SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="time-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('date')}:</SafeText>
            <SafeText style={styles.infoValue}>{formatTime(purchase.requested_at)}</SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="cash-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('purchasesEstimatedCost')}:</SafeText>
            <SafeText style={styles.infoValue}>{formatPrice(purchase.price_estimate)}</SafeText>
          </View>
          
          <View style={styles.infoRow}>
            <Ionicons name="cube-outline" size={20} color="#6B7280" style={styles.icon} />
            <SafeText style={styles.infoLabel}>{i18n.t('purchasesQuantity')}:</SafeText>
            <SafeText style={styles.infoValue}>{purchase.quantity || "-"}</SafeText>
          </View>
          
          {purchase.description && (
            <>
              <View style={styles.infoRow}>
                <Ionicons name="document-text-outline" size={20} color="#6B7280" style={styles.icon} />
                <SafeText style={styles.infoLabel}>{i18n.t('purchasesDescription')}:</SafeText>
              </View>
              <SafeText style={styles.description}>{purchase.description}</SafeText>
            </>
          )}
          
          {renderPhotoSection()}
          
          <View style={styles.divider} />
          
          {/* Action buttons for different statuses */}
          {purchase.status === 'pending' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[styles.approveButton, isApproving && styles.disabledButton]} 
                onPress={handleApprove}
                disabled={isApproving}
              >
                {isApproving ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                ) : (
                  <Ionicons name="checkmark-circle-outline" size={20} color="white" />
                )}
                <SafeText style={styles.actionButtonText}>
                  {isApproving ? i18n.t('loading') : i18n.t('apply')}
                </SafeText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.cancelButton, isCanceling && styles.disabledButton]} 
                onPress={handleCancel}
                disabled={isCanceling}
              >
                {isCanceling ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                ) : (
                  <Ionicons name="close-circle-outline" size={20} color="white" />
                )}
                <SafeText style={styles.actionButtonText}>
                  {isCanceling ? i18n.t('loading') : i18n.t('cancel')}
                </SafeText>
              </TouchableOpacity>
            </View>
          )}
          
          {purchase.status === 'approved' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[styles.orderButton, isOrdering && styles.disabledButton]} 
                onPress={handleOrder}
                disabled={isOrdering}
              >
                {isOrdering ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                ) : (
                  <Ionicons name="cart-outline" size={20} color="white" />
                )}
                <SafeText style={styles.actionButtonText}>
                  {isOrdering ? i18n.t('loading') : i18n.t('purchasesOrderedFilter')}
                </SafeText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.cancelButton, isCanceling && styles.disabledButton]} 
                onPress={handleCancel}
                disabled={isCanceling}
              >
                {isCanceling ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                ) : (
                  <Ionicons name="close-circle-outline" size={20} color="white" />
                )}
                <SafeText style={styles.actionButtonText}>
                  {isCanceling ? i18n.t('loading') : i18n.t('cancel')}
                </SafeText>
              </TouchableOpacity>
            </View>
          )}
          
          {purchase.status === 'ordered' && (
            <View style={styles.actionButtons}>
              <TouchableOpacity 
                style={[styles.deliverButton, isDelivering && styles.disabledButton]} 
                onPress={handleDeliver}
                disabled={isDelivering}
              >
                {isDelivering ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                ) : (
                  <Ionicons name="checkmark-done-outline" size={20} color="white" />
                )}
                <SafeText style={styles.actionButtonText}>
                  {isDelivering ? i18n.t('loading') : i18n.t('purchasesDeliveredFilter')}
                </SafeText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.cancelButton, isCanceling && styles.disabledButton]} 
                onPress={handleCancel}
                disabled={isCanceling}
              >
                {isCanceling ? (
                  <ActivityIndicator size="small" color="white" style={styles.buttonLoader} />
                ) : (
                  <Ionicons name="close-circle-outline" size={20} color="white" />
                )}
                <SafeText style={styles.actionButtonText}>
                  {isCanceling ? i18n.t('loading') : i18n.t('cancel')}
                </SafeText>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

// Uproszczone style
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginLeft: 8,
    display: 'none', // Ukrywamy tekst "Powrót"
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  contentContainer: {
    flex: 1,
  },
  contentWrapper: {
    padding: 20,
  },
  cardContainer: {
    flex: 1,
  },
  card: {
    backgroundColor: 'white',
    padding: 0,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  statusBadgeContainer: {
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    fontSize: 14,
    fontWeight: '500',
    overflow: 'hidden',
    color: '#92400E',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
  },
  icon: {
    marginRight: 8,
    width: 24,
  },
  infoLabel: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
    marginRight: 8,
    width: 140,
  },
  infoValue: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1,
  },
  priorityValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    color: '#1F2937',
    lineHeight: 20,
    marginBottom: 20,
    marginLeft: 32,
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 16,
  },
  actionButtons: {
    flexDirection: 'column',
    marginTop: 8,
    gap: 12,
  },
  approveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10B981',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EF4444',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#1F2937',
    marginTop: 12,
    marginBottom: 20,
  },
  disabledButton: {
    opacity: 0.7,
  },
  buttonLoader: {
    marginRight: 8,
  },
  orderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6', // Blue
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  deliverButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8B5CF6', // Purple
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  // Styles dla zdjęć
  photoSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  photoContainer: {
    marginRight: 10,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  photo: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  fullScreenContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  photoViewerHeader: {
    height: 60,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  photoViewerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  photoViewerFooter: {
    height: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledNavButton: {
    opacity: 0.3,
  },
  fullScreenImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 120,
  },
  photoCounter: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  // Dodatkowe style dla obsługi iOS
  iosOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 4,
    borderTopLeftRadius: 8,
  },
  iosOverlayText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // Style dla bezpiecznego podglądu zdjęć
  photoIndexBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderTopLeftRadius: 8,
  },
  photoIndexText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  safePhotoViewerContainer: {
    flex: 1,
    backgroundColor: 'white',
    justifyContent: 'space-between',
  },
  staticFullScreenImage: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height - 140,
    backgroundColor: '#f9f9f9',
  },
  // Nowe style dla uproszczonego widoku zdjęć
  selectedImageContainer: {
    width: '100%',
    height: 300,
    backgroundColor: '#f9f9f9',
    marginBottom: 10,
    borderRadius: 8,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
});

export default PurchaseDetails; 