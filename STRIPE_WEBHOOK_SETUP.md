# Stripe Webhook Setup Guide

This guide will help you set up the necessary components to handle Stripe webhook events in your application.

## 1. Utworzenie tabel i funkcji pomocniczych

1. Zaloguj się do panelu Supabase pod adresem https://app.supabase.com
2. Przejdź do SQL Editor
3. Wykonaj poniższe zapytania SQL:

### Krok 1: Utwórz tabelę webhook_logs

```sql
-- Tabela do logowania zdarzeń webhook ze Stripe
CREATE TABLE IF NOT EXISTS webhook_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type TEXT NOT NULL,
  event_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Polityka bezpieczeństwa
ALTER TABLE webhook_logs ENABLE ROW LEVEL SECURITY;

-- Tylko administratorzy mogą widzieć logi webhooków
CREATE POLICY "Tylko administratorzy mogą widzieć logi webhooków" 
  ON webhook_logs FOR SELECT 
  USING (auth.role() = 'service_role' OR 
         EXISTS (SELECT 1 FROM companies WHERE companies.id = webhook_logs.company_id::UUID AND companies.owner_id = auth.uid()));

-- Dodaj uprawnienia do tabeli
GRANT ALL ON webhook_logs TO service_role;
GRANT ALL ON webhook_logs TO authenticated;
```

### Krok 2: Utwórz funkcje pomocnicze

```sql
-- Funkcja do aktualizacji statusu firmy na premium
CREATE OR REPLACE FUNCTION update_company_premium(company_uuid TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE companies 
  SET account_type = 'premium'
  WHERE id = company_uuid::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funkcja do tworzenia subskrypcji
CREATE OR REPLACE FUNCTION create_subscription(
  company_uuid TEXT,
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT
)
RETURNS VOID AS $$
DECLARE
  plan_id UUID;
  now_time TIMESTAMPTZ := now();
  period_end TIMESTAMPTZ;
BEGIN
  -- Pobierz pierwszy dostępny plan
  SELECT id INTO plan_id FROM subscription_plans LIMIT 1;
  
  -- Ustaw datę końca okresu na miesiąc od teraz
  period_end := now_time + INTERVAL '1 month';
  
  -- Utwórz wpis w tabeli company_subscriptions
  INSERT INTO company_subscriptions (
    company_id, 
    plan_id, 
    stripe_subscription_id, 
    stripe_customer_id, 
    status, 
    current_period_start, 
    current_period_end, 
    cancel_at_period_end
  ) VALUES (
    company_uuid::UUID,
    plan_id,
    COALESCE(stripe_subscription_id, 'sub_' || gen_random_uuid()),
    COALESCE(stripe_customer_id, 'cus_' || gen_random_uuid()),
    'active',
    now_time,
    period_end,
    false
  );
  
  -- Dodaj wpis do historii płatności
  INSERT INTO payment_history (
    company_id,
    subscription_id,
    amount,
    currency,
    status,
    description
  ) VALUES (
    company_uuid::UUID,
    (SELECT id FROM company_subscriptions WHERE company_id = company_uuid::UUID ORDER BY created_at DESC LIMIT 1),
    (SELECT price FROM subscription_plans WHERE id = plan_id),
    'pln',
    'succeeded',
    'Subskrypcja premium'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funkcja do zapisywania zdarzeń webhook
CREATE OR REPLACE FUNCTION log_webhook_event(
  event_type TEXT,
  event_id TEXT,
  company_uuid TEXT,
  event_data JSONB
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO webhook_logs (
    event_type,
    event_id,
    company_id,
    data
  ) VALUES (
    event_type,
    event_id,
    company_uuid,
    event_data
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 2. Wdrożenie funkcji webhook

1. Zaloguj się do CLI Supabase:
```
npx supabase login
```

2. Wdróż funkcję webhook:
```
npx supabase functions deploy stripe-webhook --no-verify-jwt
```

## 3. Aktualizacja ustawień webhook w Stripe

1. Zaloguj się do panelu Stripe pod adresem https://dashboard.stripe.com
2. Przejdź do Developers > Webhooks
3. Dodaj nowy endpoint webhook z URL:
   ```
   https://eunekpxgtwvqlphtkczi.supabase.co/functions/v1/stripe-webhook
   ```
4. Wybierz następujące zdarzenia do nasłuchiwania:
   - `checkout.session.completed`
   - `invoice.paid`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`

## 4. Testowanie webhooka

1. W panelu Stripe, przejdź do utworzonego endpointu webhook
2. Kliknij "Send test webhook"
3. Wybierz typ zdarzenia (np. `checkout.session.completed`)
4. Wyślij testowy webhook
5. Sprawdź logi Supabase, aby zweryfikować, czy webhook został odebrany:
   ```
   npx supabase functions logs stripe-webhook
   ```

## 5. Weryfikacja aktualizacji bazy danych

Po otrzymaniu prawdziwego zdarzenia Stripe (np. zakończonej sesji checkout), sprawdź czy:

1. Status firmy (`account_type`) został zaktualizowany na `premium` w tabeli `companies`
2. Nowy rekord został dodany do tabeli `company_subscriptions`
3. Nowy rekord został dodany do tabeli `payment_history`
4. Zdarzenie zostało zalogowane w tabeli `webhook_logs`

## Rozwiązywanie problemów

Jeśli webhook nie aktualizuje bazy danych:

1. Sprawdź logi funkcji Supabase:
   ```
   npx supabase functions logs stripe-webhook
   ```

2. Sprawdź, czy webhook otrzymuje zdarzenia w panelu Stripe pod "Webhooks" > Twój Endpoint > "Recent events"

3. Upewnij się, że zmienne środowiskowe Supabase są poprawnie ustawione:
   ```
   npx supabase secrets set SUPABASE_URL=https://eunekpxgtwvqlphtkczi.supabase.co
   npx supabase secrets set SUPABASE_SERVICE_ROLE_KEY=twój_klucz_service_role
   ```

4. Sprawdź, czy identyfikator firmy w polu client_reference_id pasuje do istniejącej firmy w bazie danych

5. Sprawdź, czy w tabeli `webhook_logs` pojawiają się nowe wpisy po otrzymaniu zdarzeń 