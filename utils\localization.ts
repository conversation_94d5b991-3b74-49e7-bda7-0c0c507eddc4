import { I18n } from 'i18n-js';

// Definicje tłumacz<PERSON>ń
const translations = {
  pl: {
    hello: '<PERSON><PERSON><PERSON><PERSON>',
    selectLanguage: '<PERSON><PERSON><PERSON><PERSON> język',
    settings: 'Ustawienia',
    dashboard: 'Pulpit',
    // Dodaj więcej tłumaczeń dla języka polskiego
    languagePolish: 'Polski',
    languageEnglish: '<PERSON><PERSON><PERSON>',
    languageUkrainian: '<PERSON><PERSON><PERSON><PERSON>',
    workFlow: 'WorkFlow', // Przykład tłumaczenia dla logo
    
    // Panel subskrypcji Premium - nowe tłumaczenia
    choosePlan: 'Wybierz plan',
    monthly: 'Miesięcznie',
    yearly: 'Rocznie', 
    save20Percent: 'Oszczędź 20%',
    subscriptionWillEnd: 'Twoja subskrypcja zostanie zakończona po aktualnym okresie rozliczeniowym.',
    codesUsed: 'kodów użytych',
    
    // Menu i nawigacja
    menu: 'Menu',
    employees: 'Pracownicy',
    tasks: '<PERSON><PERSON><PERSON>nia',
    myTasks: '<PERSON><PERSON>',
    schedule: 'Harmonogram',
    maintenance: 'Naprawy',
    purchases: 'Zakupy',
    admin: 'Panel administratora',
    myHours: '<PERSON><PERSON> godziny',
    hours: 'Godziny',
    logout: 'Wyloguj',
    allCompanyTasks: 'Wszystkie zlecenia firmy',
    noTasksFound: 'Nie znaleziono zleceń',
    
    // Komunikaty
    error: 'Błąd',
    errorMessage: 'Wystąpił błąd', // Dodane brakujące tłumaczenie
    errorSavingSession: 'Nie udało się zapisać sesji pracy', // Dodane brakujące tłumaczenie
    errorSavingLanguage: 'Błąd podczas zapisywania języka. Spróbuj ponownie później.',
    more: 'więcej',
    collapse: 'Zwiń',
    invalidDate: 'Nieprawidłowa data',
    noDate: 'Brak daty',
    
    // EmployeesList - nowe tłumaczenia dla sekcji godzin pracy
    employeeWorkHistory: 'Historia pracy pracowników',
    filterByEmployee: 'Filtruj według pracownika',
    dateRange: 'Zakres dat',
    searchEmployee: 'Szukaj pracownika...',
    selectStartDate: 'Wybierz datę początkową',
    selectEndDate: 'Wybierz datę końcową',
    clearFilters: 'Wyczyść filtry',
    selectAll: 'Zaznacz wszystkich',
    deselectAll: 'Odznacz wszystkich',
    employee: 'Pracownik',
    jobOrder: 'Zlecenie',
    workTime: 'Czas pracy',
    duration: 'Czas trwania',
    startAddress: 'Adres rozpoczęcia',
    endAddress: 'Adres zakończenia',
    totalWorkTime: 'Całkowity czas pracy:',
    exportToPdf: 'Eksportuj do PDF',
    loadingAddress: 'Ładowanie adresu...',
    errorFetchingWorkSessions: 'Błąd podczas pobierania historii pracy.',
    thisMonth: 'Bieżący miesiąc',
    previousMonth: 'Poprzedni miesiąc',
    
    // Tłumaczenia dla rozszerzonych kart
    dueToday: 'Na dziś',
    noTasksForToday: 'Brak zadań na dziś',
    noJobSpecified: 'Brak określonego zlecenia',
    noActiveEmployees: 'Brak aktywnych pracowników',
    unknownEmployee: 'Nieznany pracownik',
    reportedBy: 'Zgłoszone przez:',
    todaysTasks: 'Dzisiejsze zadania',
    plannedTasks: 'Zaplanowane zlecenia',
    tasksPlanningTitle: 'Zlecenia',
    tasksPlanned: 'Zaplanowane zlecenia',
    maintenanceReportsTitle: 'Zgłoszenia',
    maintenanceReportsSub: 'Awarii',
    maintenancePending: 'Oczekujące na rozwiązanie',
    purchaseRequestsTitle: 'Zgłoszenia',
    purchaseRequestsSub: 'Zakupów',
    purchasePending: 'Oczekujące wnioski',
    noMaintenanceReports: 'Brak zgłoszeń awarii',
    noPurchaseRequests: 'Brak oczekujących wniosków',
    
    // Admin Panel
    employeeRole: 'Pracownik',
    coordinatorRole: 'Koordynator',
    adminRole: 'Administrator',
    verificationCodes: 'Kody weryfikacyjne',
    remainingCodes: 'Pozostało kodów',
    of: 'z',
    generateCode: 'Wygeneruj kod',
    used: 'Wykorzystany',
    available: 'Dostępny',
    limitReached: 'Limit osiągnięty',
    verificationCodeLimitMessage: 'Osiągnąłeś limit kodów weryfikacyjnych dla swojego aktualnego planu. Ulepsz plan, aby wygenerować więcej kodów.',
    understand: 'Rozumiem',
    buyPremium: 'Wykup Premium',
    upgradeToPremium: 'Ulepsz do Premium',
    premiumActiveDescription: 'Masz aktywną subskrypcję Premium z dostępem do wszystkich funkcji.',
    premiumBenefitsDescription: 'Ulepsz do Premium, aby uzyskać dostęp do nieograniczonych kodów weryfikacyjnych i zaawansowanych funkcji.',
    independentEmployeeTitle: 'Niezależne konto pracownika',
    independentEmployeeDescription: 'Twoje konto nie jest obecnie przypisane do żadnej firmy z aktywną subskrypcją.',
    independentEmployeeInstructions: 'Skontaktuj się z administratorem firmy, aby zostać przypisanym do firmy z aktywną subskrypcją i uzyskać dostęp do wszystkich funkcji.',
    
    // Nowe tłumaczenia dla planów subskrypcji
    freePlan: 'Plan Darmowy',
    premiumFeatures: 'Funkcje Premium',
    premiumActive: 'Premium Aktywne',
    managePremium: 'Zarządzaj Subskrypcją',
    verificationCodeLimit: 'Limit kodów weryfikacyjnych',
    upgradePlan: 'Ulepsz Plan',
    managePlan: 'Zarządzaj Planem',
    
    // Plan names
    planBasic: 'Basic',
    planPro: 'Pro',
    planBusiness: 'Business',
    planBasicYearly: 'Basic (Roczny)',
    planProYearly: 'Pro (Roczny)',
    planBusinessYearly: 'Business (Roczny)',
    
    // Plan features
    basicPlanFeatures: '10 kodów weryfikacyjnych, Podstawowe wsparcie',
    proPlanFeatures: '50 kodów weryfikacyjnych, Priorytetowe wsparcie',
    businessPlanFeatures: 'Nieograniczone kody weryfikacyjne, Dedykowane wsparcie',
    
    editEmployee: 'Edytuj pracownika',
    fullName: 'Imię i nazwisko',
    role: 'Rola',
    save: 'Zapisz',
    cancel: 'Anuluj',
    delete: 'Usuń',
    confirmation: 'Potwierdzenie',
    confirmDeleteEmployee: 'Czy na pewno chcesz usunąć tego pracownika?',
    errorUpdatingEmployee: 'Błąd aktualizacji pracownika',
    failedToUpdateEmployee: 'Nie udało się zaktualizować danych pracownika',
    unexpectedErrorUpdatingEmployee: 'Wystąpił nieoczekiwany błąd podczas aktualizacji pracownika',
    failedToDeleteEmployee: 'Nie udało się usunąć pracownika',
    unexpectedError: 'Wystąpił nieoczekiwany błąd',
    errorFetchingUser: 'Nie znaleziono danych użytkownika',
    
    // Dodatkowe tłumaczenia dla AdminPanel
    errorFetchingEmployees: 'Błąd pobierania pracowników',
    exceptionInFetchEmployees: 'Wyjątek w fetchEmployees',
    errorFetchingCodes: 'Błąd pobierania kodów',
    redirectingToPremium: 'Przekierowanie do zakupu premium',
    errorGeneratingCode: 'Błąd generowania kodu',
    updatingEmployee: 'Aktualizacja pracownika',
    employeeUpdatedSuccessfully: 'Pracownik zaktualizowany pomyślnie',
    exceptionInHandleSaveEdit: 'Wyjątek w handleSaveEdit',
    errorDeletingEmployee: 'Błąd usuwania pracownika',
    exceptionInHandleDeleteEmployee: 'Wyjątek w handleDeleteEmployee',
    copyCode: 'Kopiuj kod',
    
    // Panel subskrypcji Premium
    subscriptionManagement: 'Zarządzanie subskrypcją',
    loadingSubscription: 'Ładowanie informacji o subskrypcji...',
    activeSubscription: 'Aktywna subskrypcja',
    availablePlans: 'Dostępne plany',
    currentPlan: 'Aktualny plan',
    plan: 'Plan',
    status: 'Status',
    periodStart: 'Początek okresu',
    periodEnd: 'Koniec okresu',
    price: 'Cena',
    perMonth: '/miesiąc',
    perYear: '/rok',
    cancelSubscription: 'Anuluj subskrypcję',
    cancelSubscriptionConfirmation: 'Czy na pewno chcesz anulować swoją subskrypcję? Dostęp do funkcji premium będzie aktywny do końca opłaconego okresu rozliczeniowego.',
    keepSubscription: 'Zachowaj subskrypcję',
    cancelNow: 'Anuluj teraz',
    subscriptionCancelled: 'Twoja subskrypcja została anulowana. Będziesz mieć dostęp do funkcji premium do końca okresu rozliczeniowego.',
    subscriptionEndsOn: 'Twoja subskrypcja wygaśnie dnia:',
    resourceUsage: 'Wykorzystanie zasobów',
    total: 'Całkowicie',
    used: 'Wykorzystane',
    available: 'Dostępne',
    paymentHistory: 'Historia płatności',
    subscribe: 'Subskrybuj',
    subscriptionError: 'Wystąpił błąd podczas tworzenia subskrypcji',
    cancelSubscriptionError: 'Wystąpił błąd podczas anulowania subskrypcji',
    ok: 'OK',
    success: 'Sukces',
    
    // Statusy subskrypcji
    active: 'Aktywna',
    pastDue: 'Zaległa płatność',
    canceled: 'Anulowana',
    trial: 'Wersja próbna',
    
    // Karty na dashboardzie
    activeEmployees: 'Aktywni pracownicy',
    currentlyWorking: 'Obecnie pracuje',
    upcomingTasks: 'Nadchodzące zlecenia',
    plannedTasks: 'Zaplanowane zlecenia',
    maintenanceReports: 'Zgłoszenia awarii',
    purchaseRequests: 'Wnioski zakupowe',
    quickActions: 'Szybkie akcje',
    noUpcomingTasks: 'Brak nadchodzących zleceń',
    todaysTasks: 'Dzisiejsze zadania',
    dueToday: 'Na dziś',
    noTasksForToday: 'Brak zadań na dziś',
    noJobSpecified: 'Brak określonego zlecenia',
    noActiveEmployees: 'Brak aktywnych pracowników',
    unknownEmployee: 'Nieznany pracownik',
    reportedBy: 'Zgłoszone przez:',
    
    // Statusy
    statusPending: 'Oczekujące',
    statusInProgress: 'W trakcie',
    statusCompleted: 'Zakończone',
    pending: 'Oczekujące',
    inProgress: 'W trakcie',
    completed: 'Zakończone',
    cancelled: 'Anulowane',
    
    // Przyciski akcji
    startWork: 'Rozpocznij pracę',
    endWork: 'Zakończ pracę',
    stopWork: 'Zakończ pracę', // Dodane tłumaczenie dla przycisku
    completeTask: 'Zakończ zadanie',
    addWorkDay: 'Dodaj dzień pracy',
    reportIssue: 'Zgłoś awarię',
    addPurchase: 'Dodaj zakup',
    newTask: 'Nowe zadanie',
    addTeam: 'Dodaj zespół',
    addWorkDayTitle: 'Dodaj dzień pracy',
    submitAddWorkDay: 'Dodaj dzień pracy',
    
    // Dialog Rozpocznij pracę (Quick Action) - DODANE
    rozpocznijprace: 'Rozpocznij pracę',
    podajnazwezlecenia: 'Podaj nazwę zlecenia:',
    wprowadznazwezlecenia: 'Wprowadź nazwę zlecenia',
    anuluj: 'Anuluj',
    rozpocznij: 'Rozpocznij',
    finish: 'Zakończ',
    start: 'Rozpocznij', // Added missing translation for start button
    enterJobOrderName: 'Podaj nazwę zlecenia:', // Added missing translation for enterJobOrderName
    
    // Moje godziny pracy i aktywna sesja - DODANE
    activeSessionTitle: 'Aktywna sesja pracy', // Dodane brakujące tłumaczenie
    
    // Priorytety
    priorityLow: 'Niski',
    priorityMedium: 'Średni',
    priorityHigh: 'Wysoki',
    priorityCritical: 'Krytyczny',
    
    // Statusy dla zgłoszeń awarii
    maintenanceStatusReported: 'Zgłoszony',
    maintenanceStatusInProgress: 'W trakcie',
    maintenanceStatusResolved: 'Rozwiązany',
    maintenanceStatusCanceled: 'Anulowany',
    maintenanceStatusToCheck: 'Do sprawdzenia',
    maintenanceStatusPending: 'Oczekujący',
    
    // Statusy dla zakupów
    purchaseStatusPending: 'Oczekujący',
    purchaseStatusApproved: 'Zatwierdzony',
    purchaseStatusOrdered: 'Zamówiony',
    purchaseStatusDelivered: 'Dostarczony',
    purchaseStatusCanceled: 'Anulowany',
    
    // EmployeesList
    employeeWorkHistory: 'Historia pracy pracowników',
    filterByEmployee: 'Filtruj po pracowniku',
    dateRange: 'Zakres dat',
    searchEmployee: 'Wyszukaj pracownika...',
    selectStartDate: 'Wybierz datę początkową',
    selectEndDate: 'Wybierz datę końcową',
    apply: 'Zastosuj',
    clearFilters: 'Wyczyść filtry',
    selectAll: 'Zaznacz wszystkich',
    deselectAll: 'Odznacz wszystkich',
    date: 'Data',
    employee: 'Pracownik',
    jobOrder: 'Zlecenie',
    workTime: 'Czas pracy',
    duration: 'Czas',
    startAddress: 'Start Adres',
    endAddress: 'Stop Adres',
    totalWorkTime: 'Łączny czas pracy:',
    exportToPdf: 'Eksportuj do PDF',
    loadingAddress: 'Ładowanie adresu...',
    errorFetchingWorkSessions: 'Nie udało się pobrać historii pracy.',
    all: 'Wszystkie',
    thisMonth: 'Ten miesiąc',
    previousMonth: 'Poprzedni miesiąc',
    selectDateRange: 'Wybierz zakres dat',
    clear: 'Wyczyść',
    dateFrom: 'Od',
    dateTo: 'Do',
    
    // Zadania (Tasks) - nowe tłumaczenia
    taskDetails: 'Szczegóły zlecenia',
    clientName: 'Nazwa klienta',
    address: 'Adres',
    workScope: 'Zakres prac',
    startTime: 'Czas rozpoczęcia',
    endTime: 'Czas zakończenia',
    assignedEmployees: 'Przypisani pracownicy',
    additionalInfo: 'Dodatkowe informacje',
    photos: 'Zdjęcia',
    photosBefore: 'Zdjęcia przed',
    photosAfter: 'Zdjęcia po',
    addTask: 'Dodaj zlecenie',
    editTask: 'Edytuj zlecenie',
    saveTask: 'Zapisz zlecenie',
    cancelTask: 'Anuluj',
    deleteTask: 'Usuń zlecenie',
    search: 'Szukaj',
    filters: 'Filtry',
    sortBy: 'Sortuj wg',
    filterClient: 'Filtruj po kliencie',
    filterAddress: 'Filtruj po adresie',
    filterStatus: 'Filtruj po statusie',
    selectEmployee: 'Wybierz pracownika',
    requiredField: 'Pole wymagane',
    taskList: 'Lista zleceń',
    activeEmployeesCount: 'Liczba aktywnych pracowników',
    taskScope: 'Zakres prac',
    searchTasks: 'Szukaj zleceń...',
    jobName: 'Nazwa zlecenia',
    
    // Dodatkowe tłumaczenia dla zadań
    clearAllFilters: 'Wyczyść wszystkie filtry',
    dateRangeSelect: 'Wybierz zakres dat',
    client: 'Klient',
    status: 'Status',
    loading: 'Ładowanie...',
    loadingTask: 'Ładowanie zadania...',
    failedToLoadTask: 'Nie udało się załadować zadania',
    back: 'Wróć',
    basicInfo: 'Informacje podstawowe',
    startedAt: 'Rozpoczęto',
    finishedAt: 'Zakończono',
    taskDuration: 'Czas trwania zadania',
    startTask: 'Rozpocznij zadanie',
    noEmployees: 'Brak przypisanych pracowników',
    activeEmployee: 'Aktywny pracownik',
    activateEmployee: 'Aktywuj pracownika',
    email: 'Email',
    phone: 'Telefon',
    activityTime: 'Czas aktywności',
    photosBeforeWork: 'Zdjęcia przed wykonaniem',
    photosAfterWork: 'Zdjęcia po wykonaniu',
    saveChanges: 'Zapisz zmiany',
    createTask: 'Utwórz zlecenie',
    selectEmployees: 'Wybierz pracowników',
    clientNameLabel: 'Zleceniodawca*',
    taskAddressLabel: 'Adres zlecenia*',
    workScopeLabel: 'Zakres prac*',
    additionalInfoLabel: 'Dodatkowe informacje',
    requiredFields: '* Pola wymagane',
    
    // Szczegóły sesji pracy
    workSessionDetails: 'Szczegóły sesji pracy',
    selectedSessionDetails: 'Szczegóły wybranej sesji',
    manuallyAddedWorkTime: 'Czas pracy został dodany ręcznie',
    manuallyEditedWorkTime: 'Czas pracy był edytowany ręcznie',
    jobOrderLabel: 'Zlecenie:',
    startTimeLabel: 'Czas rozpoczęcia:',
    endTimeLabel: 'Czas zakończenia:',
    workTimeLabel: 'Czas pracy:',
    startLocationLabel: 'Lokalizacja rozpoczęcia:',
    endLocationLabel: 'Lokalizacja zakończenia:',
    notesLabel: 'Notatki:',
    noRecordsFound: 'Nie znaleziono szczegółów sesji.',
    noData: 'Brak danych',
    editWorkSession: 'Edytuj sesję pracy',
    dateLabel: 'Data rozpoczęcia',
    startDateLabel: 'Data rozpoczęcia', // Dodane dla spójności
    endDateLabel: 'Data zakończenia',
    startTimeInputLabel: 'Godzina rozpoczęcia',
    endTimeInputLabel: 'Godzina zakończenia',
    jobOrderInputLabel: 'Zlecenie/Opis',
    notesInputLabel: 'Notatki (opcjonalne)',
    calculatedWorkTime: 'Obliczony czas pracy:',
    workTimeUpdated: 'Czas pracy został zaktualizowany',
    workSessionStarted: 'Rozpoczęto pracę pomyślnie',
    workSessionEnded: 'Twoja sesja pracy została zakończona',
    confirmEndWork: 'Czy na pewno chcesz zakończyć swoją pracę?',
    confirmCompleteTask: 'Czy na pewno chcesz zakończyć aktywność w zadaniu "{{taskName}}"?',
    noActiveTasksToComplete: 'Nie masz obecnie aktywnych zadań do zakończenia.',
    dbError: 'Błąd bazy danych:',
    errorStartingWork: 'Błąd podczas rozpoczynania pracy',
    invalidTimeFormat: 'Nieprawidłowy format czasu. Użyj formatu HH:MM',
    endTimeMustBeLater: 'Czas zakończenia musi być późniejszy niż czas rozpoczęcia',
    errorOpeningForm: 'Nie można otworzyć formularza edycji. Spróbuj ponownie.',
    unknownStatus: 'Nieznany',
    activeStatus: 'Aktywne',
    noDate: 'Brak daty',
    enterDescription: 'Opis pracy lub nazwa zlecenia',
    additionalNotes: 'Dodatkowe informacje...',
    selectDate: 'Wybierz datę',
    chooseTime: 'Wybierz czas',
    jobNamePlaceholder: 'Wprowadź nazwę zlecenia',
    descriptionPlaceholder: 'Podaj nazwę zadania lub projektu, nad którym pracowałeś',
    startTimeInput: 'Czas rozpoczęcia',
    endTimeInput: 'Czas zakończenia',
    data: 'Data',
    
    // Task Details additional translations
    featureInDevelopment: 'Funkcja w trakcie implementacji',
    activityInProgress: 'Aktywność w toku',
    completedActivity: 'Zakończona aktywność',
    
    // Task Edit Form translations
    taskNotFound: 'Nie znaleziono zadania',
    errorLoadingData: 'Wystąpił błąd podczas pobierania danych',
    galleryPermissionsNeeded: 'Potrzebujemy uprawnień do galerii, aby dodać zdjęcia!',
    errorPickingImage: 'Wystąpił błąd podczas wybierania zdjęcia',
    pleaseCompleteAllRequiredFields: 'Proszę wypełnić wszystkie wymagane pola',
    confirmSaveChanges: 'Potwierdź zapisanie zmian',
    areYouSureYouWantToSaveChanges: 'Czy na pewno chcesz zapisać zmiany w zadaniu?',
    errorSavingChanges: 'Wystąpił nieoczekiwany błąd podczas zapisywania zmian',
    failedToUpdateTask: 'Nie udało się zaktualizować zadania',
    taskUpdated: 'Zadanie zostało zaktualizowane',
    selectStartTime: 'Wybierz godzinę rozpoczęcia',
    adding: 'Dodawanie...',
    addPhoto: 'Dodaj zdjęcie',
    photosWillBeSaved: 'Zdjęcia zostaną zapisane jako linki w bazie danych',
    selectedEmployees: 'Wybrani pracownicy',
    
    // Maintenance module translations
    maintenanceTitle: 'Tytuł',
    maintenanceSearchPlaceholder: 'Szukaj zgłoszeń...',
    maintenanceDate: 'Data',
    maintenanceStatus: 'Status',
    maintenancePriority: 'Priorytet',
    maintenanceAllFilter: 'Wszystkie',
    maintenanceReportedFilter: 'Zgłoszone',
    maintenanceInProgressFilter: 'W realizacji',
    maintenanceResolvedFilter: 'Rozwiązane',
    maintenanceToCheckFilter: 'Do sprawdzenia',
    maintenanceCanceledFilter: 'Anulowane',
    maintenanceActiveFilter: 'Aktywne',
    maintenanceAddReport: 'Dodaj zgłoszenie',
    maintenanceReportsList: 'Lista zgłoszeń',
    maintenanceNoReports: 'Brak zgłoszeń awarii do wyświetlenia',
    maintenanceShortTitlePlaceholder: 'Krótki tytuł opisujący awarię',
    maintenanceDescription: 'Opis',
    maintenanceDetailedDescription: 'Szczegółowy opis problemu',
    maintenanceLocation: 'Lokalizacja',
    maintenanceLocationPlaceholder: 'Gdzie znajduje się awaria?',
    maintenanceReporterName: 'Twoje imię i nazwisko',
    maintenanceContactInfo: 'Dane kontaktowe',
    maintenanceContactInfoPlaceholder: 'Telefon lub inne dane kontaktowe',
    maintenancePhotos: 'Zdjęcia awarii',
    maintenanceAddPhoto: 'Dodaj zdjęcie',
    maintenancePhotosWillBeSaved: 'Zdjęcia zostaną zapisane jako linki w bazie danych',
    maintenanceCreateReport: 'Utwórz zgłoszenie',
    maintenanceFillRequiredFields: 'Wypełnij wszystkie wymagane pola',
    maintenanceReportCreated: 'Zgłoszenie awarii zostało utworzone',
    maintenanceSaveError: 'Błąd zapisu',
    maintenanceFailedToSave: 'Nie udało się zapisać zgłoszenia. Sprawdź połączenie z bazą danych i uprawnienia.',
    maintenanceSelectDateRange: 'Wybierz zakres dat',
    maintenanceFromDate: 'Od',
    maintenanceToDate: 'Do',
    maintenanceClear: 'Wyczyść',
    maintenanceApply: 'Zastosuj',
    maintenanceFilter: 'Filtruj',
    maintenanceDateFilter: 'Data',
    maintenanceAddReportHeader: 'Dodaj zgłoszenie awarii',
    maintenanceNoPermissions: 'Brak uprawnień',
    maintenancePhotoAccessNeeded: 'Potrzebujemy dostępu do galerii zdjęć',
    maintenanceImagePickError: 'Wystąpił problem podczas wybierania zdjęcia',
    maintenanceUnexpectedError: 'Wystąpił nieoczekiwany błąd podczas tworzenia zgłoszenia',
    
    // Maintenance details translations
    maintenanceDetailsHeader: 'Szczegóły zgłoszenia awarii',
    maintenancePriorityLabel: 'Priorytet:',
    maintenanceLocationLabel: 'Lokalizacja:',
    maintenanceReporterLabel: 'Zgłaszający:',
    maintenanceReportDateLabel: 'Data zgłoszenia:',
    maintenanceReportTimeLabel: 'Godzina zgłoszenia:',
    maintenanceRepairedByLabel: 'Naprawę wykonał:',
    maintenanceRepairStartLabel: 'Rozpoczęcie naprawy:',
    maintenanceRepairEndLabel: 'Zakończenie naprawy:',
    maintenanceResolvedTimeLabel: 'Czas rozwiązania:',
    maintenanceResolvedByLabel: 'Rozwiązane przez:',
    maintenanceCanceledTimeLabel: 'Czas anulowania:',
    maintenanceCanceledByLabel: 'Anulowane przez:',
    maintenanceDescriptionLabel: 'Opis:',
    maintenancePhotosLabel: 'Zdjęcia:',
    maintenanceUnknownUser: 'Nieznany',
    maintenanceStartRepairButton: 'Rozpocznij naprawę',
    maintenanceCompleteRepairButton: 'Zakończ naprawę',
    maintenanceFinalizeButton: 'Gotowe',
    maintenanceCancelButton: 'Anuluj zgłoszenie',
    
    // Purchases module translations
    purchasesTitle: 'Tytuł',
    purchasesSearchPlaceholder: 'Szukaj zakupów...',
    purchasesDate: 'Data',
    purchasesStatus: 'Status',
    purchasesPriority: 'Priorytet',
    purchasesAllFilter: 'Wszystkie',
    purchasesPendingFilter: 'Oczekujące',
    purchasesApprovedFilter: 'Zatwierdzone',
    purchasesOrderedFilter: 'Zamówione',
    purchasesDeliveredFilter: 'Dostarczone',
    purchasesCanceledFilter: 'Anulowane',
    purchasesAddPurchase: 'Dodaj zakup',
    purchasesPurchasesList: 'Lista zakupów',
    purchasesNoPurchases: 'Brak zakupów do wyświetlenia',
    purchasesLoadingPurchases: 'Ładowanie zakupów...',
    purchasesEstimatedCost: 'Szacowany koszt',
    purchasesActualCost: 'Rzeczywisty koszt',
    purchasesQuantity: 'Ilość',
    purchasesCategory: 'Kategoria',
    purchasesDescription: 'Opis',
    purchasesAdditionalNotes: 'Dodatkowe uwagi',
    purchasesProductPhotos: 'Zdjęcia produktu',
    purchasesCreatePurchase: 'Utwórz wniosek zakupowy',
    purchasesFillRequiredFields: 'Wypełnij wszystkie wymagane pola',
    purchasesPurchaseCreated: 'Wniosek zakupowy został utworzony',
    purchasesSaveError: 'Błąd zapisu',
    purchasesFailedToSave: 'Nie udało się zapisać wniosku. Sprawdź połączenie z bazą danych i uprawnienia.',
    purchasesSelectDateRange: 'Filtruj według daty',
    purchasesFromDate: 'Od',
    purchasesToDate: 'Do',
    purchasesApply: 'Zastosuj',
    purchasesFilter: 'Filtruj',
    purchasesDateFilter: 'Data',
    purchasesNoPermissions: 'Brak uprawnień',
    purchasesPhotoAccessNeeded: 'Potrzebujemy dostępu do galerii zdjęć',
    purchasesImagePickError: 'Wystąpił problem podczas wybierania zdjęcia',
    purchasesImageLimitReached: 'Możesz dodać maksymalnie 3 zdjęcia do jednego wniosku. Usuń jakieś zdjęcie, aby dodać nowe.',
    purchasesUnexpectedError: 'Wystąpił nieoczekiwany błąd podczas tworzenia wniosku',
    purchasesSortBy: 'Sortuj wg:',
    purchasesSortByDate: 'Data',
    purchasesSortByTitle: 'Tytuł',
    purchasesSortByCategory: 'Kategoria',
    purchasesSortByPrice: 'Cena',
    purchasesSortByStatus: 'Status',
    purchasesSortByPriority: 'Priorytet',
    purchasesRequesterLabel: 'Wnioskujący:',
    purchasesRequestDateLabel: 'Data wniosku:',
    purchasesApproverLabel: 'Zatwierdził:',
    purchasesApprovalDateLabel: 'Data zatwierdzenia:',
    purchasesOrderDateLabel: 'Data zamówienia:',
    purchasesDeliveryDateLabel: 'Data dostawy:',
    purchasesCancellationDateLabel: 'Data anulowania:',
    purchasesCanceledByLabel: 'Anulowane przez:',
    purchasesUnknownUser: 'Nieznany',
    purchasesCategoryLabel: 'Kategoria:',
    purchasesAddCategory: 'Dodaj kategorię',
    purchasesNewCategoryName: 'Nazwa nowej kategorii',
    purchasesCreate: 'Utwórz',
    purchasesCancel: 'Anuluj',
    purchasesTitlePlaceholder: 'Podaj tytuł zakupu',
    purchasesDescriptionPlaceholder: 'Podaj szczegółowy opis zakupu',
    purchasesCostPlaceholder: '0,00',
    purchasesQuantityPlaceholder: 'Podaj ilość',
    purchasesNotesPlaceholder: 'Dodatkowe informacje, które mogą być przydatne',
    purchasesAddPhoto: 'Dodaj zdjęcie',
    purchasesPhotosWillBeSaved: 'Zdjęcia zostaną zapisane jako linki w bazie danych',
    purchasesRequiredFields: '* Pola wymagane',
    purchasesAddNewPurchase: 'Dodaj nowy wniosek o zakup',
    purchasesAddNewCategory: 'Dodaj nową kategorię',
    purchasesSelectCategory: 'Wybierz kategorię',
    addCategoryName: 'Nazwa nowej kategorii',
    categoryNamePlaceholder: 'Wpisz nazwę kategorii...',
    addCategory: 'Dodaj kategorię',
    noCategoriesFound: 'Nie znaleziono kategorii',
    pleaseSelectCategory: 'Wybierz kategorię lub dodaj nową',
    purchasesDetailsHeader: 'Szczegóły zgłoszenia zakupów',
    purchasesProductName: 'Nazwa produktu',
    purchasesNotes: 'Uwagi',
    purchasesTitle: 'Tytuł', // Added Polish translation
    purchasesPhotos: 'Zdjęcia', // Added missing Polish translation
    purchasesTitlePlaceholder: 'Podaj tytuł zakupu',
    purchasesDescriptionPlaceholder: 'Podaj szczegółowy opis zakupu',
    purchasesCostPlaceholder: '0,00',
    purchasesQuantityPlaceholder: 'Podaj ilość',
    purchasesNotesPlaceholder: 'Dodatkowe informacje, które mogą być przydatne',
    purchasesAddPhoto: 'Dodaj zdjęcie',
    purchasesPhotosWillBeSaved: 'Zdjęcia zostaną zapisane jako linki w bazie danych',
    purchasesRequiredFields: '* Pola wymagane',
    purchasesAddNewPurchase: 'Dodaj nowy wniosek o zakup',
    purchasesAddNewCategory: 'Dodaj nową kategorię',
    purchasesSelectCategory: 'Wybierz kategorię',
    addCategoryName: 'Nazwa nowej kategorii',
    categoryNamePlaceholder: 'Wpisz nazwę kategorii...',
    addCategory: 'Dodaj kategorię',
    noCategoriesFound: 'Nie znaleziono kategorii',
    pleaseSelectCategory: 'Wybierz kategorię lub dodaj nową',
    purchasesDetailsHeader: 'Szczegóły zgłoszenia zakupów',
    purchasesProductName: 'Nazwa produktu',
    purchasesNotes: 'Uwagi',
    // Remove the duplicate entry: purchasesTitle: 'Title', // Added this line
    purchasesTitlePlaceholder: 'Enter purchase title',
    purchasesDescriptionPlaceholder: 'Enter detailed purchase description',
    
    // Dialogi i powiadomienia
    confirm: 'Potwierdź',
    success: 'Sukces',
    ok: 'OK',
    quantity: 'Ilość',
    unknown: 'Nieznany',
    
    // --- General/Shared ---
    requiredFields: '* Pola wymagane',
    loading: 'Ładowanie...',
    
    // Employee Order Details Screen
    employeeOrderDetails: 'Szczegóły zlecenia pracownika',
    employeeData: 'Dane pracownika',
    activeWorkSession: 'Aktywna sesja pracy',
    
    // Purchase filter buttons
    purchasesAllFilter: 'Wszystkie',
    purchasesPendingFilter: 'Oczekujące',
    purchasesApprovedFilter: 'Zatwierdzone',
    purchasesOrderedFilter: 'Zamówione',
    purchasesDeliveredFilter: 'Dostarczone',
    purchasesCanceledFilter: 'Anulowane',
    
    // Purchase date filter
    purchasesDateFilter: 'Data',
    purchasesSelectDateRange: 'Wybierz zakres dat',
    purchasesFromDate: 'Od',
    purchasesToDate: 'Do',
    purchasesApply: 'Zastosuj',
    purchasesClear: 'Wyczyść',
    
    // Admin Panel
    employeeRole: 'Employee',
    coordinatorRole: 'Coordinator',
    adminRole: 'Administrator',
    verificationCodes: 'Verification Codes',
    remainingCodes: 'Remaining codes',
    of: 'of',
    generateCode: 'Generate Code',
    used: 'Used',
    available: 'Available',
    limitReached: 'Limit Reached',
    verificationCodeLimitMessage: 'You have reached the verification code limit for your current plan. Upgrade to generate more codes.',
    understand: 'I Understand',
    buyPremium: 'Buy Premium',
    editEmployee: 'Edit Employee',
    fullName: 'Full Name',
    role: 'Role',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    confirmation: 'Confirmation',
    confirmDeleteEmployee: 'Are you sure you want to delete this employee?',
    errorUpdatingEmployee: 'Error updating employee',
    failedToUpdateEmployee: 'Failed to update employee data',
    unexpectedErrorUpdatingEmployee: 'An unexpected error occurred while updating the employee',
    failedToDeleteEmployee: 'Failed to delete employee',
    unexpectedError: 'An unexpected error occurred',
    errorFetchingUser: 'User data not found',
    
    // Subscription plans
    freePlan: 'Free Plan',
    premiumFeatures: 'Premium Features',
    premiumActive: 'Premium Active',
    managePremium: 'Manage Subscription',
    upgradeToPremium: 'Upgrade to Premium',
    verificationCodeLimit: 'Verification Code Limit',
    upgradePlan: 'Upgrade Plan',
    managePlan: 'Manage Plan',
    
    // Plan names
    planBasic: 'Basic',
    planPro: 'Pro',
    planBusiness: 'Business',
    planBasicYearly: 'Basic (Yearly)',
    planProYearly: 'Pro (Yearly)',
    planBusinessYearly: 'Business (Yearly)',
    
    // Plan features
    basicPlanFeatures: '10 verification codes, Basic support',
    proPlanFeatures: '50 verification codes, Priority support',
    businessPlanFeatures: 'Unlimited verification codes, Dedicated support',
    
    // Additional translations for AdminPanel
    errorFetchingEmployees: 'Error fetching employees',
    exceptionInFetchEmployees: 'Exception in fetchEmployees',
    errorFetchingCodes: 'Error fetching codes',
    errorGeneratingCode: 'Error generating code',
    copyCode: 'Copy code',

    // Panel subskrypcji Premium - dodane nowe tłumaczenia
    choosePlan: 'Wybierz plan',
    monthly: 'Miesięcznie',
    yearly: 'Rocznie', 
    save20Percent: 'Oszczędź 20%',
    manageSubscription: 'Zarządzaj subskrypcją',
    subscriptionWillEnd: 'Twoja subskrypcja zostanie zakończona po aktualnym okresie rozliczeniowym.',
    codesUsed: 'kodów użytych',
    month: 'miesiąc',
    year: 'rok',
    adminPanel: 'Panel administratora',
    enjoyPremiumFeatures: 'Korzystaj ze wszystkich funkcji premium i rozszerzonych limitów',
    upgradeToPremiumDesc: 'Ulepsz do wersji premium, aby uzyskać więcej funkcji',
    basicPlan: 'Plan Podstawowy',
    confirmCancelSubscription: 'Czy na pewno chcesz anulować subskrypcję? Będzie ona aktywna do końca opłaconego okresu.',
    subscriptionCancelledSuccess: 'Subskrypcja została anulowana. Będzie aktywna do końca opłaconego okresu.',
    subscriptionCancelledError: 'Wystąpił błąd podczas anulowania subskrypcji. Spróbuj ponownie później.',
    subscriptionActive: 'Subskrypcja aktywna',
    subscriptionCancelledEndDate: 'Subskrypcja anulowana (aktywna do {{date}})',
    cancelling: 'Anulowanie...',
    changePlan: 'Zmień plan',

    // Employee Status Management
    employeeStatusManagement: 'Zarządzanie statusami pracowników',
    employeeStatusActive: 'Aktywny',
    employeeStatusInactive: 'Nieaktywny',
    employeeStatusLastChange: 'Ostatnia zmiana',
    employeeStatusChangeLimit: 'Możesz zmienić status pracownika raz na 7 dni',
    employeeStatusSubscriptionLimit: 'Liczba aktywnych pracowników jest ograniczona przez plan subskrypcji',
    employeeStatusActivate: 'Aktywuj',
    employeeStatusDeactivate: 'Dezaktywuj',
    employeeStatusChangeSuccess: 'Status pracownika został zmieniony',
    employeeStatusChangeError: 'Wystąpił błąd podczas zmiany statusu pracownika',
    employeeStatusChangeTooSoon: 'Nie możesz zmienić statusu tego pracownika. Musisz poczekać 7 dni od ostatniej zmiany.',
    employeeStatusSubscriptionLimitReached: 'Nie możesz aktywować więcej pracowników. Osiągnięto limit dla twojego planu subskrypcji.',
  },
  en: {
    hello: 'Hello',
    selectLanguage: 'Select language',
    settings: 'Settings',
    dashboard: 'Dashboard',
    // Add more English translations
    languagePolish: 'Polish',
    languageEnglish: 'English',
    languageUkrainian: 'Ukrainian',
    workFlow: 'WorkFlow', // Example translation for logo
    
    // Menu and navigation
    menu: 'Menu',
    employees: 'Employees',
    tasks: 'Tasks',
    myTasks: 'My Tasks',
    schedule: 'Schedule',
    maintenance: 'Maintenance',
    purchases: 'Purchases',
    admin: 'Admin Panel',
    myHours: 'My Hours',
    hours: 'Hours',
    logout: 'Logout',
    allCompanyTasks: 'All Company Tasks',
    noTasksFound: 'No tasks found',
    
    // Messages
    error: 'Error',
    errorMessage: 'An error occurred', // Added missing translation
    errorSavingSession: 'Failed to save work session', // Added missing translation
    errorSavingLanguage: 'Error saving language. Please try again later.',
    more: 'more',
    collapse: 'collapse',
    
    // EmployeesList - new translations for work hours section
    employeeWorkHistory: 'Employee Work History',
    filterByEmployee: 'Filter by employee',
    dateRange: 'Date range',
    searchEmployee: 'Search employee...',
    selectStartDate: 'Select start date',
    selectEndDate: 'Select end date',
    clearFilters: 'Clear filters',
    selectAll: 'Select all',
    deselectAll: 'Deselect all',
    employee: 'Employee',
    jobOrder: 'Job Order',
    workTime: 'Work Time',
    duration: 'Duration',
    startAddress: 'Start Address',
    endAddress: 'End Address',
    totalWorkTime: 'Total Work Time:',
    exportToPdf: 'Export to PDF',
    loadingAddress: 'Loading address...',
    errorFetchingWorkSessions: 'Failed to fetch work history.',
    thisMonth: 'This Month',
    previousMonth: 'Previous Month',
    
    // Translations for expanded cards
    dueToday: 'Due Today',
    noTasksForToday: 'No tasks for today',
    noJobSpecified: 'No job specified',
    noActiveEmployees: 'No active employees',
    unknownEmployee: 'Unknown employee',
    reportedBy: 'Reported by:',
    todaysTasks: 'Today\'s Tasks',
    plannedTasks: 'Planned Tasks',
    tasksPlanningTitle: 'Tasks',
    tasksPlanned: 'Planned tasks',
    maintenanceReportsTitle: 'Reports',
    maintenanceReportsSub: 'Maintenance',
    maintenancePending: 'Pending resolution',
    purchaseRequestsTitle: 'Requests',
    purchaseRequestsSub: 'Purchases',
    purchasePending: 'Pending requests',
    noMaintenanceReports: 'No maintenance reports',
    noPurchaseRequests: 'No pending purchase requests',
    
    // Admin Panel
    employeeRole: 'Employee',
    coordinatorRole: 'Coordinator',
    adminRole: 'Administrator',
    verificationCodes: 'Verification Codes',
    remainingCodes: 'Remaining codes',
    of: 'of',
    generateCode: 'Generate Code',
    used: 'Used',
    available: 'Available',
    limitReached: 'Limit Reached',
    verificationCodeLimitMessage: 'You have reached the verification code limit for your current plan. Upgrade to generate more codes.',
    understand: 'I Understand',
    buyPremium: 'Buy Premium',
    upgradeToPremium: 'Upgrade to Premium',
    premiumActiveDescription: 'You have an active Premium subscription with access to all features.',
    premiumBenefitsDescription: 'Upgrade to Premium to get unlimited verification codes and advanced features.',
    independentEmployeeTitle: 'Independent Employee Account',
    independentEmployeeDescription: 'Your account is not currently assigned to any company with an active subscription.',
    independentEmployeeInstructions: 'Contact your company administrator to be assigned to a company with an active subscription and gain access to all features.',
    
    // Subscription plans
    freePlan: 'Free Plan',
    premiumFeatures: 'Premium Features',
    premiumActive: 'Premium Active',
    managePremium: 'Manage Subscription',
    verificationCodeLimit: 'Verification Code Limit',
    upgradePlan: 'Upgrade Plan',
    managePlan: 'Manage Plan',
    
    // Plan names
    planBasic: 'Basic',
    planPro: 'Pro',
    planBusiness: 'Business',
    planBasicYearly: 'Basic (Yearly)',
    planProYearly: 'Pro (Yearly)',
    planBusinessYearly: 'Business (Yearly)',
    
    // Plan features
    basicPlanFeatures: '10 verification codes, Basic support',
    proPlanFeatures: '50 verification codes, Priority support',
    businessPlanFeatures: 'Unlimited verification codes, Dedicated support',
    
    editEmployee: 'Edit Employee',
    fullName: 'Full Name',
    role: 'Role',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    confirmation: 'Confirmation',
    confirmDeleteEmployee: 'Are you sure you want to delete this employee?',
    errorUpdatingEmployee: 'Error updating employee',
    failedToUpdateEmployee: 'Failed to update employee data',
    unexpectedErrorUpdatingEmployee: 'An unexpected error occurred while updating the employee',
    failedToDeleteEmployee: 'Failed to delete employee',
    unexpectedError: 'An unexpected error occurred',
    errorFetchingUser: 'User data not found',
    
    // Additional translations for AdminPanel
    errorFetchingEmployees: 'Error fetching employees',
    exceptionInFetchEmployees: 'Exception in fetchEmployees',
    errorFetchingCodes: 'Error fetching codes',
    redirectingToPremium: 'Redirecting to premium purchase',
    errorGeneratingCode: 'Error generating code',
    updatingEmployee: 'Updating employee',
    employeeUpdatedSuccessfully: 'Employee updated successfully',
    exceptionInHandleSaveEdit: 'Exception in handleSaveEdit',
    errorDeletingEmployee: 'Error deleting employee',
    exceptionInHandleDeleteEmployee: 'Exception in handleDeleteEmployee',
    copyCode: 'Copy code',
    
    // Dashboard cards
    activeEmployees: 'Active Employees',
    currentlyWorking: 'Currently Working',
    upcomingTasks: 'Upcoming Tasks',
    plannedTasks: 'Planned Tasks',
    maintenanceReports: 'Maintenance Reports',
    purchaseRequests: 'Purchase Requests',
    quickActions: 'Quick Actions',
    noUpcomingTasks: 'No upcoming tasks',
    
    // Statuses
    statusPending: 'Pending',
    statusInProgress: 'In Progress',
    statusCompleted: 'Completed',
    pending: 'Pending',
    inProgress: 'In Progress',
    completed: 'Completed',
    cancelled: 'Cancelled',
    
    // Action buttons
    startWork: 'Start Work',
    endWork: 'End Work',
    stopWork: 'End Work', // Added translation for button
    completeTask: 'Complete Task',
    addWorkDay: 'Add Work Day',
    reportIssue: 'Report Issue',
    addPurchase: 'Add Purchase',
    newTask: 'New Task',
    addTeam: 'Add Team',
    addWorkDayTitle: 'Add Work Day',
    submitAddWorkDay: 'Add Work Day',
    
    // Start Work Dialog (Quick Action) - ADDED
    rozpocznijprace: 'Start Work',
    podajnazwezlecenia: 'Enter job name:',
    wprowadznazwezlecenia: 'Enter job name',
    anuluj: 'Cancel',
    rozpocznij: 'Start',
    finish: 'Finish',
    start: 'Start', // Adding missing entry
    enterJobOrderName: 'Enter job order name:', // Adding missing entry
    
    // Priorities
    priorityLow: 'Low',
    priorityMedium: 'Medium',
    priorityHigh: 'High',
    priorityCritical: 'Critical',
    
    // Maintenance statuses
    maintenanceStatusReported: 'Reported',
    maintenanceStatusInProgress: 'In Progress',
    maintenanceStatusResolved: 'Resolved',
    maintenanceStatusCanceled: 'Canceled',
    maintenanceStatusToCheck: 'To Check',
    maintenanceStatusPending: 'Pending',
    
    // Purchase statuses
    purchaseStatusPending: 'Pending',
    purchaseStatusApproved: 'Approved',
    purchaseStatusOrdered: 'Ordered',
    purchaseStatusDelivered: 'Delivered',
    purchaseStatusCanceled: 'Canceled',
    
    // Purchase details translations
    purchasesDetailsHeader: 'Purchase Request Details',
    purchasesPriority: 'Priority',
    purchasesRequesterLabel: 'Requester:',
    purchasesCategoryLabel: 'Category:',
    purchasesRequestDateLabel: 'Request date:',
    purchasesEstimatedCost: 'Estimated cost',
    purchasesQuantity: 'Quantity',
    purchasesDescription: 'Description',
    purchasesProductPhotos: 'Product photos',
    purchasesProductName: 'Product Name',
    purchasesNotes: 'Notes',
    purchasesTitle: 'Title', // Add missing translation
    purchasesTitlePlaceholder: 'Enter purchase title',
    purchasesDescriptionPlaceholder: 'Enter detailed purchase description',
    purchasesSelectCategory: 'Select category',
    purchasesCostPlaceholder: '0.00',
    purchasesQuantityPlaceholder: 'Enter quantity',
    purchasesAddNewPurchase: 'Add new purchase request',
    purchasesAddNewCategory: 'Add New Category',
    purchasesPhotos: 'Photos',
    purchasesAddPhoto: 'Add photo',
    purchasesPhotosWillBeSaved: 'Photos will be saved as links in the database',
    purchasesRequiredFields: '* Required fields',
    purchasesCreatePurchase: 'Create purchase request',
    addPurchase: 'Add Purchase',
    purchasesPurchasesList: 'Purchases List',
    purchasesAdditionalNotes: 'Additional notes',
    purchasesNotes: 'Notes',
    
    // Purchase filter buttons
    purchasesAllFilter: 'All',
    purchasesPendingFilter: 'Pending',
    purchasesApprovedFilter: 'Approved',
    purchasesOrderedFilter: 'Ordered',
    purchasesDeliveredFilter: 'Delivered',
    purchasesCanceledFilter: 'Canceled',
    
    // EmployeesList
    employeeWorkHistory: 'Employee Work History',
    filterByEmployee: 'Filter by Employee',
    dateRange: 'Date Range',
    searchEmployee: 'Search employee...',
    selectStartDate: 'Select start date',
    selectEndDate: 'Select end date',
    apply: 'Apply',
    clearFilters: 'Clear Filters',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    date: 'Date',
    employee: 'Employee',
    jobOrder: 'Job Order',
    workTime: 'Work Time',
    duration: 'Duration',
    startAddress: 'Start Address',
    endAddress: 'End Address',
    totalWorkTime: 'Total Work Time:',
    exportToPdf: 'Export to PDF',
    loadingAddress: 'Loading address...',
    errorFetchingWorkSessions: 'Failed to fetch work history.',
    all: 'All',
    thisMonth: 'This Month',
    previousMonth: 'Previous Month',
    selectDateRange: 'Select Date Range',
    clear: 'Clear',
    dateFrom: 'From',
    dateTo: 'To',
    
    // Tasks - new translations
    taskDetails: 'Task Details',
    clientName: 'Client Name',
    address: 'Address',
    workScope: 'Work Scope',
    startTime: 'Start Time',
    endTime: 'End Time',
    assignedEmployees: 'Assigned Employees',
    additionalInfo: 'Additional Information',
    photos: 'Photos',
    photosBefore: 'Photos Before',
    photosAfter: 'Photos After',
    addTask: 'Add Task',
    editTask: 'Edit Task',
    saveTask: 'Save Task',
    cancelTask: 'Cancel',
    deleteTask: 'Delete Task',
    search: 'Search',
    filters: 'Filters',
    sortBy: 'Sort By',
    filterClient: 'Filter by Client',
    filterAddress: 'Filter by Address',
    filterStatus: 'Filter by Status',
    selectEmployee: 'Select Employee',
    requiredField: 'Required field',
    taskList: 'Task List',
    activeEmployeesCount: 'Active Employees Count',
    taskScope: 'Task Scope',
    searchTasks: 'Search tasks...',
    jobName: 'Job Name',
    
    // Additional task translations
    clearAllFilters: 'Clear all filters',
    dateRangeSelect: 'Select date range',
    client: 'Client',
    status: 'Status',
    loading: 'Loading...',
    loadingTask: 'Loading task...',
    failedToLoadTask: 'Failed to load task',
    back: 'Back',
    basicInfo: 'Basic Information',
    startedAt: 'Started at',
    finishedAt: 'Finished at',
    taskDuration: 'Task duration',
    startTask: 'Start task',
    noEmployees: 'No assigned employees',
    activeEmployee: 'Active employee',
    activateEmployee: 'Activate employee',
    email: 'Email',
    phone: 'Phone',
    activityTime: 'Activity time',
    photosBeforeWork: 'Photos before work',
    photosAfterWork: 'Photos after work',
    saveChanges: 'Save changes',
    createTask: 'Create task',
    selectEmployees: 'Select employees',
    clientNameLabel: 'Client name*',
    taskAddressLabel: 'Task address*',
    workScopeLabel: 'Work scope*',
    additionalInfoLabel: 'Additional information',
    requiredFields: '* Required fields',
    
    // Work Session Details
    workSessionDetails: 'Work Session Details',
    selectedSessionDetails: 'Selected Session Details',
    manuallyAddedWorkTime: 'Work time was manually added',
    manuallyEditedWorkTime: 'Work time was manually edited',
    jobOrderLabel: 'Job Order:',
    startTimeLabel: 'Start Time:',
    endTimeLabel: 'End Time:',
    workTimeLabel: 'Work Time:',
    startLocationLabel: 'Start Location:',
    endLocationLabel: 'End Location:',
    dateLabel: 'Start Date',
    startDateLabel: 'Start Date', // Dodane dla spójności
    endDateLabel: 'End Date',
    notesLabel: 'Notes:',
    employeeOrderDetails: 'Employee Order Details',
    employeeData: 'Employee Data',
    activeWorkSession: 'Active Work Session',
    noRecordsFound: 'Session details not found.',
    
    // Additional aspects for work day form
    chooseDate: 'Choose date',
    today: 'Today',
    chooseTime: 'Choose time',
    timeFormatHint: 'Time format: HH:MM',
    addWorkDayForm: 'Work Day Form',
    jobNameRequired: 'Job name is required',
    dateRequired: 'Date is required',
    startTimeRequired: 'Start time is required',
    endTimeRequired: 'End time is required',
    validationError: 'Validation error',
    
    // Priorities
    priorityLow: 'Low',
    priorityMedium: 'Medium',
    priorityHigh: 'High',
    priorityCritical: 'Critical',
    
    // Search and filters
    searchTasks: 'Search tasks...',
    date: 'Date',
    all: 'All',
    selectDateRange: 'Select Date Range',
    clear: 'Clear',
    dateFrom: 'From',
    dateTo: 'To',
    apply: 'Apply',
    client: 'Client',
    address: 'Address',
    status: 'Status',
    
    // Main settings and interface
    taskDetails: 'Task Details',
    basicInfo: 'Basic Information',
    workScope: 'Work Scope',
    startTime: 'Start Time',
    taskDuration: 'Task duration',
    startTask: 'Start task',
    assignedEmployees: 'Assigned Employees',
    
    // Work Session Details
    workSessionDetails: 'Work Session Details',
    selectedSessionDetails: 'Selected Session Details',
    manuallyAddedWorkTime: 'Work time was manually added',
    manuallyEditedWorkTime: 'Work time was manually edited',
    jobOrderLabel: 'Job Order:',
    startTimeLabel: 'Start Time:',
    endTimeLabel: 'End Time:',
    workTimeLabel: 'Work Time:',
    startLocationLabel: 'Start Location:',
    endLocationLabel: 'End Location:',
    notesLabel: 'Notes:',
    dateLabel: 'Start Date',
    startDateLabel: 'Start Date', // Dodane dla spójności
    endDateLabel: 'End Date',
    employeeOrderDetails: 'Employee Order Details',
    employeeData: 'Employee Data',
    activeWorkSession: 'Active Work Session',
    noRecordsFound: 'Session details not found.',
    
    // Statuses for purchases (English)
    purchaseStatusPending: 'Pending',
    purchaseStatusApproved: 'Approved',
    purchaseStatusOrdered: 'Ordered',
    purchaseStatusDelivered: 'Delivered',
    purchaseStatusCanceled: 'Canceled',
    
    // Purchase details translations (English)
    purchasesDetailsHeader: 'Purchase Request Details',
    purchasesPriority: 'Priority',
    purchasesRequesterLabel: 'Requester:',
    purchasesCategoryLabel: 'Category:',
    purchasesRequestDateLabel: 'Request date:',
    purchasesEstimatedCost: 'Estimated cost',
    purchasesQuantity: 'Quantity',
    purchasesDescription: 'Description',
    purchasesProductPhotos: 'Product photos',
    purchasesProductName: 'Product Name',
    purchasesNotes: 'Notes',
    purchasesTitlePlaceholder: 'Enter purchase title',
    purchasesDescriptionPlaceholder: 'Enter detailed purchase description',
    purchasesSelectCategory: 'Select category',
    purchasesCostPlaceholder: '0.00',
    purchasesQuantityPlaceholder: 'Enter quantity',
    purchasesAddNewPurchase: 'Add new purchase request',
    purchasesAddNewCategory: 'Add New Category',
    purchasesPhotos: 'Photos',
    purchasesAddPhoto: 'Add photo',
    purchasesPhotosWillBeSaved: 'Photos will be saved as links in the database',
    purchasesRequiredFields: '* Required fields',
    purchasesCreatePurchase: 'Create purchase request',
    addPurchase: 'Add Purchase',
    purchasesPurchasesList: 'Purchases List',
    purchasesAdditionalNotes: 'Additional notes',
    purchasesNotes: 'Notes',

    // --- Maintenance Reports ---
    maintenanceReports: 'Maintenance Reports',
    maintenanceAddReport: 'Add Report',
    maintenanceTitle: 'Title',
    maintenanceShortTitlePlaceholder: 'Short title describing the failure',
    maintenanceDescription: 'Description',
    maintenanceDetailedDescription: 'Detailed description of the problem',
    maintenancePriority: 'Priority',
    priorityLow: 'Low',
    priorityMedium: 'Medium',
    priorityHigh: 'High',
    priorityCritical: 'Critical',
    maintenancePhotos: 'Photos',
    maintenanceAddPhoto: 'Add photo',
    maintenancePhotosWillBeSaved: 'Photos will be saved as links in the database.',
    maintenanceCreateReport: 'Create report',
    noMaintenanceReports: 'No maintenance reports',
    maintenanceReportCreated: 'The maintenance report has been created',
    maintenanceReportUpdated: 'The maintenance report has been updated',
    errorCreatingMaintenanceReport: 'Error creating maintenance report',
    errorUpdatingMaintenanceReport: 'Error updating maintenance report',
    confirmDeleteMaintenanceReport: 'Are you sure you want to delete this maintenance report?',
    maintenanceReportDeleted: 'The maintenance report has been deleted',
    errorDeletingMaintenanceReport: 'Error deleting maintenance report',
    maintenanceReportDetails: 'Maintenance report details',
    maintenanceLocation: 'Location',
    maintenanceStatus: 'Status',
    maintenanceReportedBy: 'Reported by',
    maintenanceCreatedAt: 'Created at',
    maintenanceRepairedBy: 'Repaired by',
    maintenanceResolvedBy: 'Resolved by',
    maintenanceCanceledBy: 'Canceled by',
    maintenanceRepairStartTime: 'Repair start time',
    maintenanceRepairEndTime: 'Repair end time',
    maintenanceResolvedTime: 'Resolution time',
    maintenanceCanceledTime: 'Cancellation time',
    maintenanceUpdateStatus: 'Update status',
    maintenanceAssignRepairer: 'Assign repairer',
    maintenanceSelectRepairer: 'Select repairer',
    maintenanceCancelReport: 'Cancel report',
    maintenanceResolveReport: 'Resolve report',
    maintenanceStartRepair: 'Start repair',
    maintenanceCompleteRepair: 'Complete repair',
    maintenanceReopenReport: 'Reopen report',
    maintenanceStatusReported: 'Reported',
    maintenanceStatusInProgress: 'In Progress',
    maintenanceStatusResolved: 'Resolved',
    maintenanceStatusCanceled: 'Canceled',
    maintenanceStatusToCheck: 'To Check',
    maintenanceStatusPending: 'Pending',
    maintenanceReportsList: 'Reports List',
    
    // Maintenance filter buttons
    maintenanceAllFilter: 'All',
    maintenanceReportedFilter: 'Reported',
    maintenanceInProgressFilter: 'In Progress',
    maintenanceResolvedFilter: 'Resolved',
    maintenanceToCheckFilter: 'To Check',
    maintenanceCanceledFilter: 'Canceled',
    maintenanceActiveFilter: 'Active',
    
    // Maintenance date filter
    maintenanceSelectDateRange: 'Select date range',
    maintenanceFromDate: 'From',
    maintenanceToDate: 'To',
    maintenanceClear: 'Clear',
    maintenanceApply: 'Apply',
    maintenanceDateFilter: 'Date',

    // --- General/Shared ---
    requiredFields: '* Required fields',
    loading: 'Loading...',
    
    // Employee Order Details Screen
    employeeOrderDetails: 'Employee Order Details',
    employeeData: 'Employee Data',
    activeWorkSession: 'Active Work Session',
    
    // Purchase date filter
    purchasesDateFilter: 'Date',
    purchasesSelectDateRange: 'Select Date Range',
    purchasesFromDate: 'From',
    purchasesToDate: 'To',
    purchasesApply: 'Apply',
    purchasesClear: 'Clear',
    
    // Purchase filter buttons
    purchasesAllFilter: 'All',
    purchasesPendingFilter: 'Pending',
    purchasesApprovedFilter: 'Approved',
    purchasesOrderedFilter: 'Ordered',
    purchasesDeliveredFilter: 'Delivered',
    purchasesCanceledFilter: 'Canceled',
    
    // Maintenance reports details
    maintenanceDetailsHeader: 'Maintenance Report Details',
    maintenancePriorityLabel: 'Priority:',
    maintenanceReporterLabel: 'Reported by:',
    maintenanceReportDateLabel: 'Report date:',
    maintenanceReportTimeLabel: 'Report time:',
    maintenanceRepairedByLabel: 'Repaired by:',
    maintenanceRepairStartLabel: 'Repair start:',
    maintenanceRepairEndLabel: 'Repair end:',
    maintenanceResolvedTimeLabel: 'Resolved time:',
    maintenanceResolvedByLabel: 'Resolved by:',
    maintenanceCanceledTimeLabel: 'Canceled time:',
    maintenanceCanceledByLabel: 'Canceled by:',
    maintenanceDescriptionLabel: 'Description:',
    maintenancePhotosLabel: 'Photos:',
    maintenanceLocationLabel: 'Location:',
    maintenanceUnknownUser: 'Unknown user',
    maintenanceStartRepairButton: 'Start repair',
    maintenanceCompleteRepairButton: 'Complete repair',
    maintenanceFinalizeRepairButton: 'Finalize repair',
    maintenanceCancelButton: 'Cancel report',
    
    // Dialog titles
    success: 'Success', // Add missing English translation for success dialog title
    
    // Work session messages
    workSessionStarted: 'Work started successfully', // Add missing English translation for work started successfully message
    workSessionEnded: 'Your work session has been ended',
    confirmEndWork: 'Are you sure you want to end your work?',
    
    // Dialog buttons and common UI elements
    ok: 'OK',
    yes: 'Yes', // Added missing translation for "yes"
    no: 'No', // Added missing translation for "no"
    unknown: 'Unknown', // Added missing translation for "unknown"
    saved: 'Saved',
    confirm: 'Confirm',

    // Panel subskrypcji Premium - dodane nowe tłumaczenia
    choosePlan: 'Wybierz plan',
    monthly: 'Miesięcznie',
    yearly: 'Rocznie', 
    save20Percent: 'Oszczędź 20%',
    manageSubscription: 'Zarządzaj subskrypcją',
    subscriptionWillEnd: 'Twoja subskrypcja zostanie zakończona po aktualnym okresie rozliczeniowym.',
    codesUsed: 'kodów użytych',
    month: 'miesiąc',
    year: 'rok',
    adminPanel: 'Panel administratora',
    enjoyPremiumFeatures: 'Korzystaj ze wszystkich funkcji premium i rozszerzonych limitów',
    upgradeToPremiumDesc: 'Ulepsz do wersji premium, aby uzyskać więcej funkcji',
    basicPlan: 'Plan Podstawowy',
    changePlan: 'Change Plan',

    // Employee Status Management
    employeeStatusManagement: 'Zarządzanie statusami pracowników',
    employeeStatusActive: 'Aktywny',
    employeeStatusInactive: 'Nieaktywny',
    employeeStatusLastChange: 'Ostatnia zmiana',
    employeeStatusChangeLimit: 'Możesz zmienić status pracownika raz na 7 dni',
    employeeStatusSubscriptionLimit: 'Liczba aktywnych pracowników jest ograniczona przez plan subskrypcji',
    employeeStatusActivate: 'Aktywuj',
    employeeStatusDeactivate: 'Dezaktywuj',
    employeeStatusChangeSuccess: 'Status pracownika został zmieniony',
    employeeStatusChangeError: 'Wystąpił błąd podczas zmiany statusu pracownika',
    employeeStatusChangeTooSoon: 'Nie możesz zmienić statusu tego pracownika. Musisz poczekać 7 dni od ostatniej zmiany.',
    employeeStatusSubscriptionLimitReached: 'Nie możesz aktywować więcej pracowników. Osiągnięto limit dla twojego planu subskrypcji.',
  },
  uk: {
    hello: 'Привіт',
    selectLanguage: 'Виберіть мову',
    settings: 'Налаштування',
    dashboard: 'Головна панель',
    // Додайте більше перекладів для української мови
    languagePolish: 'Польська',
    languageEnglish: 'Англійська',
    languageUkrainian: 'Українська',
    workFlow: 'WorkFlow', // Приклад перекладу для логотипу
    
    // Меню і навігація
    menu: 'Меню',
    employees: 'Працівники',
    tasks: 'Завдання',
    myTasks: 'Мої завдання',
    schedule: 'Розклад',
    maintenance: 'Обслуговування',
    purchases: 'Закупівлі',
    admin: 'Панель адміністратора',
    myHours: 'Мої години',
    hours: 'Години',
    logout: 'Вийти',
    allCompanyTasks: 'Усі завдання компанії',
    noTasksFound: 'Завдань не знайдено',
    
    // Повідомлення
    error: 'Помилка',
    errorSavingLanguage: 'Не вдалося зберегти обрану мову. Будь ласка, спробуйте пізніше.',
    more: 'більше',
    collapse: 'Згорнути',
    
    // EmployeesList - нові переклади для розділу годин роботи
    employeeWorkHistory: 'Історія роботи працівників',
    filterByEmployee: 'Фільтр за працівником',
    dateRange: 'Діапазон дат',
    searchEmployee: 'Шукати працівника...',
    selectStartDate: 'Виберіть початкову дату',
    selectEndDate: 'Виберіть кінцеву дату',
    clearFilters: 'Очистити фільтри',
    selectAll: 'Вибрати всіх',
    deselectAll: 'Скасувати вибір всіх',
    employee: 'Працівник',
    jobOrder: 'Завдання',
    workTime: 'Час роботи',
    duration: 'Тривалість',
    startAddress: 'Адреса початку',
    endAddress: 'Адреса завершення',
    totalWorkTime: 'Загальний час роботи:',
    exportToPdf: 'Експорт у PDF',
    loadingAddress: 'Завантаження адреси...',
    errorFetchingWorkSessions: 'Не вдалося отримати історію роботи.',
    thisMonth: 'Цей місяць',
    previousMonth: 'Попередній місяць',
    
    // Переклади для розширених карт
    dueToday: 'На сьогодні',
    noTasksForToday: 'Немає завдань на сьогодні',
    noJobSpecified: 'Завдання не вказано',
    noActiveEmployees: 'Немає активних працівників',
    unknownEmployee: 'Невідомий працівник',
    reportedBy: 'Повідомлено:',
    todaysTasks: 'Сьогоднішні завдання',
    plannedTasks: 'Заплановані завдання',
    tasksPlanningTitle: 'Завдання',
    tasksPlanned: 'Заплановані завдання',
    maintenanceReportsTitle: 'Звіти',
    maintenanceReportsSub: 'Технічне обслуговування',
    maintenancePending: 'Очікують вирішення',
    purchaseRequestsTitle: 'Запити',
    purchaseRequestsSub: 'Закупівлі',
    purchasePending: 'Очікуючі запити',
    noMaintenanceReports: 'Немає звітів з технічного обслуговування',
    noPurchaseRequests: 'Немає очікуючих запитів на закупівлю',
    
    // Admin Panel
    employeeRole: 'Працівник',
    coordinatorRole: 'Координатор',
    adminRole: 'Адміністратор',
    verificationCodes: 'Коди верифікації',
    remainingCodes: 'Залишилось кодів',
    of: 'з',
    generateCode: 'Згенерувати код',
    used: 'Використаний',
    available: 'Доступний',
    limitReached: 'Ліміт досягнуто',
    verificationCodeLimitMessage: 'У безкоштовному плані можна згенерувати максимум 2 коди. Придбайте преміум, щоб генерувати більше кодів.',
    understand: 'Зрозуміло',
    buyPremium: 'Купити Преміум',
    editEmployee: 'Редагувати працівника',
    fullName: 'Повне ім\'я',
    role: 'Роль',
    save: 'Зберегти',
    cancel: 'Скасувати',
    delete: 'Видалити',
    confirmation: 'Підтвердження',
    confirmDeleteEmployee: 'Ви впевнені, що хочете видалити цього працівника?',
    errorUpdatingEmployee: 'Помилка оновлення працівника',
    failedToUpdateEmployee: 'Не вдалося оновити дані працівника',
    unexpectedErrorUpdatingEmployee: 'Виникла неочікувана помилка під час оновлення працівника',
    failedToDeleteEmployee: 'Не вдалося видалити працівника',
    unexpectedError: 'Виникла неочікувана помилка',
    errorFetchingUser: 'Дані користувача не знайдено',
    
    // Додаткові переклади для AdminPanel
    errorFetchingEmployees: 'Помилка отримання працівників',
    exceptionInFetchEmployees: 'Виняток у fetchEmployees',
    errorFetchingCodes: 'Помилка отримання кодів',
    redirectingToPremium: 'Перенаправлення на покупку преміум',
    errorGeneratingCode: 'Помилка генерації коду',
    updatingEmployee: 'Оновлення працівника',
    employeeUpdatedSuccessfully: 'Працівник успішно оновлений',
    exceptionInHandleSaveEdit: 'Виняток у handleSaveEdit',
    errorDeletingEmployee: 'Помилка видалення працівника',
    exceptionInHandleDeleteEmployee: 'Виняток у handleDeleteEmployee',
    copyCode: 'Копіювати код',
    
    // Карти на панелі інструментів
    activeEmployees: 'Активні працівники',
    currentlyWorking: 'Зараз працюють',
    upcomingTasks: 'Майбутні завдання',
    plannedTasks: 'Заплановані завдання',
    maintenanceReports: 'Звіти про обслуговування',
    purchaseRequests: 'Запити на закупівлю',
    quickActions: 'Швидкі дії',
    noUpcomingTasks: 'Немає майбутніх завдань',
    
    // Статуси
    statusPending: 'В очікуванні',
    statusInProgress: 'В процесі',
    statusCompleted: 'Завершено',
    pending: 'В очікуванні',
    inProgress: 'В процесі',
    completed: 'Завершено',
    cancelled: 'Скасовано',
    
    // Przyciski акцій
    startWork: 'Розпочати роботу',
    endWork: 'Закінчити роботу',
    stopWork: 'Закінчити роботу', // Додано переклад для кнопки
    completeTask: 'Завершити завдання',
    addWorkDay: 'Додати робочий день',
    reportIssue: 'Повідомити про несправність',
    addPurchase: 'Додати закупівлю',
    newTask: 'Нове завдання',
    addTeam: 'Додати команду',
    submitAddWorkDay: 'Додати робочий день',
    
    // Кнопки в нижньому меню
    addTask: 'Додати завдання', 
    taskList: 'Список завдань',
    
    // Форма початку роботи
    enterJobOrderName: 'Вкажіть назву завдання:',
    enterJobOrderNamePlaceholder: 'Введіть назву завдання',
    start: 'Розпочати',
    
    // Специфічні переклади для "Розпочати роботу" діалогу
    rozpocznijprace: 'Розпочати роботу',
    podajnazwezlecenia: 'Вкажіть назву завдання:',
    wprowadznazwezlecenia: 'Введіть назву завдання',
    anuluj: 'Скасувати',
    rozpocznij: 'Розпочати',
    finish: 'Завершити',
    
    // Форма додавання робочого дня
    addWorkDayTitle: 'Додай день праці',
    data: 'Дата',
    startTimeInput: 'Час початку',
    endTimeInput: 'Час закінчення',
    jobName: 'Назва злецення',
    jobNamePlaceholder: 'Введіть назву злецення',
    description: 'Опис',
    descriptionPlaceholder: 'Вкажіть назву завдання або проекту, над яким ви працювали',
    nazwazlecenia: 'Назва завдання', // Specific translation for label in form
    czasrozpoczecia: 'Час початку', // Form field label
    czaszakonczenia: 'Час закінчення', // Form field label
    selectDate: 'Виберіть дату',
    enterValue: 'Введіть значення',
    editTask: 'Редагувати завдання',
    photosBefore: 'Фотографії до',
    photosAfter: 'Фотографії після',
    photosBeforeWork: 'Фотографії перед виконанням',
    photosAfterWork: 'Фотографії після виконання',
    saveChanges: 'Зберегти зміни',
    confirmSaveChanges: 'Підтвердіть збереження змін',
    areYouSureYouWantToSaveChanges: 'Ви впевнені, що хочете зберегти зміни в завданні?',
    
    // Діалоги та сповіщення
    confirm: 'Підтвердіть',
    confirmEndWork: 'Ви впевнені, що хочете завершити роботу?',
    workSessionEnded: 'Сесія роботи успішно завершена',
    potwierdz: 'Підтвердити',
    yes: 'Так',
    no: 'Ні',
    confirmCompleteTask: 'Ви впевнені, що хочете завершити активність у завданні "{{taskName}}"?',
    noActiveTasksToComplete: 'Наразі у вас немає активних завдань для завершення.',
    saved: 'Збережено',
    errorMessage: 'Помилка',
    fillAllRequiredFields: 'Будь ласка, заповніть усі обов\'язкові поля',
    invalidTimeFormat: 'Неправильний формат часу. Використовуйте формат ГГ:ХХ',
    endTimeMustBeLater: 'Час закінчення повинен бути пізнішим за час початку',
    errorSavingSession: 'Помилка збереження сесії',
    errorStartingWork: 'Помилка під час запуску робочої сесії',
    
    // Додаткові аспекти для форми додавання робочого дня
    chooseDate: 'Виберіть дату',
    today: 'Сьогодні',
    chooseTime: 'Виберіть час',
    timeFormatHint: 'Формат часу: ГГ:ХХ',
    addWorkDayForm: 'Форма додавання робочого дня',
    jobNameRequired: 'Назва завдання обов\'язкова',
    dateRequired: 'Дата обов\'язкова',
    startTimeRequired: 'Час початку обов\'язковий',
    endTimeRequired: 'Час закінчення обов\'язковий',
    validationError: 'Помилка валідації',
    
    // Пріоритети
    priorityLow: 'Низький',
    priorityMedium: 'Середній',
    priorityHigh: 'Високий',
    priorityCritical: 'Критичний',
    
    // Пошук і фільтри
    searchTasks: 'Пошук завдань...',
    date: 'Дата',
    all: 'Всі',
    selectDateRange: 'Вибрати діапазон дат',
    clear: 'Очистити',
    dateFrom: 'Від',
    dateTo: 'До',
    apply: 'Застосувати',
    client: 'Клієнт',
    address: 'Адреса',
    status: 'Статус',
    
    // Основні налаштування і інтерфейс
    taskDetails: 'Деталі завдання',
    basicInfo: 'Основна інформація',
    workScope: 'Обсяг робіт',
    startTime: 'Час початку',
    taskDuration: 'Тривалість завдання',
    startTask: 'Розпочати завдання',
    assignedEmployees: 'Призначені працівники',
    
    // Детали сесії роботи
    workSessionDetails: 'Деталі сесії роботи',
    selectedSessionDetails: 'Деталі вибраної сесії',
    manuallyAddedWorkTime: 'Час роботи був доданий вручну',
    manuallyEditedWorkTime: 'Час роботи був відредагований вручну',
    jobOrderLabel: 'Завдання:',
    startTimeLabel: 'Час початку:',
    endTimeLabel: 'Час закінчення:',
    workTimeLabel: 'Час роботи:',
    startLocationLabel: 'Місце початку:',
    endLocationLabel: 'Місце закінчення:',
    notesLabel: 'Нотатки:',
    noRecordsFound: 'Деталі сесії не знайдено.',
    noData: 'Немає даних',
    editWorkSession: 'Редагувати сесію роботи',
    dateLabel: 'Дата початку',
    startDateLabel: 'Дата початку', // Dodane dla spójності
    endDateLabel: 'Дата закінчення',
    startTimeInputLabel: 'Час початку',
    endTimeInputLabel: 'Час закінчення',
    jobOrderInputLabel: 'Завдання/Опис',
    notesInputLabel: 'Нотатки (за бажанням)',
    calculatedWorkTime: 'Розрахований час роботи:',
    workTimeUpdated: 'Час роботи був оновлений',
    workSessionStarted: 'Робота успішно розпочата',
    errorOpeningForm: 'Не вдається відкрити форму редагування. Спробуйте ще раз.',
    unknownStatus: 'Невідомий',
    activeStatus: 'Активний',
    noDate: 'Немає дати',
    loading: 'Завантаження...',
    success: 'Успіх',
    workDayAdded: 'Робочий день додано',
    dbError: 'Помилка бази даних:',
    quantity: 'Кількість',
    unknown: 'Невідомий',
    
    // Statusy dla zakupów (ukraiński)
    purchaseStatusPending: 'Очікує',
    purchaseStatusApproved: 'Затверджено',
    purchaseStatusOrdered: 'Замовлено',
    purchaseStatusDelivered: 'Доставлено',
    purchaseStatusCanceled: 'Скасовано',
    
    // Purchase details translations (Ukrainian)
    purchasesDetailsHeader: 'Деталі запиту на закупівлю',
    purchasesPriority: 'Пріоритет',
    purchasesRequesterLabel: 'Запитувач:',
    purchasesCategoryLabel: 'Категорія:',
    purchasesRequestDateLabel: 'Дата запиту:',
    purchasesEstimatedCost: 'Орієнтовна вартість',
    purchasesQuantity: 'Кількість',
    purchasesDescription: 'Опис',
    purchasesProductPhotos: 'Фотографії продукту',
    purchasesProductName: 'Назва продукту',
    purchasesTitle: 'Назва', // Added Ukrainian translation
    purchasesCategory: 'Категорія', // Added Ukrainian translation
    purchasesNotes: 'Нотатки',
    purchasesTitlePlaceholder: 'Введіть назву закупівлі',
    purchasesDescriptionPlaceholder: 'Введіть детальний опис закупівлі',
    purchasesSelectCategory: 'Виберіть категорію',
    purchasesCostPlaceholder: '0,00',
    purchasesQuantityPlaceholder: 'Введіть кількість',
    purchasesAddNewPurchase: 'Додати новий запит на закупівлю',
    purchasesAddNewCategory: 'Додати нову категорію',
    purchasesPhotos: 'Фотографії продукту',
    purchasesAddPhoto: 'Додати фото',
    purchasesPhotosWillBeSaved: 'Фотографії будуть збережені як посилання в базі даних',
    purchasesRequiredFields: '* Поля, позначені зірочкою, є обов\'язковими',
    purchasesCreatePurchase: 'Створити запит на закупівлю',
    addPurchase: 'Додати закупівлю',
    purchasesPurchasesList: 'Список закупівель',
    purchasesAdditionalNotes: 'Додаткові уваги',
    purchasesPriority: 'Пріоритет',

    // --- Maintenance Reports ---
    maintenanceReports: 'Звіти про технічне обслуговування',
    maintenanceAddReport: 'Додати звіт про обслуговування',
    maintenanceTitle: 'Назва',
    maintenanceShortTitlePlaceholder: 'Коротка назва звіту',
    maintenanceDescription: 'Опис',
    maintenanceDetailedDescription: 'Детальний опис проблеми',
    maintenancePriority: 'Пріоритет',
    priorityLow: 'Низький',
    priorityMedium: 'Середній',
    priorityHigh: 'Високий',
    priorityCritical: 'Критичний',
    maintenancePhotos: 'Фотографії',
    maintenanceAddPhoto: 'Додати фото',
    maintenancePhotosWillBeSaved: 'Фотографії будуть збережені в базі даних.',
    maintenanceCreateReport: 'Створити звіт',
    noMaintenanceReports: 'Немає звітів про технічне обслуговування.',
    maintenanceReportCreated: 'Звіт про обслуговування успішно створено.',
    maintenanceReportUpdated: 'Звіт про обслуговування успішно оновлено.',
    errorCreatingMaintenanceReport: 'Помилка при створенні звіту про обслуговування.',
    errorUpdatingMaintenanceReport: 'Помилка при оновленні звіту про обслуговування.',
    confirmDeleteMaintenanceReport: 'Ви впевнені, що хочете видалити цей звіт?',
    maintenanceReportDeleted: 'Звіт про обслуговування видалено.',
    errorDeletingMaintenanceReport: 'Помилка при видаленні звіту.',
    maintenanceReportDetails: 'Деталі звіту про обслуговування',
    maintenanceLocation: 'Місцезнаходження',
    maintenanceStatus: 'Статус',
    maintenanceReportedBy: 'Повідомив(ла)',
    maintenanceCreatedAt: 'Створено',
    maintenanceRepairedBy: 'Відремонтував(ла)',
    maintenanceResolvedBy: 'Вирішив(ла)',
    maintenanceCanceledBy: 'Скасував(ла)',
    maintenanceRepairStartTime: 'Час початку ремонту',
    maintenanceRepairEndTime: 'Час завершення ремонту',
    maintenanceResolvedTime: 'Час вирішення',
    maintenanceCanceledTime: 'Час скасування',
    maintenanceUpdateStatus: 'Оновити статус',
    maintenanceAssignRepairer: 'Призначити виконавця',
    maintenanceSelectRepairer: 'Виберіть виконавця',
    maintenanceCancelReport: 'Скасувати звіт',
    maintenanceResolveReport: 'Вирішити звіт',
    maintenanceStartRepair: 'Розпочати ремонт',
    maintenanceCompleteRepair: 'Завершити ремонт',
    maintenanceReopenReport: 'Відновити звіт',
    maintenanceStatusReported: 'Повідомлено',
    maintenanceStatusInProgress: 'В процесі',
    maintenanceStatusResolved: 'Вирішено',
    maintenanceStatusCanceled: 'Скасовано',
    maintenanceStatusToCheck: 'До перевірки',
    maintenanceStatusPending: 'Очікує',
    maintenanceReportsList: 'Список звітів',
    
    // Maintenance filter buttons
    maintenanceAllFilter: 'Всі',
    maintenanceReportedFilter: 'Повідомлені',
    maintenanceInProgressFilter: 'В процесі',
    maintenanceResolvedFilter: 'Вирішені',
    maintenanceToCheckFilter: 'До перевірки',
    maintenanceCanceledFilter: 'Скасовані',
    maintenanceActiveFilter: 'Активні',
    
    // Maintenance date filter
    maintenanceSelectDateRange: 'Виберіть діапазон дат',
    maintenanceFromDate: 'Від',
    maintenanceToDate: 'До',
    maintenanceClear: 'Очистити',
    maintenanceApply: 'Застосувати',
    maintenanceDateFilter: 'Дата',

    // --- General/Shared ---
    requiredFields: '* Поля, позначені зірочкою, є обов\'язковими',
    loading: 'Завантаження...',
    
    // Employee Order Details Screen
    employeeOrderDetails: 'Деталі завдання працівника',
    employeeData: 'Дані працівника',
    activeWorkSession: 'Активна сесія роботи',
    
    // Purchase date filter
    purchasesDateFilter: 'Дата',
    purchasesSelectDateRange: 'Виберіть діапазон дат',
    purchasesFromDate: 'Від',
    purchasesToDate: 'До',
    purchasesApply: 'Застосувати',
    purchasesClear: 'Очистити',
    
    // Purchase filter buttons
    purchasesAllFilter: 'Всі',
    purchasesPendingFilter: 'Очікуючі',
    purchasesApprovedFilter: 'Затверджені',
    purchasesOrderedFilter: 'Замовлені',
    purchasesDeliveredFilter: 'Доставлені',
    purchasesCanceledFilter: 'Скасовані',
    
    // Maintenance details translations (Ukrainian)
    maintenanceDetailsHeader: 'Деталі звіту про аварію',
    maintenancePriorityLabel: 'Пріоритет:',
    maintenanceLocationLabel: 'Розташування:',
    maintenanceReporterLabel: 'Повідомив:',
    maintenanceReportDateLabel: 'Дата повідомлення:',
    maintenanceReportTimeLabel: 'Час повідомлення:',
    maintenanceRepairedByLabel: 'Ремонт виконав:',
    maintenanceRepairStartLabel: 'Початок ремонту:',
    maintenanceRepairEndLabel: 'Кінець ремонту:',
    maintenanceResolvedTimeLabel: 'Час вирішення:',
    maintenanceResolvedByLabel: 'Вирішив:',
    maintenanceCanceledTimeLabel: 'Час скасування:',
    maintenanceCanceledByLabel: 'Скасував:',
    maintenanceDescriptionLabel: 'Опис:',
    maintenancePhotosLabel: 'Фотографії:',
    maintenanceUnknownUser: 'Невідомий',
    maintenanceStartRepairButton: 'Розпочати ремонт',
    maintenanceCompleteRepairButton: 'Завершити ремонт',
    maintenanceFinalizeButton: 'Готово',
    maintenanceCancelButton: 'Скасувати звіт',
    
    // Quick action translations
    rozpocznijprace: 'Почати роботу',
    podajnazwezlecenia: 'Введіть назву завдання:',
    wprowadznazwezlecenia: 'Введіть назву завдання',
    anuluj: 'Скасувати',
    rozpocznij: 'Почати',
    finish: 'Завершити',
    start: 'Почати', // Added missing translation for start button
    enterJobOrderName: 'Введіть назву завдання:', // Added missing translation for enterJobOrderName
    
    // Dialog titles and work messages
    success: 'Успіх',
    workSessionStarted: 'Роботу успішно розпочато',
    // Моя робоча сесія - ДОДАНО
    activeSessionTitle: 'Активна робоча сесія', // Додано переклад для української мови

    // Employee Status Management
    employeeStatusManagement: 'Zarządzanie statusami pracowników',
    employeeStatusActive: 'Aktywny',
    employeeStatusInactive: 'Nieaktywny',
    employeeStatusLastChange: 'Ostatnia zmiana',
    employeeStatusChangeLimit: 'Możesz zmienić status pracownika raz na 7 dni',
    employeeStatusSubscriptionLimit: 'Liczba aktywnych pracowników jest ograniczona przez plan subskrypcji',
    employeeStatusActivate: 'Aktywuj',
    employeeStatusDeactivate: 'Dezaktywuj',
    employeeStatusChangeSuccess: 'Status pracownika został zmieniony',
    employeeStatusChangeError: 'Wystąpił błąd podczas zmiany statusu pracownika',
    employeeStatusChangeTooSoon: 'Nie możesz zmienić statusu tego pracownika. Musisz poczekać 7 dni od ostatniej zmiany.',
    employeeStatusSubscriptionLimitReached: 'Nie możesz aktywować więcej pracowników. Osiągnięto limit dla twojego planu subskrypcji.',
  },
};

// Inicjalizacja i18n
const i18n = new I18n(translations);

// Domyślny język
i18n.defaultLocale = 'pl';
i18n.locale = 'pl'; 

// Funkcja do zmiany języka
export const setLocale = (locale: string) => {
  i18n.locale = locale;
};

// Funkcja do pobrania aktualnego języka
export const getLocale = (): string => {
  return i18n.locale;
};

export { i18n }; 