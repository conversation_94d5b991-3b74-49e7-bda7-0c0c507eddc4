-- Dodanie kolumn do zarządzania statusem subskrypcji pracownika (jeśli nie istnieją)
ALTER TABLE employees
ADD COLUMN IF NOT EXISTS subscription_status TEXT NOT NULL DEFAULT 'ACTIVE'
  CHECK (subscription_status IN ('ACTIVE', 'SUBSCRIPTION_EXPIRED')),
ADD COLUMN IF NOT EXISTS last_status_change TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS verification_code TEXT;

-- Indeks dla szy<PERSON>go wyszukiwania po statusie
CREATE INDEX IF NOT EXISTS idx_employees_subscription_status 
  ON employees(subscription_status);

-- <PERSON>gger do aktualizacji last_status_change przy zmianie statusu
CREATE OR REPLACE FUNCTION update_employee_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.subscription_status IS DISTINCT FROM NEW.subscription_status THEN
    NEW.last_status_change = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_employee_status_timestamp ON employees;
CREATE TRIGGER trigger_update_employee_status_timestamp
  BEFORE UPDATE ON employees
  FOR EACH ROW
  EXECUTE FUNCTION update_employee_status_timestamp();

-- Funkcja do aktualizacji statusów pracowników przy zmianie subskrypcji
CREATE OR REPLACE FUNCTION update_employees_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- UWAGA: Nie dezaktywuj pracowników przy anulowaniu - tylko przy rzeczywistym wygaśnięciu
  -- Anulowanie oznacza, że subskrypcja nie będzie odnawiana, ale jest aktywna do końca okresu

  -- Reaktywuj pracowników przy aktywacji subskrypcji
  IF NEW.status = 'active' AND (OLD.status IS NULL OR OLD.status != 'active') THEN
    PERFORM reactivate_company_employees(NEW.company_id);
  END IF;

  -- Dezaktywuj pracowników tylko przy rzeczywistym wygaśnięciu (status expired)
  IF NEW.status = 'expired' AND OLD.status = 'active' THEN
    PERFORM deactivate_company_employees_on_expiry(NEW.company_id);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger do sprawdzania czy kod weryfikacyjny może być użyty
CREATE OR REPLACE FUNCTION check_verification_code_usage()
RETURNS TRIGGER AS $$
DECLARE
  company_id_for_code UUID;
BEGIN
  -- Pobierz company_id dla tego kodu
  SELECT company_id INTO company_id_for_code
  FROM verification_codes
  WHERE code = NEW.verification_code;

  -- Sprawdź czy kod może być użyty
  IF NOT can_use_verification_code(NEW.verification_code, company_id_for_code) THEN
    RAISE EXCEPTION 'Nie można użyć tego kodu weryfikacyjnego. Firma przekroczyła limit aktywnych pracowników lub kod jest nieprawidłowy.';
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_employees_on_subscription_change ON company_subscriptions;
CREATE TRIGGER trigger_update_employees_on_subscription_change
  AFTER INSERT OR UPDATE ON company_subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_employees_on_subscription_change();

-- Trigger do sprawdzania kodów weryfikacyjnych przy tworzeniu konta pracownika
DROP TRIGGER IF EXISTS trigger_check_verification_code_usage ON employees;
CREATE TRIGGER trigger_check_verification_code_usage
  BEFORE INSERT ON employees
  FOR EACH ROW
  WHEN (NEW.verification_code IS NOT NULL)
  EXECUTE FUNCTION check_verification_code_usage();
