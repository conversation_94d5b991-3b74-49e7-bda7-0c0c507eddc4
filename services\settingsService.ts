import { supabase } from './supabaseClient';
import { Session, User } from '@supabase/supabase-js';

export interface UserSettings {
  preferred_language: string;
  // Można tu dodać inne ustawienia w prz<PERSON>ł<PERSON>
}

const DEFAULT_LANGUAGE = 'pl';

/**
 * Pobiera ustawienia użytkownika z bazy danych.
 * Jeśli użytkownik nie ma jeszcze ustawień, zwraca domyślny język.
 * @returns {Promise<UserSettings>} Obiekt z ustawieniami użytkownika.
 */
export const getUserSettings = async (): Promise<UserSettings> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.warn('getUserSettings: User not logged in.');
      return { preferred_language: DEFAULT_LANGUAGE };
    }

    const { data, error, status } = await supabase
      .from('settings')
      .select('preferred_language')
      .eq('user_id', user.id)
      .single();

    if (error && status !== 406) { // 406 = No rows found, which is okay here
      console.error('Error fetching user settings:', error);
      // W razie błędu zwracamy domyślny język
      return { preferred_language: DEFAULT_LANGUAGE };
    }

    if (data) {
      return { preferred_language: data.preferred_language || DEFAULT_LANGUAGE };
    } else {
      // Jeśli brak ustawień, zwracamy domyślne
      return { preferred_language: DEFAULT_LANGUAGE };
    }
  } catch (err) {
    console.error('Unexpected error fetching settings:', err);
    return { preferred_language: DEFAULT_LANGUAGE };
  }
};

/**
 * Aktualizuje lub tworzy ustawienia użytkownika w bazie danych.
 * @param {Partial<UserSettings>} settings - Obiekt z ustawieniami do zapisania (np. { preferred_language: 'en' }).
 * @returns {Promise<boolean>} True jeśli operacja się powiodła, false w przeciwnym razie.
 */
export const updateUserSettings = async (settings: Partial<UserSettings>): Promise<boolean> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.warn('updateUserSettings: User not logged in.');
      return false;
    }

    const updates = {
      user_id: user.id,
      ...settings,
      updated_at: new Date().toISOString(), // Ręczna aktualizacja updated_at dla upsert
    };

    // Używamy upsert, aby wstawić nowy rekord jeśli nie istnieje, lub zaktualizować istniejący
    const { error } = await supabase
      .from('settings')
      .upsert(updates, { onConflict: 'user_id' }); 

    if (error) {
      console.error('Error updating user settings:', error);
      return false;
    }

    console.log('User settings updated successfully.', updates);
    return true;

  } catch (err) {
    console.error('Unexpected error updating settings:', err);
    return false;
  }
}; 