-- Funkcja do przekształcenia pracownika w niezależnego (nie przypisanego do firmy)
-- Zwalnia kod weryfikacyjny i usuwa powiązania z firmą

CREATE OR REPLACE FUNCTION convert_employee_to_independent(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_employee_name TEXT;
  v_verification_code TEXT;
  v_employee_exists BOOLEAN := FALSE;
BEGIN
  -- Sprawdź czy pracownik istnieje i należy do firmy
  SELECT 
    full_name, 
    verification_code,
    TRUE
  INTO 
    v_employee_name, 
    v_verification_code,
    v_employee_exists
  FROM employees 
  WHERE id = p_employee_id 
  AND company_id = p_company_id;

  -- Jeśli pracownik nie istnieje lub nie należy do firmy
  IF NOT v_employee_exists THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie został znaleziony lub nie należy do tej firmy',
      'alert_type', 'error'
    );
  END IF;

  -- <PERSON>rzeksz<PERSON>łć pracownika w niezależnego
  UPDATE employees
  SET 
    company_id = NULL,                    -- Usuń powiązanie z firmą
    verification_code = NULL,             -- Usuń kod weryfikacyjny
    subscription_status = 'ACTIVE',       -- Ustaw jako aktywny (niezależny)
    last_status_change = NOW(),
    last_manual_status_change = NULL,     -- Wyzeruj daty ręcznych zmian
    role = 'independent',                 -- Oznacz jako niezależny
    updated_at = NOW()
  WHERE id = p_employee_id;

  -- Usuń kod weryfikacyjny z tabeli verification_codes (jeśli istnieje)
  IF v_verification_code IS NOT NULL THEN
    DELETE FROM verification_codes 
    WHERE code = v_verification_code 
    AND company_id = p_company_id;
    
    RAISE NOTICE 'Usunięto kod weryfikacyjny: % dla firmy: %', v_verification_code, p_company_id;
  END IF;

  -- Zwróć sukces z informacją
  RETURN jsonb_build_object(
    'success', true,
    'message', format('Pracownik "%s" został przekształcony w niezależnego. Kod weryfikacyjny został zwolniony.', 
                     COALESCE(v_employee_name, 'Nieznany')),
    'alert_type', 'success',
    'freed_verification_code', v_verification_code
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', format('Błąd podczas przekształcania pracownika: %s', SQLERRM),
      'alert_type', 'error'
    );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzenia czy pracownik jest niezależny
CREATE OR REPLACE FUNCTION is_independent_employee(
  p_employee_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM employees 
    WHERE id = p_employee_id 
    AND company_id IS NULL
    AND role = 'independent'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do pobrania listy niezależnych pracowników
CREATE OR REPLACE FUNCTION get_independent_employees()
RETURNS TABLE (
  id UUID,
  full_name TEXT,
  email TEXT,
  phone TEXT,
  subscription_status TEXT,
  created_at TIMESTAMPTZ,
  last_status_change TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id,
    e.full_name,
    e.email,
    e.phone,
    e.subscription_status,
    e.created_at,
    e.last_status_change
  FROM employees e
  WHERE e.company_id IS NULL
  AND e.role = 'independent'
  ORDER BY e.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Test funkcji (usuń po testach)
-- SELECT convert_employee_to_independent('test-uuid'::UUID, 'company-uuid'::UUID);
