const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const supabaseUrl = 'https://bqjjlxqzlpjjkqzqzqzq.supabase.co';
const supabaseKey = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxampseHF6bHBqamtxenF6cXpxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzQxMzI5NCwiZXhwIjoyMDQ4OTg5Mjk0fQ.Ej8XQJBHgoGVTKWKOdStjhiOJme_1Ej8XQJBHgoGVTKW';

const supabase = createClient(supabaseUrl, supabaseKey);

async function executeFix() {
  try {
    console.log('🔧 Naprawianie triggera zmiany planu...');
    
    // Zaktualizuj trigger
    const triggerSQL = `
-- Poprawka: Nie dezaktywuj pracowników przy zmianie planu subskrypcji
CREATE OR REPLACE FUNCTION update_employees_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- UWAGA: Nie dezaktywuj pracowników przy anulowaniu - tylko przy rzeczywistym wygaśnięciu
  -- Anulowanie oznacza, że subskrypcja nie będzie odnawiana, ale jest aktywna do końca okresu

  -- Reaktywuj pracowników TYLKO przy pierwszej aktywacji subskrypcji (nie przy zmianie planu)
  IF NEW.status = 'active' AND (OLD IS NULL OR OLD.status != 'active') THEN
    -- Dodatkowe sprawdzenie: czy to rzeczywiście nowa subskrypcja, a nie zmiana planu
    -- Jeśli plan_id się zmienił, ale status pozostał 'active', to jest to zmiana planu
    IF OLD IS NULL OR OLD.status != 'active' THEN
      PERFORM reactivate_company_employees(NEW.company_id);
      RAISE NOTICE 'Reaktywowano pracowników dla nowej subskrypcji firmy %', NEW.company_id;
    END IF;
  END IF;

  -- Dezaktywuj pracowników tylko przy rzeczywistym wygaśnięciu (status expired)
  IF NEW.status = 'expired' AND OLD.status = 'active' THEN
    PERFORM auto_deactivate_company_employees_on_expiry(NEW.company_id);
    RAISE NOTICE 'Dezaktywowano pracowników przy wygaśnięciu subskrypcji firmy %', NEW.company_id;
  END IF;

  -- Loguj zmiany dla debugowania
  IF OLD IS NOT NULL THEN
    RAISE NOTICE 'Subscription change: company_id=%, old_status=%, new_status=%, old_plan_id=%, new_plan_id=%', 
      NEW.company_id, OLD.status, NEW.status, OLD.plan_id, NEW.plan_id;
  ELSE
    RAISE NOTICE 'New subscription: company_id=%, status=%, plan_id=%', 
      NEW.company_id, NEW.status, NEW.plan_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
    `;
    
    const { data: triggerData, error: triggerError } = await supabase.rpc('execute_sql', { 
      sql_query: triggerSQL 
    });
    
    if (triggerError) {
      console.error('❌ Błąd aktualizacji triggera:', triggerError);
      return;
    }
    
    console.log('✅ Trigger zaktualizowany pomyślnie');
    
    // Sprawdź aktywne triggery
    console.log('\n📋 Sprawdzanie aktywnych triggerów...');
    const { data: triggers, error: triggersError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT 
          trigger_name, 
          event_manipulation, 
          event_object_table, 
          action_timing
        FROM information_schema.triggers 
        WHERE event_object_table = 'company_subscriptions'
        ORDER BY trigger_name;
      `
    });
    
    if (!triggersError && triggers) {
      console.log('Aktywne triggery na company_subscriptions:');
      triggers.forEach(trigger => {
        console.log(`- ${trigger.trigger_name}: ${trigger.action_timing} ${trigger.event_manipulation}`);
      });
    }
    
    // Sprawdź stan pracowników
    console.log('\n👥 Sprawdzanie stanu pracowników...');
    const { data: employeeStats, error: statsError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT 
          c.name as company_name,
          c.account_type,
          c.verification_code_limit,
          COUNT(e.id) as total_employees,
          COUNT(CASE WHEN e.subscription_status = 'ACTIVE' THEN 1 END) as active_employees,
          COUNT(CASE WHEN e.subscription_status = 'SUBSCRIPTION_EXPIRED' THEN 1 END) as expired_employees
        FROM companies c
        LEFT JOIN employees e ON c.id = e.company_id
        LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
        WHERE cs.status = 'active'
        GROUP BY c.id, c.name, c.account_type, c.verification_code_limit
        ORDER BY c.name;
      `
    });
    
    if (!statsError && employeeStats) {
      console.log('\nStatystyki pracowników dla firm z aktywnymi subskrypcjami:');
      employeeStats.forEach(stat => {
        console.log(`${stat.company_name} (${stat.account_type}): ${stat.active_employees}/${stat.total_employees} aktywnych`);
      });
    }
    
    console.log('\n🎯 Poprawka zakończona! Teraz zmiana planu nie powinna dezaktywować pracowników.');
    
  } catch (err) {
    console.error('❌ Błąd wykonania:', err.message);
  }
}

executeFix();
