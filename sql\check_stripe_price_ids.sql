-- Check current price IDs
SELECT name, stripe_price_id FROM subscription_plans;

-- Update price IDs if needed
UPDATE subscription_plans
SET stripe_price_id = 'price_1RVH3EPaKRxqYgSxOEuTVBhE'
WHERE name = 'Basic'
  AND (stripe_price_id IS NULL OR stripe_price_id = 'price_basic_monthly');

UPDATE subscription_plans
SET stripe_price_id = 'price_1RVH4nPaKRxqYgSxRL6Wofu4'
WHERE name = 'Pro'
  AND (stripe_price_id IS NULL OR stripe_price_id = 'price_pro_monthly');

UPDATE subscription_plans
SET stripe_price_id = 'price_1RVH5mPaKRxqYgSxgh62XnY0'
WHERE name = 'Business'
  AND (stripe_price_id IS NULL OR stripe_price_id = 'price_business_monthly');

-- Check updated price IDs
SELECT name, stripe_price_id FROM subscription_plans; 