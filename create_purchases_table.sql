-- Kod SQL do utworzenia tabeli purchases w bazie danych Supabase
-- Wykonaj te polecenia w SQL Editor w konsoli Supabase

-- Najpierw sprawdźmy strukturę tabel employees i company_managers
DO $$
DECLARE
    employee_id_column TEXT := 'user_id';
    manager_id_column TEXT := 'user_id';
BEGIN
    -- Sprawdź czy istnieje kolumna user_id w tabeli employees, je<PERSON><PERSON> nie, sprawdź auth_user_id lub id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns WHERE table_name = 'employees' AND column_name = 'user_id'
    ) THEN
        IF EXISTS (
            SELECT 1 FROM information_schema.columns WHERE table_name = 'employees' AND column_name = 'auth_user_id'
        ) THEN
            employee_id_column := 'auth_user_id';
        ELSE
            employee_id_column := 'id';
        END IF;
    END IF;

    -- Sprawd<PERSON> czy istnieje kolumna user_id w tabeli company_managers, je<PERSON><PERSON> nie, sprawdź auth_user_id lub id
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns WHERE table_name = 'company_managers' AND column_name = 'user_id'
    ) THEN
        IF EXISTS (
            SELECT 1 FROM information_schema.columns WHERE table_name = 'company_managers' AND column_name = 'auth_user_id'
        ) THEN
            manager_id_column := 'auth_user_id';
        ELSE
            manager_id_column := 'id';
        END IF;
    END IF;

    -- Wypisz znalezione nazwy kolumn dla celów diagnostycznych
    RAISE NOTICE 'Znaleziono kolumnę ID w employees: %', employee_id_column;
    RAISE NOTICE 'Znaleziono kolumnę ID w company_managers: %', manager_id_column;
END
$$;

-- Tworzenie typu enum dla statusu zakupów, jeśli nie istnieje
DO $$
BEGIN
    -- Sprawdzenie czy typ purchase_status już istnieje
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'purchase_status') THEN
        CREATE TYPE purchase_status AS ENUM ('pending', 'approved', 'ordered', 'delivered', 'canceled');
    END IF;
END
$$;

-- Tworzenie tabeli purchases
CREATE TABLE IF NOT EXISTS purchases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT,
  price_estimate NUMERIC(10, 2),
  actual_price NUMERIC(10, 2),
  status purchase_status DEFAULT 'pending',
  requested_by UUID REFERENCES auth.users(id),
  requested_by_name TEXT,
  requested_at TIMESTAMPTZ DEFAULT NOW(),
  approved_by UUID REFERENCES auth.users(id),
  approved_by_name TEXT,
  approved_at TIMESTAMPTZ,
  ordered_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  canceled_at TIMESTAMPTZ,
  canceled_by UUID REFERENCES auth.users(id),
  canceled_by_name TEXT,
  supplier TEXT,
  invoice_number TEXT,
  invoice_date DATE,
  warranty_end_date DATE,
  attachments JSONB,
  location TEXT,
  notes TEXT,
  priority TEXT DEFAULT 'medium',
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tworzenie indeksów dla lepszej wydajności
CREATE INDEX IF NOT EXISTS purchases_company_id_idx ON purchases(company_id);
CREATE INDEX IF NOT EXISTS purchases_status_idx ON purchases(status);
CREATE INDEX IF NOT EXISTS purchases_requested_by_idx ON purchases(requested_by);

-- Tworzenie funkcji dla automatycznej aktualizacji pola updated_at
-- Użyjemy CREATE OR REPLACE, które jest bezpieczniejsze niż sprawdzanie czy funkcja istnieje
CREATE OR REPLACE FUNCTION update_purchases_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Usuwamy istniejący trigger, jeśli istnieje, i tworzymy nowy
DROP TRIGGER IF EXISTS update_purchases_updated_at ON purchases;
CREATE TRIGGER update_purchases_updated_at
BEFORE UPDATE ON purchases
FOR EACH ROW
EXECUTE FUNCTION update_purchases_updated_at();

-- Ustawienie Row Level Security (RLS)
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;

-- Simplest approach - create policies without referencing employee or manager tables
-- This way we avoid issues with column names in those tables
DROP POLICY IF EXISTS "Users can view purchases for their company" ON purchases;
CREATE POLICY "Users can view purchases for their company"
ON purchases
FOR SELECT
USING (true);  -- Temporarily allow all users to view all purchases

DROP POLICY IF EXISTS "Users can insert purchases" ON purchases;
CREATE POLICY "Users can insert purchases"
ON purchases
FOR INSERT
WITH CHECK (requested_by = auth.uid());  -- Allow users to insert purchases as themselves

DROP POLICY IF EXISTS "Users can update their own pending purchases" ON purchases;
CREATE POLICY "Users can update their own pending purchases"
ON purchases
FOR UPDATE
USING (
  requested_by = auth.uid() AND
  status = 'pending'
);

DROP POLICY IF EXISTS "Users can delete their own pending purchases" ON purchases;
CREATE POLICY "Users can delete their own pending purchases"
ON purchases
FOR DELETE
USING (
  requested_by = auth.uid() AND
  status = 'pending'
);

-- Sprawdźmy czy tabela kategorii zakupów już istnieje
DO $$
DECLARE
    has_description_column BOOLEAN;
BEGIN
    -- Sprawdź czy tabela purchase_categories istnieje
    IF EXISTS (
        SELECT 1 FROM information_schema.tables WHERE table_name = 'purchase_categories'
    ) THEN
        -- Jeśli istnieje, sprawdź czy ma kolumnę description
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'purchase_categories' AND column_name = 'description'
        ) INTO has_description_column;
        
        -- Jeśli nie ma kolumny description, spróbuj ją dodać
        IF NOT has_description_column THEN
            BEGIN
                RAISE NOTICE 'Tabela purchase_categories nie ma kolumny description. Dodajemy ją...';
                ALTER TABLE purchase_categories ADD COLUMN description TEXT;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Nie można dodać kolumny description do tabeli purchase_categories: %', SQLERRM;
            END;
        END IF;
    ELSE
        -- Jeśli tabela nie istnieje, tworzymy ją z kolumną description
        CREATE TABLE purchase_categories (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            company_id UUID REFERENCES companies(id) ON DELETE CASCADE NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- Indeks dla kategorii
        CREATE INDEX IF NOT EXISTS purchase_categories_company_id_idx ON purchase_categories(company_id);
        
        -- RLS dla kategorii
        ALTER TABLE purchase_categories ENABLE ROW LEVEL SECURITY;
        
        -- Zmieniona polityka - użytkownicy mogą wyświetlać kategorie tylko dla swojej firmy
        DROP POLICY IF EXISTS "Users can view purchase categories" ON purchase_categories;
        CREATE POLICY "Users can view purchase categories"
        ON purchase_categories
        FOR SELECT
        USING (true); -- Tymczasowo pozwalamy wszystkim użytkownikom widzieć wszystkie kategorie
        
        -- Zmieniona polityka - użytkownicy mogą dodawać kategorie tylko dla swojej firmy
        DROP POLICY IF EXISTS "Users can insert purchase categories" ON purchase_categories;
        CREATE POLICY "Users can insert purchase categories"
        ON purchase_categories
        FOR INSERT
        WITH CHECK (
            company_id IN (
                -- Dla pracowników - ich firma
                SELECT company_id FROM employees WHERE user_id = auth.uid()
                UNION
                -- Dla menedżerów - ich firma
                SELECT id FROM companies WHERE manager_id = auth.uid()
            )
        );
        
        has_description_column := TRUE;
    END IF;
    
    -- Tę wartość będziemy wykorzystywać później w skrypcie
    RAISE NOTICE 'Tabela purchase_categories ma kolumnę description: %', has_description_column;
END
$$;

-- Dodawanie domyślnych kategorii dla każdej firmy w systemie
DO $$
DECLARE
    company_record RECORD;
    has_description_column BOOLEAN;
BEGIN
    -- Sprawdź czy tabela purchase_categories ma kolumnę description
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'purchase_categories' AND column_name = 'description'
    ) INTO has_description_column;

    -- Iteruj przez wszystkie firmy w systemie
    FOR company_record IN SELECT id FROM companies
    LOOP
        -- Sprawdź czy dla tej firmy już istnieją jakieś kategorie
        IF NOT EXISTS (SELECT 1 FROM purchase_categories WHERE company_id = company_record.id LIMIT 1) THEN
            -- Wstaw domyślne kategorie dla tej firmy
            IF has_description_column THEN
                -- Jeśli jest kolumna description, używamy jej
                INSERT INTO purchase_categories (company_id, name, description)
                VALUES 
                  (company_record.id, 'Narzędzia', 'Narzędzia ręczne, elektronarzędzia itp.'),
                  (company_record.id, 'Materiały budowlane', 'Cegły, cement, drewno itp.'),
                  (company_record.id, 'Sprzęt biurowy', 'Urządzenia i akcesoria biurowe'),
                  (company_record.id, 'Sprzęt BHP', 'Kaski, rękawice, okulary ochronne itp.'),
                  (company_record.id, 'Pojazdy', 'Samochody, części samochodowe, paliwo'),
                  (company_record.id, 'Oprogramowanie', 'Licencje na oprogramowanie, usługi IT');
            ELSE
                -- Jeśli nie ma kolumny description, nie używamy jej
                INSERT INTO purchase_categories (company_id, name)
                VALUES 
                  (company_record.id, 'Narzędzia'),
                  (company_record.id, 'Materiały budowlane'),
                  (company_record.id, 'Sprzęt biurowy'),
                  (company_record.id, 'Sprzęt BHP'),
                  (company_record.id, 'Pojazdy'),
                  (company_record.id, 'Oprogramowanie');
            END IF;
        END IF;
    END LOOP;
END
$$;

-- Aktualizacja polityk dla istniejącej tabeli purchase_categories
-- Ten kod należy wykonać niezależnie od tego, czy tabela była tworzona na nowo czy już istniała

-- Włączenie Row Level Security dla tabeli kategorii, jeśli nie jest włączone
ALTER TABLE purchase_categories ENABLE ROW LEVEL SECURITY;

-- Aktualizacja polityki SELECT - tymczasowo pozwalamy wszystkim użytkownikom widzieć wszystkie kategorie
DROP POLICY IF EXISTS "Users can view purchase categories" ON purchase_categories;
CREATE POLICY "Users can view purchase categories"
ON purchase_categories
FOR SELECT
-- USING (
--     company_id IN (
--         -- Dla pracowników - ich firma
--         SELECT company_id FROM employees WHERE user_id = auth.uid()
--         UNION
--         -- Dla menedżerów - ich firma
--         SELECT id FROM companies WHERE manager_id = auth.uid()
--     )
-- );
USING (true); -- Tymczasowo pozwalamy wszystkim użytkownikom widzieć wszystkie kategorie

-- Aktualizacja polityki INSERT - użytkownicy mogą dodawać kategorie tylko dla swojej firmy
DROP POLICY IF EXISTS "Users can insert purchase categories" ON purchase_categories;
CREATE POLICY "Users can insert purchase categories"
ON purchase_categories
FOR INSERT
WITH CHECK (
    company_id IN (
        -- Dla pracowników - ich firma
        SELECT company_id FROM employees WHERE user_id = auth.uid()
        UNION
        -- Dla menedżerów - ich firma
        SELECT id FROM companies WHERE manager_id = auth.uid()
    )
);

-- Dodaj politykę UPDATE - użytkownicy mogą aktualizować kategorie tylko dla swojej firmy
DROP POLICY IF EXISTS "Users can update purchase categories" ON purchase_categories;
CREATE POLICY "Users can update purchase categories"
ON purchase_categories
FOR UPDATE
USING (
    company_id IN (
        -- Dla pracowników - ich firma
        SELECT company_id FROM employees WHERE user_id = auth.uid()
        UNION
        -- Dla menedżerów - ich firma
        SELECT id FROM companies WHERE manager_id = auth.uid()
    )
);

-- Dodaj politykę DELETE - użytkownicy mogą usuwać kategorie tylko dla swojej firmy
DROP POLICY IF EXISTS "Users can delete purchase categories" ON purchase_categories;
CREATE POLICY "Users can delete purchase categories"
ON purchase_categories
FOR DELETE
USING (
    company_id IN (
        -- Dla pracowników - ich firma
        SELECT company_id FROM employees WHERE user_id = auth.uid()
        UNION
        -- Dla menedżerów - ich firma
        SELECT id FROM companies WHERE manager_id = auth.uid()
    )
); 