// Mock dla podstawowych operacji bazodanowych Supabase
// Ten plik zawiera symulowane odpowiedzi dla zapytań bazodanowych

// Symulowana tabela settings
const settingsTable = [
  {
    id: 1,
    user_id: 'mock-user-id',
    preferred_language: 'pl',
    theme: 'light',
    notifications_enabled: true
  }
];

// Symulowana tabela users/profiles
const usersTable = [
  {
    id: 'mock-user-id',
    full_name: 'Test User',
    email: '<EMAIL>',
    role: 'company',
    company_id: 'mock-company-id'
  }
];

// Symulowana tabela companies
const companiesTable = [
  {
    id: 'mock-company-id',
    name: 'Test Company',
    owner_id: 'mock-user-id',
    created_at: new Date().toISOString()
  }
];

// Symulowana tabela tasks
const tasksTable = [
  {
    id: 'mock-task-id-1',
    company_id: 'mock-company-id',
    client_name: 'Test Client',
    address: 'ul. Testowa 1, 00-001 Miasto',
    work_scope: 'Przykładowy zakres prac',
    status: 'pending',
    start_time: new Date().toISOString(),
    assigned_employees: ['mock-user-id']
  }
];

// Dodajmy więcej symulowanych tabel

// Symulowana tabela work_sessions
const workSessionsTable = [
  {
    id: 'mock-work-session-1',
    employee_id: 'mock-user-id',
    company_id: 'mock-company-id',
    task_id: 'mock-task-id-1',
    job_order: 'Test Job',
    start_time: new Date(Date.now() - 3600000).toISOString(), // 1 godzina temu
    end_time: null,
    duration_minutes: null,
    status: 'active',
    is_main_session: true
  }
];

// Symulowana tabela task_activities
const taskActivitiesTable = [
  {
    id: 'mock-activity-1',
    task_id: 'mock-task-id-1',
    employee_id: 'mock-user-id',
    start_time: new Date(Date.now() - 3600000).toISOString(),
    end_time: null,
    status: 'active'
  }
];

// Klasa MockTable do symulacji zapytań do tabel
class MockTable {
  private table: any[];
  private name: string;
  private filteredData: any[] | null = null;

  constructor(name: string, data: any[]) {
    this.table = data;
    this.name = name;
  }

  select(columns: string) {
    console.log(`[MOCK DB] SELECT ${columns} FROM ${this.name}`);
    this.filteredData = null; // Reset filtrowanych danych
    
    // Symuluj zwracanie wyniku z potencjalnymi metodami filtrowania
    const result = {
      eq: this.eq.bind(this),
      not: this.not.bind(this),
      is: this.is.bind(this),
      limit: this.limit.bind(this),
      order: this.order.bind(this),
      single: this.single.bind(this),
      data: this.table,
      error: null
    };
    
    return result;
  }

  // Metoda filtrowania eq (equals)
  eq(column: string, value: any) {
    console.log(`[MOCK DB] Filter ${this.name} WHERE ${column} = ${value}`);
    const data = this.filteredData || this.table;
    this.filteredData = data.filter(item => item[column] === value);
    
    return {
      eq: this.eq.bind(this),
      not: this.not.bind(this),
      is: this.is.bind(this),
      single: this.single.bind(this),
      order: this.order.bind(this),
      limit: this.limit.bind(this),
      data: this.filteredData,
      error: null
    };
  }

  // Metoda filtrowania not
  not(column: string, operator: string, value: any) {
    console.log(`[MOCK DB] Filter ${this.name} WHERE NOT ${column} ${operator} ${value}`);
    const data = this.filteredData || this.table;
    
    if (operator === 'eq' || operator === '=') {
      this.filteredData = data.filter(item => item[column] !== value);
    } else if (operator === 'is') {
      if (value === null) {
        this.filteredData = data.filter(item => item[column] !== null);
      } else {
        this.filteredData = data.filter(item => item[column] !== value);
      }
    }
    
    return {
      eq: this.eq.bind(this),
      not: this.not.bind(this),
      is: this.is.bind(this),
      single: this.single.bind(this),
      order: this.order.bind(this),
      limit: this.limit.bind(this),
      data: this.filteredData,
      error: null
    };
  }

  // Metoda filtrowania is (dla null wartości)
  is(column: string, value: any) {
    console.log(`[MOCK DB] Filter ${this.name} WHERE ${column} IS ${value}`);
    const data = this.filteredData || this.table;
    
    if (value === null) {
      this.filteredData = data.filter(item => item[column] === null);
    } else {
      this.filteredData = data.filter(item => item[column] === value);
    }
    
    return {
      eq: this.eq.bind(this),
      not: this.not.bind(this),
      is: this.is.bind(this),
      single: this.single.bind(this),
      order: this.order.bind(this),
      limit: this.limit.bind(this),
      data: this.filteredData,
      error: null
    };
  }

  // Metoda limitowania wyników
  limit(limit: number) {
    console.log(`[MOCK DB] LIMIT ${limit} on ${this.name}`);
    const data = this.filteredData || this.table;
    const limitedData = data.slice(0, limit);
    
    return {
      single: this.single.bind(this),
      data: limitedData,
      error: null
    };
  }

  // Metoda sortowania wyników
  order(column: string, options: { ascending: boolean } = { ascending: true }) {
    console.log(`[MOCK DB] ORDER BY ${column} ${options.ascending ? 'ASC' : 'DESC'} on ${this.name}`);
    const data = this.filteredData || this.table;
    
    const sortedData = [...data].sort((a, b) => {
      if (a[column] === null) return options.ascending ? -1 : 1;
      if (b[column] === null) return options.ascending ? 1 : -1;
      
      if (a[column] < b[column]) return options.ascending ? -1 : 1;
      if (a[column] > b[column]) return options.ascending ? 1 : -1;
      return 0;
    });
    
    this.filteredData = sortedData;
    
    return {
      eq: this.eq.bind(this),
      not: this.not.bind(this),
      is: this.is.bind(this),
      single: this.single.bind(this),
      limit: this.limit.bind(this),
      data: this.filteredData,
      error: null
    };
  }

  // Metoda pobierająca pojedynczy wiersz
  single() {
    console.log(`[MOCK DB] Get single row from ${this.name}`);
    const data = this.filteredData || this.table;
    return {
      data: data.length > 0 ? data[0] : null,
      error: null
    };
  }

  // Metoda do wstawiania danych
  insert(data: any, options = {}) {
    console.log(`[MOCK DB] INSERT INTO ${this.name}`, data);
    const newItem = { id: `mock-${this.name}-${Date.now()}`, ...data };
    this.table.push(newItem);
    
    const response = {
      data: newItem,
      error: null
    };
    
    // Dodajemy metodę select() aby emulować Supabase
    Object.defineProperty(response, 'select', {
      value: () => response,
      enumerable: true
    });
    
    return response;
  }

  // Metoda do aktualizacji danych
  update(data: any) {
    console.log(`[MOCK DB] UPDATE ${this.name}`, data);
    return {
      eq: (column: string, value: any) => {
        const index = this.table.findIndex(item => item[column] === value);
        if (index !== -1) {
          this.table[index] = { ...this.table[index], ...data };
          
          const response = {
            data: this.table[index],
            error: null
          };
          
          // Dodajemy metodę select() aby emulować Supabase
          Object.defineProperty(response, 'select', {
            value: () => response,
            enumerable: true
          });
          
          return response;
        }
        
        const response = {
          data: null,
          error: null
        };
        
        // Dodajemy metodę select() aby emulować Supabase
        Object.defineProperty(response, 'select', {
          value: () => response,
          enumerable: true
        });
        
        return response;
      }
    };
  }
  
  // Metoda do usuwania danych
  delete() {
    console.log(`[MOCK DB] DELETE FROM ${this.name}`);
    return {
      eq: (column: string, value: any) => {
        const index = this.table.findIndex(item => item[column] === value);
        let removed = null;
        
        if (index !== -1) {
          removed = this.table[index];
          this.table.splice(index, 1);
        }
        
        return {
          data: removed,
          error: null
        };
      }
    };
  }
}

// Eksportuj mocki dla różnych tabel
export const mockDatabase = {
  from: (tableName: string) => {
    switch(tableName) {
      case 'settings':
        return new MockTable('settings', settingsTable);
      case 'users':
      case 'profiles':
        return new MockTable('users', usersTable);
      case 'companies':
        return new MockTable('companies', companiesTable);
      case 'tasks':
        return new MockTable('tasks', tasksTable);
      case 'work_sessions':
        return new MockTable('work_sessions', workSessionsTable);
      case 'task_activities':
        return new MockTable('task_activities', taskActivitiesTable);
      default:
        console.log(`[MOCK DB] No mock for table: ${tableName}`);
        return new MockTable(tableName, []);
    }
  },
  
  // Dodajemy metodę rpc aby symulować procedury RPC Supabase
  rpc: (procedureName: string, params: any = {}) => {
    console.log(`[MOCK DB] Calling RPC ${procedureName} with params:`, params);
    
    // Możemy obsłużyć różne procedury
    if (procedureName === 'get_category_by_id') {
      return {
        data: { id: params.cat_id, name: 'Mock Category', company_id: 'mock-company-id' },
        error: null
      };
    }
    
    if (procedureName === 'update_category_active_status') {
      return {
        data: { success: true },
        error: null
      };
    }
    
    // Domyślna odpowiedź dla nieobsługiwanych procedur
    return {
      data: null,
      error: { message: `Mock RPC: Procedura ${procedureName} nie jest obsługiwana` }
    };
  }
}; 