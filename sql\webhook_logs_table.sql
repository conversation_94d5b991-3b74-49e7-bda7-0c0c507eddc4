-- Tabela do logowania zdarzeń webhook ze Stripe
CREATE TABLE IF NOT EXISTS webhook_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type TEXT NOT NULL,
  event_id TEXT NOT NULL,
  company_id TEXT NOT NULL,
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Polityka bezpieczeństwa
ALTER TABLE webhook_logs ENABLE ROW LEVEL SECURITY;

-- Tylko administratorzy mogą widzieć logi webhooków
CREATE POLICY "Tylko administratorzy mogą widzieć logi webhooków" 
  ON webhook_logs FOR SELECT 
  USING (auth.role() = 'service_role' OR 
         EXISTS (SELECT 1 FROM companies WHERE companies.id = webhook_logs.company_id AND companies.owner_id = auth.uid())); 