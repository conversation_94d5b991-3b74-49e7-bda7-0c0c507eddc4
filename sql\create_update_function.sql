-- Ten skrypt tworzy funkcję RPC do aktualizacji typu konta firmy
-- Wykonaj ten skrypt w konsoli SQL Supabase

-- Funkcja RPC do aktualizacji typu konta firmy
CREATE OR REPLACE FUNCTION update_company_account_type_rpc(
  p_company_id UUID,
  p_plan_name TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  verification_limit INTEGER;
BEGIN
  -- Ustaw limit kodów weryfikacyjnych na podstawie planu
  verification_limit := CASE
    WHEN p_plan_name LIKE '%Basic%' THEN 5
    WHEN p_plan_name LIKE '%Pro%' THEN 20
    WHEN p_plan_name LIKE '%Business%' THEN 999999
    ELSE 5
  END;
  
  -- Aktualizuj firmę
  UPDATE companies
  SET account_type = p_plan_name,
      verification_code_limit = verification_limit,
      updated_at = now()
  WHERE id = p_company_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER; -- Ważne: funkcja działa z uprawnieniami właściciela (nie podlega RLS)

-- Nadaj uprawnienia do funkcji
GRANT EXECUTE ON FUNCTION update_company_account_type_rpc TO service_role;
GRANT EXECUTE ON FUNCTION update_company_account_type_rpc TO authenticated;

-- Testowa aktualizacja dla firmy z obrazka
SELECT update_company_account_type_rpc(
  '90c35057-24c0-432e-8d06-68ae46ce3979'::UUID, 
  'Basic'
);

-- Sprawdź wyniki
SELECT id, name, account_type, verification_code_limit
FROM companies
WHERE id = '90c35057-24c0-432e-8d06-68ae46ce3979'; 