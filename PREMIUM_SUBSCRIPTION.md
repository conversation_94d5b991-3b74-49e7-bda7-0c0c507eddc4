# Implementacja konta premium z integracją Stripe

Ten dokument opisuje implementację funkcji konta premium w aplikacji WorkFlow, zintegrowaną z systemem płatności Stripe.

## Struktura implementacji

Implementacja składa się z następujących elementów:

1. **Baza danych** - tabele Supabase do obsługi planów subskrypcji, płatności i danych użytkowników premium
2. **Serwis integracji** - `stripeService.ts` do komunikacji z API Stripe
3. **Interfejs użytkownika** - panele zarządzania subskrypcjami w panelu administratora
4. **Webhook handler** - funkcje Edge w Supabase do obsługi zdarzeń z systemu Stripe

## Struktura bazy danych

Wdrożenie wymaga utworzenia następujących tabel w bazie danych:

- `subscription_plans` - dostępne plany subskrypcji
- `company_subscriptions` - informacje o subskrypcjach firm
- `payment_history` - historia płatności i transakcji
- `payment_methods` - zapisane metody płatności użytkowników

Szczegółowa struktura tabel znajduje się w pliku `sql/subscription_tables.sql`.

## Integracja z API Stripe

Integracja z systemem Stripe została zaimplementowana w pliku `services/stripeService.ts`. Główne funkcje to:

- `createCheckoutSession` - tworzenie sesji płatności
- `getSubscriptionPlans` - pobieranie dostępnych planów
- `getActiveSubscription` - sprawdzanie aktywnej subskrypcji
- `cancelSubscription` - anulowanie subskrypcji
- `hasActiveSubscription` - sprawdzenie czy firma ma aktywny plan premium

## Interfejs użytkownika

Interfejs użytkownika dla zarządzania subskrypcjami został zaimplementowany w następujących komponentach:

1. **SubscriptionManagement.tsx** - główny komponent zarządzania subskrypcjami
   - Wyświetla aktualny plan
   - Pozwala na wykupienie nowego planu
   - Umożliwia anulowanie subskrypcji
   - Wyświetla historię płatności

2. **AdminPanel.tsx** - rozszerzenie panelu administratora
   - Przycisk do wykupu premium
   - Informacja o limicie kodów weryfikacyjnych
   - Przekierowanie do panelu zarządzania subskrypcjami

## Obsługa webhooków Stripe

Webhooks Stripe są obsługiwane przez funkcję Edge w Supabase (`stripe-webhooks/index.ts`), która przetwarza następujące zdarzenia:

- `customer.subscription.created` - utworzenie nowej subskrypcji
- `customer.subscription.updated` - aktualizacja subskrypcji
- `customer.subscription.deleted` - usunięcie subskrypcji
- `invoice.paid` - opłacenie faktury
- `invoice.payment_failed` - nieudana płatność

## Wdrożenie i konfiguracja

### Konfiguracja Stripe

1. Utwórz konto Stripe na stronie [stripe.com](https://stripe.com)
2. Utwórz produkty i cenniki odpowiadające planom w bazie danych
3. Skonfiguruj webhooks, wskazując na URL funkcji Edge w Supabase
4. Uzyskaj klucze API: publiczny i prywatny

### Konfiguracja Supabase

1. Uruchom skrypt SQL z `sql/subscription_tables.sql` aby utworzyć wymagane tabele
2. Wdróż funkcję Edge dla webhooków do Supabase
3. Ustaw zmienne środowiskowe w Supabase:
   - `STRIPE_SECRET_KEY` - tajny klucz API Stripe
   - `STRIPE_WEBHOOK_SECRET` - tajny klucz do weryfikacji webhooków
   - `STRIPE_PUBLIC_KEY` - publiczny klucz API Stripe

## Funkcje premium

Aktualnie wprowadzone funkcje premium:

1. **Nieograniczone kody weryfikacyjne** - użytkownicy darmowi mają limit 2 kodów, premium nie mają limitu
2. **Wsparcie priorytetowe** - użytkownicy premium mają dostęp do priorytetowego wsparcia
3. **Dodatkowe funkcje administracyjne** - zależnie od planu, użytkownicy premium mają dostęp do dodatkowych funkcji

## Rozszerzenia w przyszłości

Planowane rozszerzenia systemu subskrypcji premium:

1. **Panel analityczny** - zaawansowane analizy i raporty
2. **Integracje z zewnętrznymi systemami** - dodatkowe API i integracje
3. **Więcej administratorów** - możliwość dodania większej liczby administratorów
4. **Niestandardowe logo firmy** - możliwość dodania własnego logo
5. **Eksport danych** - zaawansowane opcje eksportu danych

## Wartości manipulacji dla planów (cenniki)

Zaimplementowane plany subskrypcji:

| Plan          | Cena miesięczna | Cena roczna | Stripe ID             |
|---------------|-----------------|--------------|-----------------------|
| Basic         | 19,90 PLN       | 199,00 PLN   | price_basic_monthly   |
| Pro           | 39,90 PLN       | 399,00 PLN   | price_pro_monthly     |
| Business      | 89,90 PLN       | 899,00 PLN   | price_business_monthly|

## Testowanie

Aby przetestować integrację Stripe w środowisku deweloperskim:

1. Użyj konta testowego Stripe i kluczy API dla środowiska testowego
2. Użyj [Stripe CLI](https://stripe.com/docs/stripe-cli) do przesyłania testowych webhooków
3. Testowe numery kart do różnych scenariuszy płatności:
   - `4242 4242 4242 4242` - sukces
   - `4000 0000 0000 0002` - odrzucona
   - `4000 0000 0000 9995` - brak środków

## Troubleshooting

Typowe problemy i ich rozwiązania:

1. **Webhook nie działa** - sprawdź klucz webhook secret i logi funkcji Edge
2. **Płatność nie została zarejestrowana** - sprawdź logi webhooków i bazy danych
3. **Użytkownik nie otrzymuje uprawnień premium** - sprawdź aktualizację statusu firmy w bazie danych 