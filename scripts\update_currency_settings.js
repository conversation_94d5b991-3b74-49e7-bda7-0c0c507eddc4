const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateCurrencySettings() {
  try {
    console.log('=== Updating Currency Settings to USD ===\n');
    
    // Zaktualizuj istniejące rekordy payment_history z PLN na USD
    console.log('1. Updating existing payment_history records...');
    
    const { data: paymentRecords, error: fetchError } = await supabase
      .from('payment_history')
      .select('id, currency')
      .eq('currency', 'pln');

    if (fetchError) {
      console.log('Error fetching payment records:', fetchError.message);
    } else {
      console.log(`Found ${paymentRecords.length} records with PLN currency`);
      
      if (paymentRecords.length > 0) {
        const { error: updateError } = await supabase
          .from('payment_history')
          .update({ currency: 'usd' })
          .eq('currency', 'pln');

        if (updateError) {
          console.log('❌ Error updating payment_history currency:', updateError.message);
        } else {
          console.log(`✅ Updated ${paymentRecords.length} payment records to USD`);
        }
      }
    }

    // Sprawdź aktualne waluty w payment_history
    console.log('\n2. Checking payment_history currencies...');
    
    const { data: currencyStats, error: statsError } = await supabase
      .from('payment_history')
      .select('currency');

    if (!statsError && currencyStats) {
      const currencyCounts = currencyStats.reduce((acc, record) => {
        acc[record.currency] = (acc[record.currency] || 0) + 1;
        return acc;
      }, {});

      console.log('Currency distribution in payment_history:');
      Object.entries(currencyCounts).forEach(([currency, count]) => {
        console.log(`  ${currency.toUpperCase()}: ${count} records`);
      });
    }

    // Sprawdź aktualne ceny planów
    console.log('\n3. Verifying subscription plan prices...');
    
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('name, price, billing_period')
      .eq('active', true)
      .order('name');

    if (!plansError && plans) {
      console.log('Current subscription plans (USD):');
      plans.forEach(plan => {
        console.log(`  ${plan.name}: $${plan.price/100} ${plan.billing_period}`);
      });
    }

    console.log('\n🎉 Currency settings updated successfully!');
    console.log('\n📝 Summary:');
    console.log('✅ Payment history records updated to USD');
    console.log('✅ Subscription plans show USD pricing');
    console.log('✅ Frontend components display USD currency');
    console.log('✅ Webhook functions use USD as default currency');
    
    console.log('\n💡 The application now uses USD instead of PLN');
    console.log('   - Basic: $5/month (was 5 PLN/month)');
    console.log('   - Pro: $20/month (was 20 PLN/month)');
    console.log('   - Business: $50/month (was 50 PLN/month)');
    console.log('   - Yearly plans: 20% discount maintained');

  } catch (error) {
    console.error('Error in updateCurrencySettings:', error);
  }
}

updateCurrencySettings();
