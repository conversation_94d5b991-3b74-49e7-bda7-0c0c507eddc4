-- Kod SQL do usunięcia indeksu GIN na kolumnie photos w tabeli purchases
-- Wykonaj ten kod w SQL Editor w konsoli Supabase

-- Ten skrypt rozwiązuje problem z błędem "index row requires X bytes, maximum size is 8191"
-- Problem wynika z próby indeksowania dużych zdjęć lub wielu zdjęć w kolumnie photos

-- 1. <PERSON><PERSON><PERSON><PERSON> istniejącego indeksu GIN na photos
DROP INDEX IF EXISTS purchases_photos_idx;

-- 2. Sprawd<PERSON>ie czy indeks został usunięty
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'purchases_photos_idx'
    ) THEN
        RAISE NOTICE 'UWAGA: Indeks purchases_photos_idx nadal istnieje!';
    ELSE
        RAISE NOTICE 'Indeks purchases_photos_idx został pomyślnie usunięty.';
    END IF;
END
$$;

-- 3. Dodanie informacji o wykonaniu skryptu
SELECT NOW() AS czas_wykonania, 'Usunięcie indeksu GIN na kolumnie photos w tabeli purchases' AS operacja; 