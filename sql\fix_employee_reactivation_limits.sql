-- Poprawka funkcji reaktywacji pracowników - uwzględnienie limitów planu
-- Problem: <PERSON><PERSON><PERSON> reaktywowała wszystkich pracowników bez sprawdzania aktualnych limitów

-- 1. Zaktuali<PERSON><PERSON> reactivate_company_employees
CREATE OR REPLACE FUNCTION reactivate_company_employees(
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_subscription_limit INTEGER;
  v_expired_count INTEGER;
  v_active_count INTEGER;
  v_available_slots INTEGER;
  v_reactivated_count INTEGER := 0;
  employee_record RECORD;
BEGIN
  -- Pobierz limit aktywnych pracowników z planu subskrypcji
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz liczbę aktywnych pracowników
  SELECT COUNT(*) INTO v_active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- O<PERSON>licz dostępne miejsca
  v_available_slots := v_subscription_limit - v_active_count;

  -- <PERSON><PERSON><PERSON> nie ma dostępnych miejsc, zakończ
  IF v_available_slots <= 0 THEN
    RETURN jsonb_build_object(
      'success', true,
      'message', 'Osiągnięto limit aktywnych pracowników',
      'reactivated_count', 0,
      'active_count', v_active_count,
      'limit', v_subscription_limit
    );
  END IF;

  -- Pobierz liczbę nieaktywnych pracowników
  SELECT COUNT(*) INTO v_expired_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'SUBSCRIPTION_EXPIRED';

  -- Jeśli nie ma nieaktywnych pracowników, zakończ
  IF v_expired_count = 0 THEN
    RETURN jsonb_build_object(
      'success', true,
      'message', 'Brak nieaktywnych pracowników do reaktywacji',
      'reactivated_count', 0
    );
  END IF;

  -- Reaktywuj pracowników do dostępnych miejsc
  FOR employee_record IN
    SELECT id FROM employees
    WHERE company_id = p_company_id
    AND subscription_status = 'SUBSCRIPTION_EXPIRED'
    ORDER BY last_status_change ASC -- Najpierw ci, którzy zostali dezaktywowani najwcześniej
    LIMIT v_available_slots
  LOOP
    UPDATE employees
    SET subscription_status = 'ACTIVE',
        last_status_change = NOW()
    WHERE id = employee_record.id;
    
    v_reactivated_count := v_reactivated_count + 1;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'message', format('Reaktywowano %s z %s nieaktywnych pracowników (dostępne miejsca: %s)', v_reactivated_count, v_expired_count, v_available_slots),
    'reactivated_count', v_reactivated_count,
    'remaining_expired', v_expired_count - v_reactivated_count,
    'active_count', v_active_count + v_reactivated_count,
    'limit', v_subscription_limit
  );
END;
$$ LANGUAGE plpgsql;

-- 2. Sprawdź obecną sytuację firm z przekroczonymi limitami
SELECT 
  c.id,
  c.name,
  c.account_type,
  c.verification_code_limit as limit,
  COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') as active_employees,
  COUNT(e.id) FILTER (WHERE e.subscription_status = 'SUBSCRIPTION_EXPIRED') as expired_employees,
  COUNT(e.id) as total_employees,
  CASE 
    WHEN COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') > c.verification_code_limit 
    THEN 'PRZEKROCZONY LIMIT'
    ELSE 'OK'
  END as status
FROM companies c
LEFT JOIN employees e ON c.id = e.company_id
WHERE c.account_type != 'free'
GROUP BY c.id, c.name, c.account_type, c.verification_code_limit
HAVING COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') > c.verification_code_limit
ORDER BY active_employees DESC;

-- 3. Funkcja do naprawy firm z przekroczonymi limitami
CREATE OR REPLACE FUNCTION fix_companies_with_exceeded_limits()
RETURNS TABLE(
  company_id UUID,
  company_name TEXT,
  plan_type TEXT,
  limit_value INTEGER,
  active_before INTEGER,
  deactivated_count INTEGER,
  active_after INTEGER
) AS $$
DECLARE
  company_rec RECORD;
  employees_to_deactivate INTEGER;
  deactivated INTEGER;
BEGIN
  FOR company_rec IN
    SELECT 
      c.id,
      c.name,
      c.account_type,
      c.verification_code_limit as limit,
      COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') as active_count
    FROM companies c
    LEFT JOIN employees e ON c.id = e.company_id
    WHERE c.account_type != 'free'
    GROUP BY c.id, c.name, c.account_type, c.verification_code_limit
    HAVING COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') > c.verification_code_limit
  LOOP
    employees_to_deactivate := company_rec.active_count - company_rec.limit;
    
    -- Dezaktywuj nadmiarowych pracowników (ostatnio aktywowanych)
    UPDATE employees
    SET subscription_status = 'SUBSCRIPTION_EXPIRED',
        last_status_change = NOW()
    WHERE id IN (
      SELECT id FROM employees
      WHERE company_id = company_rec.id
      AND subscription_status = 'ACTIVE'
      ORDER BY last_status_change DESC NULLS LAST
      LIMIT employees_to_deactivate
    );
    
    GET DIAGNOSTICS deactivated = ROW_COUNT;
    
    -- Zwróć wyniki
    company_id := company_rec.id;
    company_name := company_rec.name;
    plan_type := company_rec.account_type;
    limit_value := company_rec.limit;
    active_before := company_rec.active_count;
    deactivated_count := deactivated;
    active_after := company_rec.active_count - deactivated;
    
    RETURN NEXT;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 4. Wyświetl instrukcje
SELECT 'INSTRUKCJE:' as info;
SELECT '1. Sprawdź firmy z przekroczonymi limitami powyżej' as step;
SELECT '2. Jeśli chcesz naprawić automatycznie, uruchom: SELECT * FROM fix_companies_with_exceeded_limits();' as step;
SELECT '3. Sprawdź wyniki ponownie po naprawie' as step;
