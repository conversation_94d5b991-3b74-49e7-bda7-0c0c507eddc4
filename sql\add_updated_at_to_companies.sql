-- <PERSON><PERSON><PERSON> kolumnę updated_at do tabeli companies jeśli nie istnieje
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();

-- <PERSON><PERSON><PERSON> trigger do automatycznej aktualizacji updated_at
CREATE OR REPLACE FUNCTION update_companies_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Utw<PERSON>rz trigger jeśli nie istnieje
DROP TRIGGER IF EXISTS update_companies_updated_at_trigger ON companies;
CREATE TRIGGER update_companies_updated_at_trigger
  BEFORE UPDATE ON companies
  FOR EACH ROW
  EXECUTE FUNCTION update_companies_updated_at();

-- Sprawdź strukturę tabeli companies
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM 
  information_schema.columns 
WHERE 
  table_name = 'companies' 
  AND column_name IN ('account_type', 'verification_code_limit', 'updated_at')
ORDER BY 
  ordinal_position;
