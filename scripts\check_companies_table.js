const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkCompaniesTable() {
  try {
    console.log('Checking companies table structure...');
    
    // Sprawdź czy kolumna updated_at istnieje w tabeli companies
    console.log('Checking if updated_at column exists in companies table...');
    const { data: companyWithUpdatedAt, error: updatedAtError } = await supabase
      .from('companies')
      .select('id, name, account_type, verification_code_limit, updated_at')
      .limit(1)
      .single();

    if (updatedAtError) {
      console.error('Error checking updated_at column:', updatedAtError);

      // Jeśli kolumna nie istnieje, dodaj ją
      console.log('Adding updated_at column to companies table...');
      const { data: addColumnResult, error: addColumnError } = await supabase
        .from('companies')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', 'a0736ed8-4ac3-4728-b0a0-a0fad9164e4a');

      if (addColumnError) {
        console.error('Column updated_at does not exist and cannot be added via API:', addColumnError);
      }
    } else {
      console.log('✓ updated_at column exists:', companyWithUpdatedAt.updated_at);
    }

    // Sprawdź obecne dane w tabeli companies
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('id, name, account_type, verification_code_limit')
      .limit(5);

    if (companiesError) {
      console.error('Error fetching companies:', companiesError);
    } else {
      console.log('Sample companies data:', companies);
    }

    // Sprawdź subskrypcje bezpośrednio z tabel
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('company_subscriptions')
      .select(`
        id,
        company_id,
        status,
        companies!inner(id, name, account_type, verification_code_limit),
        subscription_plans!inner(id, name)
      `)
      .in('status', ['active', 'canceled'])
      .limit(10);

    if (subscriptionsError) {
      console.error('Error fetching subscriptions:', subscriptionsError);
    } else {
      console.log('Companies with subscriptions:', subscriptions);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkCompaniesTable();
