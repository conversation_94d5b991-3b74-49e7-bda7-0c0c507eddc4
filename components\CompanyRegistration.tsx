import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, Platform, ScrollView } from 'react-native';
import { supabase } from '../services/supabaseClient';
import { Ionicons } from '@expo/vector-icons';
import PrismIcon from './PrismIcon';

interface CompanyRegistrationProps {
  onBack: () => void;
}

const CompanyRegistration = ({ onBack }: CompanyRegistrationProps) => {
  const [companyName, setCompanyName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    if (!companyName.trim()) {
      Alert.alert('Error', 'Please enter company name');
      return false;
    }
    if (!email.trim()) {
      Alert.alert('Error', 'Please enter email address');
      return false;
    }
    if (!password) {
      Alert.alert('Error', 'Please enter password');
      return false;
    }
    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return false;
    }
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }
    return true;
  };

  const createCompanyRecord = async (userId: string) => {
    console.log('Creating company record for user:', userId);
    try {
      // First try to sign in to ensure we have a valid session
      try {
        await signInWithEmail();
        console.log('Successfully signed in before creating company record');
      } catch (signInError) {
        console.error('Error signing in before company creation:', signInError);
        throw signInError;
      }

      // Now try to create the company record
      const { data, error } = await supabase
        .from('companies')
        .insert([
          {
            id: userId,
            name: companyName,
            employee_count: 0
          }
        ])
        .select();

      if (error) {
        console.error('Error creating company record:', error.message);
        console.error('Error details:', error);
        throw error;
      }

      console.log('Company record created successfully:', data);
      return data;
    } catch (error) {
      console.error('Exception in createCompanyRecord:', error);
      throw error;
    }
  };

  const signInWithEmail = async () => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Sign in error:', error);
      throw error;
    }

    return data;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      console.log('Starting registration process...');
      
      // 1. Register user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            company_name: companyName,
            user_type: 'company',
          }
        },
      });

      console.log('Auth response:', { authData, authError });

      if (authError) {
        console.error('Auth error:', authError);
        
        // Handle rate limit error specifically
        if (authError.message.includes('rate limit')) {
          Alert.alert(
            'Too Many Attempts',
            'You have made too many registration attempts. Please try again later or use a different email address.',
            [{ text: 'OK' }]
          );
          return;
        }
        
        Alert.alert('Error', authError.message);
        return;
      }

      if (!authData.user) {
        console.error('No user data received');
        Alert.alert('Error', 'Failed to create user account');
        return;
      }

      console.log('User created successfully:', authData.user.id);

      // 2. Create company record with retry logic
      let retryCount = 0;
      const maxRetries = 3;
      let companyData = null;

      while (retryCount < maxRetries) {
        try {
          // Add a delay before first attempt
          if (retryCount === 0) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          
          companyData = await createCompanyRecord(authData.user.id);
          console.log('Company creation successful:', companyData);
          break;
        } catch (companyError: any) {
          console.error(`Error in company creation attempt ${retryCount + 1}:`, companyError);
          if (retryCount === maxRetries - 1) {
            Alert.alert('Error', 'Failed to create company record. Please try signing in and completing the setup.');
            return;
          }
          retryCount++;
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }
      
      Alert.alert(
        'Account Created Successfully',
        'Your account has been created and you will be redirected to the dashboard.',
        [{ text: 'OK', onPress: onBack }]
      );

    } catch (error: any) {
      console.error('Registration error:', error);
      
      // Handle rate limit error in catch block as well
      if (error.message?.includes('rate limit')) {
        Alert.alert(
          'Too Many Attempts',
          'You have made too many registration attempts. Please try again later or use a different email address.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      Alert.alert('Error', 'An unexpected error occurred during registration');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView 
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={styles.logoContainer}>
        <PrismIcon />
      </View>

      <View style={styles.formWrapper}>
        <Text style={styles.title}>Create Company Account</Text>
        <Text style={styles.subtitle}>Register your company to start managing employees</Text>

        <View style={styles.form}>
          <Text style={styles.label}>Company Name</Text>
          <TextInput
            style={styles.input}
            value={companyName}
            onChangeText={setCompanyName}
            placeholder="Enter company name"
            editable={!isLoading}
          />

          <Text style={styles.label}>Email Address</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
            editable={!isLoading}
          />

          <Text style={styles.label}>Password</Text>
          <TextInput
            style={styles.input}
            value={password}
            onChangeText={setPassword}
            placeholder="Enter password"
            secureTextEntry
            editable={!isLoading}
          />

          <Text style={styles.label}>Confirm Password</Text>
          <TextInput
            style={styles.input}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            placeholder="Confirm password"
            secureTextEntry
            editable={!isLoading}
          />

          <TouchableOpacity 
            style={[styles.registerButton, isLoading && styles.registerButtonDisabled]}
            onPress={handleRegister}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.registerButtonText}>Create Account</Text>
            )}
          </TouchableOpacity>
          
          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <TouchableOpacity onPress={onBack}>
              <Text style={styles.signInLink}>Sign in</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E6EDF',
  },
  contentContainer: {
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 10 : 20,
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 20,
  },
  formWrapper: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    marginBottom: 32,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  label: {
    fontSize: 14,
    color: 'white',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 0,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  registerButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  registerButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  registerButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    marginTop: 24,
    justifyContent: 'center',
  },
  footerText: {
    color: 'white',
    fontSize: 16,
  },
  signInLink: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CompanyRegistration; 