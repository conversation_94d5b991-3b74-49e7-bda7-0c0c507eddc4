// Prosty polyfill dla modułu zlib, potrzebny dla biblioteki ws (WebSocket)
const zlib = {
  // Funkcje kompresji, które zwracają pusty bufor lub dane wejściowe bez zmian
  createDeflate: () => createDummyStream(),
  createInflate: () => createDummyStream(),
  createDeflateRaw: () => createDummyStream(),
  createInflateRaw: () => createDummyStream(),
  createGzip: () => createDummyStream(),
  createGunzip: () => createDummyStream(),
  createUnzip: () => createDummyStream(),
  
  // Funkcje synchroniczne zwracające dane wejściowe bez zmian
  deflateSync: (buffer) => buffer,
  inflateSync: (buffer) => buffer,
  deflateRawSync: (buffer) => buffer,
  inflateRawSync: (buffer) => buffer,
  gzipSync: (buffer) => buffer,
  gunzipSync: (buffer) => buffer,
  unzipSync: (buffer) => buffer,
  
  // Stałe
  Z_NO_FLUSH: 0,
  Z_PARTIAL_FLUSH: 1,
  Z_SYNC_FLUSH: 2,
  Z_FULL_FLUSH: 3,
  Z_FINISH: 4,
  Z_BLOCK: 5,
  Z_OK: 0,
  Z_STREAM_END: 1,
  Z_NEED_DICT: 2,
  Z_ERRNO: -1,
  Z_STREAM_ERROR: -2,
  Z_DATA_ERROR: -3,
  Z_MEM_ERROR: -4,
  Z_BUF_ERROR: -5,
  Z_VERSION_ERROR: -6,
  Z_NO_COMPRESSION: 0,
  Z_BEST_SPEED: 1,
  Z_BEST_COMPRESSION: 9,
  Z_DEFAULT_COMPRESSION: -1,
  Z_FILTERED: 1,
  Z_HUFFMAN_ONLY: 2,
  Z_RLE: 3,
  Z_FIXED: 4,
  Z_DEFAULT_STRATEGY: 0
};

// Funkcja pomocnicza tworząca "stream" dla metod kompresji
function createDummyStream() {
  const stream = {
    on: (event, handler) => stream,
    once: (event, handler) => stream,
    pipe: (destination) => destination,
    flush: (callback) => callback && callback(),
    end: (data, callback) => callback && callback(),
    write: (data, callback) => callback && callback(),
    close: () => {},
    destroy: () => {}
  };
  return stream;
}

// Używamy module.exports dla lepszej kompatybilności z modułami CommonJS
module.exports = zlib; 