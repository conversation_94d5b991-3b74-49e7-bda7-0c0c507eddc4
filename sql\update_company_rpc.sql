-- Funkcja RPC do aktualizacji typu konta firmy
CREATE OR REPLACE FUNCTION update_company_account_type_rpc(
  p_company_id UUID,
  p_plan_name TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  verification_limit INTEGER;
  normalized_plan_name TEXT;
BEGIN
  -- Normalizuj nazwę planu do małych liter
  normalized_plan_name := lower(p_plan_name);
  
  -- Ustaw limit kodów weryfikacyjnych na podstawie planu
  verification_limit := CASE
    WHEN normalized_plan_name LIKE '%basic%' THEN 5
    WHEN normalized_plan_name LIKE '%pro%' THEN 20
    WHEN normalized_plan_name LIKE '%business%' THEN 999999
    ELSE 2 -- dla planu free
  END;
  
  -- Aktualizuj firmę
  UPDATE companies
  SET account_type = normalized_plan_name,
      verification_code_limit = verification_limit
  WHERE id = p_company_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER; -- Ważne: funkcja działa z uprawnieniami właściciela (nie podlega RLS)

-- Nadaj uprawnienia do funkcji
GRANT EXECUTE ON FUNCTION update_company_account_type_rpc TO service_role;
GRANT EXECUTE ON FUNCTION update_company_account_type_rpc TO authenticated; 