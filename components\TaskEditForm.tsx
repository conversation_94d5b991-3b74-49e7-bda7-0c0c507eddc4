import React, { useState, useEffect, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  Modal,
  Platform,
  Alert,
  Image,
  ActivityIndicator
} from 'react-native';
import { supabase } from '../services/supabaseClient';
import { Ionicons } from '@expo/vector-icons';
import { TopBar } from './TopBar';
import DatePicker from './DatePicker';
import TimePicker from './TimePicker';
import * as ImagePicker from 'expo-image-picker';
import { decode } from 'base64-arraybuffer';
import { i18n } from '../utils/localization';

interface TaskEditFormProps {
  taskId: string;
  onBack: () => void;
  onMenuPress: () => void;
  companyId: string;
}

interface Employee {
  id: string;
  full_name: string;
  email: string;
  status: string;
}

interface AlertButton {
  text: string;
  onPress: () => void;
  style?: 'cancel' | 'default';
}

interface TaskForm {
  date: Date;
  clientName: string;
  address: string;
  workScope: string;
  startTime: Date;
  selectedEmployees: string[];
  additionalInfo: string;
  status: 'pending' | 'in_progress' | 'completed';
  photosBefore: string[];
  photosAfter: string[];
}

export const TaskEditForm: React.FC<TaskEditFormProps> = ({ 
  taskId, 
  onBack, 
  onMenuPress,
  companyId
}) => {
  const [loading, setLoading] = useState(true);
  const [uploadingPhotos, setUploadingPhotos] = useState(false);
  const [formError, setFormError] = useState('');
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [showEmployeeModal, setShowEmployeeModal] = useState(false);
  
  // Alert state
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [alertButtons, setAlertButtons] = useState<AlertButton[]>([]);
  
  // Web alert implementation for consistent styling
  const showWebAlert = useCallback((title: string, message: string, buttons: AlertButton[]) => {
    if (!document) return;
    
    // Find or create the alert container
    let container = document.getElementById('task-edit-alert-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'task-edit-alert-container';
      container.style.position = 'fixed';
      container.style.top = '0';
      container.style.left = '0';
      container.style.width = '100%';
      container.style.height = '100%';
      container.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      container.style.zIndex = '9999';
      container.style.display = 'flex';
      container.style.justifyContent = 'center';
      container.style.alignItems = 'center';
      document.body.appendChild(container);
    }
    
    // Clear any existing content
    container.innerHTML = '';
    
    // Set app font family and apply to all elements
    const fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
    
    // Create alert dialog
    const alertBox = document.createElement('div');
    alertBox.style.backgroundColor = 'white';
    alertBox.style.borderRadius = '8px';
    alertBox.style.width = '90%';
    alertBox.style.maxWidth = '400px';
    alertBox.style.overflow = 'hidden';
    alertBox.style.fontFamily = fontFamily;
    
    // Title
    const titleDiv = document.createElement('div');
    titleDiv.style.padding = '20px 16px';
    titleDiv.style.textAlign = 'center';
    
    const titleText = document.createElement('h3');
    titleText.textContent = title;
    titleText.style.margin = '0';
    titleText.style.fontSize = '18px';
    titleText.style.fontWeight = 'bold';
    titleText.style.color = 'black';
    titleText.style.fontFamily = fontFamily;
    
    titleDiv.appendChild(titleText);
    alertBox.appendChild(titleDiv);
    
    // Message
    const messageDiv = document.createElement('div');
    messageDiv.style.paddingBottom = '20px';
    messageDiv.style.paddingLeft = '16px';
    messageDiv.style.paddingRight = '16px';
    messageDiv.style.textAlign = 'center';
    
    const messageText = document.createElement('p');
    messageText.textContent = message;
    messageText.style.margin = '0';
    messageText.style.fontSize = '16px';
    messageText.style.color = '#333';
    messageText.style.fontFamily = fontFamily;
    
    messageDiv.appendChild(messageText);
    alertBox.appendChild(messageDiv);
    
    // Buttons
    const buttonDiv = document.createElement('div');
    buttonDiv.style.display = 'flex';
    buttonDiv.style.padding = '16px';
    buttonDiv.style.justifyContent = 'space-between';
    
    buttons.forEach((button) => {
      const btnElement = document.createElement('button');
      btnElement.textContent = button.text;
      btnElement.style.flex = '1';
      btnElement.style.padding = '14px 12px';
      btnElement.style.margin = '0 4px';
      btnElement.style.border = 'none';
      btnElement.style.borderRadius = '8px';
      btnElement.style.color = 'white';
      btnElement.style.fontSize = '16px';
      btnElement.style.fontWeight = '500';
      btnElement.style.cursor = 'pointer';
      btnElement.style.fontFamily = fontFamily;
      
      if (button.style === 'cancel') {
        btnElement.style.backgroundColor = '#6B7280';
      } else {
        btnElement.style.backgroundColor = '#2563EB';
      }
      
      btnElement.onclick = () => {
        container.style.display = 'none';
        if (button.onPress) {
          setTimeout(button.onPress, 0);
        }
      };
      
      buttonDiv.appendChild(btnElement);
    });
    
    alertBox.appendChild(buttonDiv);
    container.appendChild(alertBox);
    
    // Show the alert
    container.style.display = 'flex';
  }, []);
  
  // Show alert based on platform
  const showAlert = useCallback((
    title: string, 
    message: string, 
    buttons: AlertButton[]
  ) => {
    // For web, use custom web alert
    if (Platform.OS === 'web') {
      showWebAlert(title, message, buttons);
    } 
    // For mobile, use our custom React Native alert
    else {
      setAlertTitle(title);
      setAlertMessage(message);
      setAlertButtons(buttons);
      setAlertVisible(true);
    }
  }, [showWebAlert]);

  const hideCustomAlert = useCallback(() => {
    setAlertVisible(false);
  }, []);
  
  // Custom alert component
  const CustomAlert = useCallback(() => (
    <Modal
      transparent={true}
      visible={alertVisible}
      onRequestClose={hideCustomAlert}
      animationType="none"
    >
      <View style={styles.modalOverlay}>
        <View style={styles.alertContainer}>
          <View style={styles.alertTitleContainer}>
            <Text style={styles.alertTitle}>{alertTitle}</Text>
          </View>
          
          <View style={styles.alertBodyContainer}>
            <Text style={styles.alertMessage}>{alertMessage}</Text>
          </View>
          
          <View style={styles.alertButtonsContainer}>
            {alertButtons.map((button, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.alertButton,
                  button.style === 'cancel' ? styles.cancelButton : styles.confirmButton,
                  alertButtons.length > 1 && index === 0 ? { marginRight: 8 } : {}
                ]}
                onPress={() => {
                  hideCustomAlert();
                  button.onPress();
                }}
              >
                <Text style={styles.alertButtonText}>
                  {button.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  ), [alertVisible, alertTitle, alertMessage, alertButtons, hideCustomAlert]);
  
  // Inicjalizacja formularza z domyślnymi wartościami
  const [form, setForm] = useState<TaskForm>({
    date: new Date(),
    clientName: '',
    address: '',
    workScope: '',
    startTime: new Date(),
    selectedEmployees: [],
    additionalInfo: '',
    status: 'pending',
    photosBefore: [],
    photosAfter: []
  });

  // Pobierz dane zadania do edycji oraz listę pracowników
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Pobierz dane zadania
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();
        
        if (taskError) {
          console.error('Error fetching task:', taskError);
          setFormError(i18n.t('failedToLoadTask'));
          setLoading(false);
          return;
        }
        
        if (!taskData) {
          setFormError(i18n.t('taskNotFound'));
          setLoading(false);
          return;
        }
        
        // Użyj zdjęć zapisanych w bazie danych, jeśli istnieją
        let photosBefore: string[] = taskData.photos_before || [];
        let photosAfter: string[] = taskData.photos_after || [];
        
        // Jeśli nie ma zdjęć w bazie danych, spróbuj je pobrać z bucketu storage
        if ((!photosBefore || photosBefore.length === 0) && (!photosAfter || photosAfter.length === 0)) {
          console.log("No photos found in database, trying to fetch from storage bucket");
          
          // Pobierz zdjęcia przed z bucketu
        const { data: photosBeforeData, error: photosBeforeError } = await supabase
          .storage
            .from('tasks')
          .list(`${taskId}/before`);
          
        if (!photosBeforeError && photosBeforeData) {
          // Pobierz URLe zdjęć
          photosBefore = await Promise.all(photosBeforeData.map(async (file) => {
            const { data } = await supabase.storage
                .from('tasks')
              .getPublicUrl(`${taskId}/before/${file.name}`);
            return data.publicUrl;
          }));
        }
        
          // Pobierz zdjęcia po z bucketu
        const { data: photosAfterData, error: photosAfterError } = await supabase
          .storage
            .from('tasks')
          .list(`${taskId}/after`);
          
        if (!photosAfterError && photosAfterData) {
          // Pobierz URLe zdjęć
          photosAfter = await Promise.all(photosAfterData.map(async (file) => {
            const { data } = await supabase.storage
                .from('tasks')
              .getPublicUrl(`${taskId}/after/${file.name}`);
            return data.publicUrl;
          }));
          }
        } else {
          console.log("Using photos from database:", { photosBefore, photosAfter });
        }
        
        // Wypełnij formularz danymi zadania
        setForm({
          date: new Date(taskData.date),
          clientName: taskData.client_name,
          address: taskData.address,
          workScope: taskData.work_scope,
          startTime: new Date(`${taskData.date}T${taskData.start_time}`),
          selectedEmployees: taskData.assigned_employees || [],
          additionalInfo: taskData.additional_info || '',
          status: taskData.status,
          photosBefore: photosBefore,
          photosAfter: photosAfter
        });
        
        // Pobierz listę pracowników
        await fetchEmployees();
        
        setLoading(false);
      } catch (error) {
        console.error('Error in fetchData:', error);
        setFormError(i18n.t('errorLoadingData'));
        setLoading(false);
      }
    };
    
    fetchData();
  }, [taskId, companyId]);
  
  // Pobierz listę pracowników przypisanych do danej firmy
  const fetchEmployees = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('company_id', companyId)
        .eq('subscription_status', 'ACTIVE'); // Tylko aktywni pracownicy

      if (error) {
        console.error('Error fetching employees:', error);
        Alert.alert(
          i18n.t('error'),
          i18n.t('errorFetchingEmployees'),
          [{ text: i18n.t('ok'), style: 'default' }]
        );
        return;
      }

      setEmployees(data || []);
    } catch (error) {
      console.error('Exception in fetchEmployees:', error);
      Alert.alert(
        i18n.t('error'),
        i18n.t('unexpectedError'),
        [{ text: i18n.t('ok'), style: 'default' }]
      );
    }
  };
  
  // Przełącz wybór pracownika (dodaj/usuń z listy)
  const toggleEmployeeSelection = (employeeId: string) => {
    setForm(prev => {
      const isSelected = prev.selectedEmployees.includes(employeeId);
      return {
        ...prev,
        selectedEmployees: isSelected
          ? prev.selectedEmployees.filter(id => id !== employeeId)
          : [...prev.selectedEmployees, employeeId]
      };
    });
  };
  
  // Request permissions for media library
  const requestMediaLibraryPermissions = async () => {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        alert(i18n.t('galleryPermissionsNeeded'));
        return false;
      }
      return true;
    }
    return true;
  };
  
  // Upload image to Supabase Storage
  const uploadImage = async (base64Image: string, type: 'before' | 'after'): Promise<string | null> => {
    try {
      const fileName = `${new Date().getTime()}.jpg`;
      const filePath = `${taskId}/${type}/${fileName}`;
      
      // Remove data:image/jpeg;base64, prefix
      const formattedBase64 = base64Image.split(',')[1];
      
      // Upload file
      const { data, error } = await supabase.storage
        .from('tasks')
        .upload(filePath, decode(formattedBase64), {
          contentType: 'image/jpeg',
          upsert: true
        });
      
      if (error) {
        console.error('Error uploading image:', error);
        return null;
      }
      
      // Get public URL
      const { data: urlData } = await supabase.storage
        .from('tasks')
        .getPublicUrl(filePath);
      
      return urlData.publicUrl;
    } catch (error) {
      console.error('Exception in uploadImage:', error);
      return null;
    }
  };
  
  // Handle image picking
  const handleImagePick = async (type: 'before' | 'after') => {
    const hasPermission = await requestMediaLibraryPermissions();
    if (!hasPermission) return;
    
    try {
      setUploadingPhotos(true);
      
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: true
      });
      
      if (!result.canceled && result.assets && result.assets[0].base64) {
        const base64Image = `data:image/jpeg;base64,${result.assets[0].base64}`;
        const imageUrl = await uploadImage(base64Image, type);
        
        if (imageUrl) {
          if (type === 'before') {
            setForm(prev => ({
              ...prev,
              photosBefore: [...prev.photosBefore, imageUrl]
            }));
          } else {
            setForm(prev => ({
              ...prev,
              photosAfter: [...prev.photosAfter, imageUrl]
            }));
          }
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      alert(i18n.t('errorPickingImage'));
    } finally {
      setUploadingPhotos(false);
    }
  };
  
  // Remove image
  const removeImage = (type: 'before' | 'after', index: number) => {
    if (type === 'before') {
      setForm(prev => ({
        ...prev,
        photosBefore: prev.photosBefore.filter((_, i) => i !== index)
      }));
    } else {
      setForm(prev => ({
        ...prev,
        photosAfter: prev.photosAfter.filter((_, i) => i !== index)
      }));
    }
  };
  
  // Validate form
  const validateForm = () => {
    if (
      form.clientName.trim() === '' ||
      form.address.trim() === '' ||
      form.workScope.trim() === ''
    ) {
      setFormError(i18n.t('pleaseCompleteAllRequiredFields'));
      return false;
    }
    
    setFormError('');
    return true;
  };
  
  // Are required fields empty
  const areRequiredFieldsEmpty = () => {
    return (
      form.clientName.trim() === '' ||
      form.address.trim() === '' ||
      form.workScope.trim() === ''
    );
  };
  
  // Save changes
  const handleSaveChanges = async () => {
    if (!validateForm()) return;
    
    try {
      showAlert(
        i18n.t('confirmSaveChanges'),
        i18n.t('areYouSureYouWantToSaveChanges'),
        [
          {
            text: i18n.t('cancel'),
            style: 'cancel',
            onPress: () => {}
          },
          {
            text: i18n.t('save'),
            style: 'default',
            onPress: saveChanges
          }
        ]
      );
    } catch (error) {
      console.error('Exception in handleSaveChanges:', error);
      setFormError(i18n.t('errorSavingChanges'));
    }
  };
  
  // Save changes to database
  const saveChanges = async () => {
    try {
      setLoading(true);
      
      // Format date as YYYY-MM-DD
      const formattedDate = form.date.toISOString().split('T')[0];
      
      // Format time as HH:MM:SS
      const hours = form.startTime.getHours().toString().padStart(2, '0');
      const minutes = form.startTime.getMinutes().toString().padStart(2, '0');
      const seconds = form.startTime.getSeconds().toString().padStart(2, '0');
      const formattedTime = `${hours}:${minutes}:${seconds}`;
      
      // Update task in database
      const { error } = await supabase
        .from('tasks')
        .update({
          date: formattedDate,
          client_name: form.clientName,
          address: form.address,
          work_scope: form.workScope,
          start_time: formattedTime,
          additional_info: form.additionalInfo,
          status: form.status,
          assigned_employees: form.selectedEmployees,  // Ensure we save the selected employees
          photos_before: form.photosBefore,           // Dodajemy zdjęcia przed
          photos_after: form.photosAfter              // Dodajemy zdjęcia po
        })
        .eq('id', taskId);
      
      if (error) {
        console.error('Error updating task:', error);
        showAlert(
          i18n.t('error'),
          i18n.t('failedToUpdateTask'),
          [{ text: i18n.t('ok'), onPress: () => {}, style: 'default' }]
        );
        setLoading(false);
        return;
      }
      
      showAlert(
        i18n.t('success'),
        i18n.t('taskUpdated'),
        [{ text: i18n.t('ok'), onPress: onBack, style: 'default' }]
      );
      
      setLoading(false);
    } catch (error) {
      console.error('Exception in saveChanges:', error);
      showAlert(
        i18n.t('error'),
        i18n.t('errorSavingChanges'),
        [{ text: i18n.t('ok'), onPress: () => {}, style: 'default' }]
      );
      setLoading(false);
    }
  };
  
  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Ionicons name="arrow-back" size={22} color="#1F2937" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{i18n.t('editTask')}</Text>
          <View style={styles.emptySpaceForBalance} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text style={styles.loadingText}>{i18n.t('loadingTask')}</Text>
        </View>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('editTask')}</Text>
        <View style={styles.emptySpaceForBalance} />
      </View>
      
      {/* Custom Alert */}
      <CustomAlert />
      
      <ScrollView 
        style={styles.content} 
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {formError ? <Text style={styles.errorText}>{formError}</Text> : null}

        {/* Data zlecenia */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('date')}</Text>
          <DatePicker
            date={form.date.toISOString().split('T')[0]}
            onDateChange={(value) => {
              if (value) {
                const newDate = new Date(value);
                setForm(prev => ({ ...prev, date: newDate }));
              }
            }}
            placeholder={i18n.t('selectDate')}
          />
        </View>

        {/* Zleceniodawca */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('clientName')}</Text>
          <TextInput
            style={[styles.formInput, form.clientName.trim() === '' ? styles.inputRequired : {}]}
            value={form.clientName}
            onChangeText={(text) => setForm(prev => ({ ...prev, clientName: text }))}
            placeholder={`${i18n.t('clientNameLabel')}`}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Adres */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('address')}</Text>
          <TextInput
            style={[styles.formInput, form.address.trim() === '' ? styles.inputRequired : {}]}
            value={form.address}
            onChangeText={(text) => setForm(prev => ({ ...prev, address: text }))}
            placeholder={`${i18n.t('taskAddressLabel')}`}
            placeholderTextColor="#9CA3AF"
          />
        </View>

        {/* Zakres prac */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('workScope')}</Text>
          <TextInput
            style={[styles.formInput, styles.formTextArea, form.workScope.trim() === '' ? styles.inputRequired : {}]}
            value={form.workScope}
            onChangeText={(text) => setForm(prev => ({ ...prev, workScope: text }))}
            placeholder={`${i18n.t('workScopeLabel')}`}
            placeholderTextColor="#9CA3AF"
            multiline
            numberOfLines={4}
          />
        </View>

        {/* Godzina rozpoczęcia */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('startTime')}</Text>
          <TimePicker
            time={form.startTime}
            onTimeChange={(newTime) => {
              setForm(prev => ({ ...prev, startTime: newTime }));
            }}
            placeholder={i18n.t('selectStartTime')}
          />
        </View>

        {/* Informacje dodatkowe */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('additionalInfo')}</Text>
          <TextInput
            style={[styles.formInput, styles.formTextArea]}
            value={form.additionalInfo}
            onChangeText={(text) => setForm(prev => ({ ...prev, additionalInfo: text }))}
            placeholder={i18n.t('additionalInfoLabel')}
            placeholderTextColor="#9CA3AF"
            multiline
            numberOfLines={4}
          />
        </View>

        {/* Zdjęcia przed */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('photosBeforeWork')} ({form.photosBefore.length})</Text>
          <TouchableOpacity 
            style={styles.photoButton}
            onPress={() => handleImagePick('before')}
            disabled={uploadingPhotos}
          >
            <Ionicons name="camera-outline" size={24} color="#2563EB" />
            <Text style={styles.photoButtonText}>
              {uploadingPhotos ? i18n.t('adding') : i18n.t('addPhoto')}
            </Text>
          </TouchableOpacity>
          <ScrollView horizontal style={styles.photosList}>
            {form.photosBefore.map((photo, index) => (
              <View key={index} style={styles.photoContainer}>
                <Image source={{ uri: photo }} style={styles.photoThumbnail} />
                <TouchableOpacity 
                  style={styles.removePhotoButton}
                  onPress={() => removeImage('before', index)}
                >
                  <Ionicons name="close-circle" size={24} color="#DC2626" />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
          <Text style={styles.formFieldNote}>
            {i18n.t('photosWillBeSaved')}
          </Text>
        </View>

        {/* Zdjęcia po */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('photosAfterWork')} ({form.photosAfter.length})</Text>
          <TouchableOpacity 
            style={styles.photoButton}
            onPress={() => handleImagePick('after')}
            disabled={uploadingPhotos}
          >
            <Ionicons name="camera-outline" size={24} color="#2563EB" />
            <Text style={styles.photoButtonText}>
              {uploadingPhotos ? i18n.t('adding') : i18n.t('addPhoto')}
            </Text>
          </TouchableOpacity>
          <ScrollView horizontal style={styles.photosList}>
            {form.photosAfter.map((photo, index) => (
              <View key={index} style={styles.photoContainer}>
                <Image source={{ uri: photo }} style={styles.photoThumbnail} />
                <TouchableOpacity 
                  style={styles.removePhotoButton}
                  onPress={() => removeImage('after', index)}
                >
                  <Ionicons name="close-circle" size={24} color="#DC2626" />
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
          <Text style={styles.formFieldNote}>
            {i18n.t('photosWillBeSaved')}
          </Text>
        </View>

        {/* Pole wyboru pracowników */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('assignedEmployees')}*</Text>
          <TouchableOpacity 
            style={styles.photoButton} 
            onPress={() => setShowEmployeeModal(true)}
          >
            <View style={styles.photoButtonContent}>
              <Ionicons name="people-outline" size={20} color="#2563EB" />
              <Text style={styles.photoButtonText}>
                {form.selectedEmployees.length > 0 
                  ? `${i18n.t('selectedEmployees')} (${form.selectedEmployees.length})` 
                  : i18n.t('selectEmployees')}
              </Text>
            </View>
          </TouchableOpacity>
          
          {form.selectedEmployees.length > 0 && (
            <View style={styles.selectedEmployeeChips}>
              {employees
                .filter(emp => form.selectedEmployees.includes(emp.id))
                .map(emp => (
                  <View key={emp.id} style={styles.employeeChip}>
                    <Text style={styles.employeeChipText}>{emp.full_name}</Text>
                    <TouchableOpacity 
                      onPress={() => toggleEmployeeSelection(emp.id)}
                      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                    >
                      <Ionicons name="close-circle" size={18} color="#4B5563" />
                    </TouchableOpacity>
                  </View>
                ))}
            </View>
          )}
        </View>

        {/* Modal wyboru pracowników */}
        <Modal
          visible={showEmployeeModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowEmployeeModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{i18n.t('selectEmployees')}</Text>
                <TouchableOpacity onPress={() => setShowEmployeeModal(false)}>
                  <Ionicons name="close" size={24} color="#4B5563" />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.modalBody}>
                {employees.map((employee) => (
                  <TouchableOpacity
                    key={employee.id}
                    style={styles.employeeItem}
                    onPress={() => toggleEmployeeSelection(employee.id)}
                  >
                    <View style={styles.employeeInfo}>
                      <Text style={styles.employeeName}>{employee.full_name}</Text>
                      <Text style={styles.employeeEmail}>{employee.email}</Text>
                    </View>
                    <View style={[
                      styles.checkbox,
                      form.selectedEmployees.includes(employee.id) && styles.checkboxSelected
                    ]}>
                      {form.selectedEmployees.includes(employee.id) && (
                        <Ionicons name="checkmark" size={16} color="white" />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>

        <Text style={styles.formRequiredFieldsInfo}>{i18n.t('requiredFields')}</Text>

        {/* Przycisk zapisania zmian */}
        <TouchableOpacity 
          style={[
            styles.formSubmitButton, 
            areRequiredFieldsEmpty() ? styles.formSubmitButtonDisabled : {}
          ]} 
          onPress={handleSaveChanges}
          disabled={areRequiredFieldsEmpty() || uploadingPhotos}
        >
          <Text style={styles.submitButtonText}>{i18n.t('saveChanges')}</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    backgroundColor: 'white',
  },
  scrollContent: {
    padding: 20,
    paddingHorizontal: 24,
    paddingBottom: 40,
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    elevation: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptySpaceForBalance: {
    width: 36,
  },
  errorText: {
    color: '#DC2626',
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B7280',
  },
  formInputGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 10,
  },
  formInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#1F2937',
  },
  inputRequired: {
    borderColor: '#FCA5A5',
    backgroundColor: '#FEF2F2',
  },
  formTextArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  employeesButton: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  employeeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  employeesButtonText: {
    marginLeft: 8,
    color: '#1F2937',
    fontSize: 16,
  },
  selectedEmployeeChips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  employeeChip: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
  },
  employeeChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '90%',
    maxWidth: 500,
    maxHeight: '80%',
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    padding: 20,
  },
  employeeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
  },
  employeeEmail: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#2563EB',
    borderColor: '#2563EB',
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'transparent',
  },
  photoButtonText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  photosList: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  photoContainer: {
    marginRight: 10,
    position: 'relative',
  },
  photoThumbnail: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  saveButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  saveButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Custom alert styles
  alertContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    width: '90%',
    maxWidth: 400,
    overflow: 'hidden',
  },
  alertTitleContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  alertTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'black',
    textAlign: 'center',
  },
  alertBodyContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  alertMessage: {
    fontSize: 16,
    color: 'black',
    textAlign: 'center',
    lineHeight: 22,
  },
  alertButtonsContainer: {
    flexDirection: 'row',
    padding: 16,
    justifyContent: 'center',
  },
  alertButton: {
    flex: 1,
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#6B7280',
  },
  confirmButton: {
    backgroundColor: '#2563EB',
  },
  alertButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  photoButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  formFieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  formRequiredFieldsInfo: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 