import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';

interface MaintenanceAccessErrorProps {
  onBack: () => void;
  onMenuPress?: () => void;
}

export const MaintenanceAccessError: React.FC<MaintenanceAccessErrorProps> = ({
  onBack,
  onMenuPress
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={onBack}
        >
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('maintenanceReports') || 'Maintenance Reports'}</Text>
        {onMenuPress ? (
          <TouchableOpacity style={styles.menuButton} onPress={onMenuPress}>
            <Ionicons name="menu" size={22} color="#1F2937" />
          </TouchableOpacity>
        ) : (
          <View style={styles.placeholderView} />
        )}
      </View>
      
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <View style={styles.errorContainer}>
          <Ionicons name="lock-closed" size={64} color="#EF4444" style={styles.errorIcon} />
          <Text style={styles.errorTitle}>{i18n.t('accessDenied') || 'Access Denied'}</Text>
          <Text style={styles.errorText}>
            {i18n.t('maintenanceRLSError') || 
            'You do not have permission to access maintenance reports. This feature requires specific database permissions set by your administrator.'}
          </Text>
          
          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>{i18n.t('whatHappened') || 'What happened?'}</Text>
            <Text style={styles.infoText}>
              {i18n.t('maintenanceRLSErrorExplanation') || 
              'This application uses Row-Level Security (RLS) in the database to control access to different features. Your user account does not have the required permissions to access maintenance reports.'}
            </Text>
          </View>
          
          <View style={styles.infoBox}>
            <Text style={styles.infoTitle}>{i18n.t('whatToDo') || 'What to do?'}</Text>
            <Text style={styles.infoText}>
              {i18n.t('maintenanceRLSErrorSolution') || 
              'Please contact your system administrator and ask them to:'}
            </Text>
            <View style={styles.bulletPoints}>
              <Text style={styles.bulletPoint}>• Check the RLS policies for the maintenance_reports table</Text>
              <Text style={styles.bulletPoint}>• Ensure your user account has the correct role assigned</Text>
              <Text style={styles.bulletPoint}>• Update the database permissions if needed</Text>
            </View>
          </View>
          
          <TouchableOpacity style={styles.button} onPress={onBack}>
            <Text style={styles.buttonText}>{i18n.t('goBack') || 'Go Back'}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorContainer: {
    alignItems: 'center',
    maxWidth: 500,
    width: '100%',
  },
  errorIcon: {
    marginBottom: 24,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  errorText: {
    fontSize: 16,
    color: '#4B5563',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  infoBox: {
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 16,
    width: '100%',
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 12,
    lineHeight: 20,
  },
  bulletPoints: {
    marginLeft: 8,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 4,
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#2563EB',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
}); 