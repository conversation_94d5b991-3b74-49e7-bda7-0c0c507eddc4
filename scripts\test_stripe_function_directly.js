const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testStripeFunctionDirectly() {
  try {
    console.log('=== Testing Stripe Functions Directly ===\n');
    
    // Lista funkcji do przetestowania
    const functionsToTest = [
      'stripe-checkout',
      'smooth-handler',
      'stripe-webhooks'
    ];

    for (const functionName of functionsToTest) {
      console.log(`Testing function: ${functionName}`);
      
      try {
        // Test z minimalnym body
        const { data, error } = await supabase.functions.invoke(functionName, {
          body: { test: true }
        });

        if (error) {
          console.log(`❌ ${functionName}: ${error.message}`);
          if (error.context?.status === 404) {
            console.log(`   Function not deployed or not found`);
          }
        } else {
          console.log(`✅ ${functionName}: Function exists and responds`);
          console.log(`   Response:`, data);
        }
      } catch (err) {
        console.log(`❌ ${functionName}: Exception - ${err.message}`);
      }
      
      console.log('');
    }

    // Sprawdź czy możemy użyć alternatywnej metody dla checkout
    console.log('=== Testing Alternative Checkout Methods ===\n');
    
    // Znajdź firmę i plan
    const { data: companies } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1);

    const { data: yearlyPlan } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', 'Basic Yearly')
      .single();

    if (companies && companies.length > 0 && yearlyPlan) {
      const company = companies[0];
      
      console.log(`Using company: ${company.name}`);
      console.log(`Using plan: ${yearlyPlan.name} (${yearlyPlan.stripe_price_id})`);

      // Test smooth-handler z checkout request
      console.log('\nTesting smooth-handler for checkout...');
      try {
        const { data, error } = await supabase.functions.invoke('smooth-handler', {
          body: {
            type: 'create_checkout_session',
            companyId: company.id,
            planId: yearlyPlan.id,
            successUrl: 'https://example.com/success',
            cancelUrl: 'https://example.com/cancel'
          }
        });

        if (error) {
          console.log(`❌ smooth-handler checkout: ${error.message}`);
        } else {
          console.log(`✅ smooth-handler checkout: Success`);
          console.log(`   Response:`, data);
        }
      } catch (err) {
        console.log(`❌ smooth-handler checkout: Exception - ${err.message}`);
      }

      // Sprawdź czy możemy utworzyć checkout bezpośrednio przez Stripe Payment Links
      console.log('\nTesting Stripe Payment Links approach...');
      
      // Mapowanie Price ID do Payment Links (jeśli istnieją)
      const stripePaymentLinks = {
        'price_1RXnK2PaKRxqYgSxSpZzOpRc': 'https://buy.stripe.com/test_basic_yearly',
        'price_1RXnJXPaKRxqYgSxYwuVFMS2': 'https://buy.stripe.com/test_pro_yearly',
        'price_1RXnIvPaKRxqYgSx9ZwLw4R0': 'https://buy.stripe.com/test_business_yearly'
      };

      const paymentLink = stripePaymentLinks[yearlyPlan.stripe_price_id];
      if (paymentLink) {
        console.log(`✅ Payment Link available: ${paymentLink}`);
        console.log(`   Can be used as fallback for ${yearlyPlan.name}`);
      } else {
        console.log(`❌ No Payment Link configured for ${yearlyPlan.stripe_price_id}`);
        console.log(`   Need to create Payment Links in Stripe Dashboard`);
      }
    }

    console.log('\n=== Recommendations ===');
    console.log('1. Deploy stripe-checkout function if it\'s missing');
    console.log('2. Create Stripe Payment Links as fallback');
    console.log('3. Fix yearly plan prices to offer discounts');
    console.log('4. Test with real Stripe Price IDs');

  } catch (error) {
    console.error('Error in testStripeFunctionDirectly:', error);
  }
}

testStripeFunctionDirectly();
