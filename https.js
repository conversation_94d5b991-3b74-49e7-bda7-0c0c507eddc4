// Prosty polyfill dla modułu https, potrzebny dla biblioteki ws (WebSocket)
const https = {
  createServer: () => ({
    listen: () => ({}),
    on: () => ({}),
    close: () => ({})
  }),
  
  // Inne często używane funkcje/obiekty
  Agent: function() {},
  request: () => ({
    on: () => ({}),
    end: () => ({})
  }),
  get: () => ({
    on: () => ({}),
    end: () => ({})
  })
};

// Używamy module.exports dla lepszej kompatybilności z modułami CommonJS
module.exports = https; 