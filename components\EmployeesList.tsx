import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, TextInput, Modal, Pressable, Platform, Dimensions } from 'react-native';
import { supabase } from '../services/supabaseClient';
import { Ionicons } from '@expo/vector-icons';
import FilterDrawer from './FilterDrawer';
import DatePicker from './DatePicker';
import { i18n } from '../utils/localization';

interface Employee {
  id: string;
  full_name: string;
  email: string;
  status: string;
  subscription_status: string;
  last_status_change: string;
}

interface WorkSession {
  id: string;
  job_order: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  employee: Employee;
  start_location?: any;
  end_location?: any;
}

interface Filters {
  selectedEmployees: string[];
  dateFrom: string;
  dateTo: string;
}

type SortField = 'date' | 'employee' | 'job_order' | 'duration';
type SortOrder = 'asc' | 'desc';

interface EmployeesListProps {
  companyId: string;
  onRowClick?: (sessionId: string, date: string, employeeId: string, employeeName: string) => void;
}

// Component for displaying loading animation for addresses
const LoadingAddress = () => {
  const [dots, setDots] = useState('.');
  
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(current => {
        if (current === '...') return '.';
        return current + '.';
      });
    }, 500);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <Text style={[styles.locationText, {fontStyle: 'italic', color: '#6B7280'}]}>
      {i18n.t('loadingAddress')}{dots}
    </Text>
  );
};

const EmployeesList = ({ companyId, onRowClick }: EmployeesListProps) => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [workSessions, setWorkSessions] = useState<WorkSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [geocodeCache, setGeocodeCache] = useState<{[key: string]: string}>({});
  
  // Dodanie detekcji szerokości ekranu
  const [screenWidth, setScreenWidth] = useState(Dimensions.get('window').width);
  
  // Określenie czy to jest mały ekran (smartfon)
  const isSmallScreen = screenWidth < 768;
  
  // Nasłuchiwanie na zmiany rozmiaru ekranu (przydatne głównie dla web)
  useEffect(() => {
    const handleResize = () => {
      setScreenWidth(Dimensions.get('window').width);
    };
    
    const subscription = Dimensions.addEventListener('change', handleResize);
    
    return () => {
      // Usunięcie nasłuchiwania przy unmount komponentu
      subscription.remove();
    };
  }, []);

  // Get the first day of the current month
  const firstDayOfMonth = React.useMemo(() => {
    const today = new Date();
    // Create date at local midnight to avoid timezone issues
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    // Format as YYYY-MM-DD ensuring we use local date
    const year = firstDay.getFullYear();
    const month = String(firstDay.getMonth() + 1).padStart(2, '0');
    const day = String(firstDay.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }, []);

  // Get the last day of the current month
  const lastDayOfMonth = React.useMemo(() => {
    const today = new Date();
    // Last day of current month (0th day of next month is the last day of current month)
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    // Format as YYYY-MM-DD ensuring we use local date
    const year = lastDay.getFullYear();
    const month = String(lastDay.getMonth() + 1).padStart(2, '0');
    const day = String(lastDay.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }, []);

  // Function to format date in a consistent way
  const formatDateString = (dateString: string) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      // Ensure it's a valid date
      if (isNaN(date.getTime())) return '';
      
      // Format as DD.MM.RR
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = String(date.getFullYear()).slice(-2); // Pobierz tylko ostatnie 2 cyfry roku
      
      return `${day}.${month}.${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const [filters, setFilters] = useState<Filters>({
    selectedEmployees: [],
    dateFrom: firstDayOfMonth,
    dateTo: lastDayOfMonth,
  });
  const [showEmployeeFilter, setShowEmployeeFilter] = useState(false);
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [filterButtonPosition, setFilterButtonPosition] = useState({ top: 60, left: 10, width: 300 });
  const filterButtonRef = React.useRef<View>(null);
  const employeeFilterButtonRef = React.useRef<View>(null);
  const [searchText, setSearchText] = useState('');

  const [locationDisplay, setLocationDisplay] = useState<{[key: string]: { fetching: boolean, address: string | null } }>({});

  useEffect(() => {
    if (companyId) {
      fetchEmployees();
    }
  }, [companyId]);

  useEffect(() => {
    if (employees.length > 0) {
      fetchAllWorkSessions();
    }
  }, [employees, filters]);

  const fetchEmployees = async () => {
    try {
      if (!companyId) {
        console.error('No company ID provided');
        return;
      }

      const { data: employeesData, error: employeesError } = await supabase
        .from('employees')
        .select('*')
        .eq('company_id', companyId);

      if (employeesError) {
        console.error('Error fetching employees:', employeesError);
        Alert.alert(i18n.t('error'), i18n.t('errorFetchingEmployees'));
        return;
      }

      // Sortuj pracowników - aktywni na górze
      const sortedEmployees = (employeesData || []).sort((a, b) => {
        if (a.subscription_status === 'ACTIVE' && b.subscription_status !== 'ACTIVE') return -1;
        if (a.subscription_status !== 'ACTIVE' && b.subscription_status === 'ACTIVE') return 1;
        return a.full_name.localeCompare(b.full_name);
      });

      setEmployees(sortedEmployees);
      // Ustaw tylko aktywnych pracowników jako domyślnie wybranych
      if (sortedEmployees.length > 0 && filters.selectedEmployees.length === 0) {
        const activeEmployeeIds = sortedEmployees
          .filter(emp => emp.subscription_status === 'ACTIVE')
          .map(emp => emp.id);
        setFilters(prev => ({ ...prev, selectedEmployees: activeEmployeeIds }));
      }
    } catch (error) {
      console.error('Exception in fetchEmployees:', error);
      Alert.alert(i18n.t('error'), 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllWorkSessions = async () => {
    try {
      setLoading(true);
      const employeeIds = filters.selectedEmployees.length > 0 
        ? filters.selectedEmployees 
        : employees.map(emp => emp.id);
      
      // Najpierw pobierz aktywnych pracowników
      const { data: activeEmployees, error: employeesError } = await supabase
        .from('employees')
        .select('id')
        .in('id', employeeIds)
        .eq('subscription_status', 'ACTIVE');

      if (employeesError) {
        console.error('Error fetching active employees:', employeesError);
        Alert.alert(i18n.t('error'), i18n.t('errorFetchingEmployees'));
        return;
      }

      const activeEmployeeIds = activeEmployees?.map(emp => emp.id) || [];
      
      let query = supabase
        .from('work_sessions')
        .select(`
          id,
          job_order,
          start_time,
          end_time,
          duration_minutes,
          employee_id,
          start_location,
          end_location
        `)
        .in('employee_id', activeEmployeeIds)
        .not('end_time', 'is', null)
        .order('start_time', { ascending: false });

      if (filters.dateFrom) {
        query = query.gte('start_time', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte('start_time', filters.dateTo);
      }

      const { data: sessionsData, error: sessionsError } = await query;

      if (sessionsError) {
        console.error('Error fetching work sessions:', sessionsError);
        Alert.alert(i18n.t('error'), i18n.t('errorFetchingWorkSessions'));
        return;
      }

      if (sessionsData) {
        const formattedSessions = sessionsData.map(session => ({
          ...session,
          employee: employees.find(emp => emp.id === session.employee_id) as Employee
        }));

        setWorkSessions(formattedSessions);
      }
    } catch (error) {
      console.error('Exception in fetchAllWorkSessions:', error);
      Alert.alert(i18n.t('error'), 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('pl-PL', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const toggleEmployeeSelection = (employeeId: string) => {
    setFilters(prev => ({
      ...prev,
      selectedEmployees: prev.selectedEmployees.includes(employeeId)
        ? prev.selectedEmployees.filter(id => id !== employeeId)
        : [...prev.selectedEmployees, employeeId]
    }));
  };

  const handleDateChange = (key: 'dateFrom' | 'dateTo', value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const filteredWorkSessions = React.useMemo(() => {
    if (!searchText.trim()) {
      return workSessions.filter(session => 
        (filters.selectedEmployees.includes(session.employee.id)) &&
        (!filters.dateFrom || new Date(session.start_time) >= new Date(filters.dateFrom)) &&
        (!filters.dateTo || new Date(session.start_time) <= new Date(filters.dateTo + 'T23:59:59'))
      );
    }
    
    const searchLower = searchText.toLowerCase();
    return workSessions.filter(session => 
      (filters.selectedEmployees.includes(session.employee.id)) &&
      (!filters.dateFrom || new Date(session.start_time) >= new Date(filters.dateFrom)) &&
      (!filters.dateTo || new Date(session.start_time) <= new Date(filters.dateTo + 'T23:59:59')) &&
      (
        session.employee.full_name.toLowerCase().includes(searchLower) ||
        session.job_order.toLowerCase().includes(searchLower)
      )
    );
  }, [workSessions, filters, searchText]);

  const sortedFilteredWorkSessions = React.useMemo(() => {
    return [...filteredWorkSessions].sort((a, b) => {
      if (sortField === 'date') {
        const dateA = new Date(a.start_time).getTime();
        const dateB = new Date(b.start_time).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      }
      if (sortField === 'employee') {
        const nameA = a.employee.full_name.toLowerCase();
        const nameB = b.employee.full_name.toLowerCase();
        return sortOrder === 'asc' 
          ? nameA.localeCompare(nameB)
          : nameB.localeCompare(nameA);
      }
      if (sortField === 'job_order') {
        const jobA = a.job_order.toLowerCase();
        const jobB = b.job_order.toLowerCase();
        return sortOrder === 'asc'
          ? jobA.localeCompare(jobB)
          : jobB.localeCompare(jobA);
      }
      if (sortField === 'duration') {
        return sortOrder === 'asc'
          ? a.duration_minutes - b.duration_minutes
          : b.duration_minutes - a.duration_minutes;
      }
      return 0;
    });
  }, [filteredWorkSessions, sortField, sortOrder]);

  // Calculate total hours from filtered sessions
  const totalDuration = React.useMemo(() => {
    const totalMinutes = sortedFilteredWorkSessions.reduce(
      (total, session) => total + session.duration_minutes, 
      0
    );
    return formatDuration(totalMinutes);
  }, [sortedFilteredWorkSessions]);

  const handleClearFilters = () => {
    setFilters({
      selectedEmployees: [],
      dateFrom: firstDayOfMonth,
      dateTo: lastDayOfMonth
    });
  };

  // Function to generate PDF from employee work time data
  const exportToPDF = useCallback(() => {
    if (Platform.OS !== 'web') return;
    
    // We're on the web platform, so we can access the window object
    const loadScript = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
        document.head.appendChild(script);
      });
    };

    const generatePDF = async () => {
      try {
        // Load jsPDF and jsPDF-AutoTable scripts if not already loaded
        if (!(window as any).jspdf) {
          await loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
        }
        if (!(window as any).jspdf?.jsPDF) {
          console.error('jsPDF not properly loaded');
          alert('Nie można załadować biblioteki PDF. Odśwież stronę i spróbuj ponownie.');
          return;
        }
        
        if (!(window as any).jspdf.autoTable) {
          await loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js');
        }

        // Create new PDF document
        const { jsPDF } = (window as any).jspdf;
        const doc = new jsPDF();
        
        // Add title
        doc.setFontSize(18);
        doc.text(i18n.t('employeeWorkHistory'), 14, 22);
        
        // Add date range
        doc.setFontSize(12);
        doc.text(`${i18n.t('dateRange')}: ${formatDateString(filters.dateFrom)} - ${formatDateString(filters.dateTo)}`, 14, 30);
        
        // Add selected employees info if not all employees are selected
        if (filters.selectedEmployees.length < employees.length) {
          const selectedEmployeeNames = filters.selectedEmployees
            .map(id => employees.find(emp => emp.id === id)?.full_name || '')
            .filter(name => name !== '')
            .join(', ');
          
          doc.text(`${i18n.t('employee')}: ${selectedEmployeeNames}`, 14, 38);
        }
        
        // Add total duration
        const totalDuration = calculateTotalDuration(sortedFilteredWorkSessions);
        const yPosition = filters.selectedEmployees.length < employees.length ? 46 : 38;
        doc.text(`${i18n.t('totalWorkTime')} ${formatDuration(totalDuration)}`, 14, yPosition);
        
        // Create table with work session data
        const tableColumn = [
          i18n.t('date'), 
          i18n.t('employee'), 
          i18n.t('jobOrder'), 
          i18n.t('workTime'), 
          i18n.t('duration')
        ];
        
        // Sort data by date ascending (from oldest to newest) for the PDF regardless of UI sort
        const sortedForPdfSessions = [...sortedFilteredWorkSessions].sort((a, b) => {
          const dateA = new Date(a.start_time).getTime();
          const dateB = new Date(b.start_time).getTime();
          return dateA - dateB; // Always ascending for PDF
        });
        
        const tableRows = sortedForPdfSessions.map(session => [
          formatDateString(session.start_time),
          session.employee.full_name,
          session.job_order,
          `${formatDateTime(session.start_time)} - ${formatDateTime(session.end_time)}`,
          formatDuration(session.duration_minutes)
        ]);
        
        // Add table to document with smaller font size
        doc.autoTable({
          head: [tableColumn],
          body: tableRows,
          startY: filters.selectedEmployees.length < employees.length ? 50 : 42,
          styles: { 
            fontSize: 8, // Reduced font size from 10 to 8
            cellPadding: 2  // Reduced padding from 3 to 2
          },
          headStyles: { 
            fillColor: [37, 99, 235],
            fontSize: 9 // Slightly larger than body text but still reduced
          },
        });
        
        // Save PDF
        doc.save('raport-czasu-pracy-pracownikow.pdf');
      } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Wystąpił błąd podczas generowania PDF: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    };

    generatePDF();
  }, [sortedFilteredWorkSessions, filters, employees]);

  // Calculate total duration for all filtered work sessions
  const calculateTotalDuration = (sessions: WorkSession[]) => {
    return sessions.reduce((total, session) => total + (session.duration_minutes || 0), 0);
  };

  // Function to get address from coordinates
  const getAddressFromCoordinates = async (latitude: number, longitude: number): Promise<string> => {
    // Check if we already have this address cached
    const cacheKey = `${latitude.toFixed(6)},${longitude.toFixed(6)}`;
    if (geocodeCache[cacheKey]) {
      return geocodeCache[cacheKey];
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'WorkFlow Mobile App',
            'Accept-Language': 'pl'
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Błąd pobierania adresu: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.address) {
        // Wyciągamy tylko kluczowe elementy adresu
        const road = data.address.road || '';
        const houseNumber = data.address.house_number || '';
        const city = data.address.city || data.address.town || data.address.village || '';
        
        // Tworzymy skrócony format adresu: "Ulica Numer, Miasto"
        let formattedAddress = '';
        
        // Dodaj ulicę i numer jeśli istnieją
        if (road) {
          formattedAddress += road;
          if (houseNumber) {
            formattedAddress += ' ' + houseNumber;
          }
        }
        
        // Dodaj miasto jeśli istnieje
        if (city) {
          if (formattedAddress) {
            formattedAddress += ', ';
          }
          formattedAddress += city;
        }
        
        // Jeśli nie mamy żadnych elementów adresu, użyj ogólnej nazwy z API
        if (!formattedAddress && data.display_name) {
          // Skróć display_name do pierwszych dwóch części
          formattedAddress = data.display_name.split(',').slice(0, 2).join(',');
        }
        
        // Zapisz do cache i zwróć
        setGeocodeCache(prev => ({
          ...prev,
          [cacheKey]: formattedAddress || 'Nieznany adres'
        }));
        
        return formattedAddress || 'Nieznany adres';
      }
      
      return 'Nie udało się pobrać adresu';
    } catch (error) {
      console.error('Error fetching address:', error);
      return 'Błąd pobierania adresu';
    }
  };

  const renderLocationInfo = (location: any) => {
    if (!location) return <Text style={styles.locationText}>Brak danych</Text>;
    
    try {
      let locationData = location;
      
      // Obsługa danych w formacie string
      if (typeof location === 'string') {
        try {
          locationData = JSON.parse(location);
        } catch (e) {
          return <Text style={styles.locationText}>Błędny format</Text>;
        }
      }
      
      if (locationData) {
        // Wyciąganie współrzędnych z różnych formatów danych
        let latitude = null;
        let longitude = null;
        
        // Format bezpośrednich współrzędnych
        if (locationData.latitude !== undefined && locationData.longitude !== undefined) {
          latitude = Number(locationData.latitude);
          longitude = Number(locationData.longitude);
        } 
        // Format z obiektem coords
        else if (locationData.coords && locationData.coords.latitude !== undefined && locationData.coords.longitude !== undefined) {
          latitude = Number(locationData.coords.latitude);
          longitude = Number(locationData.coords.longitude);
        }
        // Format z lat/lng
        else if (locationData.lat !== undefined && locationData.lng !== undefined) {
          latitude = Number(locationData.lat);
          longitude = Number(locationData.lng);
        }
        
        if (latitude && longitude) {
          // Tworzenie klucza cache
          const cacheKey = `${latitude.toFixed(6)},${longitude.toFixed(6)}`;
          
          // Sprawdzanie czy mamy już ten adres lub czy jest w trakcie pobierania
          if (!locationDisplay[cacheKey]) {
            // Inicjowanie stanu pobierania
            setLocationDisplay(prev => ({
              ...prev,
              [cacheKey]: { fetching: true, address: null }
            }));
            
            // Asynchroniczne pobieranie adresu
            getAddressFromCoordinates(latitude, longitude).then(address => {
              setLocationDisplay(prev => ({
                ...prev,
                [cacheKey]: { fetching: false, address }
              }));
            });
          }
          
          // Zwracanie adresu z cache lub informacji o ładowaniu
          if (locationDisplay[cacheKey]) {
            if (locationDisplay[cacheKey].fetching) {
              // Zamiast współrzędnych pokazujemy informację o ładowaniu
              return <LoadingAddress />;
            }
            // Jeśli mamy adres, zwracamy go
            return <Text style={styles.locationText}>{locationDisplay[cacheKey].address || "Nie udało się pobrać adresu"}</Text>;
          }
          
          // Jeśli nie mamy jeszcze klucza w cache, pokazujemy informację o ładowaniu
          return <LoadingAddress />;
        }
      }
      
      return <Text style={styles.locationText}>Niepełne dane lokalizacyjne</Text>;
    } catch (error) {
      console.error("Błąd przetwarzania danych lokalizacji:", error);
      return <Text style={styles.locationText}>Błąd danych lokalizacji</Text>;
    }
  };

  const tableHeader = () => (
    <View style={styles.tableHeader}>
      <TouchableOpacity 
        style={[styles.headerCell, { flex: 1 }]}
        onPress={() => handleSort('date')}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerText}>{i18n.t('date')}</Text>
          {sortField === 'date' && (
            <Ionicons 
              name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
              size={14} 
              color="#4B5563" 
              style={styles.sortIcon} 
            />
          )}
        </View>
      </TouchableOpacity>

      <TouchableOpacity 
        style={[styles.headerCell, { flex: isSmallScreen ? 3 : 2 }]}
        onPress={() => handleSort('employee')}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerText}>{i18n.t('employee')}</Text>
          {sortField === 'employee' && (
            <Ionicons 
              name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
              size={14} 
              color="#4B5563" 
              style={styles.sortIcon} 
            />
          )}
        </View>
      </TouchableOpacity>

      <TouchableOpacity 
        style={[styles.headerCell, { flex: isSmallScreen ? 3 : 2 }]}
        onPress={() => handleSort('job_order')}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerText}>{i18n.t('jobOrder')}</Text>
          {sortField === 'job_order' && (
            <Ionicons 
              name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
              size={14} 
              color="#4B5563" 
              style={styles.sortIcon} 
            />
          )}
        </View>
      </TouchableOpacity>

      <View style={[styles.headerCell, { flex: 2 }]}>
        <Text style={styles.headerText}>{i18n.t('workTime')}</Text>
      </View>

      <TouchableOpacity 
        style={[styles.headerCell, { flex: isSmallScreen ? 1.5 : 1 }]}
        onPress={() => handleSort('duration')}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerText}>{i18n.t('duration')}</Text>
          {sortField === 'duration' && (
            <Ionicons 
              name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
              size={14} 
              color="#4B5563" 
              style={styles.sortIcon} 
            />
          )}
        </View>
      </TouchableOpacity>

      {!isSmallScreen && (
        <>
          <View style={[styles.headerCell, { flex: 2 }]}>
            <Text style={styles.headerText}>{i18n.t('startAddress')}</Text>
          </View>

          <View style={[styles.headerCell, { flex: 2 }]}>
            <Text style={styles.headerText}>{i18n.t('endAddress')}</Text>
          </View>
        </>
      )}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>{i18n.t('loadingData')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView 
        style={styles.content}
        contentContainerStyle={{ width: '100%', padding: 0 }}
      >
        <View style={styles.filtersContainer}>
          <View style={styles.searchBarContainer}>
            <View style={styles.searchInputWrapper}>
              <Ionicons name="search-outline" size={16} color="#6B7280" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder={i18n.t('searchEmployee')}
                value={searchText}
                onChangeText={setSearchText}
                placeholderTextColor="#9CA3AF"
              />
              {searchText.length > 0 && (
                <TouchableOpacity onPress={() => setSearchText('')} style={styles.clearSearchButton}>
                  <Ionicons name="close-circle" size={18} color="#6B7280" />
                </TouchableOpacity>
              )}
            </View>
            <View style={styles.filterButtonsContainer}>
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  (filters.dateFrom || filters.dateTo) && styles.dateButtonActive,
                ]}
                onPress={() => setShowDateFilter(true)}
              >
                <Ionicons
                  name="calendar-outline"
                  size={16}
                  color={(filters.dateFrom || filters.dateTo) ? '#2563EB' : '#4B5563'}
                />
                <Text
                  style={[
                    styles.dateButtonText,
                    (filters.dateFrom || filters.dateTo) && styles.dateButtonTextActive,
                  ]}
                >
                  {i18n.t('date')}
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[
                  styles.dateButton,
                  (filters.selectedEmployees.length < employees.length) && styles.dateButtonActive,
                ]}
                onPress={() => setShowEmployeeFilter(true)}
              >
                <Ionicons
                  name="person-outline"
                  size={16}
                  color={(filters.selectedEmployees.length < employees.length) ? '#2563EB' : '#4B5563'}
                />
                <Text
                  style={[
                    styles.dateButtonText,
                    (filters.selectedEmployees.length < employees.length) && styles.dateButtonTextActive,
                  ]}
                >
                  {i18n.t('employee')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={styles.statusFilter}>
            <TouchableOpacity
              style={[
                styles.statusFilterButton,
                (!filters.dateFrom && !filters.dateTo) && styles.statusFilterButtonActive
              ]}
              onPress={() => {
                setFilters(prev => ({
                  ...prev,
                  dateFrom: '',
                  dateTo: ''
                }));
              }}
            >
              <Text style={[
                styles.statusFilterText,
                (!filters.dateFrom && !filters.dateTo) && styles.statusFilterTextActive
              ]}>{i18n.t('all')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.statusFilterButton,
                (filters.dateFrom === firstDayOfMonth && filters.dateTo === lastDayOfMonth) && styles.statusFilterButtonActive
              ]}
              onPress={() => {
                setFilters(prev => ({
                  ...prev,
                  dateFrom: firstDayOfMonth,
                  dateTo: lastDayOfMonth
                }));
              }}
            >
              <Text style={[
                styles.statusFilterText,
                (filters.dateFrom === firstDayOfMonth && filters.dateTo === lastDayOfMonth) && styles.statusFilterTextActive
              ]}>{i18n.t('thisMonth')}</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.statusFilterButton,
                (filters.dateFrom !== firstDayOfMonth || filters.dateTo !== lastDayOfMonth) && 
                filters.dateFrom !== '' && 
                filters.dateTo !== '' && 
                styles.statusFilterButtonActive
              ]}
              onPress={() => {
                const today = new Date();
                const prevMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const prevMonthLastDay = new Date(today.getFullYear(), today.getMonth(), 0);
                const firstDayPrevMonth = `${prevMonth.getFullYear()}-${String(prevMonth.getMonth() + 1).padStart(2, '0')}-01`;
                const lastDayPrevMonth = `${prevMonthLastDay.getFullYear()}-${String(prevMonthLastDay.getMonth() + 1).padStart(2, '0')}-${String(prevMonthLastDay.getDate()).padStart(2, '0')}`;
                
                setFilters(prev => ({
                  ...prev,
                  dateFrom: firstDayPrevMonth,
                  dateTo: lastDayPrevMonth
                }));
              }}
            >
              <Text style={[
                styles.statusFilterText,
                (filters.dateFrom !== firstDayOfMonth || filters.dateTo !== lastDayOfMonth) && 
                filters.dateFrom !== '' && 
                filters.dateTo !== '' && 
                styles.statusFilterTextActive
              ]}>{i18n.t('previousMonth')}</Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.totalDurationContainer}>
          <Text style={styles.totalDurationLabel}>{i18n.t('totalWorkTime')}</Text>
          <View style={styles.totalDurationControls}>
            <Text style={styles.totalDurationValue}>{totalDuration}</Text>
            {Platform.OS === 'web' && (
              <TouchableOpacity style={styles.exportButton} onPress={exportToPDF}>
                <Ionicons name="download-outline" size={18} color="#FFFFFF" style={{ marginRight: 6 }} />
                <Text style={styles.exportButtonText}>{i18n.t('exportToPdf')}</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        <View style={styles.tableContainer}>
          {tableHeader()}

          <ScrollView style={styles.tableBody}>
            {sortedFilteredWorkSessions.map((session) => (
              <TouchableOpacity 
                key={session.id} 
                style={[styles.tableRow, styles.clickableRow]}
                activeOpacity={0.7}
                onPress={() => onRowClick && onRowClick(
                  session.id, 
                  formatDateString(session.start_time),
                  session.employee.id,
                  session.employee.full_name
                )}
              >
                <Text style={[styles.tableCell, { flex: 1 }]}>
                  {formatDateString(session.start_time)}
                </Text>
                <Text style={[styles.tableCell, { flex: isSmallScreen ? 3 : 2 }]}>
                  {session.employee.full_name}
                </Text>
                <Text style={[styles.tableCell, { flex: isSmallScreen ? 3 : 2 }]}>
                  {session.job_order}
                </Text>
                <Text style={[styles.tableCell, { flex: 2 }]}>
                  {formatDateTime(session.start_time)} - {formatDateTime(session.end_time)}
                </Text>
                <Text style={[styles.tableCell, { flex: isSmallScreen ? 1.5 : 1, color: '#2563EB', fontWeight: '500' }]}>
                  {formatDuration(session.duration_minutes)}
                </Text>
                
                {!isSmallScreen && (
                  <>
                    <View style={[styles.locationCell, { flex: 2 }]}>
                      {renderLocationInfo(session.start_location)}
                    </View>
                    <View style={[styles.locationCell, { flex: 2 }]}>
                      {renderLocationInfo(session.end_location)}
                    </View>
                  </>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Date Filter Modal */}
      <Modal
        visible={showDateFilter}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDateFilter(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('selectDateRange')}</Text>
              <TouchableOpacity onPress={() => setShowDateFilter(false)}>
                <Ionicons name="close" size={24} color="#4B5563" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalBody}>
              <DatePicker
                label={i18n.t('dateFrom')}
                date={filters.dateFrom}
                onDateChange={(value) => handleDateChange('dateFrom', value)}
                placeholder={i18n.t('selectStartDate')}
              />
              <View style={{ height: 16 }} />
              <DatePicker
                label={i18n.t('dateTo')}
                date={filters.dateTo}
                onDateChange={(value) => handleDateChange('dateTo', value)}
                placeholder={i18n.t('selectEndDate')}
              />
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={styles.modalCancelButton}
                  onPress={() => {
                    setFilters(prev => ({
                      ...prev,
                      dateFrom: '',
                      dateTo: ''
                    }));
                    setShowDateFilter(false);
                  }}
                >
                  <Text style={styles.modalCancelButtonText}>{i18n.t('clear')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.modalConfirmButton}
                  onPress={() => setShowDateFilter(false)}
                >
                  <Text style={styles.modalConfirmButtonText}>{i18n.t('apply')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Employee Filter Modal */}
      <Modal
        visible={showEmployeeFilter}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowEmployeeFilter(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('filterByEmployee')}</Text>
              <TouchableOpacity onPress={() => setShowEmployeeFilter(false)}>
                <Ionicons name="close" size={24} color="#4B5563" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalBody}>
              <Text style={styles.filterLabel}>{i18n.t('employee')}</Text>
              <ScrollView style={styles.employeesList}>
                {employees.map((employee) => (
                  <TouchableOpacity
                    key={employee.id}
                    style={[
                      styles.employeeItem,
                      employee.subscription_status !== 'ACTIVE' && styles.inactiveEmployee
                    ]}
                    onPress={() => toggleEmployeeSelection(employee.id)}
                  >
                    <View style={styles.employeeInfo}>
                      <Text style={[
                        styles.employeeName,
                        employee.subscription_status !== 'ACTIVE' && styles.inactiveText
                      ]}>
                        {employee.full_name}
                        {employee.subscription_status !== 'ACTIVE' && ' (Nieaktywny)'}
                      </Text>
                      <Text style={[
                        styles.employeeEmail,
                        employee.subscription_status !== 'ACTIVE' && styles.inactiveText
                      ]}>
                        {employee.email}
                      </Text>
                    </View>
                    <View style={[
                      styles.checkbox,
                      filters.selectedEmployees.includes(employee.id) && styles.checkboxSelected
                    ]}>
                      {filters.selectedEmployees.includes(employee.id) && (
                        <Ionicons name="checkmark" size={16} color="white" />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={styles.modalCancelButton}
                  onPress={() => {
                    setFilters(prev => ({
                      ...prev,
                      selectedEmployees: employees.map(emp => emp.id)
                    }));
                    setShowEmployeeFilter(false);
                  }}
                >
                  <Text style={styles.modalCancelButtonText}>{i18n.t('selectAll')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.modalConfirmButton}
                  onPress={() => setShowEmployeeFilter(false)}
                >
                  <Text style={styles.modalConfirmButtonText}>{i18n.t('apply')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    width: '100%',
  },
  content: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
  },
  filtersContainer: {
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingBottom: 8,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  filterButtonsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchIcon: {
    marginRight: 6,
  },
  searchInput: {
    flex: 1,
    padding: 4,
    fontSize: 14,
  },
  clearSearchButton: {
    padding: 4,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingVertical: 4,
    paddingHorizontal: 6,
    borderRadius: 16,
    marginLeft: 4,
    flexShrink: 0,
  },
  dateButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  dateButtonText: {
    fontSize: 12,
    color: '#4B5563',
    marginLeft: 4,
  },
  dateButtonTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4B5563',
    marginBottom: 8,
    marginTop: 16,
  },
  filterInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    color: '#1F2937',
  },
  tableContainer: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  headerCell: {
    paddingHorizontal: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    paddingHorizontal: 2,
  },
  headerText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4B5563',
    textAlign: 'center',
  },
  sortIcon: {
    marginLeft: 4,
  },
  tableBody: {
    flex: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  tableCell: {
    fontSize: 13,
    color: '#1F2937',
    paddingHorizontal: 6,
    textAlign: 'center',
    alignSelf: 'center',
  },
  locationCell: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'center',
    flexWrap: 'wrap',
    maxWidth: 250,
    minHeight: 40,
    justifyContent: 'center',
  },
  locationText: {
    fontSize: 12,
    color: '#4B5563',
    textAlign: 'left',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    width: '80%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    padding: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalCancelButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    marginRight: 8,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  modalConfirmButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#2563EB',
    borderRadius: 6,
    alignItems: 'center',
  },
  modalConfirmButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  employeesList: {
    maxHeight: 200,
    marginBottom: 16,
  },
  employeeItem: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  employeeInfo: {
    flex: 1,
    marginRight: 8,
  },
  employeeName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
  },
  employeeEmail: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: '#2563EB',
    borderColor: '#2563EB',
  },
  dateInput: {
    marginBottom: 15,
  },
  dateLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  input: {
    backgroundColor: '#F5F6FA',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
  },
  filterContent: {
    padding: 16,
  },
  totalDurationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#F9FAFB',
  },
  totalDurationLabel: {
    fontSize: 14,
    color: '#4B5563',
  },
  totalDurationValue: {
    fontSize: 14,
    color: '#2563EB',
    fontWeight: '600',
    marginRight: Platform.OS === 'web' ? 16 : 0,
  },
  totalDurationControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  exportButton: {
    flexDirection: 'row',
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  exportButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  clickableRow: {
    backgroundColor: 'white',
    cursor: 'pointer',
    position: 'relative',
  },
  clickableRowActive: {
    backgroundColor: '#F0F9FF',
  },
  statusFilter: {
    flexDirection: 'row',
    marginTop: 16,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 8,
  },
  statusFilterButton: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusFilterButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  statusFilterText: {
    fontSize: 14,
    color: '#4B5563',
  },
  statusFilterTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  inactiveEmployee: {
    opacity: 0.7,
    backgroundColor: '#f5f5f5',
  },
  inactiveText: {
    color: '#666',
  },
});

export default EmployeesList; 