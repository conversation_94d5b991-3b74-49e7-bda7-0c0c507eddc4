import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';
import { updateSubscriptionAfterPayment } from '../services/stripeService';

interface PaymentSuccessProps {
  companyId: string;
  planId: string;
  onClose: () => void;
}

const PaymentSuccess: React.FC<PaymentSuccessProps> = ({ companyId, planId, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processPayment = async () => {
      try {
        setLoading(true);
        
        // Aktualizuj bazę danych po udanej płatności
        const result = await updateSubscriptionAfterPayment(companyId, planId);
        
        if (result) {
          setSuccess(true);
        } else {
          setError(i18n.t('paymentProcessingError'));
        }
      } catch (err) {
        console.error('Error processing payment:', err);
        setError(i18n.t('unexpectedError'));
      } finally {
        setLoading(false);
      }
    };

    if (companyId && planId) {
      processPayment();
    } else {
      setError(i18n.t('missingPaymentInfo'));
      setLoading(false);
    }
  }, [companyId, planId]);

  return (
    <View style={styles.container}>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={styles.loadingText}>{i18n.t('processingPayment')}</Text>
        </View>
      ) : success ? (
        <View style={styles.successContainer}>
          <View style={styles.iconContainer}>
            <Ionicons name="checkmark-circle" size={80} color="#10B981" />
          </View>
          <Text style={styles.title}>{i18n.t('paymentSuccessful')}</Text>
          <Text style={styles.message}>{i18n.t('subscriptionActivated')}</Text>
          <TouchableOpacity style={styles.button} onPress={onClose}>
            <Text style={styles.buttonText}>{i18n.t('backToDashboard')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.errorContainer}>
          <View style={styles.iconContainer}>
            <Ionicons name="alert-circle" size={80} color="#DC2626" />
          </View>
          <Text style={styles.title}>{i18n.t('paymentError')}</Text>
          <Text style={styles.errorMessage}>{error || i18n.t('unknownError')}</Text>
          <TouchableOpacity style={styles.button} onPress={onClose}>
            <Text style={styles.buttonText}>{i18n.t('tryAgain')}</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#4B5563',
  },
  successContainer: {
    alignItems: 'center',
  },
  errorContainer: {
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#4B5563',
    marginBottom: 32,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#DC2626',
    marginBottom: 32,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default PaymentSuccess; 