-- Funkcja do sprawdzania czy można zmienić status pracownika (ograniczenie czasowe)
CREATE OR REPLACE FUNCTION can_change_employee_status(
  p_employee_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  last_change TIMESTAMPTZ;
BEGIN
  SELECT last_status_change INTO last_change
  FROM employees
  WHERE id = p_employee_id;

  -- <PERSON><PERSON><PERSON> nie ma poprzedniej zmiany, pozwól na zmianę
  IF last_change IS NULL THEN
    RETURN TRUE;
  END IF;

  -- Sprawd<PERSON> czy minął tydzień od ostatniej zmiany
  RETURN (NOW() - last_change) >= INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzania czy firma ma aktywną subskrypcję
CREATE OR REPLACE FUNCTION company_has_active_subscription(
  p_company_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  subscription_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO subscription_count
  FROM company_subscriptions
  WHERE company_id = p_company_id
  AND status = 'active'
  AND (current_period_end IS NULL OR current_period_end > NOW());

  RETURN subscription_count > 0;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzania czy kod weryfikacyjny może być użyty
CREATE OR REPLACE FUNCTION can_use_verification_code(
  p_code TEXT,
  p_company_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  code_exists BOOLEAN;
  has_active_sub BOOLEAN;
  active_employees_count INTEGER;
  employee_limit INTEGER;
BEGIN
  -- Sprawdź czy kod istnieje i nie jest użyty
  SELECT EXISTS(
    SELECT 1 FROM verification_codes
    WHERE code = p_code
    AND company_id = p_company_id
    AND used = false
  ) INTO code_exists;

  IF NOT code_exists THEN
    RETURN FALSE;
  END IF;

  -- Sprawdź czy firma ma aktywną subskrypcję
  SELECT company_has_active_subscription(p_company_id) INTO has_active_sub;

  -- Pobierz limit pracowników i aktualną liczbę
  SELECT verification_code_limit INTO employee_limit
  FROM companies WHERE id = p_company_id;

  SELECT COUNT(*) INTO active_employees_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Jeśli firma ma aktywną subskrypcję, pozwól na użycie kodu
  IF has_active_sub THEN
    RETURN TRUE;
  END IF;

  -- Jeśli nie ma aktywnej subskrypcji, sprawdź limit
  RETURN active_employees_count < employee_limit;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do aktywacji pracownika
CREATE OR REPLACE FUNCTION activate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_subscription_limit INTEGER;
  v_active_count INTEGER;
BEGIN
  -- Sprawdź czy można zmienić status (ograniczenie czasowe)
  IF NOT can_change_employee_status(p_employee_id) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Nie można zmienić statusu pracownika częściej niż raz na tydzień'
    );
  END IF;

  -- Pobierz limit aktywnych pracowników z planu subskrypcji
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz aktualną liczbę aktywnych pracowników
  SELECT COUNT(*) INTO v_active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Sprawdź, czy nie przekroczono limitu
  IF v_active_count >= v_subscription_limit THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Osiągnięto limit aktywnych pracowników dla tego planu subskrypcji'
    );
  END IF;

  -- Aktualizuj status pracownika
  UPDATE employees
  SET subscription_status = 'ACTIVE',
      last_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Status pracownika został zaktualizowany'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do dezaktywacji pracownika
CREATE OR REPLACE FUNCTION deactivate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  can_change BOOLEAN;
BEGIN
  -- Sprawdź czy można zmienić status (ograniczenie czasowe)
  SELECT can_change_employee_status(p_employee_id) INTO can_change;
  IF NOT can_change THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Nie można zmienić statusu pracownika częściej niż raz na tydzień'
    );
  END IF;

  -- Dezaktywuj pracownika
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Status pracownika został zaktualizowany'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do automatycznej dezaktywacji wszystkich pracowników firmy po wygaśnięciu subskrypcji
-- UWAGA: Ta funkcja powinna być wywoływana tylko przy rzeczywistym wygaśnięciu, nie przy anulowaniu
CREATE OR REPLACE FUNCTION deactivate_company_employees_on_expiry(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  -- Dezaktywuj pracowników tylko jeśli subskrypcja rzeczywiście wygasła
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW()
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE'
  AND NOT EXISTS (
    SELECT 1 FROM company_subscriptions
    WHERE company_id = p_company_id
    AND status = 'active'
    AND current_period_end > NOW()
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzania i dezaktywacji wygasłych subskrypcji (do uruchamiania cron job)
CREATE OR REPLACE FUNCTION check_and_deactivate_expired_subscriptions()
RETURNS VOID AS $$
DECLARE
  expired_company RECORD;
BEGIN
  -- Znajdź firmy z wygasłymi subskrypcjami
  FOR expired_company IN
    SELECT DISTINCT company_id
    FROM company_subscriptions
    WHERE status = 'active'
    AND current_period_end <= NOW()
  LOOP
    -- Dezaktywuj pracowników tej firmy
    PERFORM deactivate_company_employees_on_expiry(expired_company.company_id);

    -- Zaktualizuj status subskrypcji na expired
    UPDATE company_subscriptions
    SET status = 'expired',
        updated_at = NOW()
    WHERE company_id = expired_company.company_id
    AND status = 'active'
    AND current_period_end <= NOW();
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do automatycznej reaktywacji pracowników przy ponownym wykupieniu subskrypcji
CREATE OR REPLACE FUNCTION reactivate_company_employees(
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_subscription_limit INTEGER;
  v_expired_count INTEGER;
  v_active_count INTEGER;
  v_available_slots INTEGER;
  v_reactivated_count INTEGER := 0;
  employee_record RECORD;
BEGIN
  -- Pobierz limit aktywnych pracowników z planu subskrypcji
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz liczbę aktywnych pracowników
  SELECT COUNT(*) INTO v_active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Oblicz dostępne miejsca
  v_available_slots := v_subscription_limit - v_active_count;

  -- Jeśli nie ma dostępnych miejsc, zakończ
  IF v_available_slots <= 0 THEN
    RETURN jsonb_build_object(
      'success', true,
      'message', 'Osiągnięto limit aktywnych pracowników',
      'reactivated_count', 0,
      'active_count', v_active_count,
      'limit', v_subscription_limit
    );
  END IF;

  -- Pobierz liczbę nieaktywnych pracowników
  SELECT COUNT(*) INTO v_expired_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'SUBSCRIPTION_EXPIRED';

  -- Jeśli nie ma nieaktywnych pracowników, zakończ
  IF v_expired_count = 0 THEN
    RETURN jsonb_build_object(
      'success', true,
      'message', 'Brak nieaktywnych pracowników do reaktywacji',
      'reactivated_count', 0
    );
  END IF;

  -- Reaktywuj pracowników do dostępnych miejsc
  FOR employee_record IN
    SELECT id FROM employees
    WHERE company_id = p_company_id
    AND subscription_status = 'SUBSCRIPTION_EXPIRED'
    ORDER BY last_status_change ASC -- Najpierw ci, którzy zostali dezaktywowani najwcześniej
    LIMIT v_available_slots
  LOOP
    UPDATE employees
    SET subscription_status = 'ACTIVE',
        last_status_change = NOW()
    WHERE id = employee_record.id;
    
    v_reactivated_count := v_reactivated_count + 1;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'message', format('Reaktywowano %s z %s nieaktywnych pracowników (dostępne miejsca: %s)', v_reactivated_count, v_expired_count, v_available_slots),
    'reactivated_count', v_reactivated_count,
    'remaining_expired', v_expired_count - v_reactivated_count,
    'active_count', v_active_count + v_reactivated_count,
    'limit', v_subscription_limit
  );
END;
$$ LANGUAGE plpgsql;
