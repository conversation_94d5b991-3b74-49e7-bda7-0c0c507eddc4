-- Poprawka: Nie dezaktywuj pracowników przy zmianie planu subskrypcji
-- Problem: Trigger uruchamia się przy każdej zmianie subskrypcji, nawet przy upgrade/downgrade

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trigger, żeby nie uruchamiał się przy zmianie planu
CREATE OR REPLACE FUNCTION update_employees_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- UWAGA: Nie dezaktywuj pracowników przy anulowaniu - tylko przy rzeczywistym wygaśnięciu
  -- Anulowanie oznacza, że subskrypcja nie będzie odnawiana, ale jest aktywna do końca okresu

  -- Reaktywuj pracowników TYLKO przy pierwszej aktywacji subskrypcji (nie przy zmianie planu)
  IF NEW.status = 'active' AND (OLD IS NULL OR OLD.status != 'active') THEN
    -- Dodatkowe sprawdzenie: czy to rzeczywiście nowa subskrypcja, a nie zmiana planu
    -- Jeś<PERSON> plan_id się zmienił, ale status pozostał 'active', to jest to zmiana planu
    IF OLD IS NULL OR OLD.status != 'active' THEN
      PERFORM reactivate_company_employees(NEW.company_id);
      RAISE NOTICE 'Reaktywowano pracowników dla nowej subskrypcji firmy %', NEW.company_id;
    END IF;
  END IF;

  -- Dezaktywuj pracowników tylko przy rzeczywistym wygaśnięciu (status expired)
  IF NEW.status = 'expired' AND OLD.status = 'active' THEN
    PERFORM auto_deactivate_company_employees_on_expiry(NEW.company_id);
    RAISE NOTICE 'Dezaktywowano pracowników przy wygaśnięciu subskrypcji firmy %', NEW.company_id;
  END IF;

  -- Loguj zmiany dla debugowania
  IF OLD IS NOT NULL THEN
    RAISE NOTICE 'Subscription change: company_id=%, old_status=%, new_status=%, old_plan_id=%, new_plan_id=%', 
      NEW.company_id, OLD.status, NEW.status, OLD.plan_id, NEW.plan_id;
  ELSE
    RAISE NOTICE 'New subscription: company_id=%, status=%, plan_id=%', 
      NEW.company_id, NEW.status, NEW.plan_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Sprawdź czy są inne triggery, które mogą dezaktywować pracowników
SELECT 
  trigger_name, 
  event_manipulation, 
  event_object_table, 
  action_statement,
  action_timing
FROM information_schema.triggers 
WHERE event_object_table IN ('company_subscriptions', 'companies', 'employees')
AND action_statement LIKE '%deactivate%'
ORDER BY event_object_table, trigger_name;

-- 3. Sprawdź aktywne triggery na tabeli company_subscriptions
SELECT 
  trigger_name, 
  event_manipulation, 
  event_object_table, 
  action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'company_subscriptions'
ORDER BY trigger_name;

-- 4. Sprawdź czy funkcja reactivate_company_employees dezaktywuje pracowników
-- (powinna tylko reaktywować tych, którzy są już nieaktywni)
SELECT routine_name, routine_definition
FROM information_schema.routines 
WHERE routine_name = 'reactivate_company_employees';

-- 5. Test: Sprawdź aktualny stan pracowników dla firm z aktywnymi subskrypcjami
SELECT 
  c.name as company_name,
  c.account_type,
  c.verification_code_limit,
  COUNT(e.id) as total_employees,
  COUNT(CASE WHEN e.subscription_status = 'ACTIVE' THEN 1 END) as active_employees,
  COUNT(CASE WHEN e.subscription_status = 'SUBSCRIPTION_EXPIRED' THEN 1 END) as expired_employees
FROM companies c
LEFT JOIN employees e ON c.id = e.company_id
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
WHERE cs.status = 'active'
GROUP BY c.id, c.name, c.account_type, c.verification_code_limit
ORDER BY c.name;
