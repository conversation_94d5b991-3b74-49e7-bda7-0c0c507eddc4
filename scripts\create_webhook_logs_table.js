// Script to create webhook_logs table in Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase credentials
const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function main() {
  try {
    // Read SQL file
    const sqlFilePath = path.join(__dirname, '..', 'sql', 'webhook_logs_table.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('Creating webhook_logs table...');
    
    // Execute SQL using Supabase's rpc function
    const { data, error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error creating webhook_logs table:', error);
      
      // Try alternative approach if the first one fails
      console.log('Trying alternative approach...');
      
      // Split SQL into separate statements
      const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
      
      for (const statement of statements) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) {
          console.error('Error executing statement:', error);
        } else {
          console.log('Statement executed successfully');
        }
      }
    } else {
      console.log('webhook_logs table created successfully');
    }
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

main(); 