-- Ten skrypt tworzy funkcję mapującą ID produktów Stripe na nazwy planów subskrypcji
-- oraz aktualizuje funkcję obsługi webhooka
-- Wykonaj ten skrypt w konsoli SQL Supabase

-- Funkcja mapująca ID produktu Stripe na nazwę planu
CREATE OR REPLACE FUNCTION get_plan_name_by_stripe_product_id(stripe_product_id TEXT)
RETURNS TEXT AS $$
BEGIN
  -- Mapowanie ID produktów Stripe na nazwy planów
  -- Zaktualizuj te wartości na podstawie rzeczywistych ID produktów w Stripe
  RETURN CASE stripe_product_id
    WHEN 'prod_PUvVGpVvbdcYGn' THEN 'Basic'
    WHEN 'prod_PUvWlcBSdSPMbw' THEN 'Pro'
    WHEN 'prod_PUvXOXMGcMTAuc' THEN 'Business'
    ELSE 'Basic' -- Domy<PERSON><PERSON>ie Basic, jeśli nie znaleziono dopasowania
  END;
END;
$$ LANGUAGE plpgsql;

-- Funkcja do aktualizacji typu konta firmy na podstawie subskrypcji
CREATE OR REPLACE FUNCTION update_company_account_type()
RETURNS TRIGGER AS $$
DECLARE
  plan_name TEXT;
  verification_limit INTEGER;
BEGIN
  -- Pobierz nazwę planu z tabeli subscription_plans
  SELECT sp.name INTO plan_name
  FROM subscription_plans sp
  WHERE sp.id = NEW.plan_id;
  
  -- Jeśli nie znaleziono planu, użyj domyślnej wartości
  IF plan_name IS NULL THEN
    plan_name := 'Basic';
  END IF;
  
  -- Ustaw limit kodów weryfikacyjnych na podstawie planu
  verification_limit := CASE
    WHEN plan_name LIKE '%Basic%' THEN 5
    WHEN plan_name LIKE '%Pro%' THEN 20
    WHEN plan_name LIKE '%Business%' THEN 999999
    ELSE 5
  END;
  
  -- Aktualizuj firmę
  UPDATE companies
  SET account_type = plan_name,
      verification_code_limit = verification_limit,
      updated_at = now()
  WHERE id = NEW.company_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Utwórz wyzwalacz dla tabeli company_subscriptions
DROP TRIGGER IF EXISTS update_company_account_type_trigger ON company_subscriptions;

CREATE TRIGGER update_company_account_type_trigger
AFTER INSERT OR UPDATE OF status ON company_subscriptions
FOR EACH ROW
WHEN (NEW.status = 'active')
EXECUTE PROCEDURE update_company_account_type();

-- Funkcja do resetowania typu konta firmy na darmowy, gdy subskrypcja wygasa
CREATE OR REPLACE FUNCTION reset_company_account_type()
RETURNS TRIGGER AS $$
BEGIN
  -- Sprawdź, czy firma ma inne aktywne subskrypcje
  IF NOT EXISTS (
    SELECT 1 FROM company_subscriptions 
    WHERE company_id = OLD.company_id 
      AND status = 'active' 
      AND id != OLD.id
  ) THEN
    -- Jeśli nie ma innych aktywnych subskrypcji, przywróć darmowy plan
    UPDATE companies
    SET account_type = 'free',
        verification_code_limit = 2,
        updated_at = now()
    WHERE id = OLD.company_id;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Utwórz wyzwalacz dla anulowanych subskrypcji
DROP TRIGGER IF EXISTS reset_company_account_type_trigger ON company_subscriptions;

CREATE TRIGGER reset_company_account_type_trigger
AFTER UPDATE OF status ON company_subscriptions
FOR EACH ROW
WHEN (OLD.status = 'active' AND NEW.status IN ('canceled', 'unpaid', 'incomplete_expired'))
EXECUTE PROCEDURE reset_company_account_type();

-- Testowa aktualizacja dla firmy z obrazka
UPDATE company_subscriptions
SET status = 'active'
WHERE company_id = '90c35057-24c0-432e-8d06-68ae46ce3979';

-- Sprawdź wyniki
SELECT c.id, c.name, c.account_type, c.verification_code_limit, 
       cs.id as subscription_id, cs.status, sp.name as plan_name
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE c.id = '90c35057-24c0-432e-8d06-68ae46ce3979'; 