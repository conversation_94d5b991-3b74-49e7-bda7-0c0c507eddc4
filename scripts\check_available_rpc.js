const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkAvailableRPC() {
  try {
    console.log('Checking available RPC functions...');
    
    // Test różnych nazw funkcji RPC
    const rpcFunctions = [
      'execute_sql',
      'exec_sql', 
      'run_sql',
      'update_company_account_type_rpc',
      'process_stripe_payment'
    ];

    for (const funcName of rpcFunctions) {
      try {
        console.log(`\nTesting RPC function: ${funcName}`);
        
        if (funcName === 'update_company_account_type_rpc') {
          // Test z prawidłowymi parametrami
          const { data, error } = await supabase.rpc(funcName, {
            p_company_id: 'ababe85c-f81c-4808-9da8-03252f686bd0',
            p_plan_name: 'basic'
          });
          
          if (error) {
            console.log(`❌ ${funcName}: ${error.message}`);
          } else {
            console.log(`✓ ${funcName}: Works! Result: ${data}`);
          }
        } else {
          // Test z pustymi parametrami
          const { data, error } = await supabase.rpc(funcName, {});
          
          if (error) {
            console.log(`❌ ${funcName}: ${error.message}`);
          } else {
            console.log(`✓ ${funcName}: Works! Result: ${data}`);
          }
        }
      } catch (err) {
        console.log(`❌ ${funcName}: Exception - ${err.message}`);
      }
    }

    console.log('\n=== Manual trigger test ===');
    
    // Ponieważ nie możemy wykonać SQL bezpośrednio, sprawdźmy czy trigger'y już istnieją
    // poprzez test funkcjonalności
    
    // Znajdź firmę z aktywną subskrypcją
    const { data: activeSubscriptions, error: subscriptionsError } = await supabase
      .from('company_subscriptions')
      .select(`
        id,
        company_id,
        status,
        companies!inner(id, name, account_type, verification_code_limit)
      `)
      .eq('status', 'active')
      .limit(1);

    if (subscriptionsError || !activeSubscriptions || activeSubscriptions.length === 0) {
      console.log('No active subscriptions found for testing');
      return;
    }

    const testSubscription = activeSubscriptions[0];
    console.log(`\nTesting with company: ${testSubscription.companies.name}`);
    console.log(`Current: ${testSubscription.companies.account_type} (limit: ${testSubscription.companies.verification_code_limit})`);

    // Ponieważ trigger'y mogą nie działać, użyjmy webhook'ów do symulacji
    console.log('\nSince triggers may not work, let\'s use RPC function directly...');
    
    // Test anulowania przez RPC
    const { data: rpcResult, error: rpcError } = await supabase
      .rpc('update_company_account_type_rpc', {
        p_company_id: testSubscription.company_id,
        p_plan_name: 'free'
      });

    if (rpcError) {
      console.error('RPC function failed:', rpcError);
    } else {
      console.log('✓ RPC function succeeded:', rpcResult);
      
      // Sprawdź rezultat
      const { data: updatedCompany, error: fetchError } = await supabase
        .from('companies')
        .select('account_type, verification_code_limit')
        .eq('id', testSubscription.company_id)
        .single();

      if (!fetchError) {
        console.log(`After RPC: ${updatedCompany.account_type} (limit: ${updatedCompany.verification_code_limit})`);
        
        // Przywróć oryginalny stan
        const originalPlan = testSubscription.companies.account_type;
        await supabase.rpc('update_company_account_type_rpc', {
          p_company_id: testSubscription.company_id,
          p_plan_name: originalPlan
        });
        console.log(`✓ Restored to: ${originalPlan}`);
      }
    }

  } catch (error) {
    console.error('Error checking RPC functions:', error);
  }
}

checkAvailableRPC();
