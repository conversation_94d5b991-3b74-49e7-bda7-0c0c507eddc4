import React from 'react';
import { Text, TextProps } from 'react-native';

/**
 * SafeText to komponent opakowujący standardowy Text z React Native,
 * kt<PERSON>ry zabezpiecza przed przypadkowym renderowaniem pustych ciągów tekstowych
 * lub warto<PERSON>, które nie są ciągami.
 */
export const SafeText: React.FC<TextProps & { children: React.ReactNode }> = ({ children, ...props }) => {
  // Zabezpieczenie przed niepoprawnymi typami
  if (children === null || children === undefined) {
    return null;
  }

  // Konwersja wartości, które nie są ciągami, na ciągi
  const safeChildren = typeof children === 'string' || typeof children === 'number'
    ? children
    : Array.isArray(children)
      ? children.map((child, index) => 
          typeof child === 'string' || typeof child === 'number' 
            ? <React.Fragment key={index}>{child}</React.Fragment>
            : child
        )
      : String(children);
  
  return <Text {...props}>{safeChildren}</Text>;
};

export default SafeText; 