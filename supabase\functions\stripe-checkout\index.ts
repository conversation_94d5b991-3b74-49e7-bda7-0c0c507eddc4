import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers dla Edge Functions
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
};

// Funkcja pomocnicza do obsługi CORS preflight
function handleCors(req: Request) {
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  return null;
}

// Konfiguracja Stripe
const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
const stripe = new Stripe(stripeApiKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient(),
});

// Funkcja do tworzenia sesji płatności w Stripe
serve(async (req) => {
  // Obsługa CORS dla zapytań preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Sprawdź metodę HTTP
    if (req.method !== 'POST') {
      throw new Error('Method not allowed');
    }

    // Parsowanie danych z żądania
    const { companyId, planId, successUrl, cancelUrl } = await req.json();

    // Walidacja danych wejściowych
    if (!companyId || !planId || !successUrl || !cancelUrl) {
      throw new Error('Missing required parameters');
    }

    // Inicjalizacja klienta Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Brakujące zmienne środowiskowe Supabase');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Pobierz dane planu subskrypcji
    const { data: plan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', planId)
      .single();
    
    if (planError || !plan) {
      throw new Error('Plan not found');
    }
    
    if (!plan.stripe_price_id) {
      throw new Error('Plan not configured with Stripe');
    }
    
    // Pobierz dane firmy
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('name, email')
      .eq('id', companyId)
      .single();
    
    if (companyError || !company) {
      throw new Error('Company not found');
    }
    
    // Sprawdź czy firma ma już klienta w Stripe
    let customerId: string;
    
    const { data: subscription, error: subError } = await supabase
      .from('company_subscriptions')
      .select('stripe_customer_id')
      .eq('company_id', companyId)
      .maybeSingle();
    
    if (subscription?.stripe_customer_id) {
      customerId = subscription.stripe_customer_id;
    } else {
      // Jeśli nie ma klienta w Stripe, utwórz nowego
      const customer = await stripe.customers.create({
        name: company.name,
        email: company.email,
        metadata: {
          company_id: companyId
        }
      });
      
      customerId = customer.id;
    }
    
    // Utwórz sesję płatności w Stripe
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      client_reference_id: companyId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: plan.stripe_price_id,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        company_id: companyId,
        plan_id: planId
      },
      subscription_data: {
        metadata: {
          company_id: companyId,
          plan_id: planId
        }
      }
    });
    
    // Zwróć URL do sesji płatności
    return new Response(JSON.stringify({ url: session.url }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Error creating checkout session:', error);
    const errorMessage = error instanceof Error ? error.message : 'Nieznany błąd';
    return new Response(JSON.stringify({ error: `Błąd serwera: ${errorMessage}` }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}); 