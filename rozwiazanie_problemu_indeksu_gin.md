# Rozwiązanie problemu z indeksem GIN na kolumnie photos

## Problem

Występuje błąd podczas aktualizacji rekordu zakupu ze zdjęciami:

```
Błąd podczas aktualizacji ze zdjęciami: 
Object { code: "54000", details: null, hint: null, message: "index row requires 78064 bytes, maximum size is 8191" }
```

Problem wynika z przekroczenia maksymalnego rozmiaru wiersza indeksu GIN na kolumnie `photos` w tabeli `purchases`. PostgreSQL ogranicza rozmiar wiersza indeksu do 8191 bajtów, podczas gdy w tym przypadku próba zapisania zdjęć wymaga 78064 bajtów.

## Rozwiązanie

Problem rozwiązaliśmy na kilka sposobów:

### 1. Usunięcie problematycznego indeksu GIN na kolumnie photos

Przygotowaliśmy skrypt SQL `remove_photos_index.sql`, który usuwa indeks GIN z kolumny `photos`:

```sql
DROP INDEX IF EXISTS purchases_photos_idx;
```

Ten skrypt należy wykonać w SQL Editor w konsoli Supabase. Jest to najszybszy sposób rozwiązania problemu.

### 2. Dodanie alternatywnej kolumny photo_urls bez indeksu GIN

Dodatkowo utworzyliśmy skrypt `add_photo_urls_column.sql`, który dodaje nową kolumnę `photo_urls` do tabeli `purchases`. Kolumna ta nie ma indeksu GIN, dzięki czemu nie będzie powodować problemów z przekroczeniem maksymalnego rozmiaru wiersza indeksu.

```sql
ALTER TABLE purchases 
ADD COLUMN IF NOT EXISTS photo_urls TEXT[] DEFAULT '{}';
```

### 3. Modyfikacja kodu aplikacji

Zmodyfikowaliśmy kod aplikacji, aby korzystał z nowej kolumny `photo_urls` zamiast kolumny `photos`:

#### W komponencie PurchasesManager.tsx:

Zmieniliśmy funkcję `handleSubmitPurchase`, aby używała kolumny `photo_urls` zamiast `photos`:

```javascript
// Używamy photo_urls zamiast photos, aby uniknąć problemów z indeksem GIN
const { error: updateError } = await supabase
  .from('purchases')
  .update({ photo_urls: form.attachments })
  .eq('id', purchaseId);
```

#### W komponencie PurchaseDetails.tsx:

Zmodyfikowaliśmy komponent, aby wyświetlał zdjęcia zarówno z kolumny `photo_urls` jak i `photos` (dla kompatybilności wstecznej):

```javascript
// Określamy które zdjęcia wyświetlić - najpierw sprawdzamy photo_urls, potem photos
const imagesToDisplay = purchase?.photo_urls?.length ? purchase.photo_urls : purchase?.photos || [];
```

### 4. Dodatkowe optymalizacje

Dodatkowo wprowadziliśmy:

1. **Limit ilości zdjęć** - maksymalna liczba zdjęć dla jednego wniosku to 3
2. **Zmniejszenie jakości zdjęć** - zmniejszenie jakości z 0.8 do 0.5 (większa kompresja)
3. **Wyłączenie metadanych EXIF** - dodatkowa redukcja rozmiaru zdjęć

```javascript
// Sprawdzamy czy nie przekroczyliśmy limitu zdjęć
if (form.attachments.length >= 3) {
  Alert.alert(
    "Limit zdjęć", 
    "Możesz dodać maksymalnie 3 zdjęcia do jednego wniosku. Usuń jakieś zdjęcie, aby dodać nowe."
  );
  setLoading(false);
  return;
}

// Pick image z większą kompresją
const result = await ImagePicker.launchImageLibraryAsync({
  mediaTypes: ImagePicker.MediaTypeOptions.Images,
  allowsEditing: true,
  aspect: [4, 3],
  quality: 0.5, // Zmniejszenie jakości dla mniejszego rozmiaru pliku
  base64: false,
  exif: false, // Wyłączamy metadane EXIF aby dodatkowo zmniejszyć rozmiar
});
```

## Instrukcja wdrożenia

1. Najpierw wykonaj skrypt `remove_photos_index.sql` w SQL Editor w konsoli Supabase
2. Następnie wykonaj skrypt `add_photo_urls_column.sql`, który doda nową kolumnę i zmigruje istniejące dane
3. Zmiany w kodzie aplikacji już zostały wprowadzone i będą używać nowej kolumny `photo_urls`

Po tych zmianach problem z przekroczeniem maksymalnego rozmiaru wiersza indeksu powinien zostać rozwiązany. 