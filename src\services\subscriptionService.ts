import { supabase } from '../lib/supabase';
import { STRIPE_API_URL } from '../config';

export const changeSubscriptionPlan = async (subscriptionId: string, newPriceId: string) => {
  try {
    // Pobierz aktualną subskrypcję
    const { data: subscription, error: subscriptionError } = await supabase
      .from('company_subscriptions')
      .select('*')
      .eq('stripe_subscription_id', subscriptionId)
      .single();

    if (subscriptionError) {
      throw new Error('Nie można znaleźć subskrypcji');
    }

    // Wywołaj endpoint Stripe do zmiany planu
    const response = await fetch(`${STRIPE_API_URL}/update-subscription`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscriptionId,
        newPriceId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Błąd podczas zmiany planu');
    }

    const result = await response.json();

    // Aktualizuj lokalny stan subskrypcji
    const { error: updateError } = await supabase
      .from('company_subscriptions')
      .update({
        stripe_price_id: newPriceId,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', subscriptionId);

    if (updateError) {
      console.error('Błąd podczas aktualizacji subskrypcji w bazie:', updateError);
    }

    return result;
  } catch (error) {
    console.error('Błąd podczas zmiany planu:', error);
    throw error;
  }
}; 