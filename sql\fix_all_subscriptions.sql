-- Ten skrypt naprawia wszystkie istniejące subskrypcje, aktualizując typ konta firm
-- Wykonaj ten skrypt w konsoli SQL Supabase

-- Najpierw wyświetl bieżące dane
SELECT 
  c.id as company_id, 
  c.name as company_name, 
  c.account_type, 
  c.verification_code_limit,
  cs.id as subscription_id, 
  cs.status as subscription_status,
  sp.name as plan_name
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
ORDER BY c.created_at DESC;

-- Aktualizuj wszystkie firmy z aktywnymi subskrypcjami
DO $$
DECLARE
  company_rec RECORD;
BEGIN
  FOR company_rec IN 
    SELECT 
      c.id as company_id, 
      sp.name as plan_name,
      CASE
        WHEN sp.name LIKE '%Basic%' THEN 5
        WHEN sp.name LIKE '%Pro%' THEN 20
        WHEN sp.name LIKE '%Business%' THEN 999999
        ELSE 5
      END as verification_limit
    FROM companies c
    JOIN company_subscriptions cs ON c.id = cs.company_id
    JOIN subscription_plans sp ON cs.plan_id = sp.id
    WHERE cs.status = 'active'
  LOOP
    -- Aktualizuj typ konta i limit kodów weryfikacyjnych
    UPDATE companies
    SET account_type = company_rec.plan_name,
        verification_code_limit = company_rec.verification_limit,
        updated_at = now()
    WHERE id = company_rec.company_id;
    
    RAISE NOTICE 'Zaktualizowano firmę % na plan % z limitem %', 
      company_rec.company_id, company_rec.plan_name, company_rec.verification_limit;
  END LOOP;
END $$;

-- Aktualizuj firmy bez aktywnych subskrypcji na 'free'
UPDATE companies c
SET account_type = 'free',
    verification_code_limit = 2,
    updated_at = now()
WHERE NOT EXISTS (
  SELECT 1 
  FROM company_subscriptions cs 
  WHERE cs.company_id = c.id AND cs.status = 'active'
)
AND account_type != 'free';

-- Sprawdź wyniki po aktualizacji
SELECT 
  c.id as company_id, 
  c.name as company_name, 
  c.account_type, 
  c.verification_code_limit,
  cs.id as subscription_id, 
  cs.status as subscription_status,
  sp.name as plan_name
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
ORDER BY c.created_at DESC; 