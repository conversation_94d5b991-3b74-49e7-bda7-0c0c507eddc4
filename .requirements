You are an expert in React Native, Expo, Supabase, and mobile development.

## **Code Structure & Guidelines**
- Use **React Native with Expo** for mobile development.
- Use **TypeScript** for all files.
- Follow functional programming principles; **avoid class components**.
- **Use Zustand** for state management.
- Structure files as follows:
  - **`/components`** – reusable UI components.
  - **`/screens`** – main app views.
  - **`/store`** – global state management.
  - **`/services`** – API and database logic.
  - **`/utils`** – helper functions.
- Use **named exports** for all modules.
- Keep components small and modular.

## **Authentication & Authorization**
- Implement **Supabase Auth** for user authentication.
- Users can register as:
  - **Company Managers** – can create companies and generate verification codes.
  - **Employees** – must enter a verification code from their employer.
- Employees **cannot** access admin functionality.

## **Time Tracking**
- Employees can **start and stop work shifts**.
- Work session timestamps are stored in Supabase.
- Managers can view employee work history.

## **Task Management**
- Managers can **create, assign, and edit tasks**.
- Employees can **view their assigned tasks** but **cannot edit them**.
- Task updates should be **real-time** via WebSockets.

## **Database & API**
- Use **Supabase as the backend**.
- Implement **Row-Level Security (RLS)** to prevent unauthorized data access.
- Use **Supabase Edge Functions** for business logic (e.g., task notifications).
- Optimize database queries using **select, filter, and pagination**.

## **Performance & Optimization**
- Use **lazy loading** for screens and components.
- Optimize images and assets for mobile performance.
- **Avoid excessive re-renders** – use memoization where needed.

## **UI & UX**
- Use **Tailwind CSS / Tamagui** for styling.
- Ensure **dark mode support**.
- Implement **responsive design** for various screen sizes.
- Follow **accessibility (a11y) best practices**.

## **Best Practices**
- Write **clean, maintainable, and DRY code**.
- Use **Zod for validation** of forms and API responses.
- **Write unit tests** for critical components and logic.
- Follow **secure coding practices** for user authentication and database access.
