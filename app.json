{"expo": {"name": "Workflow", "slug": "workflow", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "scheme": "workflow", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/ccd28265-86a5-442b-9759-d39c3e2bb8a8"}, "assetBundlePatterns": ["**/*", "assets/images/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.yourcompany.workflow", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSLocationWhenInUseUsageDescription": "Ta aplikacja używa Twojej lokalizacji do zapisywania miejsca rozpoczęcia i zakończenia pracy.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Ta aplikacja używa Twojej lokalizacji do zapisywania miejsca rozpoczęcia i zakończenia pracy.", "NSCameraUsageDescription": "Ta aplikacja używa aparatu do robienia zdjęć zadań.", "NSPhotoLibraryUsageDescription": "Ta aplikacja potrzebuje dostępu do zdjęć, aby umożliwić przesyłanie zdjęć zadań."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.yourcompany.workflow", "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "title": "Workflow"}, "plugins": ["expo-location", "expo-image-picker"], "extra": {"eas": {"projectId": "ccd28265-86a5-442b-9759-d39c3e2bb8a8"}}, "runtimeVersion": {"policy": "sdkVersion"}}}