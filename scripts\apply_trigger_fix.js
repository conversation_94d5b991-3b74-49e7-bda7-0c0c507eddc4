const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyTriggerFix() {
  try {
    console.log('Reading SQL file...');
    const sqlContent = fs.readFileSync('sql/fix_triggers_for_companies.sql', 'utf8');
    
    // Podziel SQL na poszczególne instrukcje
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Found ${statements.length} SQL statements to execute`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`\nExecuting statement ${i + 1}/${statements.length}:`);
      console.log(statement.substring(0, 100) + (statement.length > 100 ? '...' : ''));

      try {
        // Użyj rpc do wykonania SQL
        const { data, error } = await supabase.rpc('execute_sql', { sql: statement });
        
        if (error) {
          console.error(`Error in statement ${i + 1}:`, error);
        } else {
          console.log(`✓ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`Exception in statement ${i + 1}:`, err);
      }
    }

    console.log('\n=== Testing trigger functionality ===');
    
    // Test trigger'ów
    await testTriggers();

  } catch (error) {
    console.error('Error applying trigger fix:', error);
  }
}

async function testTriggers() {
  try {
    // Znajdź firmę z aktywną subskrypcją
    const { data: activeSubscriptions, error: subscriptionsError } = await supabase
      .from('company_subscriptions')
      .select(`
        id,
        company_id,
        status,
        companies!inner(id, name, account_type, verification_code_limit)
      `)
      .eq('status', 'active')
      .limit(1);

    if (subscriptionsError || !activeSubscriptions || activeSubscriptions.length === 0) {
      console.log('No active subscriptions found for testing');
      return;
    }

    const testSubscription = activeSubscriptions[0];
    console.log(`\nTesting with company: ${testSubscription.companies.name}`);
    console.log(`Before: ${testSubscription.companies.account_type} (limit: ${testSubscription.companies.verification_code_limit})`);

    // Test 1: Anuluj subskrypcję
    console.log('\n1. Testing cancellation trigger...');
    const { error: cancelError } = await supabase
      .from('company_subscriptions')
      .update({ status: 'canceled' })
      .eq('id', testSubscription.id);

    if (cancelError) {
      console.error('Error canceling subscription:', cancelError);
      return;
    }

    // Poczekaj na trigger
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Sprawdź rezultat
    const { data: afterCancel, error: afterCancelError } = await supabase
      .from('companies')
      .select('account_type, verification_code_limit')
      .eq('id', testSubscription.company_id)
      .single();

    if (afterCancelError) {
      console.error('Error fetching company after cancel:', afterCancelError);
    } else {
      console.log(`After cancel: ${afterCancel.account_type} (limit: ${afterCancel.verification_code_limit})`);
      if (afterCancel.account_type === 'free' && afterCancel.verification_code_limit === 2) {
        console.log('✓ Cancellation trigger works correctly!');
      } else {
        console.log('❌ Cancellation trigger did not work');
      }
    }

    // Test 2: Przywróć subskrypcję
    console.log('\n2. Testing activation trigger...');
    const { error: activateError } = await supabase
      .from('company_subscriptions')
      .update({ status: 'active' })
      .eq('id', testSubscription.id);

    if (activateError) {
      console.error('Error activating subscription:', activateError);
      return;
    }

    // Poczekaj na trigger
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Sprawdź rezultat
    const { data: afterActivate, error: afterActivateError } = await supabase
      .from('companies')
      .select('account_type, verification_code_limit')
      .eq('id', testSubscription.company_id)
      .single();

    if (afterActivateError) {
      console.error('Error fetching company after activate:', afterActivateError);
    } else {
      console.log(`After activate: ${afterActivate.account_type} (limit: ${afterActivate.verification_code_limit})`);
      if (afterActivate.account_type !== 'free') {
        console.log('✓ Activation trigger works correctly!');
      } else {
        console.log('❌ Activation trigger did not work');
      }
    }

  } catch (error) {
    console.error('Error testing triggers:', error);
  }
}

applyTriggerFix();
