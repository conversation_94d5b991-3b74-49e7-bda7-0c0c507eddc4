import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, Platform } from 'react-native';
import { Calendar, DateData } from 'react-native-calendars';
import { Ionicons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';

interface DatePickerProps {
  date: string | null;
  onDateChange: (date: string) => void;
  placeholder?: string;
  label?: string;
}

const DatePicker = ({ 
  date, 
  onDateChange, 
  placeholder = 'Wybierz datę', 
  label 
}: DatePickerProps) => {
  const [showCalendar, setShowCalendar] = useState(false);

  const formatDisplayDate = (dateString: string | null) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      // Ensure it's a valid date
      if (isNaN(date.getTime())) return '';
      
      // Format as DD.MM.YYYY
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      
      return `${day}.${month}.${year}`;
    } catch (e) {
      return dateString;
    }
  };

  // Lista polskich nazw miesięcy
  const monthNames = [
    'Styczeń', 'Luty', 'Marzec', 'Kwiecień', 'Maj', 'Czerwiec',
    'Lipiec', 'Sierpień', 'Wrzesień', 'Październik', 'Listopad', 'Grudzień'
  ];

  // Funkcja formatująca nagłówek kalendarza - tylko nazwa miesiąca i rok
  const formatMonthHeader = (date: Date) => {
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    return `${month} ${year}`;
  };
  
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity 
        style={styles.pickerButton} 
        onPress={() => setShowCalendar(true)}
      >
        <Text style={[
          styles.pickerText,
          !date && styles.placeholderText
        ]}>
          {date ? formatDisplayDate(date) : placeholder}
        </Text>
        <Ionicons name="calendar-outline" size={20} color="#666" />
      </TouchableOpacity>
      
      <Modal
        transparent={true}
        visible={showCalendar}
        animationType="fade"
        onRequestClose={() => setShowCalendar(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowCalendar(false)}
        >
          <View 
            style={styles.calendarContainer}
            onStartShouldSetResponder={() => true}
          >
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>{i18n.t('selectDate')}</Text>
              <TouchableOpacity onPress={() => setShowCalendar(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            
            <Calendar
              onDayPress={(day: DateData) => {
                onDateChange(day.dateString);
                setShowCalendar(false);
              }}
              markedDates={date ? { [date]: { selected: true, selectedColor: '#2563EB' } } : {}}
              theme={{
                todayTextColor: '#2563EB',
                selectedDayBackgroundColor: '#2563EB',
                textDayFontSize: 16,
                textMonthFontSize: 16,
                textDayHeaderFontSize: 14,
                // Dostosowanie nagłówka miesiąca
                monthTextColor: '#1A1A1A',
                textMonthFontWeight: '600'
              }}
              // Własna funkcja formatująca nagłówek miesiąca
              renderHeader={(date) => {
                return (
                  <Text style={styles.monthHeaderText}>
                    {formatMonthHeader(date)}
                  </Text>
                );
              }}
            />
            
            {date && (
              <View style={styles.calendarFooter}>
                <TouchableOpacity 
                  style={styles.clearButton}
                  onPress={() => {
                    onDateChange('');
                    setShowCalendar(false);
                  }}
                >
                  <Text style={styles.clearButtonText}>{i18n.t('clear') || 'Wyczyść datę'}</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#1A1A1A',
    marginBottom: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  pickerText: {
    fontSize: 16,
    color: '#1A1A1A',
  },
  placeholderText: {
    color: '#9CA3AF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  calendarContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '100%',
    maxWidth: 400,
    padding: 16,
    maxHeight: '80%',
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  calendarTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  calendarFooter: {
    marginTop: 16,
    alignItems: 'center',
  },
  clearButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  clearButtonText: {
    color: '#666',
    fontWeight: '500',
  },
  monthHeaderText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    margin: 10,
    textAlign: 'center'
  }
});

export default DatePicker; 