const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkAndUpdateStripePrices() {
  try {
    console.log('=== Checking current Stripe Price IDs ===\n');
    
    // Pobierz wszystkie plany
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('billing_period', { ascending: true })
      .order('name', { ascending: true });

    if (plansError) {
      console.error('Error fetching plans:', plansError);
      return;
    }

    console.log('Current plans in database:');
    plans.forEach(plan => {
      console.log(`${plan.name} (${plan.billing_period}): ${plan.stripe_price_id}`);
    });

    // Nowe Stripe Price ID z Twojej wiadomości
    const newPriceIds = {
      'Business Yearly': 'price_1RXnIvPaKRxqYgSx9ZwLw4R0',
      'Pro Yearly': 'price_1RXnJXPaKRxqYgSxYwuVFMS2',
      'Basic Yearly': 'price_1RXnK2PaKRxqYgSxSpZzOpRc'
    };

    console.log('\n=== Updating Stripe Price IDs ===\n');

    // Aktualizuj Price ID dla planów rocznych
    for (const [planName, priceId] of Object.entries(newPriceIds)) {
      console.log(`Updating ${planName} to ${priceId}...`);
      
      const { error: updateError } = await supabase
        .from('subscription_plans')
        .update({ stripe_price_id: priceId })
        .eq('name', planName);

      if (updateError) {
        console.error(`Error updating ${planName}:`, updateError);
      } else {
        console.log(`✓ Updated ${planName}`);
      }
    }

    console.log('\n=== Verifying updates ===\n');

    // Sprawdź zaktualizowane plany
    const { data: updatedPlans, error: updatedError } = await supabase
      .from('subscription_plans')
      .select('*')
      .order('billing_period', { ascending: true })
      .order('name', { ascending: true });

    if (updatedError) {
      console.error('Error fetching updated plans:', updatedError);
      return;
    }

    console.log('Updated plans:');
    updatedPlans.forEach(plan => {
      console.log(`${plan.name} (${plan.billing_period}): ${plan.stripe_price_id}`);
    });

    // Sprawdź czy wszystkie plany roczne mają poprawne Price ID
    const yearlyPlans = updatedPlans.filter(plan => plan.billing_period === 'yearly');
    const missingPriceIds = yearlyPlans.filter(plan => !plan.stripe_price_id || plan.stripe_price_id.startsWith('price_'));

    if (missingPriceIds.length === 0) {
      console.log('\n✅ All yearly plans have correct Stripe Price IDs!');
    } else {
      console.log('\n❌ Some yearly plans still have incorrect Price IDs:');
      missingPriceIds.forEach(plan => {
        console.log(`- ${plan.name}: ${plan.stripe_price_id}`);
      });
    }

    console.log('\n=== Testing plan selection logic ===\n');

    // Test logiki wyboru planu
    const testPlanName = 'Basic Yearly';
    const testPlan = updatedPlans.find(plan => plan.name === testPlanName);
    
    if (testPlan) {
      console.log(`Test plan: ${testPlan.name}`);
      console.log(`Stripe Price ID: ${testPlan.stripe_price_id}`);
      console.log(`Billing period: ${testPlan.billing_period}`);
      console.log(`Price: ${testPlan.price / 100} PLN`);
      
      // Sprawdź czy Price ID jest poprawne
      if (testPlan.stripe_price_id === newPriceIds[testPlanName]) {
        console.log('✅ Price ID is correct!');
      } else {
        console.log('❌ Price ID is incorrect!');
      }
    }

  } catch (error) {
    console.error('Error in checkAndUpdateStripePrices:', error);
  }
}

checkAndUpdateStripePrices();
