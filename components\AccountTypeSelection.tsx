import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import PrismIcon from './PrismIcon';

interface AccountTypeSelectionProps {
  onBack: () => void;
  onSelectType: (type: 'company' | 'employee') => void;
}

const AccountTypeSelection = ({ onBack, onSelectType }: AccountTypeSelectionProps) => {
  return (
    <View style={styles.container}>
      <PrismIcon />
      <Text style={styles.title}>Create Account</Text>
      <Text style={styles.subtitle}>Choose your account type to get started</Text>
      
      <View style={styles.optionsContainer}>
        <TouchableOpacity 
          style={styles.option}
          onPress={() => onSelectType('company')}
        >
          <View style={styles.iconContainer}>
            <Ionicons name="business" size={24} color="#2563EB" />
          </View>
          <Text style={styles.optionTitle}>Company Account</Text>
          <Text style={styles.optionDescription}>
            Create a company account to manage employees and track work time
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.option}
          onPress={() => onSelectType('employee')}
        >
          <View style={styles.iconContainer}>
            <Ionicons name="person" size={24} color="#2563EB" />
          </View>
          <Text style={styles.optionTitle}>Employee Account</Text>
          <Text style={styles.optionDescription}>
            Join your company's workspace with a verification code
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>Already have an account? </Text>
        <TouchableOpacity onPress={onBack}>
          <Text style={styles.signInLink}>Sign in</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1E6EDF',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: 'white',
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    marginBottom: 30,
    textAlign: 'center',
  },
  optionsContainer: {
    width: '100%',
    maxWidth: 400,
    gap: 16,
  },
  option: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EBF5FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 8,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  footer: {
    flexDirection: 'row',
    marginTop: 24,
  },
  footerText: {
    color: 'white',
  },
  signInLink: {
    color: 'white',
    fontWeight: '600',
  },
});

export default AccountTypeSelection; 