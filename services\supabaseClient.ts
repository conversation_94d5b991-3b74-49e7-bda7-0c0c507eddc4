import { createClient } from '@supabase/supabase-js';
import { mockAuth } from './mockAuth';
import { mockDatabase } from './mockDatabase';

// Poprawny URL bez znaku '@' na początku
const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';

// Klucz dla anonimowego dostępu (basic)
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAzMjM4OTQsImV4cCI6MjA1NTg5OTg5NH0.lwtOqQfKhvV3VcyP_pxoZcH944NAQ1J0UaR_ZmiB1dw';

// Klucz dla dostępu z większymi uprawnieniami (service_role)
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

// Opcje klienta
const options = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storage: typeof window !== 'undefined' ? window.localStorage : undefined
  },
  global: {
    headers: {
      'X-Client-Info': 'supabase-js',
    },
  },
};

// Flaga do kontrolowania wykorzystania mocków - wyłącz mock
const USE_MOCK = false;

// Flaga do określenia czy używać klucza service_role zamiast anon
// UWAGA: używaj service_role tylko gdy naprawdę potrzebujesz większych uprawnień
const USE_SERVICE_ROLE = true;

// Tworzymy klienta Supabase z odpowiednim kluczem
const realSupabase = createClient(
  supabaseUrl, 
  USE_SERVICE_ROLE ? supabaseServiceKey : supabaseAnonKey,
  options
);

// Stwórz podstawowy mock dla wszystkich metod Supabase, które nie są pokryte przez mockAuth i mockDatabase
const mockAdditionalMethods = {
  storage: {
    from: (bucket: string) => ({
      upload: async (path: string, file: any) => {
        console.log(`[MOCK STORAGE] Upload to ${bucket}/${path}`);
        return { data: { path: `${bucket}/${path}` }, error: null };
      },
      download: async (path: string) => {
        console.log(`[MOCK STORAGE] Download from ${bucket}/${path}`);
        return { data: new Blob(['mock file content']), error: null };
      },
      getPublicUrl: (path: string) => {
        console.log(`[MOCK STORAGE] Get public URL for ${bucket}/${path}`);
        return { data: { publicUrl: `https://mock-storage-url.com/${bucket}/${path}` } };
      },
      list: async (path: string = '') => {
        console.log(`[MOCK STORAGE] List files in ${bucket}/${path}`);
        return { data: [], error: null };
      },
      remove: async (paths: string[]) => {
        console.log(`[MOCK STORAGE] Remove files: ${paths.join(', ')}`);
        return { data: { message: 'Files deleted' }, error: null };
      }
    })
  },
  rpc: (functionName: string, params: any = {}) => {
    console.log(`[MOCK RPC] Called function ${functionName} with params:`, params);
    return {
      data: { success: true, message: `Mocked RPC call to ${functionName}` },
      error: null
    };
  },
  channel: (channelName: string) => {
    console.log(`[MOCK REALTIME] Subscribed to channel ${channelName}`);
    return {
      on: () => ({ subscribe: () => ({ unsubscribe: () => {} }) }),
      subscribe: () => ({ unsubscribe: () => {} })
    };
  }
};

// Eksportujemy klienta Supabase
export const supabase = USE_MOCK 
  ? {
      auth: mockAuth,
      from: mockDatabase.from,
      storage: mockAdditionalMethods.storage,
      rpc: mockAdditionalMethods.rpc,
      channel: mockAdditionalMethods.channel
    }
  : realSupabase; 