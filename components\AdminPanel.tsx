import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, Platform, Alert, Modal, TextInput, useWindowDimensions } from 'react-native';
import { supabase } from '../services/supabaseClient';
import { Ionicons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';
import SubscriptionManagement from './SubscriptionManagement';
import { TopBar } from './TopBar';
import { hasActiveSubscription } from '../services/stripeService';
import { LinearGradient } from 'expo-linear-gradient';
// import { autoFixEmployeeStatus } from '../services/employeeRecoveryService.ts';


type MenuItem = 'dashboard' | 'employees' | 'schedule' | 'tasks' | 'admin';
type UserRole = 'employee' | 'coordinator' | 'admin';

const ROLES: { [key in UserRole]: string } = {
  employee: i18n.t('employeeRole'),
  coordinator: i18n.t('coordinatorRole'),
  admin: i18n.t('adminRole')
};

interface Employee {
  id: string;
  full_name: string;
  email: string;
  role: UserRole;
  status: string;
}

interface AdminPanelProps {
  companyId: string;
  onMenuPress: () => void;
  onNavigate: (menuItem: MenuItem) => void;
  isLargeScreen?: boolean;
}

const AdminPanel = ({ companyId, onMenuPress, onNavigate, isLargeScreen = false }: AdminPanelProps) => {
  const [verificationCodes, setVerificationCodes] = useState<Array<{ code: string; used: boolean }>>([]);
  const [showAlert, setShowAlert] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [editedName, setEditedName] = useState('');
  const [editedRole, setEditedRole] = useState<UserRole>('employee');
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [isPremium, setIsPremium] = useState(false);
  const [verificationCodeLimit, setVerificationCodeLimit] = useState(2);
  const [activeTab, setActiveTab] = useState<'codes' | 'employees'>('employees');
  const [updating, setUpdating] = useState<string | null>(null);

  // Funkcja pomocnicza dla alertów - obsługuje web i mobile
  const showAlertDialog = (title: string, message: string, buttons?: Array<{text: string, onPress?: () => void, style?: string}>) => {
    console.log('=== SHOWING ALERT ===');
    console.log('Title:', title);
    console.log('Message:', message);
    console.log('Platform:', Platform.OS);

    if (Platform.OS === 'web') {
      // Dla web używamy window.alert i window.confirm
      if (buttons && buttons.length > 1) {
        const result = window.confirm(`${title}\n\n${message}`);
        if (result && buttons[1]?.onPress) {
          buttons[1].onPress();
        } else if (!result && buttons[0]?.onPress) {
          buttons[0].onPress();
        }
      } else {
        window.alert(`${title}\n\n${message}`);
      }
    } else {
      // Dla mobile używamy Alert.alert
      Alert.alert(title, message, buttons);
    }
  };

  // Define checkPremiumStatus function before it's used
  const checkPremiumStatus = async () => {
    if (companyId) {
      console.log('Sprawdzanie statusu premium dla firmy:', companyId);
      const hasPremium = await hasActiveSubscription(companyId);
      console.log('Status premium:', hasPremium);
      setIsPremium(hasPremium);
      
      // Pobierz aktualny limit kodów z bazy danych
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('verification_code_limit')
        .eq('id', companyId)
        .single();

      console.log('Dane firmy:', companyData);
      console.log('Błąd pobierania danych firmy:', companyError);

      if (!companyError && companyData) {
        console.log('Ustawianie nowego limitu:', companyData.verification_code_limit);
        setVerificationCodeLimit(companyData.verification_code_limit);
      }
    }
  };

  useEffect(() => {
    fetchVerificationCodes();
    fetchEmployees();
    checkPremiumStatus();
  }, []);

  useEffect(() => {
    if (companyId) {
      checkPremiumStatus();
      fetchVerificationCodes();
    }
  }, [companyId]);

  // Dodaj nową funkcję do odświeżania wszystkich danych
  const refreshAllData = async () => {
    await checkPremiumStatus();
    await fetchVerificationCodes();
  };

  const openSubscriptionManagement = () => {
    setShowSubscriptionModal(true);
  };

  const handleSubscriptionModalClose = () => {
    setShowSubscriptionModal(false);
    refreshAllData(); // Odśwież dane po zamknięciu modalu
  };

  const fetchEmployees = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*, subscription_status, last_status_change')
        .eq('company_id', companyId);

      if (error) {
        console.error(i18n.t('errorFetchingEmployees'), error);
      } else {
        setEmployees(data || []);
      }
    } catch (error) {
      console.error(i18n.t('exceptionInFetchEmployees'), error);
    }
  };

  const fetchVerificationCodes = async () => {
    try {
      console.log('Fetching verification codes for company:', companyId);

      // Pobierz kody weryfikacyjne
      const { data: codes, error: codesError } = await supabase
        .from('verification_codes')
        .select('*')
        .eq('company_id', companyId);

      if (codesError) {
        console.error(i18n.t('errorFetchingCodes'), codesError);
        setVerificationCodes([]);
        return;
      }

      console.log('Fetched codes:', codes);

      // Pobierz wszystkich pracowników firmy
      const { data: employees, error: employeesError } = await supabase
        .from('employees')
        .select('full_name, verification_code')
        .eq('company_id', companyId);

      if (employeesError) {
        console.error('Error fetching employees:', employeesError);
        // Jeśli nie można pobrać pracowników, pokaż kody bez imion
        setVerificationCodes(codes || []);
        return;
      }

      console.log('Fetched employees:', employees);

      // Połącz kody z imionami pracowników
      const codesWithNames = (codes || []).map(code => {
        const employee = employees?.find(emp => emp.verification_code === code.code);
        return {
          ...code,
          employee_name: employee?.full_name || null
        };
      });

      console.log('Codes with names:', codesWithNames);
      setVerificationCodes(codesWithNames);
    } catch (error) {
      console.error('Exception in fetchVerificationCodes:', error);
      setVerificationCodes([]);
    }
  };

  const showLimitAlert = () => {
    if (Platform.OS === 'web') {
      setShowAlert(true);
    } else {
      Alert.alert(
        i18n.t('limitReached'),
        i18n.t('verificationCodeLimitMessage'),
        [
          { text: i18n.t('understand'), style: 'cancel' },
          { 
            text: i18n.t('buyPremium'),
            style: 'default',
            onPress: () => {
              setShowSubscriptionModal(true);
            }
          }
        ]
      );
    }
  };

  const generateVerificationCode = async () => {
    // Sprawdź limit kodów względem aktualnego limitu z bazy
    if (verificationCodes.length >= verificationCodeLimit) {
      showLimitAlert();
      return;
    }

    const code = Math.random().toString(36).substring(2, 8).toUpperCase();
    
    const { error } = await supabase
      .from('verification_codes')
      .insert([
        {
          code,
          company_id: companyId,
          used: false,
        },
      ]);

    if (error) {
      console.error(i18n.t('errorGeneratingCode'), error);
    } else {
      fetchVerificationCodes();
    }
  };

  const renderMenuItem = (iconName: any, title: string, menuItem: MenuItem) => (
    <TouchableOpacity 
      style={styles.menuItem}
      onPress={() => onNavigate(menuItem)}
    >
      <Ionicons name={iconName} size={24} color="#666" />
      <Text style={styles.menuItemText}>{title}</Text>
    </TouchableOpacity>
  );

  // Oblicz pozostałe dostępne kody używając limitu z bazy
  const remainingCodes = Math.max(0, verificationCodeLimit - verificationCodes.length);

  const handleEditEmployee = (employee: Employee) => {
    setSelectedEmployee(employee);
    setEditedName(employee.full_name);
    setEditedRole(employee.role || 'employee');
    setShowEditModal(true);
  };

  const handleSaveEdit = async () => {
    if (!selectedEmployee) return;

    try {
      console.log(i18n.t('updatingEmployee'), {
        id: selectedEmployee.id,
        role: editedRole,
        companyId
      });

      const { data, error } = await supabase
        .from('employees')
        .update({
          role: editedRole,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedEmployee.id)
        .eq('company_id', companyId)
        .select();

      if (error) {
        console.error(i18n.t('errorUpdatingEmployee'), error);
        if (Platform.OS === 'web') {
          window.alert(`${i18n.t('errorUpdatingEmployee')}: ${error.message}`);
        } else {
          Alert.alert(i18n.t('error'), `${i18n.t('failedToUpdateEmployee')}: ${error.message}`);
        }
      } else {
        console.log(i18n.t('employeeUpdatedSuccessfully'), data);
        fetchEmployees();
        setShowEditModal(false);
      }
    } catch (error) {
      console.error(i18n.t('exceptionInHandleSaveEdit'), error);
      if (Platform.OS === 'web') {
        window.alert(i18n.t('unexpectedErrorUpdatingEmployee'));
      } else {
        Alert.alert(i18n.t('error'), i18n.t('unexpectedErrorUpdatingEmployee'));
      }
    }
  };

  const handleDeleteEmployee = async (employeeId: string) => {
    const confirmMessage = 'Czy na pewno chcesz usunąć tego pracownika z firmy? Pracownik zostanie przekształcony w niezależnego i jego kod weryfikacyjny zostanie zwolniony.';

    if (Platform.OS === 'web') {
      if (!window.confirm(confirmMessage)) {
        return;
      }

      // Wykonaj przekształcenie w niezależnego pracownika
      try {
        const { data, error } = await supabase.rpc('remove_employee_from_company', {
          p_employee_id: employeeId,
          p_company_id: companyId
        });

        if (error) {
          console.error('Błąd usuwania pracownika z firmy:', error);
          window.alert(`Błąd: ${error.message}`);
        } else if (data && data.success) {
          console.log('Pracownik usunięty z firmy pomyślnie:', data);
          window.alert(data.message);
          fetchEmployees(); // Odśwież listę pracowników
        } else {
          window.alert(data?.message || 'Nieznany błąd podczas usuwania pracownika z firmy');
        }
      } catch (error) {
        console.error('Wyjątek podczas usuwania pracownika z firmy:', error);
        window.alert('Wystąpił nieoczekiwany błąd');
      }
    } else {
      Alert.alert(
        'Usuń z firmy',
        confirmMessage,
        [
          { text: 'Anuluj', style: 'cancel' },
          {
            text: 'Usuń z firmy',
            style: 'destructive',
            onPress: async () => {
              try {
                const { data, error } = await supabase.rpc('remove_employee_from_company', {
                  p_employee_id: employeeId,
                  p_company_id: companyId
                });

                if (error) {
                  console.error('Błąd usuwania pracownika z firmy:', error);
                  Alert.alert('Błąd', `Błąd: ${error.message}`);
                } else if (data && data.success) {
                  console.log('Pracownik usunięty z firmy pomyślnie:', data);
                  Alert.alert('Sukces', data.message);
                  fetchEmployees(); // Odśwież listę pracowników
                } else {
                  Alert.alert('Błąd', data?.message || 'Nieznany błąd podczas usuwania pracownika z firmy');
                }
              } catch (error) {
                console.error('Wyjątek podczas usuwania pracownika z firmy:', error);
                Alert.alert('Błąd', 'Wystąpił nieoczekiwany błąd');
              }
            }
          }
        ]
      );
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'coordinator':
        return '#2563EB';
      case 'admin':
        return '#DC2626';
      default:
        return '#059669';
    }
  };

  // Funkcja do naprawy statusu pracowników po zmianie planu
  // const handleFixEmployeeStatus = async () => {
  //   try {
  //     const result = await autoFixEmployeeStatus(companyId);

  //     if (!result.needed) {
  //       showAlertDialog(
  //         '✅ Status OK',
  //         'Status pracowników jest prawidłowy. Nie ma potrzeby naprawy.'
  //       );
  //       return;
  //     }

  //     if (result.result?.success) {
  //       showAlertDialog(
  //         '✅ Naprawiono',
  //         `Pomyślnie reaktywowano ${result.result.reactivated_count} pracowników.`
  //       );
  //       await fetchEmployees(); // Odśwież listę
  //     } else {
  //       showAlertDialog(
  //         '❌ Błąd',
  //         result.result?.message || 'Nie udało się naprawić statusu pracowników.'
  //       );
  //     }
  //   } catch (error) {
  //     console.error('Error fixing employee status:', error);
  //     showAlertDialog(
  //       '❌ Błąd',
  //       'Wystąpił błąd podczas naprawy statusu pracowników.'
  //     );
  //   }
  // };

  // Funkcja do zmiany statusu pracownika
  const toggleEmployeeStatus = async (employeeId: string, currentStatus: string) => {
    console.log('=== TOGGLE EMPLOYEE STATUS ===');
    console.log('Employee ID:', employeeId);
    console.log('Current status:', currentStatus);
    console.log('Company ID:', companyId);

    setUpdating(employeeId);

    try {
      // Wywołaj bezpośrednio funkcję SQL
      const functionName = currentStatus === 'ACTIVE' ? 'deactivate_employee' : 'activate_employee';

      console.log(`Calling ${functionName} with:`, {
        p_employee_id: employeeId,
        p_company_id: companyId
      });

      const { data, error } = await supabase.rpc(functionName, {
        p_employee_id: employeeId,
        p_company_id: companyId
      });

      console.log('=== DETAILED RESPONSE DATA ===');
      console.log('Function called:', functionName);
      console.log('SQL Response data:', JSON.stringify(data, null, 2));
      console.log('SQL Error:', error);

      if (error) {
        console.log('SQL Error occurred:', error);
        showAlertDialog('❌ Błąd', `Błąd bazy danych: ${error.message}`);
        return;
      }

      if (!data || !data.success) {
        console.log('Function returned failure:', data);

        // Sprawdź typ błędu na podstawie wiadomości
        const message = data?.message || 'Nieznany błąd';

        if (message.includes('limit aktywnych pracowników')) {
          // Limit pracowników przekroczony
          console.log('Showing employee limit dialog');
          showAlertDialog(
            '⚠️ Limit pracowników przekroczony',
            message + '\n\nCzy chcesz wykupić pakiet Premium, aby aktywować więcej pracowników?',
            [
              { text: 'Anuluj', style: 'cancel' },
              {
                text: '💎 Wykup Premium',
                onPress: () => {
                  console.log('Redirect to subscription management');
                  setShowSubscriptionModal(true);
                }
              }
            ]
          );
        } else if (message.includes('7 dni') || message.includes('poczekać')) {
          // Ograniczenie czasowe
          console.log('Showing time limit dialog');
          showAlertDialog(
            '⏰ Ograniczenie czasowe',
            message + '\n\nCzy chcesz wykupić pakiet Premium, aby usunąć ograniczenia czasowe?',
            [
              { text: 'Rozumiem', style: 'cancel' },
              {
                text: '💎 Wykup Premium',
                onPress: () => {
                  console.log('Redirect to subscription management from time limit');
                  setShowSubscriptionModal(true);
                }
              }
            ]
          );
        } else {
          // Inny błąd
          console.log('Showing generic error dialog:', message);
          showAlertDialog('❌ Błąd', message);
        }
      } else {
        console.log('Status changed successfully');
        // Odśwież listę pracowników
        await fetchEmployees();

        // Pokaż komunikat sukcesu z ikoną
        showAlertDialog(
          '✅ Sukces',
          data.message || 'Status pracownika został zmieniony'
        );
      }
    } catch (error) {
      console.error('Error in toggleEmployeeStatus:', error);
      showAlertDialog('❌ Błąd', 'Wystąpił błąd podczas zmiany statusu');
    } finally {
      setUpdating(null);
    }
  };

  // Jeśli modal subskrypcji jest otwarty, pokazujemy tylko komponent SubscriptionManagement
  if (showSubscriptionModal) {
    return (
      <SubscriptionManagement
        companyId={companyId}
        onClose={handleSubscriptionModalClose}
        onNavigate={onNavigate}
        onMenuPress={onMenuPress}
        hideTopBar={true}
      />
    );
  }

  // Normalny widok AdminPanel
  return (
    <View style={styles.container}>



      {/* Panel Premium/Subskrypcji */}
      {isPremium ? (
        <View style={styles.premiumCard}>
          <LinearGradient
            colors={['#4F46E5', '#7C3AED']}
            style={styles.premiumContent}
          >
            <View style={styles.premiumHeader}>
              <Ionicons name="diamond" size={28} color="#fff" />
              <Text style={styles.premiumTitle}>Premium Active</Text>
            </View>
            <Text style={styles.premiumDescription}>
              {i18n.t('premiumActiveDescription')}
            </Text>
            <TouchableOpacity
              style={styles.manageSubscriptionButton}
              onPress={openSubscriptionManagement}
            >
              <Text style={styles.manageSubscriptionText}>
                {i18n.t('manageSubscription')}
              </Text>
            </TouchableOpacity>
          </LinearGradient>
        </View>
      ) : (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{i18n.t('upgradeToPremium')}</Text>
          </View>
          <Text style={styles.modalText}>
            {i18n.t('premiumBenefitsDescription')}
          </Text>
          <TouchableOpacity
            style={styles.generateButton}
            onPress={openSubscriptionManagement}
          >
            <Ionicons name="diamond-outline" size={20} color="#fff" />
            <Text style={styles.generateButtonText}>{i18n.t('buyPremium')}</Text>
          </TouchableOpacity>
        </View>
      )}

      {activeTab === 'codes' && (
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{i18n.t('verificationCodes')}</Text>
              <View style={styles.codesCounter}>
                <Text style={styles.codesCounterText}>
                  {verificationCodes.length} {i18n.t('of')} {verificationCodeLimit}
                </Text>
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.generateButton,
                remainingCodes === 0 && styles.generateButtonDisabled
              ]}
              onPress={generateVerificationCode}
              disabled={remainingCodes === 0}
            >
              <Ionicons name="add-circle-outline" size={20} color="#fff" />
              <Text style={styles.generateButtonText}>{i18n.t('generateCode')}</Text>
            </TouchableOpacity>

            <View style={styles.codesList}>
              {verificationCodes.length === 0 ? (
                <Text style={styles.noCodesText}>Brak kodów weryfikacyjnych</Text>
              ) : (
                verificationCodes.map((item, index) => (
                  <View key={index} style={styles.codeItem}>
                    <View style={styles.codeMainInfo}>
                      <Text style={styles.codeText}>{item.code}</Text>
                      {item.employee_name && (
                        <Text style={styles.employeeNameText}>
                          {item.employee_name}
                        </Text>
                      )}
                    </View>
                    <View style={[styles.codeStatus, item.used && styles.codeUsed]}>
                      <Text style={[styles.codeStatusText, item.used && styles.codeUsedText]}>
                        {item.used ? i18n.t('used') : i18n.t('available')}
                      </Text>
                    </View>
                  </View>
                ))
              )}
            </View>
          </View>
        </ScrollView>
      )}

      {activeTab === 'employees' && (
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{i18n.t('employees')}</Text>
              {/* <TouchableOpacity
                style={styles.fixStatusButton}
                onPress={handleFixEmployeeStatus}
              >
                <Ionicons name="refresh" size={16} color="#4F46E5" />
                <Text style={styles.fixStatusButtonText}>Napraw status</Text>
              </TouchableOpacity> */}
            </View>

            <View style={styles.employeesList}>
              {employees.map((employee) => (
                <View key={employee.id} style={styles.employeeItem}>
                  <View style={styles.employeeInfo}>
                    <Text style={styles.employeeName}>{employee.full_name}</Text>
                    <View style={styles.employeeBadges}>
                      <View style={[styles.roleBadge, { backgroundColor: getRoleColor(employee.role) }]}>
                        <Text style={[styles.roleText, { color: '#fff' }]}>
                          {ROLES[employee.role] || ROLES.employee}
                        </Text>
                      </View>
                      <View style={[
                        styles.statusBadge,
                        { backgroundColor: employee.subscription_status === 'ACTIVE' ? '#DEF7EC' : '#FDE8E8' }
                      ]}>
                        <Text style={[
                          styles.statusText,
                          { color: employee.subscription_status === 'ACTIVE' ? '#03543F' : '#9B1C1C' }
                        ]}>
                          {employee.subscription_status === 'ACTIVE' ? 'Aktywny' : 'Nieaktywny'}
                        </Text>
                      </View>
                    </View>
                    {employee.last_status_change && (
                      <Text style={styles.lastChangeText}>
                        Ostatnia zmiana: {new Date(employee.last_status_change).toLocaleDateString('pl-PL')}
                      </Text>
                    )}
                  </View>
                  <View style={styles.employeeActions}>
                    <TouchableOpacity
                      style={[
                        styles.statusButton,
                        employee.subscription_status === 'ACTIVE' ? styles.deactivateButton : styles.activateButton,
                        updating === employee.id && styles.buttonDisabled
                      ]}
                      onPress={() => toggleEmployeeStatus(employee.id, employee.subscription_status)}
                      disabled={updating === employee.id}
                    >
                      {updating === employee.id ? (
                        <Text style={styles.statusButtonText}>...</Text>
                      ) : (
                        <Text style={styles.statusButtonText}>
                          {employee.subscription_status === 'ACTIVE' ? 'Dezaktywuj' : 'Aktywuj'}
                        </Text>
                      )}
                    </TouchableOpacity>
                    <View style={styles.iconButtonsContainer}>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleEditEmployee(employee)}
                      >
                        <Ionicons name="pencil" size={18} color="#4F46E5" />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleDeleteEmployee(employee.id)}
                      >
                        <Ionicons name="trash" size={18} color="#DC2626" />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      )}



      {/* Modal do edycji pracownika */}
      <Modal
        visible={showEditModal}
        transparent={false}
        animationType="slide"
        onRequestClose={() => setShowEditModal(false)}
      >
        <View style={styles.editModalContainer}>
          {/* TopBar */}
          <TopBar
            onMenuPress={onMenuPress}
            onLogoPress={() => {}}
            isLargeScreen={isLargeScreen}
          />

          {/* Header */}
          <View style={styles.editModalHeader}>
            <TouchableOpacity
              style={styles.editModalBackButton}
              onPress={() => setShowEditModal(false)}
            >
              <Ionicons name="arrow-back" size={22} color="#1F2937" />
            </TouchableOpacity>
            <Text style={styles.editModalHeaderTitle}>{i18n.t('editEmployee')}</Text>
            <View style={styles.editModalPlaceholder} />
          </View>

          {/* Content */}
          <View style={styles.editModalContent}>
            <Text style={styles.editModalInputLabel}>{i18n.t('fullName')}</Text>
            <View style={styles.editModalInputDisabled}>
              <Text style={styles.editModalInputDisabledText}>{selectedEmployee?.full_name}</Text>
            </View>
            <Text style={styles.editModalInputNote}>
              {i18n.t('nameCannotBeChanged') || 'Imię i nazwisko nie może być zmieniane'}
            </Text>

            <Text style={styles.editModalInputLabel}>{i18n.t('role')}</Text>
            <View style={styles.editModalRoleButtons}>
              {(Object.keys(ROLES) as UserRole[]).map((role) => (
                <TouchableOpacity
                  key={role}
                  style={[
                    styles.editModalRoleButton,
                    editedRole === role && styles.editModalRoleButtonSelected
                  ]}
                  onPress={() => setEditedRole(role)}
                >
                  <Text style={[
                    styles.editModalRoleButtonText,
                    editedRole === role && styles.editModalRoleButtonTextSelected
                  ]}>
                    {ROLES[role]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.editModalButtons}>
              <TouchableOpacity
                style={[styles.editModalButton, styles.editModalButtonCancel]}
                onPress={() => setShowEditModal(false)}
              >
                <Text style={styles.editModalButtonText}>{i18n.t('cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.editModalButton, styles.editModalButtonPrimary]}
                onPress={handleSaveEdit}
              >
                <Text style={[styles.editModalButtonText, styles.editModalButtonTextPrimary]}>
                  {i18n.t('save')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Alert dla limitów (tylko web) */}
      {showAlert && Platform.OS === 'web' && (
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{i18n.t('limitReached')}</Text>
            <Text style={styles.modalText}>{i18n.t('verificationCodeLimitMessage')}</Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonCancel]}
                onPress={() => setShowAlert(false)}
              >
                <Text style={styles.modalButtonText}>{i18n.t('understand')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={() => {
                  setShowAlert(false);
                  setShowSubscriptionModal(true);
                }}
              >
                <Text style={[styles.modalButtonText, styles.modalButtonTextPrimary]}>
                  {i18n.t('buyPremium')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Dolne zakładki */}
      <View style={styles.bottomTabContainer}>
        <TouchableOpacity
          style={[styles.bottomTab, activeTab === 'codes' && styles.activeBottomTab]}
          onPress={() => setActiveTab('codes')}
        >
          <Ionicons
            name="key-outline"
            size={24}
            color={activeTab === 'codes' ? '#2563EB' : '#6B7280'}
          />
          <Text style={[styles.bottomTabText, activeTab === 'codes' && styles.activeBottomTabText]}>
            {i18n.t('verificationCodes')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.bottomTab, activeTab === 'employees' && styles.activeBottomTab]}
          onPress={() => setActiveTab('employees')}
        >
          <Ionicons
            name="people-outline"
            size={24}
            color={activeTab === 'employees' ? '#2563EB' : '#6B7280'}
          />
          <Text style={[styles.bottomTabText, activeTab === 'employees' && styles.activeBottomTabText]}>
            {i18n.t('employees')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Platform.OS === 'ios' ? 100 : 80, // Miejsce na dolne zakładki
  },
  premiumCard: {
    margin: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  premiumContent: {
    padding: 20,
  },
  premiumHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  premiumTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 12,
  },
  premiumDescription: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
    marginBottom: 20,
  },
  manageSubscriptionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  manageSubscriptionText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  fixStatusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  fixStatusButtonText: {
    fontSize: 12,
    color: '#4F46E5',
    fontWeight: '500',
  },
  codesCounter: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  codesCounterText: {
    color: '#4B5563',
    fontSize: 14,
    fontWeight: '500',
  },
  generateButton: {
    backgroundColor: '#4F46E5',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  generateButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  generateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  codesList: {
    gap: 8,
  },
  codeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  codeMainInfo: {
    flex: 1,
  },
  codeText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
  },
  employeeNameText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  noCodesText: {
    textAlign: 'center',
    color: '#6B7280',
    fontSize: 16,
    padding: 20,
  },
  codeStatus: {
    backgroundColor: '#DEF7EC',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  codeStatusText: {
    color: '#03543F',
    fontSize: 14,
    fontWeight: '500',
  },
  codeUsed: {
    backgroundColor: '#FDE8E8',
  },
  codeUsedText: {
    color: '#9B1C1C',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    marginBottom: 12,
  },
  // New styles for edit modal
  editModalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  editModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  editModalBackButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editModalHeaderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  editModalPlaceholder: {
    width: 36,
  },
  editModalContent: {
    flex: 1,
    padding: 16,
  },
  modalText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 24,
    lineHeight: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
  },
  modalButtonCancel: {
    borderColor: '#E5E7EB',
    backgroundColor: 'white',
  },
  modalButtonPrimary: {
    borderColor: '#2563EB',
    backgroundColor: '#2563EB',
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  modalButtonTextPrimary: {
    color: 'white',
  },
  editInput: {
    backgroundColor: '#F5F6FA',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    marginBottom: 24,
  },
  // New edit modal input styles
  editModalInputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    marginTop: 16,
  },
  editModalInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    marginBottom: 24,
    color: '#1F2937',
  },
  editModalInputDisabled: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
  },
  editModalInputDisabledText: {
    fontSize: 16,
    color: '#6B7280',
  },
  editModalInputNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginBottom: 24,
    fontStyle: 'italic',
  },
  editModalRoleButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 32,
  },
  editModalRoleButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  editModalRoleButtonSelected: {
    backgroundColor: '#2563EB',
    borderColor: '#2563EB',
  },
  editModalRoleButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  editModalRoleButtonTextSelected: {
    color: '#FFFFFF',
  },
  editModalButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 'auto',
    paddingTop: 24,
  },
  editModalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  editModalButtonCancel: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  editModalButtonPrimary: {
    backgroundColor: '#2563EB',
  },
  editModalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  editModalButtonTextPrimary: {
    color: '#FFFFFF',
  },
  employeeBadges: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 4,
  },
  roleBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  roleButtons: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 24,
  },
  roleButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: 'center',
  },
  roleButtonSelected: {
    backgroundColor: '#2563EB',
    borderColor: '#2563EB',
  },
  roleButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  roleButtonTextSelected: {
    color: 'white',
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 16,
    width: '100%',
    alignSelf: 'center',
    maxWidth: 600,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    paddingHorizontal: 20,
  },
  menuItemText: {
    marginLeft: 15,
    fontSize: 16,
    color: '#666',
  },
  employeesList: {
    gap: 12,
  },
  employeeItem: {
    flexDirection: 'column',
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1A1A1A',
    marginBottom: 4,
  },
  employeeEmail: {
    fontSize: 14,
    color: '#666',
  },
  employeeActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  iconButtonsContainer: {
    flexDirection: 'row',
    gap: 6,
  },
  actionButton: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 90,
  },
  activateButton: {
    backgroundColor: '#059669',
  },
  deactivateButton: {
    backgroundColor: '#DC2626',
  },
  statusButtonText: {
    color: '#fff',
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 16,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  lastChangeText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },


  bottomTabContainer: {
    flexDirection: 'row',
    height: 60,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: 'white',
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  bottomTab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeBottomTab: {
    borderTopWidth: 2,
    borderTopColor: '#2563EB',
  },
  bottomTabText: {
    fontSize: 10,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
    paddingHorizontal: 2,
  },
  activeBottomTabText: {
    color: '#2563EB',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
});

export default AdminPanel; 