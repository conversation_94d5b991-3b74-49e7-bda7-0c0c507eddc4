-- Kod SQL do utworzenia bucketu product-images w Supabase Storage
-- Wykonaj ten kod w SQL Editor w konsoli Supabase

-- Ze względu na to, że tworzenie bucketu jest zwykle wykonywane przez API Supabase,
-- użyjemy funkcji wewnętrznych jeśli są dostępne

DO $$
DECLARE
  bucket_exists BOOLEAN;
BEGIN
  -- Sprawdź czy bucket już istnieje
  BEGIN
    SELECT EXISTS (
      SELECT 1 FROM storage.buckets WHERE name = 'product-images'
    ) INTO bucket_exists;
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'Tabela storage.buckets nie istnieje. Prawdopodobnie potrzebujesz użyć API Storage zamiast SQL.';
      bucket_exists := FALSE;
  END;
  
  -- Próbujemy utworzyć bucket jeśli nie istnieje i mamy dostęp do tabeli buckets
  IF bucket_exists = FALSE THEN
    BEGIN
      INSERT INTO storage.buckets (id, name, public)
      VALUES ('product-images', 'product-images', TRUE);
      
      RAISE NOTICE 'Bucket product-images został pomyślnie utworzony.';
    EXCEPTION 
      WHEN undefined_table THEN
        RAISE NOTICE 'Nie można utworzyć bucketu przez SQL. Użyj Dashboard Supabase lub API Storage.';
      WHEN others THEN
        RAISE NOTICE 'Błąd podczas tworzenia bucketu: %', SQLERRM;
    END;
  ELSE
    RAISE NOTICE 'Bucket product-images już istnieje.';
  END IF;
END $$;

-- Instrukcje jak utworzyć bucket przez interfejs Supabase:
-- 1. Przejdź do Dashboard Supabase
-- 2. Wybierz swój projekt
-- 3. Kliknij na zakładkę "Storage" w menu po lewej stronie
-- 4. Kliknij przycisk "Create new bucket"
-- 5. Wprowadź nazwę "product-images"
-- 6. Zaznacz opcję "Public bucket" jeśli zdjęcia mają być publicznie dostępne
-- 7. Kliknij "Create bucket"
-- 
-- Aby ustawić polityki dostępu:
-- 1. Na stronie Storage, wybierz utworzony bucket
-- 2. Przejdź do zakładki "Policies"
-- 3. Dodaj polityki dla Insert (upload), Select (download), Update i Delete 