// Funkcja webhook dla Stripe
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";
import Stripe from "https://esm.sh/stripe@12.5.0?target=deno";

// Funkcja do generowania UUID v4
function uuidv4() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// Najpierw dodajmy interfejs dla historii płatności na początku pliku
interface PaymentHistoryRecord {
  company_id: string;
  subscription_id: string;
  amount: number;
  currency: string;
  status: string;
  stripe_invoice_id: string;
  stripe_payment_intent_id: string | null;
  payment_date: string;
  billing_period_start: string;
  billing_period_end: string;
  description?: string;
  created_at: string;
}

// Na początku pliku, przed const corsHeaders
interface WebhookData {
  lastProcessedSubscriptionId: string | null;
  lastProcessedInvoiceId: string | null;
  processingQueue: Set<string>;
}

const webhookData: WebhookData = {
  lastProcessedSubscriptionId: null,
  lastProcessedInvoiceId: null,
  processingQueue: new Set()
};

// Nagłówki CORS
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};

// Helper function to convert Unix timestamp to ISO string
function unixToISOString(unixTimestamp: number | null): string | null {
  if (!unixTimestamp) return null;
  try {
    // Sprawdź czy timestamp jest w sekundach czy milisekundach
    const timestamp = unixTimestamp.toString().length > 10 ? 
      unixTimestamp : // już w milisekundach
      unixTimestamp * 1000; // konwersja z sekund na milisekundy
    
    const date = new Date(timestamp);
    
    // Sprawdź czy data jest prawidłowa
    if (isNaN(date.getTime())) {
      console.error('Invalid date from timestamp:', unixTimestamp);
      return null;
    }
    
    // Sprawdź czy data nie jest zbyt daleko w przyszłości (ponad 100 lat)
    const maxFutureDate = new Date().getTime() + (100 * 365 * 24 * 60 * 60 * 1000);
    if (date.getTime() > maxFutureDate) {
      console.error('Date too far in future:', unixTimestamp);
      return null;
    }
    
    return date.toISOString();
  } catch (error) {
    console.error('Error converting timestamp:', error, 'Timestamp:', unixTimestamp);
    return null;
  }
}

// Helper function to cancel old subscriptions
async function cancelOldSubscriptions(companyId: string, newSubscriptionId: string) {
  console.log('Looking for old subscriptions to cancel for company:', companyId);
  
  try {
    // Znajdź wszystkie aktywne subskrypcje dla tej firmy
    const { data: activeSubscriptions, error: queryError } = await supabase
      .from('company_subscriptions')
      .select('stripe_subscription_id, status')
      .eq('company_id', companyId)
      .in('status', ['active', 'trialing', 'past_due'])
      .neq('stripe_subscription_id', newSubscriptionId);

    if (queryError) {
      console.error('Error querying old subscriptions:', queryError);
      return;
    }

    console.log('Found active subscriptions:', activeSubscriptions);

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      for (const oldSub of activeSubscriptions) {
        try {
          console.log('Attempting to cancel subscription:', oldSub.stripe_subscription_id);
          
          // Pobierz szczegóły subskrypcji ze Stripe
          const stripeSubscription = await stripe.subscriptions.retrieve(oldSub.stripe_subscription_id);
          
          if (stripeSubscription.status !== 'canceled') {
            // Anuluj subskrypcję w Stripe natychmiast
            await stripe.subscriptions.cancel(oldSub.stripe_subscription_id, {
              invoice_now: false,
              prorate: true
            });
          }

          // Zaktualizuj status w bazie danych
          const { error: updateError } = await supabase
            .from('company_subscriptions')
            .update({
              status: 'canceled',
              cancel_at_period_end: false,
              canceled_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('stripe_subscription_id', oldSub.stripe_subscription_id);

          if (updateError) {
            console.error('Error updating subscription status in database:', updateError);
          } else {
            console.log('Successfully canceled old subscription:', oldSub.stripe_subscription_id);
          }
        } catch (cancelError) {
          console.error('Error canceling old subscription:', cancelError, oldSub.stripe_subscription_id);
        }
      }
    } else {
      console.log('No active subscriptions found to cancel');
    }
  } catch (error) {
    console.error('Error in cancelOldSubscriptions:', error);
  }
}

// Inicjalizacja Stripe
const stripe = Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient()
});

const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

let lastProcessedSubscriptionId: string | null = null;

serve(async (req) => {
  console.log('=== SMOOTH-HANDLER REQUEST RECEIVED ===');
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Headers:', Object.fromEntries(req.headers.entries()));

  // Obsługa zapytań OPTIONS (preflight)
  if (req.method === "OPTIONS") {
    console.log('Handling OPTIONS request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Sprawdź metodę HTTP
    if (req.method !== 'POST') {
      console.error('Invalid HTTP method:', req.method);
      return new Response(
        JSON.stringify({ error: 'Only POST method is allowed' }),
        {
          status: 405,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Pobierz dane z żądania
    const contentType = req.headers.get('content-type');
    console.log('Content-Type:', contentType);
    if (!contentType?.includes('application/json')) {
      console.error('Invalid content type:', contentType);
      return new Response(
        JSON.stringify({ error: 'Content-Type must be application/json' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const body = await req.text();
    console.log('Request body:', body);
    let event;

    try {
      event = JSON.parse(body);
      console.log('=== PARSED EVENT ===');
      console.log('Event type:', event.type);
      console.log('Event data:', JSON.stringify(event, null, 2));
    } catch (parseError) {
      console.error('JSON parse error:', parseError);
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Obsługa żądania tworzenia sesji checkout z frontendu
    if (event.type === 'create_checkout_session') {
      if (!event.companyId || !event.planId || !event.successUrl || !event.cancelUrl) {
        console.error('Missing required parameters for checkout session');
        return new Response(
          JSON.stringify({ error: 'Missing required parameters: companyId, planId, successUrl, cancelUrl' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      try {
        console.log('Creating checkout session for:', {
          companyId: event.companyId,
          planId: event.planId
        });

        // Pobierz dane planu
        const { data: plan, error: planError } = await supabase
          .from('subscription_plans')
          .select('*')
          .eq('id', event.planId)
          .single();

        if (planError || !plan) {
          console.error('Plan not found:', planError);
          return new Response(
            JSON.stringify({ error: 'Plan not found' }),
            {
              status: 404,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          );
        }

        if (!plan.stripe_price_id) {
          console.error('Plan has no Stripe Price ID:', plan.name);
          return new Response(
            JSON.stringify({ error: 'Plan has no Stripe Price ID configured' }),
            {
              status: 400,
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            }
          );
        }

        // Pobierz lub utwórz klienta Stripe
        let customerId: string;

        // Sprawdź czy firma już ma klienta Stripe
        const { data: existingSubscription } = await supabase
          .from('company_subscriptions')
          .select('stripe_customer_id')
          .eq('company_id', event.companyId)
          .not('stripe_customer_id', 'is', null)
          .limit(1)
          .single();

        if (existingSubscription?.stripe_customer_id) {
          customerId = existingSubscription.stripe_customer_id;
          console.log('Using existing customer:', customerId);
        } else {
          // Utwórz nowego klienta
          const customer = await stripe.customers.create({
            metadata: {
              company_id: event.companyId
            }
          });
          customerId = customer.id;
          console.log('Created new customer:', customerId);
        }

        // Utwórz sesję checkout
        const session = await stripe.checkout.sessions.create({
          customer: customerId,
          client_reference_id: event.companyId,
          payment_method_types: ['card'],
          line_items: [
            {
              price: plan.stripe_price_id,
              quantity: 1,
            },
          ],
          mode: 'subscription',
          success_url: event.successUrl,
          cancel_url: event.cancelUrl,
          metadata: {
            company_id: event.companyId,
            plan_id: event.planId
          },
          subscription_data: {
            metadata: {
              company_id: event.companyId,
              plan_id: event.planId
            }
          }
        });

        console.log('Checkout session created:', session.id);

        return new Response(
          JSON.stringify({
            success: true,
            url: session.url,
            sessionId: session.id
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      } catch (stripeError: any) {
        console.error('Stripe error creating checkout session:', stripeError);
        return new Response(
          JSON.stringify({
            error: 'Failed to create checkout session',
            details: stripeError.message
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
    }

    // Obsługa żądania anulowania subskrypcji z frontendu
    if (event.type === 'cancel_subscription') {
      if (!event.subscription_id) {
        console.error('Missing subscription_id in cancel request');
        return new Response(
          JSON.stringify({ error: 'Missing subscription_id' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      try {
        console.log('Attempting to cancel subscription:', event.subscription_id);
        
        // Anuluj subskrypcję w Stripe
        const subscription = await stripe.subscriptions.update(
          event.subscription_id,
          {
            cancel_at_period_end: event.cancel_at_period_end ?? true
          }
        );
        
        console.log('Subscription updated:', subscription.id);
        
        // Aktualizuj status w bazie danych
        const updateData: {
          cancel_at_period_end: boolean;
          updated_at: string;
          canceled_at?: string;
          cancel_at?: string;
        } = {
          cancel_at_period_end: subscription.cancel_at_period_end,
          updated_at: new Date().toISOString()
        };

        if (subscription.canceled_at) {
          updateData.canceled_at = unixToISOString(subscription.canceled_at);
        }
        if (subscription.cancel_at) {
          updateData.cancel_at = unixToISOString(subscription.cancel_at);
        }

        const { error: updateError } = await supabase
          .from('company_subscriptions')
          .update(updateData)
          .eq('stripe_subscription_id', subscription.id);

        if (updateError) {
          console.error('Error updating subscription in database:', updateError);
        }
        
        return new Response(
          JSON.stringify({ 
            success: true, 
            subscription: subscription 
          }),
          {
            status: 200,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      } catch (stripeError: any) {
        console.error('Stripe error:', stripeError);
        return new Response(
          JSON.stringify({ 
            error: 'Failed to cancel subscription',
            details: stripeError.message 
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }
    }

    // Log webhook event for Stripe events
    if (event.type && (
      event.type.startsWith('customer.subscription.') || 
      event.type.startsWith('checkout.session.') ||
      event.type === 'invoice.payment_succeeded'
    )) {
      try {
        await supabase.from('webhook_logs').insert({
          event_type: event.type,
          event_id: event.id,
          company_id: event.data.object.client_reference_id,
          data: event.data.object
        });
      } catch (logError) {
        console.error('Failed to log webhook event:', logError);
      }
    }

    // Handle different webhook event types
    switch (event.type) {
      case 'invoice.created': {
        const invoice = event.data.object;
        console.log('Processing invoice.created webhook:', {
          invoiceId: invoice.id,
          customerId: invoice.customer,
          amount: invoice.total,
          status: invoice.status
        });

        try {
          // Pobierz company_id z subskrypcji
          if (invoice.subscription) {
            const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
            const companyId = subscription.metadata?.company_id;

            if (companyId) {
              // Dodaj company_id do metadanych faktury
              await stripe.invoices.update(invoice.id, {
                metadata: {
                  company_id: companyId
                }
              });
              console.log('Added company_id to invoice metadata:', companyId);
            }
          }
        } catch (error) {
          console.error('Error updating invoice metadata:', error);
        }
        break;
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object;
        
        console.log('Processing invoice.payment_succeeded webhook - full invoice:', JSON.stringify(invoice, null, 2));

        try {
          // Pobierz company_id z metadanych faktury
          let companyId = invoice.metadata?.company_id;
          console.log('Company ID from invoice metadata:', companyId);

          // Jeśli nie ma w fakturze, spróbuj pobrać z subskrypcji
          if (!companyId && invoice.subscription) {
            console.log('Trying to get company_id from subscription:', invoice.subscription);
            const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
            console.log('Retrieved subscription:', JSON.stringify(subscription, null, 2));
            companyId = subscription.metadata?.company_id;
            console.log('Company ID from subscription metadata:', companyId);
          }

          // Jeśli nadal nie ma, spróbuj pobrać z klienta
          if (!companyId && invoice.customer) {
            console.log('Trying to get company_id from customer:', invoice.customer);
            const customer = await stripe.customers.retrieve(invoice.customer);
            console.log('Retrieved customer:', JSON.stringify(customer, null, 2));
            companyId = customer.metadata?.company_id;
            console.log('Company ID from customer metadata:', companyId);
          }

          // Jeśli nadal nie ma, spróbuj znaleźć przez stripe_customer_id w bazie danych
          if (!companyId && invoice.customer) {
            console.log('Trying to find company_id via database lookup using customer_id:', invoice.customer);
            const { data: subscriptions, error: subError } = await supabase
              .from('company_subscriptions')
              .select('company_id')
              .eq('stripe_customer_id', invoice.customer)
              .limit(1);

            if (!subError && subscriptions && subscriptions.length > 0) {
              companyId = subscriptions[0].company_id;
              console.log('Found company_id via database lookup:', companyId);
            } else {
              console.log('Database lookup failed:', subError);
            }
          }

          // Jeśli nadal nie ma, spróbuj znaleźć przez stripe_subscription_id w bazie danych
          if (!companyId && invoice.subscription) {
            console.log('Trying to find company_id via database lookup using subscription_id:', invoice.subscription);
            const { data: subscriptions, error: subError } = await supabase
              .from('company_subscriptions')
              .select('company_id')
              .eq('stripe_subscription_id', invoice.subscription)
              .limit(1);

            if (!subError && subscriptions && subscriptions.length > 0) {
              companyId = subscriptions[0].company_id;
              console.log('Found company_id via subscription database lookup:', companyId);
            } else {
              console.log('Subscription database lookup failed:', subError);
            }
          }

          if (!companyId) {
            console.error('No company_id found in metadata or database');
            throw new Error('No company_id found in metadata or database');
          }

          // Znajdź firmę po ID z metadanych
          console.log('Looking for company with ID:', companyId);
          const { data: companies, error: companiesError } = await supabase
            .from('companies')
            .select('*')
            .eq('id', companyId);

          if (companiesError) {
            console.error('Error fetching companies:', companiesError);
            throw companiesError;
          }

          console.log('Found companies:', companies);

          let matchingCompany = companies?.[0];

          if (!matchingCompany) {
            console.error('Company not found');
            throw new Error('Company not found');
          }

          console.log('Found existing company:', matchingCompany);

          // Pobierz dane o subskrypcji
          const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
          console.log('Subscription details:', JSON.stringify(subscription, null, 2));

          // Przygotuj daty
          const now = new Date().toISOString();
          const paymentDate = invoice.status_transitions?.paid_at ? 
            new Date(invoice.status_transitions.paid_at * 1000).toISOString() : 
            now;

          const periodStart = subscription.current_period_start ? 
            new Date(subscription.current_period_start * 1000).toISOString() : 
            now;
          
          const periodEnd = subscription.current_period_end ? 
            new Date(subscription.current_period_end * 1000).toISOString() : 
            new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

          // Przygotuj dane historii płatności
          const paymentHistory = {
            company_id: companyId,
            subscription_id: invoice.subscription,
            amount: invoice.amount_paid || invoice.total,
            currency: 'usd',
            status: 'succeeded',
            stripe_invoice_id: invoice.id,
            stripe_payment_intent_id: invoice.payment_intent,
            payment_date: paymentDate,
            billing_period_start: periodStart,
            billing_period_end: periodEnd,
            description: `Payment for subscription - ${invoice.lines.data[0]?.description || 'Standard subscription'}`,
            created_at: now
          };

          console.log('Attempting to create payment history:', paymentHistory);

          const { data: insertedPayment, error: insertError } = await supabase
            .from('payment_history')
            .insert([paymentHistory])
            .select();

          if (insertError) {
            console.error('Error inserting payment history:', insertError);
            throw insertError;
          }

          console.log('Successfully created payment history:', insertedPayment);

          // Zaktualizuj lub utwórz rekord subskrypcji
          const subscriptionData = {
            company_id: companyId,
            stripe_subscription_id: invoice.subscription,
            status: subscription.status,
            current_period_start: periodStart,
            current_period_end: periodEnd,
            updated_at: now
          };

          console.log('Updating subscription with data:', subscriptionData);

          const { error: upsertError } = await supabase
            .from('company_subscriptions')
            .upsert([subscriptionData]);

          if (upsertError) {
            console.error('Error upserting subscription:', upsertError);
            throw upsertError;
          }

          console.log('Successfully processed invoice:', invoice.id);
        } catch (error) {
          console.error('Error processing invoice:', error);
          throw error;
        }
        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object;
        console.log('Processing invoice.payment_failed webhook:', {
          invoiceId: invoice.id,
          customerId: invoice.customer,
          amount: invoice.total,
          status: invoice.status
        });

        const subscriptionId = invoice.subscription;
        if (!subscriptionId) {
          console.log('No subscription associated with this invoice');
          break;
        }

        // Znajdź company_id na podstawie subscription_id
        const { data: subscription, error: subError } = await supabase
          .from('company_subscriptions')
          .select('company_id')
          .eq('stripe_subscription_id', subscriptionId)
                  .single();
                  
        if (subError || !subscription) {
          console.error('Could not find subscription:', subError);
          break;
        }

        // Zapisz nieudaną próbę płatności
        const { error: paymentError } = await supabase
          .from('payment_history')
          .insert({
            company_id: subscription.company_id,
            subscription_id: subscriptionId,
            stripe_payment_intent_id: invoice.payment_intent,
            stripe_invoice_id: invoice.id,
            amount: invoice.total,
            currency: invoice.currency,
            status: 'failed',
            description: `Failed payment for ${invoice.lines.data[0]?.description || 'subscription'}`,
            created_at: new Date().toISOString()
          });

        if (paymentError) {
          console.error('Failed to create payment record:', paymentError);
        }

        // Aktualizuj status subskrypcji
        const { error: updateError } = await supabase
          .from('company_subscriptions')
          .update({
            status: 'past_due',
            updated_at: new Date().toISOString()
          })
          .eq('stripe_subscription_id', subscriptionId);

        if (updateError) {
          console.error('Failed to update subscription status:', updateError);
        }
        break;
      }

      case 'checkout.session.completed': {
        const session = event.data.object;
        const companyId = session.client_reference_id;

        console.log('Processing checkout.session.completed webhook');
        console.log('Session details:', {
          id: session.id,
          companyId: companyId,
          customerId: session.customer,
          subscriptionId: session.subscription,
          paymentStatus: session.payment_status,
          mode: session.mode
        });

        try {
          // Retrieve the session with line items
          const expandedSession = await stripe.checkout.sessions.retrieve(session.id, {
            expand: ['line_items', 'subscription']
          });

          console.log('Expanded session details:', {
            lineItems: expandedSession.line_items?.data,
            firstItemPriceId: expandedSession.line_items?.data[0]?.price?.id,
            subscription: expandedSession.subscription
          });

          if (!companyId) {
            throw new Error('No company ID found in session');
          }

          const priceId = expandedSession.line_items?.data[0]?.price?.id;
          if (!priceId) {
            throw new Error('No price ID found in expanded session');
          }

          // Get subscription details from Stripe
          let subscriptionDetails;
          if (session.subscription) {
            const subscription = await stripe.subscriptions.retrieve(session.subscription);
            console.log('Retrieved subscription details from Stripe:', subscription);
            subscriptionDetails = {
              period: {
                start: subscription.current_period_start,
                end: subscription.current_period_end
              },
              status: subscription.status
            };

            // Anuluj wszystkie aktywne subskrypcje dla tej firmy
            const { data: activeSubscriptions } = await supabase
              .from('company_subscriptions')
              .select('stripe_subscription_id')
              .eq('company_id', companyId)
              .in('status', ['active', 'trialing'])
              .neq('stripe_subscription_id', session.subscription);

            if (activeSubscriptions && activeSubscriptions.length > 0) {
              console.log('Found old subscriptions to cancel:', activeSubscriptions);
              
              for (const oldSub of activeSubscriptions) {
                try {
                  // Anuluj subskrypcję w Stripe natychmiast
                  await stripe.subscriptions.cancel(oldSub.stripe_subscription_id, {
                    invoice_now: false,
                    prorate: true
                  });

                  // Zaktualizuj status w bazie danych
                  await supabase
                    .from('company_subscriptions')
                    .update({
                      status: 'canceled',
                      cancel_at_period_end: false,
                      canceled_at: new Date().toISOString(),
                      updated_at: new Date().toISOString()
                    })
                    .eq('stripe_subscription_id', oldSub.stripe_subscription_id);

                  console.log('Successfully canceled old subscription:', oldSub.stripe_subscription_id);
                } catch (cancelError) {
                  console.error('Error canceling old subscription:', cancelError);
                }
              }
            }
          }

          // Get plan details from subscription_plans table
          const { data: plan, error: planError } = await supabase
            .from('subscription_plans')
            .select('*')
            .eq('stripe_price_id', priceId)
                    .single();
                    
          if (planError || !plan) {
            console.error('Plan not found for price ID:', priceId);
            throw new Error('Plan not found for price ID: ' + priceId);
          }

          console.log('Found plan:', plan);

          // Create subscription record
          const { error: subscriptionError } = await supabase
            .from('company_subscriptions')
            .insert({
              company_id: companyId,
              plan_id: plan.id,
              stripe_subscription_id: session.subscription,
              stripe_customer_id: session.customer,
              status: subscriptionDetails?.status || 'incomplete',
              current_period_start: subscriptionDetails?.period?.start ? 
                new Date(subscriptionDetails.period.start * 1000).toISOString() : 
                new Date().toISOString(),
              current_period_end: subscriptionDetails?.period?.end ? 
                new Date(subscriptionDetails.period.end * 1000).toISOString() : 
                new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
              cancel_at_period_end: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
                    
                  if (subscriptionError) {
            console.error('Failed to create subscription record:', subscriptionError);
            throw new Error('Failed to create subscription record: ' + subscriptionError.message);
          }

          console.log('Created subscription record');

          // Update company account type using RPC function
          const { error: rpcError } = await supabase.rpc('update_company_account_type_rpc', {
            p_company_id: companyId,
            p_plan_name: plan.name.toLowerCase()
          });

          if (rpcError) {
            console.error('Failed to update company via RPC:', rpcError);
            throw new Error('Failed to update company via RPC: ' + rpcError.message);
          }

          console.log('Updated company account type via RPC');

          // Reaktywuj pracowników przy nowej subskrypcji
          try {
            // Najpierw sprawdź czy są pracownicy do reaktywacji
            const { data: expiredEmployees, error: checkError } = await supabase
              .from('employees')
              .select('id, full_name, subscription_status')
              .eq('company_id', companyId)
              .eq('subscription_status', 'SUBSCRIPTION_EXPIRED');

            if (checkError) {
              console.error('Error checking expired employees:', checkError);
            } else if (expiredEmployees && expiredEmployees.length > 0) {
              console.log(`Found ${expiredEmployees.length} expired employees to reactivate:`,
                expiredEmployees.map(e => e.full_name));

              // Reaktywuj pracowników z uwzględnieniem limitów planu
              const { data: reactivateResult, error: reactivateError } = await supabase.rpc(
                'reactivate_company_employees',
                { p_company_id: companyId }
              );

              if (reactivateError) {
                console.error('Failed to reactivate employees:', reactivateError);
              } else {
                console.log('Employee reactivation result:', reactivateResult);
              }
            } else {
              console.log('No expired employees found to reactivate');
            }
          } catch (reactivateErr) {
            console.error('Error in employee reactivation:', reactivateErr);
          }

          // Create payment history record if payment was successful
          if (session.payment_status === 'paid') {
            try {
                      const { error: paymentError } = await supabase
                        .from('payment_history')
                        .insert({
                          company_id: companyId,
                  subscription_id: session.subscription,
                  stripe_payment_intent_id: session.payment_intent,
                  stripe_invoice_id: session.invoice,
                  amount: session.amount_total,
                  currency: 'usd',
                          status: 'succeeded',
                  description: `Subscription ${plan.name}`,
                  payment_date: new Date().toISOString(),
                  billing_period_start: subscriptionDetails?.period?.start ? 
                    new Date(subscriptionDetails.period.start * 1000).toISOString() : 
                    new Date().toISOString(),
                  billing_period_end: subscriptionDetails?.period?.end ? 
                    new Date(subscriptionDetails.period.end * 1000).toISOString() : 
                    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                        });
                        
                      if (paymentError) {
                console.error('Failed to create payment record:', paymentError);
                      } else {
                console.log('Created payment history record');
              }
            } catch (paymentErr) {
              console.error('Error in payment history creation:', paymentErr);
            }
          }

          // Po utworzeniu nowej subskrypcji
          if (session.subscription && companyId) {
            await cancelOldSubscriptions(companyId, session.subscription);
          }
        } catch (error) {
          console.error('Error in checkout.session.completed:', error);
          throw error;
        }
        break;
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        const subscriptionId = subscription.id;

        // Sprawdź czy ten webhook nie był już przetwarzany
        if (webhookData.processingQueue.has(subscriptionId)) {
          console.log('Subscription update already being processed:', subscriptionId);
          break;
        }

        console.log('Processing customer.subscription.updated webhook:', {
          id: subscriptionId,
          status: subscription.status,
          currentPeriodEnd: subscription.current_period_end,
          items: subscription.items?.data
        });

        try {
          webhookData.processingQueue.add(subscriptionId);

          // Pobierz dane o firmie
          const { data: subscriptionData, error: subscriptionError } = await supabase
            .from('company_subscriptions')
            .select('company_id, stripe_customer_id')
            .eq('stripe_subscription_id', subscriptionId)
            .single();

          if (subscriptionError || !subscriptionData) {
            console.error('Nie można znaleźć danych subskrypcji:', subscriptionError);
            break;
          }

          const companyId = subscriptionData.company_id;

          // Przygotuj daty (opcjonalne dla wygasłych subskrypcji)
          const periodStart = unixToISOString(subscription.current_period_start);
          const periodEnd = unixToISOString(subscription.current_period_end);

          // Przygotuj dane do aktualizacji
          const updateData: any = {
            status: subscription.status,
            cancel_at_period_end: subscription.cancel_at_period_end,
            updated_at: new Date().toISOString()
          };

          // Dodaj daty tylko jeśli są prawidłowe
          if (periodStart) updateData.current_period_start = periodStart;
          if (periodEnd) updateData.current_period_end = periodEnd;

          // Aktualizuj status subskrypcji
          const { error: updateError } = await supabase
            .from('company_subscriptions')
            .update(updateData)
            .eq('stripe_subscription_id', subscriptionId);

          if (updateError) {
            console.error('Error updating subscription:', updateError);
            break;
          }

          // Sprawdź czy nastąpiła zmiana planu (porównaj stripe_price_id)
          const currentPriceId = subscription.items?.data?.[0]?.price?.id;
          if (currentPriceId) {
            // Pobierz aktualny stripe_price_id z bazy
            const { data: currentSubscription, error: currentSubError } = await supabase
              .from('company_subscriptions')
              .select('stripe_price_id')
              .eq('stripe_subscription_id', subscriptionId)
              .single();

            if (!currentSubError && currentSubscription) {
              const oldPriceId = currentSubscription.stripe_price_id;

              if (oldPriceId && oldPriceId !== currentPriceId) {
                console.log('Plan change detected:', {
                  old: oldPriceId,
                  new: currentPriceId,
                  subscriptionId
                });

                // Aktualizuj stripe_price_id w bazie
                const { error: priceUpdateError } = await supabase
                  .from('company_subscriptions')
                  .update({
                    stripe_price_id: currentPriceId,
                    updated_at: new Date().toISOString()
                  })
                  .eq('stripe_subscription_id', subscriptionId);

                if (priceUpdateError) {
                  console.error('Failed to update stripe_price_id:', priceUpdateError);
                } else {
                  console.log('Successfully updated stripe_price_id');
                }

                // Znajdź nowy plan na podstawie stripe_price_id
                const { data: newPlan, error: planError } = await supabase
                  .from('subscription_plans')
                  .select('name')
                  .eq('stripe_price_id', currentPriceId)
                  .single();

                if (!planError && newPlan) {
                  // Aktualizuj typ konta firmy
                  const { error: accountUpdateError } = await supabase.rpc(
                    'update_company_account_type_rpc',
                    {
                      p_company_id: companyId,
                      p_plan_name: newPlan.name.toLowerCase()
                    }
                  );

                  if (accountUpdateError) {
                    console.error('Failed to update company account type:', accountUpdateError);
                  } else {
                    console.log('Successfully updated company account type to:', newPlan.name);
                  }
                }
              }
            }
          }

          // Jeśli subskrypcja jest aktywna, zawsze sprawdź czy trzeba reaktywować pracowników
          if (subscription.status === 'active') {
            console.log('Active subscription detected - checking if employees need reactivation');
            console.log('Subscription details:', {
              status: subscription.status,
              cancel_at_period_end: subscription.cancel_at_period_end,
              id: subscription.id
            });

            // Sprawdź czy są pracownicy do reaktywacji
            const { data: expiredEmployees, error: employeeCheckError } = await supabase
              .from('employees')
              .select('id, full_name, subscription_status')
              .eq('company_id', companyId);

            console.log('All employees found:', expiredEmployees);
            console.log('Employee check error:', employeeCheckError);

            const toReactivate = expiredEmployees?.filter(e => e.subscription_status === 'SUBSCRIPTION_EXPIRED');
            console.log('Employees to reactivate:', toReactivate);

            if (!employeeCheckError && toReactivate && toReactivate.length > 0) {
              console.log(`Found ${toReactivate.length} expired employees to reactivate:`,
                toReactivate.map(e => e.full_name));

              // Reaktywuj pracowników z uwzględnieniem limitów planu
              const { data: reactivateResult, error: reactivateError } = await supabase.rpc(
                'reactivate_company_employees',
                { p_company_id: companyId }
              );

              console.log('Reactivation result:', reactivateResult);
              console.log('Reactivation error:', reactivateError);

              if (reactivateError) {
                console.error('Failed to reactivate employees:', reactivateError);
              } else {
                console.log('Successfully reactivated employees:', reactivateResult);
              }
            } else {
              console.log('No expired employees found to reactivate');
            }
          }

          // Sprawdź czy to zmiana planu czy rzeczywiste anulowanie
          if (subscription.cancel_at_period_end) {
            console.log('Subscription marked for cancellation at period end');

            // Sprawdź czy firma ma inne aktywne subskrypcje (może to być zmiana planu)
            const { data: otherSubscriptions, error: otherSubsError } = await supabase
              .from('company_subscriptions')
              .select('id, stripe_subscription_id, status, created_at')
              .eq('company_id', companyId)
              .eq('status', 'active')
              .neq('stripe_subscription_id', subscriptionId)
              .order('created_at', { ascending: false });

            if (otherSubsError) {
              console.error('Error checking other subscriptions:', otherSubsError);
            }

            // Jeśli są inne aktywne subskrypcje, to prawdopodobnie zmiana planu - NIE dezaktywuj
            if (otherSubscriptions && otherSubscriptions.length > 0) {
              console.log('Found other active subscriptions - likely plan change, NOT deactivating employees:',
                otherSubscriptions.map(s => s.stripe_subscription_id));

              // Przy zmianie planu, upewnij się że pracownicy są aktywni
              console.log('Plan change detected - ensuring employees are active');
              const { data: expiredEmployees, error: checkError } = await supabase
                .from('employees')
                .select('id, full_name, subscription_status')
                .eq('company_id', companyId)
                .eq('subscription_status', 'SUBSCRIPTION_EXPIRED');

              if (!checkError && expiredEmployees && expiredEmployees.length > 0) {
                console.log(`Reactivating ${expiredEmployees.length} employees after plan change:`,
                  expiredEmployees.map((e: any) => e.full_name));

                // Użyj funkcji RPC z limitami
                const { data: reactivateResult, error: reactivateError } = await supabase.rpc(
                  'reactivate_company_employees',
                  { p_company_id: companyId }
                );

                if (reactivateError) {
                  console.error('Failed to reactivate employees during plan change:', reactivateError);
                } else {
                  console.log('Successfully reactivated employees during plan change:', reactivateResult);
                }
              }
            } else {
              // Jeśli nie ma innych aktywnych subskrypcji, dezaktywuj pracowników
              console.log('No other active subscriptions found - deactivating employees');

              const { error: deactivateError } = await supabase.rpc(
                'webhook_deactivate_company_employees',
                { p_company_id: companyId }
              );

              if (deactivateError) {
                console.error('Failed to deactivate employees:', deactivateError);
              } else {
                console.log('Successfully deactivated all company employees');
              }
            }
          }

          webhookData.processingQueue.delete(subscriptionId);
        } catch (error) {
          console.error('Error processing subscription update:', error);
          webhookData.processingQueue.delete(subscriptionId);
        }

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        const subscriptionId = subscription.id;

        console.log('Processing customer.subscription.deleted webhook:', {
          id: subscriptionId,
          status: subscription.status,
          canceledAt: subscription.canceled_at
        });

        try {
          // Znajdź subskrypcję w bazie danych
          const { data: subscriptionData, error: subscriptionError } = await supabase
            .from('company_subscriptions')
            .select('company_id, id')
            .eq('stripe_subscription_id', subscriptionId)
            .single();

          if (subscriptionError || !subscriptionData) {
            console.error('Nie można znaleźć subskrypcji:', subscriptionError);
            break;
          }

          const now = new Date().toISOString();

          // Aktualizuj status subskrypcji na canceled
          const { error: updateError } = await supabase
            .from('company_subscriptions')
            .update({
              status: 'canceled',
              canceled_at: now,
              updated_at: now
            })
            .eq('stripe_subscription_id', subscriptionId);

          if (updateError) {
            console.error('Error updating subscription status:', updateError);
            break;
          }

          // Dezaktywuj wszystkich pracowników firmy (bez ustawiania dat aby umożliwić ręczną aktywację)
          const { error: deactivateError } = await supabase.rpc(
            'webhook_deactivate_company_employees',
            { p_company_id: subscriptionData.company_id }
          );

          if (deactivateError) {
            console.error('Failed to deactivate employees:', deactivateError);
            // Nie przerywamy procesu, kontynuujemy z pozostałymi operacjami
          } else {
            console.log('Successfully deactivated all company employees');
          }

          // Sprawdź czy firma ma inne aktywne subskrypcje
          const { data: otherSubscriptions, error: otherSubsError } = await supabase
            .from('company_subscriptions')
            .select('id')
            .eq('company_id', subscriptionData.company_id)
            .eq('status', 'active')
            .neq('id', subscriptionData.id);

          if (otherSubsError) {
            console.error('Error checking other subscriptions:', otherSubsError);
          }

          // Jeśli nie ma innych aktywnych subskrypcji, przywróć plan darmowy
          if (!otherSubscriptions || otherSubscriptions.length === 0) {
            const { error: rpcError } = await supabase.rpc(
              'update_company_account_type_rpc',
              {
                p_company_id: subscriptionData.company_id,
                p_plan_name: 'free'
              }
            );

            if (rpcError) {
              console.error('Failed to update company account type:', rpcError);
            } else {
              console.log('Successfully reset company to free plan');
            }
          } else {
            // Jeśli są inne aktywne subskrypcje, reaktywuj pracowników
            console.log('Found other active subscriptions - reactivating employees');
            const { data: expiredEmployees, error: checkError } = await supabase
              .from('employees')
              .select('id, full_name, subscription_status')
              .eq('company_id', subscriptionData.company_id)
              .eq('subscription_status', 'SUBSCRIPTION_EXPIRED');

            if (!checkError && expiredEmployees && expiredEmployees.length > 0) {
              console.log(`Reactivating ${expiredEmployees.length} employees due to other active subscriptions:`,
                expiredEmployees.map((e: any) => e.full_name));

              // Użyj funkcji RPC z limitami
              const { data: reactivateResult, error: reactivateError } = await supabase.rpc(
                'reactivate_company_employees',
                { p_company_id: subscriptionData.company_id }
              );

              if (reactivateError) {
                console.error('Failed to reactivate employees:', reactivateError);
              } else {
                console.log('Successfully reactivated employees:', reactivateResult);
              }
            }
          }

          // Dodaj wpis o całkowitym anulowaniu subskrypcji
          try {
            await supabase
              .from('subscription_events')
              .insert({
                company_id: subscriptionData.company_id,
                subscription_id: subscriptionData.id,
                event_type: 'subscription_canceled',
                event_data: {
                  canceled_at: now,
                  reason: subscription.cancellation_details?.reason || 'unknown'
                }
              });
            console.log('Added cancellation event to history');
          } catch (eventError) {
            console.error('Failed to log cancellation event:', eventError);
          }
        } catch (error) {
          console.error('Error processing subscription deletion:', error);
        }

        break;
      }

      default: {
        console.log(`Unhandled event type: ${event.type}`);
      }
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200
    });
  } catch (error: any) {
    console.error('Error processing webhook:', error);
    return new Response(JSON.stringify({ error: error?.message || 'Unknown error' }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 400
    });
  }
});