import React, { useState, useEffect, ReactElement } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Platform,
  Dimensions,
  TextInput,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { i18n } from '../utils/localization';

// Add a separate component for date display to ensure proper wrapping
const DateDisplay = ({ dateString }: { dateString: string }) => {
  if (!dateString) return <Text>-</Text>;
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      // Invalid date
      return <Text>-</Text>;
    }
    
    // Format as dd.mm.rr
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = String(date.getFullYear()).slice(-2); // Get only last 2 digits of year
    
    return <Text style={{ fontSize: 14, color: '#1F2937' }}>{day}.{month}.{year}</Text>;
  } catch (error) {
    console.error('Error formatting date:', error);
    return <Text>-</Text>;
  }
};

// DEBUG: Check for any unwrapped text with periods that could be causing the error

interface Task {
  id: string;
  date: string;
  client_name: string;
  address: string;
  work_scope: string;
  start_time: string;
  started_at?: string;
  completed_at?: string;
  status: 'pending' | 'in_progress' | 'completed';
  assigned_employees: string[];
  active_employees_count?: number;
}

interface AllCompanyTasksProps {
  companyId: string;
  userId: string;
  onTaskSelect?: (taskId: string) => void;
  onMenuPress?: () => void;
  isLargeScreen?: boolean;
}

const AllCompanyTasks = ({ companyId, userId, onTaskSelect, onMenuPress, isLargeScreen = false }: AllCompanyTasksProps) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sortField, setSortField] = useState<'date' | 'client_name' | 'status'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'in_progress' | 'completed'>('all');
  const [filterDateFrom, setFilterDateFrom] = useState('');
  const [filterDateTo, setFilterDateTo] = useState('');
  const [showDateFilter, setShowDateFilter] = useState(false);
  const windowWidth = Dimensions.get('window').width;

  const fetchTasks = async () => {
    try {
      setLoading(true);
      
      // Fetch all company tasks
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('company_id', companyId);
      
      // Apply status filter if not 'all'
      if (filterStatus !== 'all') {
        query = query.eq('status', filterStatus);
      }

      // Get all tasks and filter dates on the client side instead of using gte/lte
      const { data, error } = await query.order(sortField, { ascending: sortOrder === 'asc' });
      
      if (error) {
        console.error('Error fetching tasks:', error);
        return;
      }
      
      if (data) {
        // Filter dates on client side
        let filteredData = data;
        
        if (filterDateFrom) {
          filteredData = filteredData.filter(task => 
            task.date && task.date >= filterDateFrom
          );
        }
        
        if (filterDateTo) {
          filteredData = filteredData.filter(task => 
            task.date && task.date <= filterDateTo
          );
        }
        
        setTasks(filteredData as Task[]);
      }
    } catch (error) {
      console.error('Error in fetchTasks:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (companyId) {
      fetchTasks();
    }
  }, [companyId, sortField, sortOrder, filterStatus, filterDateFrom, filterDateTo]);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTasks();
  };

  const handleSort = (field: 'date' | 'client_name' | 'status') => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  // This function now returns the DateDisplay component
  const formatDate = (dateString: string): ReactElement => {
    return <DateDisplay dateString={dateString} />;
  };

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'pending':
        return { color: '#B45309', backgroundColor: '#FEF3C7' };
      case 'in_progress':
        return { color: '#1E40AF', backgroundColor: '#DBEAFE' };
      case 'completed':
        return { color: '#065F46', backgroundColor: '#D1FAE5' };
      default:
        return { color: '#374151', backgroundColor: '#F3F4F6' };
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return i18n.t('statusPending');
      case 'in_progress':
        return i18n.t('statusInProgress');
      case 'completed':
        return i18n.t('statusCompleted');
      default:
        return status;
    }
  };

  const handleFilterChange = (status: 'all' | 'pending' | 'in_progress' | 'completed') => {
    setFilterStatus(status);
  };

  const clearDateFilters = () => {
    setFilterDateFrom('');
    setFilterDateTo('');
    setShowDateFilter(false);
  };

  const applyDateFilters = () => {
    setShowDateFilter(false);
  };

  const filteredTasks = tasks.filter(task => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return (
      task.client_name?.toLowerCase().includes(query) ||
      task.address?.toLowerCase().includes(query) ||
      task.work_scope?.toLowerCase().includes(query)
    );
  });

  // Ensure all text is properly wrapped in Text components
  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchWrapper}>
          <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder={i18n.t('searchTasks')}
            placeholderTextColor="#9CA3AF"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity style={styles.clearButton} onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#6B7280" />
            </TouchableOpacity>
          )}
          <TouchableOpacity 
            style={styles.dateButton}
            onPress={() => setShowDateFilter(true)}
          >
            <Ionicons name="calendar-outline" size={18} color="#4B5563" />
            <Text style={styles.dateButtonText}>{i18n.t('date')}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <View style={styles.filterTabsWrapper}>
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === 'all' && styles.activeFilterTab
            ]}
            onPress={() => handleFilterChange('all')}
          >
            <Text
              style={[
                styles.filterTabText,
                filterStatus === 'all' && styles.activeFilterTabText
              ]}
            >
              {i18n.t('all')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === 'pending' && styles.activeFilterTab
            ]}
            onPress={() => handleFilterChange('pending')}
          >
            <Text
              style={[
                styles.filterTabText,
                filterStatus === 'pending' && styles.activeFilterTabText
              ]}
            >
              {i18n.t('statusPending')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === 'in_progress' && styles.activeFilterTab
            ]}
            onPress={() => handleFilterChange('in_progress')}
          >
            <Text
              style={[
                styles.filterTabText,
                filterStatus === 'in_progress' && styles.activeFilterTabText
              ]}
            >
              {i18n.t('statusInProgress')}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterTab,
              filterStatus === 'completed' && styles.activeFilterTab
            ]}
            onPress={() => handleFilterChange('completed')}
          >
            <Text
              style={[
                styles.filterTabText,
                filterStatus === 'completed' && styles.activeFilterTabText
              ]}
            >
              {i18n.t('statusCompleted')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Date Filter Modal */}
      <Modal
        visible={showDateFilter}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDateFilter(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('selectDateRange')}</Text>
              <TouchableOpacity onPress={() => setShowDateFilter(false)}>
                <Ionicons name="close" size={24} color="#4B5563" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.dateInputContainer}>
              <Text style={styles.dateLabel}>{i18n.t('dateFrom')}</Text>
              <TextInput
                style={styles.dateInput}
                placeholder="YYYY-MM-DD"
                value={filterDateFrom}
                onChangeText={setFilterDateFrom}
              />
            </View>
            
            <View style={styles.dateInputContainer}>
              <Text style={styles.dateLabel}>{i18n.t('dateTo')}</Text>
              <TextInput
                style={styles.dateInput}
                placeholder="YYYY-MM-DD"
                value={filterDateTo}
                onChangeText={setFilterDateTo}
              />
            </View>
            
            <View style={styles.modalButtonsContainer}>
              <TouchableOpacity
                style={[styles.modalButton, styles.clearButton]}
                onPress={clearDateFilters}
              >
                <Text style={styles.clearButtonText}>{i18n.t('clear')}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.applyButton]}
                onPress={applyDateFilters}
              >
                <Text style={styles.applyButtonText}>{i18n.t('apply')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Task List */}
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading && !refreshing ? (
          <ActivityIndicator size="large" color="#2563EB" style={styles.loader} />
        ) : (
          <View>
            {/* Table Header */}
            <View style={styles.tableHeader}>
              <TouchableOpacity
                style={[styles.headerCell, { flex: 1 }]}
                onPress={() => handleSort('date')}
              >
                <Text style={styles.headerCellText}>{i18n.t('date')}</Text>
                {sortField === 'date' && (
                  <Ionicons
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'}
                    size={16}
                    color="#4B5563"
                  />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.headerCell, { flex: 2 }]}
                onPress={() => handleSort('client_name')}
              >
                <Text style={styles.headerCellText}>{i18n.t('client')}</Text>
                {sortField === 'client_name' && (
                  <Ionicons
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'}
                    size={16}
                    color="#4B5563"
                  />
                )}
              </TouchableOpacity>
              
              <View style={[styles.headerCell, { flex: 2 }]}>
                <Text style={styles.headerCellText}>{i18n.t('address')}</Text>
              </View>
              
              <TouchableOpacity
                style={[styles.headerCell, { flex: 1 }]}
                onPress={() => handleSort('status')}
              >
                <Text style={styles.headerCellText}>{i18n.t('status')}</Text>
                {sortField === 'status' && (
                  <Ionicons
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'}
                    size={16}
                    color="#4B5563"
                  />
                )}
              </TouchableOpacity>
            </View>

            {/* Table Rows */}
            {filteredTasks.length > 0 ? (
              filteredTasks.map((task) => (
                <TouchableOpacity
                  key={task.id}
                  style={styles.tableRow}
                  onPress={() => onTaskSelect && onTaskSelect(task.id)}
                >
                  <View style={[styles.cell, { flex: 1 }]}>
                    {formatDate(task.date)}
                  </View>
                  <View style={[styles.cell, { flex: 2 }]}>
                    <Text style={styles.cellText} numberOfLines={1}>
                      {task.client_name || '-'}
                    </Text>
                  </View>
                  <View style={[styles.cell, { flex: 2 }]}>
                    <Text style={styles.cellText} numberOfLines={1}>
                      {task.address || '-'}
                    </Text>
                  </View>
                  <View style={[styles.statusCell, { flex: 1 }]}>
                    <View
                      style={[
                        styles.statusBadge,
                        { backgroundColor: getStatusStyle(task.status).backgroundColor },
                      ]}
                    >
                      <Text
                        style={[
                          styles.statusText,
                          { color: getStatusStyle(task.status).color },
                        ]}
                      >
                        {getStatusLabel(task.status)}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.noTasksContainer}>
                <Ionicons name="document-text-outline" size={48} color="#9CA3AF" />
                <Text style={styles.noTasksText}>{i18n.t('noTasksFound')}</Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    paddingLeft: 12,
    paddingRight: 5,
    height: 46,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    height: 46,
    padding: 0,
  },
  clearButton: {
    padding: 6,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 5,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  dateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginLeft: 4,
  },
  filterContainer: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    padding: 8,
  },
  filterTabsWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    flexWrap: 'wrap',
    rowGap: 8,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    marginBottom: 2,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  activeFilterTab: {
    backgroundColor: '#EBF5FF',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    textAlign: 'center',
  },
  activeFilterTabText: {
    color: '#2563EB',
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  loader: {
    marginTop: 60,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  headerCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerCellText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4B5563',
    marginRight: 4,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  cell: {
    justifyContent: 'center',
    paddingRight: 8,
  },
  cellText: {
    fontSize: 14,
    color: '#1F2937',
  },
  statusCell: {
    justifyContent: 'center',
  },
  statusBadge: {
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 8,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  noTasksContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  noTasksText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  dateInputContainer: {
    marginBottom: 16,
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 4,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    padding: 8,
    fontSize: 16,
    color: '#1F2937',
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginLeft: 8,
  },
  applyButton: {
    backgroundColor: '#2563EB',
  },
  applyButtonText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 14,
  },
  clearButtonText: {
    color: '#4B5563',
    fontWeight: '500',
    fontSize: 14,
  },
});

export default AllCompanyTasks; 