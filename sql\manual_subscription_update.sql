-- Ten skrypt należy wykonać w konsoli SQL Supabase
-- Najpierw pobieramy ostatnie zdarzenie webhook
WITH latest_webhook AS (
  SELECT 
    data->>'id' as event_id,
    data->'data'->'object'->>'client_reference_id' as company_id,
    data->'data'->'object'->>'subscription' as stripe_subscription_id,
    data->'data'->'object'->>'customer' as stripe_customer_id,
    data->'data'->'object'->>'created' as created_timestamp
  FROM webhook_logs
  WHERE event_type = 'checkout.session.completed'
  ORDER BY created_at DESC
  LIMIT 1
)
-- Wyświetlamy informacje o ostatnim zdarzeniu
SELECT * FROM latest_webhook;

-- Pobierz ID firmy z ostatniego zdarzenia (dostosuj ID jeśli potrzeba)
DO $$
DECLARE
  company_uuid UUID := '90c35057-24c0-432e-8d06-68ae46ce3979'; -- Zastąp to ID firmy z ostatniego zdarzenia
  plan_uuid UUID;
  subscription_uuid UUID;
  now_time TIMESTAMPTZ := now();
  period_end TIMESTAMPTZ := now() + INTERVAL '1 month';
  stripe_subscription_id TEXT := 'sub_1RX3S8PaKRxqYgSxNIOEY6ct'; -- Zastąp to ID subskrypcji z ostatniego zdarzenia
  stripe_customer_id TEXT := 'cus_SRxCnZm6y6lrRG'; -- Zastąp to ID klienta z ostatniego zdarzenia
BEGIN
  -- Pobierz plan Basic
  SELECT id INTO plan_uuid FROM subscription_plans WHERE name = 'Basic' LIMIT 1;
  
  IF plan_uuid IS NULL THEN
    -- Jeśli nie znaleziono planu Basic, użyj pierwszego dostępnego planu
    SELECT id INTO plan_uuid FROM subscription_plans LIMIT 1;
  END IF;
  
  -- Dodaj wpis do tabeli company_subscriptions
  INSERT INTO company_subscriptions (
    company_id, 
    plan_id, 
    stripe_subscription_id, 
    stripe_customer_id, 
    status, 
    current_period_start, 
    current_period_end, 
    cancel_at_period_end
  ) VALUES (
    company_uuid,
    plan_uuid,
    stripe_subscription_id,
    stripe_customer_id,
    'active',
    now_time,
    period_end,
    false
  )
  RETURNING id INTO subscription_uuid;
  
  -- Dodaj wpis do historii płatności
  INSERT INTO payment_history (
    company_id,
    subscription_id,
    amount,
    currency,
    status,
    description
  ) VALUES (
    company_uuid,
    subscription_uuid,
    (SELECT price FROM subscription_plans WHERE id = plan_uuid),
    'pln',
    'succeeded',
    'Subskrypcja ' || (SELECT name FROM subscription_plans WHERE id = plan_uuid)
  );
  
  -- Aktualizuj status firmy i limit kodów weryfikacyjnych
  UPDATE companies 
  SET 
    account_type = (SELECT name FROM subscription_plans WHERE id = plan_uuid),
    verification_code_limit = CASE
      WHEN (SELECT name FROM subscription_plans WHERE id = plan_uuid) LIKE '%Basic%' THEN 5
      WHEN (SELECT name FROM subscription_plans WHERE id = plan_uuid) LIKE '%Pro%' THEN 20
      WHEN (SELECT name FROM subscription_plans WHERE id = plan_uuid) LIKE '%Business%' THEN 999999
      ELSE 5
    END
  WHERE id = company_uuid;
  
  RAISE NOTICE 'Subskrypcja utworzona dla firmy % z planem %', 
    company_uuid, 
    (SELECT name FROM subscription_plans WHERE id = plan_uuid);
END $$; 