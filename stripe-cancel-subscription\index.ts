import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';
import { corsHeaders } from '../_shared/cors.ts';

// Konfiguracja Stripe
const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
const stripe = new Stripe(stripeApiKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient(),
});

// Funkcja do anulowania subskrypcji w Stripe
serve(async (req) => {
  // Obsługa CORS dla zapytań preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Sprawdź metodę HTTP
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Metoda nie jest dozwolona' }), {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Parsowanie danych z żądania
    const { companyId, atPeriodEnd = true } = await req.json();

    // Walidacja danych wejściowych
    if (!companyId) {
      return new Response(JSON.stringify({ error: 'Brakujący parametr companyId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Pobierz dane subskrypcji z Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Brakujące zmienne środowiskowe Supabase');
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${supabaseServiceKey}`,
      'apikey': supabaseServiceKey
    };
    
    // Pobierz aktywną subskrypcję firmy
    const subscriptionResponse = await fetch(
      `${supabaseUrl}/rest/v1/company_subscriptions?company_id=eq.${companyId}&status=eq.active&select=*`,
      { headers }
    );
    
    const subscriptions = await subscriptionResponse.json();
    
    if (!subscriptions || subscriptions.length === 0) {
      return new Response(JSON.stringify({ error: 'Nie znaleziono aktywnej subskrypcji' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    
    const subscription = subscriptions[0];
    
    // Anuluj subskrypcję w Stripe
    await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      cancel_at_period_end: atPeriodEnd,
    });
    
    // Aktualizuj status subskrypcji w bazie danych
    const updateResponse = await fetch(
      `${supabaseUrl}/rest/v1/company_subscriptions?id=eq.${subscription.id}`,
      {
        method: 'PATCH',
        headers,
        body: JSON.stringify({
          cancel_at_period_end: atPeriodEnd,
          updated_at: new Date().toISOString()
        })
      }
    );
    
    if (!updateResponse.ok) {
      throw new Error('Nie udało się zaktualizować statusu subskrypcji w bazie danych');
    }
    
    // Jeśli anulujemy natychmiast (nie na koniec okresu), zaktualizuj status firmy na 'free'
    if (!atPeriodEnd) {
      await fetch(
        `${supabaseUrl}/rest/v1/companies?id=eq.${companyId}`,
        {
          method: 'PATCH',
          headers,
          body: JSON.stringify({
            account_type: 'free',
            updated_at: new Date().toISOString()
          })
        }
      );
    }
    
    // Zwróć sukces
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Błąd anulowania subskrypcji:', error);
    return new Response(JSON.stringify({ error: `Błąd serwera: ${error.message}` }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}); 