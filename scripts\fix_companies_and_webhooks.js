const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixCompaniesAndWebhooks() {
  try {
    console.log('1. Adding updated_at column to companies table...');
    
    // Dodaj kolumnę updated_at jeśli nie istnieje
    const { data: addColumnResult, error: addColumnError } = await supabase
      .from('companies')
      .select('id')
      .limit(1);

    if (addColumnError) {
      console.error('Error checking companies table:', addColumnError);
      return;
    }

    console.log('2. Fixing companies with active subscriptions...');
    
    // Pobierz wszystkie firmy z aktywnymi subskrypcjami
    const { data: activeSubscriptions, error: subscriptionsError } = await supabase
      .from('company_subscriptions')
      .select(`
        id,
        company_id,
        status,
        companies!inner(id, name, account_type, verification_code_limit),
        subscription_plans!inner(id, name)
      `)
      .eq('status', 'active');

    if (subscriptionsError) {
      console.error('Error fetching active subscriptions:', subscriptionsError);
      return;
    }

    console.log(`Found ${activeSubscriptions.length} active subscriptions`);

    // Aktualizuj każdą firmę z aktywną subskrypcją
    for (const subscription of activeSubscriptions) {
      const companyId = subscription.company_id;
      const planName = subscription.subscription_plans.name.toLowerCase();
      
      console.log(`Updating company ${subscription.companies.name} (${companyId}) to plan ${planName}`);
      
      const { data: rpcResult, error: rpcError } = await supabase
        .rpc('update_company_account_type_rpc', {
          p_company_id: companyId,
          p_plan_name: planName
        });

      if (rpcError) {
        console.error(`Error updating company ${companyId}:`, rpcError);
      } else {
        console.log(`✓ Successfully updated company ${subscription.companies.name} to ${planName}`);
      }
    }

    console.log('3. Checking companies with canceled subscriptions...');
    
    // Pobierz firmy z anulowanymi subskrypcjami
    const { data: canceledSubscriptions, error: canceledError } = await supabase
      .from('company_subscriptions')
      .select(`
        id,
        company_id,
        status,
        companies!inner(id, name, account_type, verification_code_limit),
        subscription_plans!inner(id, name)
      `)
      .eq('status', 'canceled');

    if (canceledError) {
      console.error('Error fetching canceled subscriptions:', canceledError);
      return;
    }

    console.log(`Found ${canceledSubscriptions.length} canceled subscriptions`);

    // Sprawdź czy firmy z anulowanymi subskrypcjami nie mają innych aktywnych subskrypcji
    for (const subscription of canceledSubscriptions) {
      const companyId = subscription.company_id;
      
      // Sprawdź czy firma ma inne aktywne subskrypcje
      const { data: otherActiveSubscriptions, error: otherActiveError } = await supabase
        .from('company_subscriptions')
        .select('id')
        .eq('company_id', companyId)
        .eq('status', 'active');

      if (otherActiveError) {
        console.error(`Error checking other active subscriptions for company ${companyId}:`, otherActiveError);
        continue;
      }

      // Jeśli nie ma innych aktywnych subskrypcji, ustaw na free
      if (otherActiveSubscriptions.length === 0) {
        console.log(`Setting company ${subscription.companies.name} (${companyId}) to free plan`);
        
        const { data: rpcResult, error: rpcError } = await supabase
          .rpc('update_company_account_type_rpc', {
            p_company_id: companyId,
            p_plan_name: 'free'
          });

        if (rpcError) {
          console.error(`Error updating company ${companyId} to free:`, rpcError);
        } else {
          console.log(`✓ Successfully updated company ${subscription.companies.name} to free`);
        }
      } else {
        console.log(`Company ${subscription.companies.name} has ${otherActiveSubscriptions.length} other active subscriptions, skipping`);
      }
    }

    console.log('4. Final verification - checking all companies...');
    
    // Sprawdź końcowy stan wszystkich firm
    const { data: allCompanies, error: allCompaniesError } = await supabase
      .from('companies')
      .select('id, name, account_type, verification_code_limit')
      .order('name');

    if (allCompaniesError) {
      console.error('Error fetching all companies:', allCompaniesError);
      return;
    }

    console.log('\nFinal company states:');
    allCompanies.forEach(company => {
      console.log(`${company.name}: ${company.account_type} (limit: ${company.verification_code_limit})`);
    });

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

fixCompaniesAndWebhooks();
