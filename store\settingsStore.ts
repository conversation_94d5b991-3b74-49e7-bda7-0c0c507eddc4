import { setLocale as setI18nLocale, getLocale as getI18nLocale } from '../utils/localization';
import { useEffect, useState } from 'react';

// Stan globalny
let currentLocale = getI18nLocale();

// System subskrypcji dla reagowania na zmiany
type Subscriber = (locale: string) => void;
const subscribers: Subscriber[] = [];

// Funkcja do dodawania subskrybenta
export function subscribe(callback: Subscriber): () => void {
  subscribers.push(callback);
  
  // Zwr<PERSON>ć funkcję do anulowania subskrypcji
  return () => {
    const index = subscribers.indexOf(callback);
    if (index !== -1) {
      subscribers.splice(index, 1);
    }
  };
}

// Funkcja do zmiany języka
export function setLocale(newLocale: string): void {
  if (currentLocale !== newLocale) {
    setI18nLocale(newLocale);
    currentLocale = newLocale;
    console.log(`Locale changed to ${newLocale}`);
    
    // Powiadom wszystkich subskrybentów o zmianie
    subscribers.forEach(callback => callback(currentLocale));
  }
}

// Funkcja do pobierania aktualnego języka
export function getLocale(): string {
  return currentLocale;
}

// Hook do użycia w komponentach React
export function useSettingsStore() {
  const [locale, setLocaleState] = useState(currentLocale);
  
  useEffect(() => {
    // Subskrybuj zmiany
    const unsubscribe = subscribe((newLocale) => {
      setLocaleState(newLocale);
    });
    
    // Anuluj subskrypcję przy odmontowaniu komponentu
    return unsubscribe;
  }, []);
  
  return {
    locale,
    setLocale
  };
}

/**
 * Funkcje do zarządzania ustawieniami aplikacji
 */

/**
 * Pobiera aktualny język aplikacji
 */
export const getCurrentLocale = (): string => {
  return getI18nLocale();
}; 