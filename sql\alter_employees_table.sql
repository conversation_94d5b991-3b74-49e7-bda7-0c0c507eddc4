-- <PERSON><PERSON>ie kolumn do zarządzania statusem subskrypcji pracownika
ALTER TABLE employees
ADD COLUMN IF NOT EXISTS subscription_status TEXT NOT NULL DEFAULT 'ACTIVE' 
  CHECK (subscription_status IN ('ACTIVE', 'SUBSCRIPTION_EXPIRED')),
ADD COLUMN IF NOT EXISTS last_status_change TIMESTAMPTZ DEFAULT NOW();

-- Indeks dla szybszego wyszukiwania po statusie
CREATE INDEX IF NOT EXISTS idx_employees_subscription_status 
  ON employees(subscription_status);

-- <PERSON>gger do aktualizacji last_status_change przy zmianie statusu
CREATE OR REPLACE FUNCTION update_employee_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.subscription_status IS DISTINCT FROM NEW.subscription_status THEN
    NEW.last_status_change = NOW();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_employee_status_timestamp
  BEFORE UPDATE ON employees
  FOR EACH ROW
  EXECUTE FUNCTION update_employee_status_timestamp();

-- <PERSON><PERSON><PERSON> do sprawdzania czy można zmienić status (raz na tydzień)
CREATE OR REPLACE FUNCTION can_change_employee_status(employee_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  last_change TIMESTAMPTZ;
BEGIN
  SELECT last_status_change 
  INTO last_change 
  FROM employees 
  WHERE id = employee_id;
  
  RETURN (
    last_change IS NULL OR 
    (NOW() - last_change) > INTERVAL '7 days'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do aktualizacji statusów pracowników przy zmianie subskrypcji
CREATE OR REPLACE FUNCTION update_employees_on_subscription_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Dezaktywuj pracowników przy anulowaniu/wygaśnięciu subskrypcji
  IF NEW.status = 'canceled' OR NEW.status = 'unpaid' THEN
    UPDATE employees
    SET subscription_status = 'SUBSCRIPTION_EXPIRED',
        last_status_change = NOW()
    WHERE company_id = NEW.company_id;
  END IF;

  -- Reaktywuj pracowników przy aktywacji subskrypcji
  IF NEW.status = 'active' AND (OLD.status IS NULL OR OLD.status != 'active') THEN
    PERFORM reactivate_company_employees(NEW.company_id);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_employees_on_subscription_change
  AFTER INSERT OR UPDATE ON company_subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_employees_on_subscription_change();

-- Funkcja do aktywacji pracownika
CREATE OR REPLACE FUNCTION activate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  active_count INTEGER;
  subscription_limit INTEGER;
  can_change BOOLEAN;
BEGIN
  -- Sprawdź czy można zmienić status (ograniczenie czasowe)
  SELECT can_change_employee_status(p_employee_id) INTO can_change;
  IF NOT can_change THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Nie można zmienić statusu pracownika częściej niż raz na tydzień'
    );
  END IF;

  -- Pobierz limit z aktualnej subskrypcji
  SELECT 
    CASE
      WHEN sp.name LIKE 'Basic%' THEN 5
      WHEN sp.name LIKE 'Pro%' THEN 20
      WHEN sp.name LIKE 'Business%' THEN 999999
      ELSE 2
    END INTO subscription_limit
  FROM company_subscriptions cs
  JOIN subscription_plans sp ON cs.plan_id = sp.id
  WHERE cs.company_id = p_company_id
  AND cs.status = 'active'
  ORDER BY cs.created_at DESC
  LIMIT 1;

  -- Pobierz liczbę aktywnych pracowników
  SELECT COUNT(*) INTO active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Sprawdź czy nie przekroczono limitu
  IF active_count >= subscription_limit THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Przekroczono limit aktywnych pracowników w obecnym planie subskrypcji'
    );
  END IF;

  -- Aktywuj pracownika
  UPDATE employees
  SET subscription_status = 'ACTIVE',
      last_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Status pracownika został zaktualizowany'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do dezaktywacji pracownika
CREATE OR REPLACE FUNCTION deactivate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  can_change BOOLEAN;
BEGIN
  -- Sprawdź czy można zmienić status (ograniczenie czasowe)
  SELECT can_change_employee_status(p_employee_id) INTO can_change;
  IF NOT can_change THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Nie można zmienić statusu pracownika częściej niż raz na tydzień'
    );
  END IF;

  -- Dezaktywuj pracownika
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW()
  WHERE id = p_employee_id
  AND company_id = p_company_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Status pracownika został zaktualizowany'
  );
END;
$$ LANGUAGE plpgsql; 