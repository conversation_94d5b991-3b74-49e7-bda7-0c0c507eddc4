-- DEBUGOWANIE PROBLEMU REJESTRACJI

-- 1. Sprawd<PERSON> czy funkcja can_use_verification_code istnieje
SELECT routine_name, routine_definition 
FROM information_schema.routines 
WHERE routine_name = 'can_use_verification_code';

-- 2. Sprawdź dane firmy z kodem POGBJK
SELECT vc.*, c.name as company_name, c.verification_code_limit, c.account_type
FROM verification_codes vc
JOIN companies c ON vc.company_id = c.id
WHERE vc.code = 'POGBJK';

-- 3. Sprawdź aktywnych pracowników tej firmy
SELECT e.*, c.name as company_name
FROM employees e
JOIN companies c ON e.company_id = c.id
WHERE c.id = (SELECT company_id FROM verification_codes WHERE code = 'POGBJK')
AND e.subscription_status = 'ACTIVE';

-- 4. Sprawd<PERSON> subskrypcje tej firmy
SELECT cs.*, sp.name as plan_name
FROM company_subscriptions cs
JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE cs.company_id = (SELECT company_id FROM verification_codes WHERE code = 'POGBJK')
ORDER BY cs.created_at DESC;

-- 5. Przetestuj funkcję can_use_verification_code
SELECT can_use_verification_code(
  'POGBJK', 
  (SELECT company_id FROM verification_codes WHERE code = 'POGBJK')
) as can_use_result;

-- 6. Sprawdź czy są jakieś rekordy w employees z tym kodem
SELECT * FROM employees WHERE verification_code = 'POGBJK';

-- 7. Sprawdź ostatnie rekordy w employees
SELECT * FROM employees ORDER BY created_at DESC LIMIT 5;
