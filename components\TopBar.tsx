import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Platform, Modal, Pressable, ActivityIndicator, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';
import { updateUserSettings } from '../services/settingsService';
import { useSettingsStore } from '../store/settingsStore';

interface TopBarProps {
  onMenuPress?: () => void;
  onLogoPress?: () => void;
  isLargeScreen?: boolean;
}

const availableLanguages = Object.keys(i18n.translations).map(key => ({
  code: key,
  name: key === 'pl' ? i18n.t('languagePolish') : key === 'en' ? i18n.t('languageEnglish') : i18n.t('languageUkrainian')
}));

export const TopBar: React.FC<TopBarProps> = ({ onMenuPress, onLogoPress, isLargeScreen = false }) => {
  const [languageModalVisible, setLanguageModalVisible] = useState(false);
  const [isSavingLanguage, setIsSavingLanguage] = useState(false);
  const { locale: currentLocale, setLocale } = useSettingsStore();

  const handleLanguagePress = () => {
    setLanguageModalVisible(true);
  };

  const handleLanguageSelect = async (langCode: string) => {
    if (isSavingLanguage || currentLocale === langCode) {
      setLanguageModalVisible(false);
      return;
    }

    setIsSavingLanguage(true);
    setLanguageModalVisible(false);

    setLocale(langCode);
    
    const success = await updateUserSettings({ preferred_language: langCode });

    setIsSavingLanguage(false);

    if (!success) {
      Alert.alert(
        i18n.t('error'),
        i18n.t('errorSavingLanguage')
      );
    } else {
      console.log(`Language preference ${langCode} saved successfully.`);
    }
  };

  return (
    <View style={styles.topBarContainer}>
      {onLogoPress && (
      <TouchableOpacity onPress={onLogoPress}>
          <Text style={styles.logoText}>{i18n.t('workFlow')}</Text>
        </TouchableOpacity>
      )}
      {!onLogoPress && (
        <Text style={styles.logoText}>{i18n.t('workFlow')}</Text>
      )}
      
      <View style={styles.rightIcons}>
        {isSavingLanguage && <ActivityIndicator size="small" color="white" style={styles.loadingIndicator} />}

        <TouchableOpacity 
          onPress={handleLanguagePress} 
          style={styles.iconButton} 
          disabled={isSavingLanguage}
        >
          <Ionicons name="language" size={28} color="white" /> 
        </TouchableOpacity>

        {!isLargeScreen && onMenuPress && (
          <TouchableOpacity 
            onPress={onMenuPress} 
            style={[styles.iconButton, styles.menuButton]} 
            disabled={isSavingLanguage}
          >
            <Ionicons name="menu" size={28} color="white" />
          </TouchableOpacity>
        )}
      </View>

      <Modal
        animationType="fade"
        transparent={true}
        visible={languageModalVisible}
        onRequestClose={() => {
          if (!isSavingLanguage) setLanguageModalVisible(false);
        }}
      >
        <Pressable 
          style={styles.modalOverlay} 
          onPress={() => {if (!isSavingLanguage) setLanguageModalVisible(false)}}
        >
          <View style={styles.modalView}>
            <Text style={styles.modalTitle}>{i18n.t('selectLanguage')}</Text>
            {availableLanguages.map((lang) => (
              <TouchableOpacity 
                key={lang.code}
                style={styles.languageOption}
                onPress={() => handleLanguageSelect(lang.code)}
                disabled={isSavingLanguage}
              >
                <Text style={styles.languageText}>{lang.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  topBarContainer: {
    height: 40,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    backgroundColor: '#1E6EDF',
    zIndex: 10,
  },
  logoText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 5,
  },
  menuButton: {
    marginLeft: 10,
  },
  loadingIndicator: {
    marginRight: 10,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    marginBottom: 15,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
  },
  languageOption: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    width: '100%',
    alignItems: 'center',
  },
  languageText: {
    fontSize: 16,
  },
}); 