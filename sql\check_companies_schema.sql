-- Sprawd<PERSON> strukturę tabeli companies
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default, 
  character_maximum_length
FROM 
  information_schema.columns 
WHERE 
  table_name = 'companies' 
ORDER BY 
  ordinal_position;

-- Sprawdź czy kolumna updated_at istnieje
SELECT EXISTS (
  SELECT 1 
  FROM information_schema.columns 
  WHERE table_name = 'companies' 
  AND column_name = 'updated_at'
) AS updated_at_exists;

-- Sprawdź obecne dane w tabeli companies
SELECT id, name, account_type, verification_code_limit 
FROM companies 
LIMIT 5;

-- Sprawdź aktywne subskrypcje
SELECT 
  c.id, 
  c.name, 
  c.account_type, 
  c.verification_code_limit,
  cs.status as subscription_status,
  sp.name as plan_name
FROM companies c
LEFT JOIN company_subscriptions cs ON c.id = cs.company_id
LEFT JOIN subscription_plans sp ON cs.plan_id = sp.id
WHERE cs.status = 'active' OR cs.status = 'canceled'
ORDER BY c.name;
