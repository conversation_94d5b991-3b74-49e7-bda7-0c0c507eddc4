import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput, ActivityIndicator, Platform, Alert, Image, Modal, useWindowDimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { TopBar } from './TopBar';
import * as ImagePicker from 'expo-image-picker';
import DatePicker from './DatePicker';
import { i18n } from '../utils/localization';

interface MaintenanceManagerProps {
  companyId: string;
  activeTab: 'add' | 'list';
  userType: 'company' | 'employee' | 'coordinator';
  userId: string;
  onTabChange: (tab: 'add' | 'list') => void;
  onMenuPress?: () => void;
  onLogoPress?: () => void;
  onReportSelect?: (report: MaintenanceReport) => void;
  isLargeScreen?: boolean;
  onAccessError?: () => void;
}

interface MaintenanceForm {
  title: string;
  description: string;
  location: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  images: string[];
  reporter_name: string;
  contact_info: string;
}

interface MaintenanceReport {
  id: string;
  title: string;
  description: string;
  location: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'reported' | 'in_progress' | 'resolved' | 'canceled' | 'to_check' | 'pending';
  created_at: string;
  reported_by_id: string;
  reported_by_name: string;
  company_id: string;
  repaired_by?: string;
  repaired_by_name?: string;
  repair_start_time?: string;
  repair_end_time?: string;
  resolved_time?: string;
  resolved_by?: string;
  resolved_by_name?: string;
  canceled_time?: string;
  canceled_by?: string;
  canceled_by_name?: string;
  photos?: string[];
}

type SortField = 'date' | 'title' | 'location' | 'priority' | 'status';
type SortOrder = 'asc' | 'desc';

// Funkcje pomocnicze do filtrowania daty
const formatISODate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const isCurrentMonth = (dateFrom?: string, dateTo?: string): boolean => {
  if (!dateFrom || !dateTo) return false;
  
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
  
  const firstDayStr = formatISODate(firstDay);
  const lastDayStr = formatISODate(lastDay);
  
  return dateFrom === firstDayStr && dateTo === lastDayStr;
};

const isLastWeek = (dateFrom?: string, dateTo?: string): boolean => {
  if (!dateFrom || !dateTo) return false;
  
  const today = new Date();
  const lastWeek = new Date(today);
  lastWeek.setDate(today.getDate() - 7);
  
  const lastWeekStr = formatISODate(lastWeek);
  const todayStr = formatISODate(today);
  
  return dateFrom === lastWeekStr && dateTo === todayStr;
};

const isCustomDateRange = (dateFrom?: string, dateTo?: string): boolean => {
  if (!dateFrom || !dateTo) return false;
  return !isCurrentMonth(dateFrom, dateTo) && !isLastWeek(dateFrom, dateTo);
};

export const MaintenanceManager: React.FC<MaintenanceManagerProps> = ({
  companyId,
  activeTab,
  userType,
  userId,
  onTabChange,
  onMenuPress,
  onLogoPress,
  onReportSelect,
  isLargeScreen = false,
  onAccessError,
}) => {
  // Form state
  const [form, setForm] = useState<MaintenanceForm>({
    title: '',
    description: '',
    location: '',
    priority: 'medium',
    images: [],
    reporter_name: '',
    contact_info: '',
  });

  // List state
  const [reports, setReports] = useState<MaintenanceReport[]>([]);
  const [loading, setLoading] = useState(false);
  const [sortField, setSortField] = useState<SortField>('priority');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [filterTitle, setFilterTitle] = useState<string>('');
  const [filterLocation, setFilterLocation] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('active');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [filterButtonPosition, setFilterButtonPosition] = useState({ top: 60, left: 10, width: 300 });
  const filterButtonRef = useRef<View>(null);
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Add state for image uploading
  const [uploadingImages, setUploadingImages] = useState(false);

  // Add a state for the current user's name
  const [currentUserName, setCurrentUserName] = useState('');

  // Check if we're on web platform
  const isWeb = Platform.OS === 'web';

  useEffect(() => {
    fetchReports();
    fetchCurrentUser();
  }, [companyId]);

  const fetchReports = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('maintenance_reports')
        .select('*')
        .eq('company_id', companyId);

      if (error) {
        console.error('Error fetching maintenance reports:', error);
        
        // Check if this is an RLS error
        if (error.code === '42501' || error.message.includes('permission denied') || error.message.includes('row-level security policy')) {
          console.warn('Access denied to maintenance reports due to RLS policy');
          // Call the onAccessError callback if provided
          if (onAccessError) {
            onAccessError();
          }
        }
        
        setReports([]);
      }

      setReports(data || []);
    } catch (error) {
      console.error('Error in fetchReports:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const handleClearFilters = () => {
    setFilterTitle('');
    setFilterLocation('');
    setFilterPriority('all');
    setFilterDateFrom('');
    setFilterDateTo('');
    setShowFilters(false);
  };

  const getStatusLabel = (status: string): string => {
    switch (status) {
      case 'reported':
        return i18n.t('maintenanceStatusReported');
      case 'in_progress':
        return i18n.t('maintenanceStatusInProgress');
      case 'resolved':
        return i18n.t('maintenanceStatusResolved');
      case 'canceled':
        return i18n.t('maintenanceStatusCanceled');
      case 'to_check':
        return i18n.t('maintenanceStatusToCheck');
      case 'pending':
        return i18n.t('maintenanceStatusPending');
      default:
        return status;
    }
  };

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'reported':
        return styles.statusReported;
      case 'in_progress':
        return styles.statusInProgress;
      case 'to_check':
        return styles.statusToCheck;
      case 'pending':
        return styles.statusPending;
      case 'resolved':
        return styles.statusResolved;
      case 'canceled':
        return styles.statusCanceled;
      default:
        return {};
    }
  };

  const getPriorityLabel = (priority: string): string => {
    switch (priority) {
      case 'low':
        return i18n.t('priorityLow');
      case 'medium':
        return i18n.t('priorityMedium');
      case 'high':
        return i18n.t('priorityHigh');
      case 'critical':
        return i18n.t('priorityCritical');
      default:
        return priority;
    }
  };

  const getPriorityStyle = (priority: string) => {
    switch (priority) {
      case 'low':
        return styles.priorityLow;
      case 'medium':
        return styles.priorityMedium;
      case 'high':
        return styles.priorityHigh;
      case 'critical':
        return styles.priorityCritical;
      default:
        return {};
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = String(date.getFullYear()).slice(-2);
    return `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${year}`;
  };

  const handleMenuPress = () => {
    if (onMenuPress) {
      onMenuPress();
    } else {
      console.log('Menu pressed, but no handler provided');
    }
  };

  const handleLogoPress = () => {
    if (onLogoPress) {
      onLogoPress();
    } else {
      console.log('Logo pressed, but no handler provided');
    }
  };

  const renderTopBar = () => {
    return (
      <TopBar
        onMenuPress={onMenuPress ?? (() => {})}
        onLogoPress={onLogoPress ?? (() => {})}
        isLargeScreen={isLargeScreen}
      />
    );
  };

  const renderBottomTabs = () => {
    return (
      <View style={styles.bottomTabBar}>
        <TouchableOpacity 
          style={[styles.tabButton, activeTab === 'list' && styles.activeTabButton]} 
          onPress={() => onTabChange('list')}
        >
          <Ionicons 
            name="list-outline" 
            size={22} 
            color={activeTab === 'list' ? '#2563EB' : '#6B7280'} 
          />
          <Text style={[
            styles.tabText, 
            activeTab === 'list' && styles.activeTabText
          ]}>
            {i18n.t('maintenanceReportsList')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.tabButton, activeTab === 'add' && styles.activeTabButton]} 
          onPress={() => onTabChange('add')}
        >
          <Ionicons 
            name="add-circle-outline" 
            size={22} 
            color={activeTab === 'add' ? '#2563EB' : '#6B7280'} 
          />
          <Text style={[
            styles.tabText, 
            activeTab === 'add' && styles.activeTabText
          ]}>
            {i18n.t('maintenanceAddReport')}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderFilters = () => (
    <View style={styles.filtersContainer}>
      <View style={styles.searchBarContainer}>
        <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={i18n.t('maintenanceSearchPlaceholder')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#9CA3AF"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearSearchButton}>
            <Ionicons name="close-circle" size={20} color="#6B7280" />
      </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[
            styles.dateButton,
            (filterDateFrom || filterDateTo) && styles.dateButtonActive,
          ]}
          onPress={() => setShowFilters(true)}
        >
          <Ionicons
            name="calendar-outline"
            size={18}
            color={(filterDateFrom || filterDateTo) ? '#2563EB' : '#4B5563'}
          />
          <Text
            style={[
              styles.dateButtonText,
              (filterDateFrom || filterDateTo) && styles.dateButtonTextActive,
            ]}
          >
            {i18n.t('maintenanceDateFilter')}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.statusFilter}>
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'all' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('all')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'all' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceAllFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'reported' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('reported')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'reported' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceReportedFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'in_progress' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('in_progress')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'in_progress' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceInProgressFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'resolved' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('resolved')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'resolved' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceResolvedFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'to_check' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('to_check')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'to_check' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceToCheckFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'canceled' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('canceled')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'canceled' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceCanceledFilter')}</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'active' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('active')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'active' && styles.statusFilterTextActive
          ]}>{i18n.t('maintenanceActiveFilter')}</Text>
        </TouchableOpacity>
      </View>

      <Modal
        visible={showFilters}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowFilters(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('maintenanceSelectDateRange')}</Text>
              <TouchableOpacity onPress={() => setShowFilters(false)}>
                <Ionicons name="close" size={24} color="#4B5563" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.modalBody}>
              <DatePicker
                label={i18n.t('maintenanceFromDate')}
                date={filterDateFrom}
                onDateChange={(date: string) => setFilterDateFrom(date)}
                placeholder={i18n.t('selectStartDate')}
              />
              <View style={{ height: 16 }} />
              <DatePicker
                label={i18n.t('maintenanceToDate')}
                date={filterDateTo}
                onDateChange={(date: string) => setFilterDateTo(date)}
                placeholder={i18n.t('selectEndDate')}
              />
              
              <View style={styles.modalButtons}>
                <TouchableOpacity 
                  style={styles.modalCancelButton}
                  onPress={() => {
                    setFilterDateFrom('');
                    setFilterDateTo('');
                    setShowFilters(false);
                  }}
                >
                  <Text style={styles.modalCancelButtonText}>{i18n.t('maintenanceClear')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.modalConfirmButton}
                  onPress={() => setShowFilters(false)}
                >
                  <Text style={styles.modalConfirmButtonText}>{i18n.t('maintenanceApply')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );

  const getPriorityValue = (priority: string): number => {
    switch (priority) {
      case 'critical': return 3;
      case 'high': return 2;
      case 'medium': return 1;
      case 'low': return 0;
      default: return 1; // domyślnie średni
    };
  };

  const sortedAndFilteredReports = React.useMemo(() => {
    let filtered = [...reports];

    // Filtrowanie według statusu
    if (filterStatus === 'active') {
      // Filtruj, aby pokazać wszystkie OPRÓCZ anulowanych i rozwiązanych
      filtered = filtered.filter(report => 
        report.status !== 'resolved' && report.status !== 'canceled'
      );
    } else if (filterStatus !== 'all') {
      // Standardowe filtrowanie dla konkretnego statusu
      filtered = filtered.filter(report => report.status === filterStatus);
    }

    // Filtrowanie według priorytetu
    if (filterPriority !== 'all') {
      filtered = filtered.filter(report => report.priority === filterPriority);
    }

    // Filtrowanie według tytułu
    if (filterTitle.trim() !== '') {
      filtered = filtered.filter(report => 
        report.title && report.title.toLowerCase().includes(filterTitle.toLowerCase())
      );
    }

    // Filtrowanie według lokalizacji
    if (filterLocation.trim() !== '') {
      filtered = filtered.filter(report => 
        report.location && report.location.toLowerCase().includes(filterLocation.toLowerCase())
      );
    }

    // Filtrowanie według pola wyszukiwania
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(report => 
        (report.title && report.title.toLowerCase().includes(query)) ||
        (report.description && report.description.toLowerCase().includes(query)) ||
        (report.location && report.location.toLowerCase().includes(query)) ||
        (report.reported_by_name && report.reported_by_name.toLowerCase().includes(query))
      );
    }

    // Filtrowanie według daty od
    if (filterDateFrom) {
      filtered = filtered.filter(report => {
        const reportDate = new Date(report.created_at);
        const fromDate = new Date(filterDateFrom);
        return reportDate >= fromDate;
      });
    }

    // Filtrowanie według daty do
    if (filterDateTo) {
      filtered = filtered.filter(report => {
        const reportDate = new Date(report.created_at);
        const toDate = new Date(filterDateTo);
        toDate.setHours(23, 59, 59, 999); // Ustawienie na koniec dnia
        return reportDate <= toDate;
      });
    }

    // Sortowanie
    return filtered.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortField) {
        case 'date':
          valueA = new Date(a.created_at).getTime();
          valueB = new Date(b.created_at).getTime();
          break;
        case 'title':
          valueA = a.title?.toLowerCase() || '';
          valueB = b.title?.toLowerCase() || '';
          break;
        case 'location':
          valueA = a.location?.toLowerCase() || '';
          valueB = b.location?.toLowerCase() || '';
          break;
        case 'priority':
          // Konwersja priorytetu na wartość liczbową dla prawidłowego sortowania
          valueA = getPriorityValue(a.priority || 'medium');
          valueB = getPriorityValue(b.priority || 'medium');
          break;
        case 'status':
          valueA = a.status;
          valueB = b.status;
          break;
        default:
          valueA = new Date(a.created_at).getTime();
          valueB = new Date(b.created_at).getTime();
      }

      const result = typeof valueA === 'string' 
        ? valueA.localeCompare(valueB as string) 
        : (valueA as number) - (valueB as number);

      return sortOrder === 'asc' ? result : -result;
    });
  }, [reports, filterStatus, filterPriority, filterTitle, filterLocation, filterDateFrom, filterDateTo, searchQuery, sortField, sortOrder]);

  const renderReportsList = () => {
    return (
      <View style={[styles.container, { flex: 1 }]}>
        <ScrollView 
          style={[styles.content, { flex: 1 }]} 
          contentContainerStyle={{ width: '100%', padding: 0, flexGrow: 1 }}
        >
          {loading ? (
            <ActivityIndicator size="large" color="#2563EB" style={styles.loader} />
          ) : reports.length === 0 ? (
            <Text style={styles.noReports}>
              {i18n.t('maintenanceNoReports')}
            </Text>
          ) : (
            <View style={{ flex: 1 }}>
              {renderFilters()}

              <View style={[styles.tableContainer, { flex: 1, marginBottom: 60 }]}>
                <View style={styles.tableHeader}>
                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 0.8 }]}
                    onPress={() => handleSort('date')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('maintenanceDate')}</Text>
                      {sortField === 'date' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 2 }]}
                    onPress={() => handleSort('title')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('maintenanceTitle')}</Text>
                      {sortField === 'title' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 1.1 }]}
                    onPress={() => handleSort('priority')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('maintenancePriority')}</Text>
                      {sortField === 'priority' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity 
                    style={[styles.headerCell, { flex: 1.1 }]}
                    onPress={() => handleSort('status')}
                  >
                    <View style={styles.headerContent}>
                      <Text style={styles.headerText}>{i18n.t('maintenanceStatus')}</Text>
                      {sortField === 'status' && (
                        <Ionicons 
                          name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                          size={14} 
                          color="#4B5563" 
                          style={styles.sortIcon} 
                        />
                      )}
                    </View>
                  </TouchableOpacity>
                </View>
                
                <ScrollView style={[styles.tableBody, { flex: 1 }]}>
                  {sortedAndFilteredReports.map((report) => (
                    <TouchableOpacity 
                      key={report.id} 
                      style={styles.tableRow}
                      onPress={() => {
                        // Handle report selection
                        console.log('Selected report:', report.id);
                        if (onReportSelect) {
                          onReportSelect(report);
                        }
                      }}
                    >
                      <Text style={[styles.cell, { flex: 0.8 }]}>
                        {formatDate(report.created_at)}
                      </Text>
                      <Text style={[styles.cell, { flex: 2 }]} numberOfLines={1} ellipsizeMode="tail">
                        {report.title}
                      </Text>
                      <View style={[styles.statusContainer, { flex: 1.1 }]}>
                        <Text style={[styles.statusBadge, getPriorityStyle(report.priority)]}>
                          {getPriorityLabel(report.priority)}
                        </Text>
                      </View>
                      <View style={[styles.statusContainer, { flex: 1.1 }]}>
                        <Text style={[styles.statusBadge, getStatusStyle(report.status)]}>
                          {getStatusLabel(report.status)}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          )}
        </ScrollView>
      </View>
    );
  };

  const resetForm = () => {
    setForm({
      title: '',
      description: '',
      location: '',
      priority: 'medium',
      images: [],
      reporter_name: '',
      contact_info: '',
    });
  };

  // Add function to fetch current user
  const fetchCurrentUser = async () => {
    try {
      // Zamiast używać tabeli 'users', używamy API Supabase Auth
      const { data, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('Error fetching user from auth:', error);
        return;
      }
      
      if (data && data.user) {
        const userName = data.user.user_metadata?.full_name || data.user.email || 'Unknown User';
        setCurrentUserName(userName);
        
        // Auto-set the reporter name
        setForm(prev => ({
          ...prev,
          reporter_name: userName
        }));
      }
    } catch (error) {
      console.error('Error in fetchCurrentUser:', error);
    }
  };

  // Update the handleCreateReport function to handle photos
  const handleCreateReport = async () => {
    if (areRequiredFieldsEmpty()) {
      Alert.alert(i18n.t('error'), i18n.t('maintenanceFillRequiredFields'));
      return;
    }

    setLoading(true);
    try {
      // Create a minimal report object with only essential fields
      // Make sure to include the userId in the reported_by field
      const minimalReportData = {
        title: form.title,
        description: form.description,
        priority: form.priority,
        status: 'reported',
        company_id: companyId,
        reported_by: userId  // Explicitly add the userId to the reported_by field
      };
      
      console.log('Attempting to insert maintenance report with minimal data:', minimalReportData);
      
      let insertSucceeded = false;
      
      try {
        const { data, error } = await supabase
          .from('maintenance_reports')
          .insert([minimalReportData])
          .select();
        
        if (!error) {
          console.log('Successfully inserted to maintenance_reports:', data);
          insertSucceeded = true;
          
          // If the insert succeeded and we have photos, try to update the record with photos
          if (insertSucceeded && form.images.length > 0 && data && data.length > 0) {
            const reportId = data[0].id;
            
            // Try to update the record with photos
            try {
              const { error: updateError } = await supabase
                .from('maintenance_reports')
                .update({ photos: form.images })
                .eq('id', reportId);
                
              if (updateError) {
                console.error('Failed to update report with photos:', updateError);
                // This is not critical, so we still consider the insert successful
              } else {
                console.log('Successfully updated report with photos');
              }
            } catch (updateErr) {
              console.error('Exception updating report with photos:', updateErr);
            }
          }
        } else {
          console.error('Error inserting to maintenance_reports:', error);
          
          // Check if this is an RLS error
          if (error.code === '42501' || error.message.includes('permission denied') || error.message.includes('row-level security policy')) {
            console.warn('Access denied to maintenance reports due to RLS policy');
            // Call the onAccessError callback if provided
            if (onAccessError) {
              onAccessError();
            }
          }
          
          // Try with even more minimal data but still include the reported_by field
          const bareMinimum = {
            title: form.title,
            company_id: companyId,
            reported_by: userId  // Make sure to include the userId here too
          };
          
          console.log('Attempting bare minimum insert:', bareMinimum);
          
          const { data: minData, error: minError } = await supabase
            .from('maintenance_reports')
            .insert([bareMinimum])
            .select();
            
          if (!minError) {
            console.log('Bare minimum insert succeeded:', minData);
            insertSucceeded = true;
          } else {
            console.error('Even bare minimum insert failed:', minError);
          }
        }
      } catch (e) {
        console.error('Exception inserting to maintenance_reports:', e);
      }
      
      // If the primary insert failed, try the fallback
      if (!insertSucceeded) {
        console.log('Primary insert failed, trying fallback...');
        insertSucceeded = await attemptStoringInAlternativeTable();
      }
      
      if (insertSucceeded) {
        // Reset form and show success
        resetForm();
        
        Alert.alert(i18n.t('success'), i18n.t('maintenanceReportCreated'));
        
        // Switch to list view
        onTabChange('list');
        
        // Refresh report list
        fetchReports();
      } else {
        Alert.alert(
          i18n.t('maintenanceSaveError'), 
          i18n.t('maintenanceFailedToSave')
        );
      }
      
    } catch (error) {
      console.error('Exception in handleCreateReport:', error);
      Alert.alert(i18n.t('error'), i18n.t('maintenanceUnexpectedError'));
    } finally {
      setLoading(false);
      setUploadingImages(false);
    }
  };

  const handleImagePick = async () => {
    try {
      setUploadingImages(true);
      
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert(i18n.t('maintenanceNoPermissions'), i18n.t('maintenancePhotoAccessNeeded'));
        return;
      }
      
      // Pick image
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        base64: false,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        
        // Log for debugging
        console.log('Selected image URI type:', typeof selectedImage.uri);
        console.log('Selected image URI preview:', selectedImage.uri.substring(0, 30) + '...');
        
        setForm(prev => ({
          ...prev,
          images: [...prev.images, selectedImage.uri],
        }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert(i18n.t('error'), i18n.t('maintenanceImagePickError'));
    } finally {
      setUploadingImages(false);
    }
  };

  const removeImage = (index: number) => {
    setForm(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  const areRequiredFieldsEmpty = () => {
    return !form.title.trim() || 
           !form.description.trim();
  };

  const renderAddReport = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => onTabChange('list')}
        >
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('maintenanceAddReport')}</Text>
        <View style={styles.placeholderView} />
      </View>
      
      <ScrollView 
        style={styles.content} 
        contentContainerStyle={styles.formScrollContent}
        keyboardShouldPersistTaps="handled"
      >
        {/* Tytuł zgłoszenia */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('maintenanceTitle')} *</Text>
          <TextInput
            style={styles.formInput}
            placeholder={i18n.t('maintenanceShortTitlePlaceholder')}
            value={form.title}
            onChangeText={text => setForm(prev => ({ ...prev, title: text }))}
          />
        </View>

        {/* Opis */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('maintenanceDescription')} *</Text>
          <TextInput
            style={[styles.formInput, styles.textArea]}
            placeholder={i18n.t('maintenanceDetailedDescription')}
            value={form.description}
            onChangeText={text => setForm(prev => ({ ...prev, description: text }))}
            multiline={true}
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Priorytet */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('maintenancePriority')}</Text>
          <View style={styles.priorityContainer}>
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'low' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm(prev => ({ ...prev, priority: 'low' }))}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotLow]} />
                <Text style={styles.priorityText}>{i18n.t('priorityLow')}</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'medium' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm(prev => ({ ...prev, priority: 'medium' }))}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotMedium]} />
                <Text style={styles.priorityText}>{i18n.t('priorityMedium')}</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'high' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm(prev => ({ ...prev, priority: 'high' }))}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotHigh]} />
                <Text style={styles.priorityText}>{i18n.t('priorityHigh')}</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'critical' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm(prev => ({ ...prev, priority: 'critical' }))}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotCritical]} />
                <Text style={styles.priorityText}>{i18n.t('priorityCritical')}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Sekcja zdjęć */}
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('maintenancePhotos')}</Text>
          <TouchableOpacity 
            style={styles.photoButton} 
            onPress={handleImagePick}
            disabled={uploadingImages}
          >
            <Ionicons name="camera-outline" size={24} color="#2563EB" />
            <Text style={styles.photoButtonText}>
              {uploadingImages ? i18n.t('loading') : i18n.t('maintenanceAddPhoto')}
            </Text>
          </TouchableOpacity>
          
          {form.images.length > 0 && (
            <View style={styles.imagesContainer}>
              {form.images.map((image, index) => (
                <View key={index} style={styles.imageItem}>
                  <Image source={{ uri: image }} style={styles.productImage} />
                  <TouchableOpacity 
                    style={styles.removeImageButton}
                    onPress={() => removeImage(index)}
                  >
                    <Ionicons name="close-circle" size={28} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
          <Text style={styles.formFieldNote}>
            {i18n.t('maintenancePhotosWillBeSaved')}
          </Text>
        </View>

        {/* Informacja o polach wymaganych */}
        <Text style={styles.formRequiredFieldsInfo}>{i18n.t('requiredFields')}</Text>

        {/* Przycisk utworzenia zgłoszenia */}
        <TouchableOpacity 
          style={[
            styles.formSubmitButton, 
            areRequiredFieldsEmpty() ? styles.formSubmitButtonDisabled : {}
          ]} 
          onPress={handleCreateReport}
          disabled={areRequiredFieldsEmpty() || uploadingImages || loading}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text style={styles.submitButtonText}>{i18n.t('maintenanceCreateReport')}</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  // Update the attemptStoringInAlternativeTable function
  const attemptStoringInAlternativeTable = async () => {
    console.log('Attempting to find an alternative table for storing maintenance reports...');
    
    try {
      // Check for common tables that might be available
      const potentialTables = ['tasks', 'issues', 'service_requests', 'tickets'];
      let availableTable = null;
      
      for (const table of potentialTables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('count(*)', { count: 'exact', head: true });
            
          if (!error) {
            console.log(`Found usable table: ${table}`);
            availableTable = table;
            break;
          }
        } catch (e) {
          console.log(`Table ${table} not available:`, e);
        }
      }
      
      if (availableTable) {
        // Try to insert into the available table with mapped fields
        const mappedData = {
          // Common fields that might exist in most tables
          company_id: companyId,
          title: form.title,
          description: form.description,
          status: 'pending', // Common status name
          priority: form.priority,
          created_by: userId, // Use created_by instead of reported_by_id
          reported_by: userId, // Also include reported_by
          created_at: new Date().toISOString(),
          // Add any additional fields that might be required
          client_name: form.reporter_name,
          photos: form.images.length > 0 ? form.images : null,
          notes: `Maintenance report created as fallback. Priority: ${form.priority}`
        };
        
        console.log(`Attempting to store in ${availableTable} table:`, mappedData);
        
        const { data, error } = await supabase
          .from(availableTable)
          .insert([mappedData]);
        
        if (error) {
          console.error(`Error storing in ${availableTable}:`, error);
          return false;
        }
        
        console.log(`Successfully stored in ${availableTable}:`, data);
        return true;
      }
      
      console.log('No alternative table found');
      return false;
    } catch (error) {
      console.error('Error in attemptStoringInAlternativeTable:', error);
      return false;
    }
  };

  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        {activeTab === 'list' ? renderReportsList() : renderAddReport()}
      </View>
      {renderBottomTabs()}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
    display: 'none', // Ukrywamy stary nagłówek
  },
  backText: {
    fontSize: 16,
    marginLeft: 8,
    color: '#1F2937',
    display: 'none', // Ukrywamy tekst "Powrót"
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
    display: 'none', // Ukrywamy tytuł, bo jest w headerze
  },
  content: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    padding: 0,
  },
  scrollContent: {
    padding: 0,
    width: '100%',
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priorityButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    marginHorizontal: 4,
    alignItems: 'center',
  },
  priorityButtonLow: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
  },
  priorityButtonMedium: {
    backgroundColor: 'rgba(245, 158, 11, 0.2)',
  },
  priorityButtonHigh: {
    backgroundColor: 'rgba(239, 68, 68, 0.2)',
  },
  priorityButtonCritical: {
    backgroundColor: 'rgba(127, 29, 29, 0.2)',
  },
  priorityButtonText: {
    fontSize: 14,
    color: '#4B5563',
  },
  priorityButtonTextActive: {
    fontWeight: '600',
    color: '#1F2937',
  },
  submitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  imageItem: {
    position: 'relative',
    marginRight: 12,
    marginBottom: 12,
  },
  productImage: {
    width: 120,
    height: 120,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  bottomTabBar: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: 'white',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderTopWidth: 2,
    borderTopColor: '#2563EB',
    paddingTop: 10,
  },
  tabText: {
    fontSize: 12,
    color: '#6B7280',
  },
  activeTabText: {
    color: '#2563EB',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterDrawer: {
    position: 'absolute',
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    width: 300,
    maxHeight: '80%',
    zIndex: 100,
  },
  filterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  filterButtonText: {
    marginHorizontal: 8,
    fontSize: 14,
    color: '#1F2937',
  },
  statusFilter: {
    flexDirection: 'row',
    paddingHorizontal: 12,
    marginTop: 8,
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  statusFilterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginBottom: 8,
    backgroundColor: '#F3F4F6',
  },
  statusFilterButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  statusFilterText: {
    fontSize: 14,
    color: '#4B5563',
  },
  statusFilterTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  filterContent: {
    padding: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  filterInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  priorityButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  priorityButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  loader: {
    marginTop: 20,
  },
  noReports: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
    fontSize: 16,
  },
  tableContainer: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 0,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginHorizontal: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    marginTop: 16,
    marginBottom: 60,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  headerCell: {
    paddingHorizontal: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    paddingHorizontal: 2,
  },
  headerText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4B5563',
    textAlign: 'center',
  },
  sortIcon: {
    marginLeft: 4,
  },
  tableBody: {
    flex: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  cell: {
    fontSize: 13,
    color: '#1F2937',
    textAlign: 'center',
    paddingHorizontal: 2,
  },
  statusContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 4,
    borderRadius: 12,
    fontSize: 10,
    fontWeight: '500',
    overflow: 'hidden',
    width: 75,
    textAlign: 'center',
    marginHorizontal: 2,
  },
  statusReported: {
    backgroundColor: '#F59E0B',
    color: '#92400E',
  },
  statusInProgress: {
    backgroundColor: '#3B82F6',
    color: '#1E40AF',
  },
  statusResolved: {
    backgroundColor: '#10B981',
    color: '#065F46',
  },
  statusCanceled: {
    backgroundColor: '#6B7280',
    color: '#1F2937',
  },
  statusToCheck: {
    backgroundColor: '#8B5CF6',
    color: '#4C1D95',
  },
  statusPending: {
    backgroundColor: '#8B5CF6',
    color: '#4C1D95',
  },
  priorityLow: {
    backgroundColor: '#D1FAE5',
    color: '#065F46',
  },
  priorityMedium: {
    backgroundColor: '#FEF3C7',
    color: '#92400E',
  },
  priorityHigh: {
    backgroundColor: '#FEE2E2',
    color: '#991B1B',
  },
  priorityCritical: {
    backgroundColor: '#FECACA',
    color: '#7F1D1D',
  },
  fieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  requiredFieldsInfo: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  disabledFeatureContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
    marginTop: 4,
  },
  disabledFeatureText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6B7280',
    flex: 1,
  },
  readOnlyField: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
  },
  readOnlyText: {
    fontSize: 16,
    color: '#4B5563',
  },
  submitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'transparent',
  },
  photoButtonText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  filtersContainer: {
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingBottom: 0,
    width: '100%',
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 8,
    marginTop: 16,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    padding: 8,
  },
  clearSearchButton: {
    padding: 8,
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  modalBody: {
    marginTop: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalCancelButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    marginRight: 8,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  modalConfirmButton: {
    flex: 1,
    padding: 10,
    backgroundColor: '#2563EB',
    borderRadius: 6,
    alignItems: 'center',
  },
  modalConfirmButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginLeft: 8,
  },
  dateButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  dateButtonText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 4,
  },
  dateButtonTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  formScrollContent: {
    padding: 20,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  formInputGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 10,
  },
  formInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#1F2937',
  },
  formTextArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    gap: 8,
  },
  priorityCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    backgroundColor: 'white',
  },
  priorityCircleSelected: {
    borderColor: '#2563EB',
    borderWidth: 2,
  },
  priorityCircleInner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  priorityDot: {
    width: 16, 
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  priorityDotLow: {
    backgroundColor: '#10B981', // zielony
  },
  priorityDotMedium: {
    backgroundColor: '#3B82F6', // niebieski
  },
  priorityDotHigh: {
    backgroundColor: '#F59E0B', // pomarańczowy
  },
  priorityDotCritical: {
    backgroundColor: '#EF4444', // czerwony
  },
  priorityText: {
    fontSize: 12,
    color: '#4B5563',
  },
  formFieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  formRequiredFieldsInfo: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
});

export default MaintenanceManager; 