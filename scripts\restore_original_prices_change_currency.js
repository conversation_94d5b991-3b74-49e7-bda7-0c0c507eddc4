const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function restoreOriginalPricesChangeCurrency() {
  try {
    console.log('=== Restoring Original Prices with USD Currency ===\n');
    
    // Przywróć oryginalne ceny ale z USD jako walutą wyświetlania
    const originalPrices = {
      'Basic': 500,           // 5.00 USD (było 5.00 PLN)
      'Pro': 2000,           // 20.00 USD (było 20.00 PLN)  
      'Business': 5000,      // 50.00 USD (było 50.00 PLN)
      'Basic Yearly': 4800,  // 48.00 USD (było 48.00 PLN z rabatem)
      'Pro Yearly': 19200,   // 192.00 USD (było 192.00 PLN z rabatem)
      'Business Yearly': 48000 // 480.00 USD (było 480.00 PLN z rabatem)
    };

    console.log('Updating plan prices to original values with USD currency:');
    
    for (const [planName, price] of Object.entries(originalPrices)) {
      console.log(`${planName}: $${price/100} USD`);
      
      const { error: updateError } = await supabase
        .from('subscription_plans')
        .update({ price: price })
        .eq('name', planName);

      if (updateError) {
        console.log(`❌ Error updating ${planName}: ${updateError.message}`);
      } else {
        console.log(`✅ Updated ${planName} to $${price/100} USD`);
      }
    }

    // Sprawdź zaktualizowane ceny
    console.log('\n=== Verification ===\n');
    
    const { data: updatedPlans, error: verifyError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    if (verifyError) {
      console.error('Error verifying updates:', verifyError);
      return;
    }

    console.log('Updated plans (USD):');
    updatedPlans.forEach(plan => {
      console.log(`${plan.name}: $${plan.price/100} USD`);
    });

    // Sprawdź oszczędności dla planów rocznych
    console.log('\n=== Yearly Plan Savings (USD) ===\n');
    
    const monthlyPlans = updatedPlans.filter(p => p.billing_period === 'monthly');
    const yearlyPlans = updatedPlans.filter(p => p.billing_period === 'yearly');

    for (const monthlyPlan of monthlyPlans) {
      const yearlyEquivalent = yearlyPlans.find(y => 
        y.name.toLowerCase().includes(monthlyPlan.name.toLowerCase())
      );
      
      if (yearlyEquivalent) {
        const monthlyYearlyCost = monthlyPlan.price * 12;
        const actualYearlyCost = yearlyEquivalent.price;
        const savings = monthlyYearlyCost - actualYearlyCost;
        const savingsPercent = Math.round((savings / monthlyYearlyCost) * 100);
        
        console.log(`${monthlyPlan.name}:`);
        console.log(`  Monthly: $${monthlyPlan.price/100}/month ($${monthlyYearlyCost/100}/year)`);
        console.log(`  Yearly: $${actualYearlyCost/100}/year`);
        console.log(`  Savings: $${savings/100} (${savingsPercent}%)`);
      }
    }

    console.log('\n🎉 Currency change completed!');
    console.log('\n📝 Summary:');
    console.log('✅ Prices restored to original values');
    console.log('✅ Currency display changed from PLN to USD');
    console.log('✅ Frontend formatPrice functions updated to show USD');
    console.log('✅ Yearly plans maintain 20% discount');

  } catch (error) {
    console.error('Error in restoreOriginalPricesChangeCurrency:', error);
  }
}

restoreOriginalPricesChangeCurrency();
