import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { i18n } from '../utils/localization';

interface IndependentEmployeeDashboardProps {
  employeeId: string;
  onNavigate: (screen: string) => void;
  onMenuPress?: () => void;
}

const IndependentEmployeeDashboard: React.FC<IndependentEmployeeDashboardProps> = ({
  employeeId,
  onNavigate,
  onMenuPress
}) => {
  const [employee, setEmployee] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchEmployeeData();
  }, [employeeId]);

  const fetchEmployeeData = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('*')
        .eq('id', employeeId)
        .is('company_id', null) // Sprawdź czy to niezależny pracownik
        .single();

      if (error) {
        console.error('Error fetching independent employee:', error);
        return;
      }

      setEmployee(data);
    } catch (error) {
      console.error('Error in fetchEmployeeData:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleJoinCompany = () => {
    Alert.alert(
      'Dołącz do firmy',
      'Aby dołączyć do firmy, potrzebujesz kodu weryfikacyjnego od administratora firmy.',
      [
        { text: 'Anuluj', style: 'cancel' },
        { text: 'Wprowadź kod', onPress: () => onNavigate('VerificationCode') }
      ]
    );
  };

  const handleViewProfile = () => {
    onNavigate('Profile');
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Ładowanie...</Text>
      </View>
    );
  }

  if (!employee) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Nie znaleziono danych pracownika</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onMenuPress} style={styles.menuButton}>
          <Ionicons name="menu" size={24} color="#1A1A1A" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Niezależny Pracownik</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {/* Karta powitalana */}
        <View style={styles.welcomeCard}>
          <View style={styles.welcomeHeader}>
            <Ionicons name="person-circle" size={48} color="#4F46E5" />
            <View style={styles.welcomeInfo}>
              <Text style={styles.welcomeName}>Witaj, {employee.full_name}!</Text>
              <Text style={styles.welcomeSubtitle}>Jesteś niezależnym pracownikiem</Text>
            </View>
          </View>
          <Text style={styles.welcomeDescription}>
            Jako niezależny pracownik masz ograniczone funkcje. Możesz dołączyć do firmy używając kodu weryfikacyjnego.
          </Text>
        </View>

        {/* Sekcja akcji */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dostępne akcje</Text>
          
          <TouchableOpacity style={styles.actionCard} onPress={handleJoinCompany}>
            <View style={styles.actionIcon}>
              <Ionicons name="business" size={24} color="#10B981" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Dołącz do firmy</Text>
              <Text style={styles.actionDescription}>
                Użyj kodu weryfikacyjnego, aby dołączyć do firmy i uzyskać pełny dostęp
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#6B7280" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard} onPress={handleViewProfile}>
            <View style={styles.actionIcon}>
              <Ionicons name="person" size={24} color="#3B82F6" />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Mój profil</Text>
              <Text style={styles.actionDescription}>
                Wyświetl i edytuj swoje dane osobowe
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#6B7280" />
          </TouchableOpacity>
        </View>

        {/* Sekcja informacyjna */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Ograniczenia konta niezależnego</Text>
          <View style={styles.limitationsList}>
            <View style={styles.limitationItem}>
              <Ionicons name="close-circle" size={16} color="#EF4444" />
              <Text style={styles.limitationText}>Brak dostępu do zamówień firmy</Text>
            </View>
            <View style={styles.limitationItem}>
              <Ionicons name="close-circle" size={16} color="#EF4444" />
              <Text style={styles.limitationText}>Brak możliwości rejestrowania godzin pracy</Text>
            </View>
            <View style={styles.limitationItem}>
              <Ionicons name="close-circle" size={16} color="#EF4444" />
              <Text style={styles.limitationText}>Brak dostępu do raportów firmy</Text>
            </View>
            <View style={styles.limitationItem}>
              <Ionicons name="checkmark-circle" size={16} color="#10B981" />
              <Text style={styles.limitationText}>Możliwość dołączenia do firmy</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  menuButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 36,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
    color: '#6B7280',
  },
  errorText: {
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
    color: '#EF4444',
  },
  welcomeCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  welcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  welcomeInfo: {
    marginLeft: 12,
    flex: 1,
  },
  welcomeName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  welcomeSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  welcomeDescription: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  actionCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 18,
  },
  infoSection: {
    backgroundColor: '#FEF3C7',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 12,
  },
  limitationsList: {
    gap: 8,
  },
  limitationItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  limitationText: {
    fontSize: 14,
    color: '#78350F',
    marginLeft: 8,
  },
});

export default IndependentEmployeeDashboard;
