-- Najpierw usuń istniejące ograniczenie
ALTER TABLE companies DROP CONSTRAINT IF EXISTS companies_account_type_check;

-- Dodaj nowe ograniczenie z wszystkimi możliwymi wartościami (włącznie z małymi literami)
ALTER TABLE companies 
ADD CONSTRAINT companies_account_type_check 
CHECK (account_type IN (
  'free', 
  'basic', 
  'pro', 
  'business',
  'Basic',
  'Pro',
  'Business',
  'basic yearly',
  'pro yearly',
  'business yearly',
  'Basic Yearly',
  'Pro Yearly',
  'Business Yearly'
)); 