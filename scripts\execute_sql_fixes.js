// Skrypt do wykonania poprawek SQL w Supabase
// Uruchom: node scripts/execute_sql_fixes.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// UWAGA: Ustaw swoje dane Supabase
const SUPABASE_URL = 'YOUR_SUPABASE_URL'; // np. https://xxxxx.supabase.co
const SUPABASE_SERVICE_ROLE_KEY = 'YOUR_SERVICE_ROLE_KEY'; // Service Role Key z Settings > API

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function executeSQLFile(filePath, description) {
  console.log(`\n🔄 Wykonywanie: ${description}`);
  console.log(`📁 Plik: ${filePath}`);
  
  try {
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    // Podziel na pojedyncze komendy SQL (rozdzie<PERSON> przez ;)
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));
    
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      if (command.length > 0) {
        console.log(`  ⏳ Wykonywanie komendy ${i + 1}/${commands.length}...`);
        
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: command
        });
        
        if (error) {
          console.error(`  ❌ Błąd w komendzie ${i + 1}:`, error.message);
          // Kontynuuj z następną komendą
        } else {
          console.log(`  ✅ Komenda ${i + 1} wykonana pomyślnie`);
        }
      }
    }
    
    console.log(`✅ Zakończono: ${description}`);
  } catch (error) {
    console.error(`❌ Błąd podczas wykonywania ${description}:`, error.message);
  }
}

async function executeDirectSQL(sql, description) {
  console.log(`\n🔄 Wykonywanie: ${description}`);
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: sql
    });
    
    if (error) {
      console.error(`❌ Błąd:`, error.message);
    } else {
      console.log(`✅ Zakończono: ${description}`);
      if (data) {
        console.log('📊 Wynik:', data);
      }
    }
  } catch (error) {
    console.error(`❌ Błąd podczas wykonywania ${description}:`, error.message);
  }
}

async function main() {
  console.log('🚀 Rozpoczynanie naprawy systemu subskrypcji...\n');
  
  // Sprawdź połączenie
  console.log('🔗 Sprawdzanie połączenia z Supabase...');
  const { data: connectionTest, error: connectionError } = await supabase
    .from('companies')
    .select('count')
    .limit(1);
  
  if (connectionError) {
    console.error('❌ Błąd połączenia z Supabase:', connectionError.message);
    console.log('\n📝 Sprawdź czy:');
    console.log('1. SUPABASE_URL jest poprawny');
    console.log('2. SUPABASE_SERVICE_ROLE_KEY jest poprawny');
    console.log('3. Masz uprawnienia do bazy danych');
    return;
  }
  
  console.log('✅ Połączenie z Supabase działa\n');
  
  // Krok 1: Usuń stare funkcje
  await executeDirectSQL(`
    DROP FUNCTION IF EXISTS can_change_employee_status(uuid);
    DROP FUNCTION IF EXISTS activate_employee(uuid, uuid);
    DROP FUNCTION IF EXISTS deactivate_employee(uuid, uuid);
    DROP FUNCTION IF EXISTS reactivate_company_employees(uuid);
    DROP FUNCTION IF EXISTS deactivate_company_employees(uuid);
    DROP TRIGGER IF EXISTS trigger_check_verification_code_usage ON employees;
  `, 'Usuwanie starych funkcji');
  
  // Krok 2: Wykonaj triggery
  await executeSQLFile(
    path.join(__dirname, '..', 'sql', 'create_employee_status_triggers.sql'),
    'Tworzenie triggerów'
  );
  
  // Krok 3: Wykonaj funkcje
  await executeSQLFile(
    path.join(__dirname, '..', 'sql', 'create_employee_status_functions.sql'),
    'Tworzenie funkcji'
  );
  
  // Krok 4: Sprawdź czy funkcje zostały utworzone
  await executeDirectSQL(`
    SELECT routine_name 
    FROM information_schema.routines 
    WHERE routine_name IN (
      'activate_employee', 
      'deactivate_employee', 
      'reactivate_company_employees', 
      'deactivate_company_employees_on_expiry',
      'can_change_employee_status',
      'company_has_active_subscription',
      'can_use_verification_code',
      'check_and_deactivate_expired_subscriptions'
    );
  `, 'Sprawdzanie utworzonych funkcji');
  
  // Krok 5: Sprawdź kolumny
  await executeDirectSQL(`
    SELECT column_name, data_type, is_nullable, column_default 
    FROM information_schema.columns 
    WHERE table_name = 'employees' 
    AND column_name IN ('subscription_status', 'last_status_change', 'verification_code');
  `, 'Sprawdzanie kolumn w tabeli employees');
  
  console.log('\n🎉 Naprawa systemu subskrypcji zakończona!');
  console.log('\n📋 Następne kroki:');
  console.log('1. Przetestuj przycisk "Aktywuj" w panelu administratora');
  console.log('2. Sprawdź czy kody weryfikacyjne respektują limity');
  console.log('3. Przetestuj rejestrację nowego pracownika');
}

// Uruchom skrypt
main().catch(console.error);
