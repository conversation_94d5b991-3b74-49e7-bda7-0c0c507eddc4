-- Kod SQL do sprawdzenia i naprawy tabeli purchases w bazie danych Supabase
-- Wykonaj te polecenia w SQL Editor w konsoli Supabase

-- Sprawdzenie jakie kolumny istnieją w tabeli purchases
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'purchases' 
ORDER BY ordinal_position;

-- Dodanie kolumny notes, jeśli nie istnieje
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'purchases' AND column_name = 'notes'
    ) THEN
        RAISE NOTICE 'Dodaję kolumnę notes do tabeli purchases...';
        
        ALTER TABLE purchases ADD COLUMN notes TEXT;
        
        -- Dodajemy komentarz do kolumny
        COMMENT ON COLUMN purchases.notes IS 'Dodatkowe notatki do wniosku zakupowego';
        
        RAISE NOTICE 'Kolumna notes została dodana.';
    ELSE
        RAISE NOTICE '<PERSON>lumna notes już istnieje w tabeli purchases.';
    END IF;
END
$$;

-- Sprawdzenie czy kolumna została dodana
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'purchases' AND column_name = 'notes';

-- Sprawdzenie czy tabela ma kolumnę photos
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'purchases' AND column_name = 'photos'
    ) THEN
        RAISE NOTICE 'Dodaję kolumnę photos do tabeli purchases...';
        
        -- Dodanie kolumny photos jako tablicy tekstowej (array of text) do przechowywania URLi zdjęć
        ALTER TABLE purchases ADD COLUMN photos TEXT[] DEFAULT '{}';
        
        -- Dodajemy komentarz do kolumny
        COMMENT ON COLUMN purchases.photos IS 'Tablica URLi do zdjęć produktów związanych z wnioskiem zakupowym';
        
        -- Dodajmy indeks dla nowej kolumny
        CREATE INDEX IF NOT EXISTS purchases_photos_idx ON purchases USING gin(photos);
        
        RAISE NOTICE 'Kolumna photos została dodana.';
    ELSE
        RAISE NOTICE 'Kolumna photos już istnieje w tabeli purchases.';
    END IF;
END
$$;

-- Sprawdzenie ponownie struktury całej tabeli po modyfikacjach
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'purchases' 
ORDER BY ordinal_position; 