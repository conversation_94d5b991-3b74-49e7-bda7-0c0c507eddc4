-- Naj<PERSON>rw usuń istniejące rekordy z payment_history (opcjonalne, usuń tę linię jeśli chcesz zachować dane)
TRUNCATE payment_history;

-- <PERSON><PERSON><PERSON> istniejące powiązanie z company_subscriptions
ALTER TABLE payment_history 
DROP CONSTRAINT IF EXISTS payment_history_subscription_id_fkey;

-- Zmień typ kolumny subscription_id na TEXT
ALTER TABLE payment_history 
ALTER COLUMN subscription_id TYPE TEXT USING subscription_id::TEXT;

-- Dodaj brakuj<PERSON>ce kolumny jeśli nie istnieją
DO $$ 
BEGIN 
    -- Dodaj stripe_payment_intent_id jeśli nie istnieje
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'payment_history' 
        AND column_name = 'stripe_payment_intent_id'
    ) THEN 
        ALTER TABLE payment_history ADD COLUMN stripe_payment_intent_id TEXT;
    END IF;

    -- Dodaj stripe_invoice_id jeśli nie istnieje
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'payment_history' 
        AND column_name = 'stripe_invoice_id'
    ) THEN 
        ALTER TABLE payment_history ADD COLUMN stripe_invoice_id TEXT;
    END IF;
END $$;

-- Zaktualizuj webhook aby używał nowej struktury
CREATE OR REPLACE FUNCTION process_stripe_payment(
    p_company_id UUID,
    p_subscription_id TEXT,
    p_amount INTEGER,
    p_currency TEXT,
    p_payment_intent_id TEXT,
    p_invoice_id TEXT,
    p_description TEXT
) RETURNS UUID AS $$
DECLARE
    payment_id UUID;
BEGIN
    INSERT INTO payment_history (
        company_id,
        subscription_id,
        amount,
        currency,
        status,
        stripe_payment_intent_id,
        stripe_invoice_id,
        description,
        created_at
    ) VALUES (
        p_company_id,
        p_subscription_id,
        p_amount,
        LOWER(p_currency),
        'succeeded',
        p_payment_intent_id,
        p_invoice_id,
        p_description,
        NOW()
    ) RETURNING id INTO payment_id;

    RETURN payment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Nadaj uprawnienia
GRANT EXECUTE ON FUNCTION process_stripe_payment TO service_role;
GRANT EXECUTE ON FUNCTION process_stripe_payment TO authenticated; 