import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, Modal, TextInput, Platform, TouchableWithoutFeedback, Keyboard, SafeAreaView, KeyboardAvoidingView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { TopBar } from './TopBar';
import DatePicker from './DatePicker';
import { format } from 'date-fns';
import { i18n } from '../utils/localization';

type MenuItem = 'dashboard' | 'employees' | 'schedule' | 'tasks' | 'admin' | 'hours' | 'task_details' | 'employee_task_details' | 'edit_task' | 'work_session_details';

interface WorkSessionDetailsProps {
  sessionId: string;
  date: string;
  employeeId: string;
  employeeName: string;
  onBack: () => void;
  onMenuPress: () => void;
  setSelectedMenuItem: (menuItem: MenuItem) => void;
  onTaskSelect?: (taskId: string) => void;
  onSessionSelect?: (sessionId: string, date: string, employeeId: string, employeeName: string) => void;
}

interface WorkSessionDetail {
  id: string;
  employee_id: string;
  job_order: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  created_at: string;
  start_location?: any;
  end_location?: any;
  notes?: string;
  task_id?: string;
  manually_edited?: boolean;
  added_manually?: boolean;
  task?: {
    id: string;
    client_name: string;
    address: string;
    work_scope: string;
    status: string;
  } | null;
}

interface TaskActivity {
  id: string;
  task_id: string;
  employee_id: string;
  start_time: string;
  end_time: string | null;
  status: string;
  notes?: string;
  task?: {
    client_name: string;
    address: string;
    work_scope: string;
    status: string;
  } | null;
}

interface DailyWorkSummary {
  totalSessions: number;
  totalDuration: number;
  allSessions: WorkSessionDetail[];
  taskActivities: TaskActivity[];
}

const LocationDisplay = ({ location }: { location: any }) => {
  const [address, setAddress] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [coordinates, setCoordinates] = useState<{latitude: number | null, longitude: number | null}>({
    latitude: null,
    longitude: null
  });

  useEffect(() => {
    console.log("LocationDisplay received location:", JSON.stringify(location, null, 2));
    
    // Parse location if it's a string
    try {
      let locationData = location;
      
      if (typeof location === 'string') {
        try {
          locationData = JSON.parse(location);
          console.log("Parsed location string:", locationData);
        } catch (e) {
          console.error("Failed to parse location string:", e);
          setError("Nieprawidłowy format danych lokalizacji");
          setIsLoading(false);
          return;
        }
      }
      
      // Extract coordinates from the location data
      if (locationData) {
        // Direct latitude/longitude format
        if (locationData.latitude !== undefined && locationData.longitude !== undefined) {
          setCoordinates({
            latitude: Number(locationData.latitude),
            longitude: Number(locationData.longitude)
          });
        } 
        // Format with coords object
        else if (locationData.coords && locationData.coords.latitude !== undefined && locationData.coords.longitude !== undefined) {
          setCoordinates({
            latitude: Number(locationData.coords.latitude),
            longitude: Number(locationData.coords.longitude)
          });
        }
        // Format with lat/lng
        else if (locationData.lat !== undefined && locationData.lng !== undefined) {
          setCoordinates({
            latitude: Number(locationData.lat),
            longitude: Number(locationData.lng)
          });
        } else {
          console.error("Nierozpoznany format danych lokalizacji:", locationData);
          setError("Nierozpoznany format danych lokalizacji");
          setIsLoading(false);
          return;
        }
        
        // If we have coordinates, fetch the address
        if (coordinates.latitude && coordinates.longitude) {
          fetchAddress(coordinates.latitude, coordinates.longitude);
        } else {
          setIsLoading(false);
          setError("Nie znaleziono współrzędnych geograficznych");
        }
      } else {
        setIsLoading(false);
        setError("Brak danych lokalizacji");
      }
    } catch (error) {
      console.error("Error processing location data:", error);
      setError("Wystąpił błąd podczas przetwarzania danych lokalizacji");
      setIsLoading(false);
    }
  }, [location]);
  
  useEffect(() => {
    // When coordinates change, fetch the address
    if (coordinates.latitude && coordinates.longitude) {
      fetchAddress(coordinates.latitude, coordinates.longitude);
    }
  }, [coordinates]);

  const fetchAddress = async (latitude: number, longitude: number) => {
    try {
      setIsLoading(true);
      
      console.log(`Fetching address for coordinates: ${latitude}, ${longitude}`);
      
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'WorkFlow Mobile App',
            'Accept-Language': 'pl'
          }
        }
      );
      
      if (!response.ok) {
        throw new Error(`Błąd pobierania adresu: ${response.status}`);
      }
      
      const data = await response.json();
      console.log("Received address data:", data);
      
      if (data && data.address) {
        // Extract just the street, number, and city
        const { road, house_number, city, town, village, suburb } = data.address;
        
        // Build simplified address
        let simplifiedAddress = '';
        
        // Add street name if available
        if (road) {
          simplifiedAddress += road;
          
          // Add house number if available
          if (house_number) {
            simplifiedAddress += ` ${house_number}`;
          }
          
          simplifiedAddress += ', ';
        }
        
        // Add city/town/village (whichever is available)
        const location = city || town || village || suburb || '';
        simplifiedAddress += location;
        
        setAddress(simplifiedAddress || `Nieznany adres (${latitude.toFixed(5)}, ${longitude.toFixed(5)})`);
      } else if (data && data.display_name) {
        setAddress(data.display_name);
      } else {
        setAddress(`Nieznany adres (${latitude.toFixed(5)}, ${longitude.toFixed(5)})`);
      }
    } catch (error) {
      console.error("Error fetching address:", error);
      setError(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`);
      setAddress(`Nie udało się pobrać adresu: (${latitude.toFixed(5)}, ${longitude.toFixed(5)})`);
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified return focused on just showing the address
  if (!coordinates.latitude || !coordinates.longitude) {
    return <Text style={stylesLocal.infoValue}>Brak danych o lokalizacji</Text>;
  }

  return (
    <>
      {isLoading ? (
        <Text style={stylesLocal.infoValue}>Ładowanie adresu...</Text>
      ) : (
        <Text style={stylesLocal.infoValue}>{address || "Nieznany adres"}</Text>
      )}
    </>
  );
};

// Local styles for LocationDisplay to use before the main styles are defined
const stylesLocal = StyleSheet.create({
  infoValue: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1, // Allow value to take remaining space
    textAlign: 'right',
  }
});

const WorkSessionDetails = ({ 
  sessionId, 
  date, 
  employeeId, 
  employeeName, 
  onBack, 
  onMenuPress,
  setSelectedMenuItem,
  onTaskSelect,
  onSessionSelect 
}: WorkSessionDetailsProps) => {
  const [session, setSession] = useState<WorkSessionDetail | null>(null);
  const [dailyWork, setDailyWork] = useState<DailyWorkSummary>({
    totalSessions: 0,
    totalDuration: 0,
    allSessions: [],
    taskActivities: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>({});
  
  // State for edit modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editedSession, setEditedSession] = useState({
    job_order: '',
    start_date: '',
    end_date: '',
    start_time: '',
    end_time: '',
    notes: ''
  });
  const [saving, setSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [calculatedDuration, setCalculatedDuration] = useState<number | null>(null);

  useEffect(() => {
    console.log('WorkSessionDetails component mounted with sessionId:', sessionId);
    console.log('employeeId:', employeeId);
    console.log('date:', date);
    
    fetchData();
  }, [sessionId]);

  // Helper for text input focus on web
  const handleInputFocus = (e: any) => {
    if (Platform.OS === 'web') {
      e.stopPropagation();
    }
    return false; // Return false to comply with the expected return type
  };

  // Add effect to recalculate duration whenever time fields change
  useEffect(() => {
    if (editedSession.start_time && editedSession.end_time) {
      // Call the function directly
      const _ = calculateDuration();
    } else {
      // Reset calculation if one of the fields is empty
      setCalculatedDuration(null);
    }
  }, [editedSession.start_time, editedSession.end_time, editedSession.start_date, editedSession.end_date]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const debug: any = {};
      
      console.log('*** FETCH DATA STARTED ***');
      console.log('SessionID:', sessionId);
      console.log('EmployeeID:', employeeId);
      console.log('Date:', date);
      
      if (!sessionId) {
        console.error('SESSION ID IS MISSING');
        setError('Brak identyfikatora sesji.');
        setLoading(false);
        return;
      }
      
      // First get the selected session to determine the exact date
      console.log('Querying for basic session information with ID:', sessionId);
      const { data: selectedSession, error: sessionError } = await supabase
        .from('work_sessions')
        .select('id, start_time')
        .eq('id', sessionId)
        .single();

      if (sessionError) {
        console.error('Error fetching session date:', sessionError);
        debug.sessionLookupError = sessionError;
        setError('Nie udało się pobrać informacji o sesji. Spróbuj ponownie.');
        setLoading(false);
        setDebugInfo(debug);
        return;
      }

      if (!selectedSession) {
        console.error(`Session with ID ${sessionId} not found in database`);
        debug.sessionLookupResult = 'Not found';
        setError(`Nie znaleziono sesji o ID: ${sessionId}`);
        setLoading(false);
        setDebugInfo(debug);
        return;
      }

      debug.sessionLookup = selectedSession;
      console.log('Found basic session data:', selectedSession);

      // Format the date to YYYY-MM-DD for comparison
      const sessionDate = new Date(selectedSession.start_time).toISOString().split('T')[0];
      debug.sessionDate = sessionDate;
      console.log('Extracted date:', sessionDate);
      
      // Fetch all data in sequence rather than parallel to ensure better debugging
      try {
        console.log('Starting sequential data fetching');
        
        const sessionDetails = await fetchSessionDetails();
        debug.sessionDetails = sessionDetails || 'Failed';
        
        const dailySessions = await fetchDailyWorkSessions(sessionDate);
        if (Array.isArray(dailySessions)) {
          debug.dailySessions = `Found ${dailySessions.length} sessions`;
        } else {
          debug.dailySessions = 'Failed';
        }
        
        const taskActivitiesData = await fetchTaskActivities(sessionDate);
        if (Array.isArray(taskActivitiesData)) {
          debug.taskActivities = `Found ${taskActivitiesData.length} activities`;
        } else {
          debug.taskActivities = 'Failed';
        }
        
        console.log('All data fetched successfully');
        
      } catch (error) {
        console.error('Error during sequential data fetching:', error);
        debug.sequentialFetchError = error;
        // Continue execution to show partial data
      }

      setDebugInfo(debug);
      
    } catch (error) {
      console.error('Exception in fetchData:', error);
      setError('Wystąpił nieoczekiwany błąd. Spróbuj ponownie.');
      setDebugInfo({ mainError: error });
    } finally {
      setLoading(false);
      console.log('*** FETCH DATA COMPLETED ***');
    }
  };

  const fetchSessionDetails = async () => {
    try {
      console.log('Fetching detailed session info for ID:', sessionId);
      
      const { data, error } = await supabase
        .from('work_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error) {
        console.error('Error fetching session details:', error);
        setError('Nie udało się pobrać szczegółów sesji pracy');
        return null;
      }

      if (!data) {
        console.error('No session data returned for ID:', sessionId);
        setError('Nie znaleziono sesji o podanym ID');
        return null;
      }
      
      // Log raw data for debugging
      console.log('Raw session data from database:', data);
      console.log('start_location from database:', data.start_location);
      console.log('end_location from database:', data.end_location);

      // Create a proper session object with task properties
      let sessionWithTask: WorkSessionDetail = {
        ...data,
        task: null
      };

      // If task_id exists, fetch the related task
      if (data.task_id) {
        try {
          const { data: task, error: taskError } = await supabase
            .from('tasks')
            .select('*')
            .eq('id', data.task_id)
            .single();

          if (task && !taskError) {
            sessionWithTask.task = task;
          }
        } catch (taskError) {
          console.error('Exception while fetching task:', taskError);
        }
      }

      // Update state with the session data
      setSession(sessionWithTask);
      return sessionWithTask;
    } catch (error) {
      console.error('Exception in fetchSessionDetails:', error);
      setError('Wystąpił nieoczekiwany błąd podczas pobierania danych');
      return null;
    }
  };

  const fetchDailyWorkSessions = async (sessionDate: string) => {
    try {
      console.log('Fetching all sessions for date:', sessionDate, 'and employee:', employeeId);
      
      // Validate inputs
      if (!sessionDate) {
        console.error('Session date is missing');
        return [];
      }
      
      if (!employeeId) {
        console.error('Employee ID is missing');
        return [];
      }

      // Sprawdź status subskrypcji pracownika
      const { data: employeeData, error: employeeError } = await supabase
        .from('employees')
        .select('subscription_status')
        .eq('id', employeeId)
        .single();

      if (employeeError) {
        console.error('Error fetching employee status:', employeeError);
        throw employeeError;
      }

      // Jeśli pracownik nie jest aktywny, zwróć puste dane
      if (employeeData?.subscription_status !== 'ACTIVE') {
        console.log('Employee is not active:', employeeId);
        setDailyWork(prev => ({
          ...prev,
          totalSessions: 0,
          totalDuration: 0,
          allSessions: []
        }));
        return [];
      }
      
      // Create explicit date range for the query
      const startOfDay = `${sessionDate}T00:00:00`;
      const endOfDay = `${sessionDate}T23:59:59`;
      
      console.log('Date range:', startOfDay, 'to', endOfDay);
      
      // Simplified query that should work with most Supabase versions
      const { data: allSessions, error: allSessionsError } = await supabase
        .from('work_sessions')
        .select('*')
        .eq('employee_id', employeeId)
        .order('start_time', { ascending: true });

      if (allSessionsError) {
        console.error('Error fetching work sessions:', allSessionsError);
        throw allSessionsError;
      }
      
      // Filter the results manually for the date range
      const filteredSessions = allSessions ? allSessions.filter(session => {
        return session.start_time >= startOfDay && session.start_time <= endOfDay;
      }) : [];
      
      if (!filteredSessions || filteredSessions.length === 0) {
        console.log('No sessions found for employee', employeeId, 'on date', sessionDate);
        setDailyWork(prev => ({
          ...prev,
          totalSessions: 0,
          totalDuration: 0,
          allSessions: []
        }));
        return [];
      }

      console.log(`Fetched ${filteredSessions.length} sessions for the day:`, filteredSessions.map((s: any) => s.id));

      // Process sessions in smaller batches to avoid overwhelming the database
      const sessionsWithTasks: WorkSessionDetail[] = [];
      
      // Process sessions one by one for better debugging
      for (const session of filteredSessions) {
        let sessionWithTask: any = { ...session, task: null };
        
        if (session.task_id) {
          try {
            console.log('Fetching task info for session', session.id, 'task:', session.task_id);
            const { data: task, error: taskError } = await supabase
              .from('tasks')
              .select('*')
              .eq('id', session.task_id)
              .single();

            if (taskError) {
              console.error(`Error fetching task for session ${session.id}:`, taskError);
            } else if (task) {
              sessionWithTask.task = task;
              console.log('Found task for session:', session.id);
            } else {
              console.log('No task found for ID:', session.task_id);
            }
          } catch (error) {
            console.error(`Error processing task for session ${session.id}:`, error);
          }
        }
        
        sessionsWithTasks.push(sessionWithTask);
      }

      // Calculate total duration
      const totalDuration = sessionsWithTasks.reduce((sum, session) => 
        sum + (session.duration_minutes || 0), 0);

      console.log(`Total duration for all sessions: ${totalDuration} minutes`);

      // Update the daily work summary with sessions
      setDailyWork(prev => ({
        ...prev,
        totalSessions: sessionsWithTasks.length,
        totalDuration: totalDuration,
        allSessions: sessionsWithTasks
      }));
      
      console.log('Daily work sessions updated');
      return sessionsWithTasks;
      
    } catch (error) {
      console.error('Exception in fetchDailyWorkSessions:', error);
      // Don't throw, allow other data to load
      return [];
    }
  };

  const fetchTaskActivities = async (sessionDate: string) => {
    try {
      console.log('Fetching task activities for date:', sessionDate, 'and employee:', employeeId);
      
      // Validate inputs
      if (!sessionDate) {
        console.error('Session date is missing');
        return [];
      }
      
      if (!employeeId) {
        console.error('Employee ID is missing');
        return [];
      }
      
      // Create explicit date range for the query
      const startOfDay = `${sessionDate}T00:00:00`;
      const endOfDay = `${sessionDate}T23:59:59`;
      
      console.log('Activities date range:', startOfDay, 'to', endOfDay);
      
      // Simplified query that should work with most Supabase versions
      const { data: activities, error: activitiesError } = await supabase
        .from('task_activities')
        .select('*')
        .eq('employee_id', employeeId)
        .order('start_time', { ascending: true });

      if (activitiesError) {
        console.error('Error fetching task activities:', activitiesError);
        throw activitiesError;
      }

      // Filter the results manually for the date range
      const filteredActivities = activities ? activities.filter(activity => {
        return activity.start_time >= startOfDay && activity.start_time <= endOfDay;
      }) : [];

      if (!filteredActivities || filteredActivities.length === 0) {
        console.log('No task activities found for employee', employeeId, 'on date', sessionDate);
        setDailyWork(prev => ({
          ...prev,
          taskActivities: []
        }));
        return [];
      }

      console.log(`Fetched ${filteredActivities.length} task activities for the day:`, filteredActivities.map((a: any) => a.id));

      // Process activities one by one for better debugging
      const activitiesWithTasks: TaskActivity[] = [];
      
      for (const activity of filteredActivities) {
        let activityWithTask: any = { ...activity, task: null };
        
        if (activity.task_id) {
          try {
            console.log('Fetching task info for activity', activity.id, 'task:', activity.task_id);
            const { data: task, error: taskError } = await supabase
              .from('tasks')
              .select('*')
              .eq('id', activity.task_id)
              .single();

            if (taskError) {
              console.error(`Error fetching task for activity ${activity.id}:`, taskError);
            } else if (task) {
              activityWithTask.task = task;
              console.log('Found task for activity:', activity.id);
            } else {
              console.log('No task found for ID:', activity.task_id);
            }
          } catch (error) {
            console.error(`Error processing task for activity ${activity.id}:`, error);
          }
        }
        
        activitiesWithTasks.push(activityWithTask);
      }

      // Update the daily work summary with task activities
      setDailyWork(prev => ({
        ...prev,
        taskActivities: activitiesWithTasks
      }));
      
      console.log('Task activities updated');
      return activitiesWithTasks;
      
    } catch (error) {
      console.error('Exception in fetchTaskActivities:', error);
      // Don't throw, allow other data to load
      return [];
    }
  };

  const formatDateTime = (dateString?: string) => {
    if (!dateString) return 'N/A';
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'N/A';
      
      return date.toLocaleTimeString('pl-PL', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (e) {
      console.error('Error formatting datetime:', e);
      return 'N/A';
    }
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes && minutes !== 0) return i18n.t('noData');
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    return `${hours}h ${mins}min`;
  };

  const formatDateForDisplay = (dateString?: string) => {
    if (!dateString) return i18n.t('noDate');
    try {
      // Remove any time component if present
      const datePart = dateString.split('T')[0];
      
      let dateObj;
      
      // Check for different formats
      // 1. Try YYYY-MM-DD format (standard ISO format)
      if (/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
        dateObj = new Date(`${datePart}T00:00:00`);
      } 
      // 2. Try DD.MM.YY format (like 13.05.25)
      else if (/^\d{2}\.\d{2}\.\d{2}$/.test(dateString)) {
        const [day, month, shortYear] = dateString.split('.');
        // Convert 2-digit year to 4-digit year (assuming 20xx for simplicity)
        const fullYear = `20${shortYear}`;
        dateObj = new Date(`${fullYear}-${month}-${day}T00:00:00`);
      }
      // 3. Try DD.MM.YYYY format (like 13.05.2025)
      else if (/^\d{2}\.\d{2}\.\d{4}$/.test(dateString)) {
        const [day, month, year] = dateString.split('.');
        dateObj = new Date(`${year}-${month}-${day}T00:00:00`);
      } 
      // If none of the formats match, try to parse it directly
      else {
        dateObj = new Date(dateString);
      }
      
      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        console.error(`Invalid date value: ${dateString}`);
        return i18n.t('invalidDate') || 'Invalid date';
      }
      
      // Format the date as DD.MM.YYYY
      return format(dateObj, 'dd.MM.yyyy');
    } catch (e) {
      console.error(`Error formatting date: ${e}`, dateString);
      return i18n.t('invalidDate') || 'Invalid date';
    }
  };

  const getTaskStatusText = (status?: string) => {
    if (!status) return i18n.t('unknownStatus');
    
    switch (status) {
      case 'pending': return i18n.t('pending');
      case 'in_progress': return i18n.t('inProgress');
      case 'completed': return i18n.t('completed');
      default: return status;
    }
  };

  const getActivityStatusText = (status?: string) => {
    if (!status) return i18n.t('unknownStatus');
    
    switch (status) {
      case 'active': return i18n.t('activeStatus');
      case 'completed': return i18n.t('completed');
      default: return status;
    }
  };

  // Update the handleEditSession function
  const handleEditSession = () => {
    if (!session) return;
    
    try {
      const startDate = new Date(session.start_time);
      const endDate = new Date(session.end_time);
      
      // Format date as YYYY-MM-DD
      const formatDateString = (date: Date) => {
        return date.toISOString().split('T')[0];
      };
      
      // Format time as HH:MM
      const formatTimeString = (date: Date) => {
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      };
      
      const newEditedSession = {
        job_order: session.job_order || '',
        start_date: formatDateString(startDate),
        end_date: formatDateString(endDate),
        start_time: formatTimeString(startDate),
        end_time: formatTimeString(endDate),
        notes: session.notes || ''
      };
      
      setEditedSession(newEditedSession);
      setCalculatedDuration(session.duration_minutes);
      setShowEditModal(true);
    } catch (error) {
      console.error('Error preparing edit form:', error);
      Alert.alert(i18n.t('error'), i18n.t('errorOpeningForm'));
    }
  };

  // Update the calculateDuration function
  const calculateDuration = () => {
    if (!editedSession.start_time || !editedSession.end_time) {
      setCalculatedDuration(null);
      return null;
    }
    
    try {
      // Parse times (HH:MM format)
      const [startHours, startMinutes] = editedSession.start_time.split(':').map(Number);
      const [endHours, endMinutes] = editedSession.end_time.split(':').map(Number);
      
      if (isNaN(startHours) || isNaN(startMinutes) || isNaN(endHours) || isNaN(endMinutes)) {
        setCalculatedDuration(null);
        return null;
      }
      
      // Create Date objects with the correct dates and times
      const startDate = new Date(editedSession.start_date);
      const endDate = new Date(editedSession.end_date);
      
      // Set hours and minutes
      startDate.setHours(startHours, startMinutes, 0, 0);
      endDate.setHours(endHours, endMinutes, 0, 0);
      
      // Calculate difference in milliseconds
      const diffMs = endDate.getTime() - startDate.getTime();
      
      // If end time is before start time, assume it's the next day if dates are the same
      if (diffMs < 0 && editedSession.start_date === editedSession.end_date) {
        // Add 24 hours if same date but end time is earlier
        const endDateNextDay = new Date(endDate);
        endDateNextDay.setDate(endDateNextDay.getDate() + 1);
        const diffMsNextDay = endDateNextDay.getTime() - startDate.getTime();
        const durationMinutes = Math.floor(diffMsNextDay / (1000 * 60));
        setCalculatedDuration(durationMinutes);
        return durationMinutes;
      } else if (diffMs < 0) {
        setCalculatedDuration(null);
        return null;
      }
      
      const durationMinutes = Math.floor(diffMs / (1000 * 60));
      
      // Update the state immediately
      setCalculatedDuration(durationMinutes);
      return durationMinutes;
    } catch (error) {
      console.error('Error calculating duration:', error);
      setCalculatedDuration(null);
      return null;
    }
  };

  // Improve the handleSaveSession function
  const handleSaveSession = async () => {
    if (!session) return;
    setSaving(true);
    setSaveError(null);
    
    try {
      // Validate time format
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
      if (!timeRegex.test(editedSession.start_time) || !timeRegex.test(editedSession.end_time)) {
        throw new Error('Nieprawidłowy format czasu. Użyj formatu HH:MM');
      }
      
      // Calculate duration
      const durationMinutes = calculateDuration();
      if (durationMinutes === null || durationMinutes <= 0) {
        throw new Error('Czas zakończenia musi być późniejszy niż czas rozpoczęcia');
      }
      
      // Create start and end date objects
      const startDate = new Date(editedSession.start_date);
      const endDate = new Date(editedSession.end_date);
      
      // Validate dates
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('Nieprawidłowe daty');
      }
      
      // Set time
      const [startHours, startMinutes] = editedSession.start_time.split(':').map(Number);
      const [endHours, endMinutes] = editedSession.end_time.split(':').map(Number);
      
      startDate.setHours(startHours, startMinutes, 0, 0);
      endDate.setHours(endHours, endMinutes, 0, 0);
      
      // Check if end date is before start date
      if (endDate < startDate) {
        throw new Error('Data i czas zakończenia muszą być późniejsze niż data i czas rozpoczęcia');
      }
      
      console.log('Saving session with times:', {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
        duration: durationMinutes
      });
      
      // Update in database
      const { error: updateError } = await supabase
        .from('work_sessions')
        .update({
          job_order: editedSession.job_order,
          start_time: startDate.toISOString(),
          end_time: endDate.toISOString(),
          duration_minutes: durationMinutes,
          notes: editedSession.notes,
          manually_edited: true
        })
        .eq('id', session.id);
      
      if (updateError) {
        console.error('Database update error:', updateError);
        throw new Error(`Błąd bazy danych: ${updateError.message}`);
      }
      
      // Refresh data and close modal
      await fetchData();
      setShowEditModal(false);
      Alert.alert('Sukces', 'Czas pracy został zaktualizowany');
    } catch (error: any) {
      console.error('Error saving session:', error);
      setSaveError(error.message || 'Wystąpił błąd podczas zapisywania zmian');
    } finally {
      setSaving(false);
    }
  };

  const handleWorkSessionSelect = (newSessionId: string, date: string, employeeId: string, employeeName: string) => {
    // Jeśli kliknięto w tę samą sesję, nie rób nic
    if (newSessionId === sessionId) return;
    
    // Wywołaj callback do nawigacji do nowej sesji
    if (onSessionSelect) {
      onSessionSelect(newSessionId, date, employeeId, employeeName);
    }
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.topRow}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.pageTitle}>{`${i18n.t('workSessionDetails')} ${formatDateForDisplay(date)}`}</Text>
        <TouchableOpacity style={styles.editButton} onPress={handleEditSession}>
          <Ionicons name="create-outline" size={18} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        {renderHeader()}
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text style={styles.loadingText}>{i18n.t('loading')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.safeArea}>
        {renderHeader()}
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#DC2626" />
          <Text style={styles.errorText}>{i18n.t('error')}: {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchData}>
            <Text style={styles.retryButtonText}>{i18n.t('apply')}</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!session) {
    return (
      <SafeAreaView style={styles.safeArea}>
        {renderHeader()}
        <View style={styles.emptyContainer}>
          <Ionicons name="information-circle-outline" size={48} color="#6B7280" />
          <Text style={styles.emptyText}>{i18n.t('noRecordsFound')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      {renderHeader()}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView style={styles.container}>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.content}>
              {/* Manual Entry Info */}
              {(session.manually_edited || session.added_manually) && (
                <View style={styles.manualInfoBox}>
                  <Ionicons name="create-outline" size={18} color="#8B5CF6" />
                  <Text style={styles.manualInfoText}>
                    {session.added_manually ? i18n.t('manuallyAddedWorkTime') : i18n.t('manuallyEditedWorkTime')}
                  </Text>
                </View>
              )}

              {/* Selected Session Details Card */}
              <View style={styles.card}>
                <Text style={styles.cardTitle}>{i18n.t('selectedSessionDetails')}</Text>
            
                <View style={styles.infoRow}>
                  <Ionicons name="document-text-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('jobOrderLabel')}</Text>
                  {session.task_id && session.task ? (
                    <TouchableOpacity onPress={() => onTaskSelect && onTaskSelect(session.task_id!)}>
                      <Text style={[styles.infoValue, styles.linkText]}>{session.job_order || session.task.client_name || 'N/A'}</Text>
                    </TouchableOpacity>
                  ) : (
                    <Text style={styles.infoValue}>{session.job_order || i18n.t('noData')}</Text>
                  )}
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="calendar-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('startDateLabel')}</Text>
                  <Text style={styles.infoValue}>{formatDateForDisplay(new Date(session.start_time).toISOString().split('T')[0])}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="time-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('startTimeLabel')}</Text>
                  <Text style={styles.infoValue}>{formatDateTime(session.start_time)}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="calendar-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('endDateLabel')}</Text>
                  <Text style={styles.infoValue}>{formatDateForDisplay(new Date(session.end_time).toISOString().split('T')[0])}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="time-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('endTimeLabel')}</Text>
                  <Text style={styles.infoValue}>{formatDateTime(session.end_time)}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="hourglass-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('workTimeLabel')}</Text>
                  <Text style={styles.infoValue}>{formatDuration(session.duration_minutes)}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="location-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('startLocationLabel')}</Text>
                  <LocationDisplay location={session.start_location} />
                </View>
                <View style={styles.infoRow}>
                  <Ionicons name="location-outline" size={18} color="#6B7280" style={styles.icon} />
                  <Text style={styles.infoLabel}>{i18n.t('endLocationLabel')}</Text>
                  <LocationDisplay location={session.end_location} />
                </View>
                {session.notes && (
                  <View style={styles.infoRow}>
                    <Ionicons name="reader-outline" size={18} color="#6B7280" style={styles.icon} />
                    <Text style={styles.infoLabel}>{i18n.t('notesLabel')}</Text>
                    <Text style={styles.infoValue}>{session.notes}</Text>
                  </View>
                )}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      </KeyboardAvoidingView>
      
      {/* Edit Session Modal - Fixed for web version */}
      <Modal
        visible={showEditModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => {
          Keyboard.dismiss();
          setShowEditModal(false);
        }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{i18n.t('editWorkSession')}</Text>
              <TouchableOpacity 
                    onPress={() => {
                      Keyboard.dismiss();
                      setShowEditModal(false);
                    }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <ScrollView 
              style={styles.modalBody}
                  keyboardShouldPersistTaps="handled"
                  keyboardDismissMode="on-drag"
            >
              <View style={styles.editFieldContainer}>
                <Text style={styles.editLabel}>{i18n.t('dateLabel')}</Text>
                <DatePicker
                  date={editedSession.start_date}
                  onDateChange={(newDate) => setEditedSession({ ...editedSession, start_date: newDate })}
                  placeholder={i18n.t('selectDate')}
                />
              </View>
              <View style={styles.editFieldContainer}>
                <Text style={styles.editLabel}>{i18n.t('endDateLabel')}</Text>
                <DatePicker
                  date={editedSession.end_date}
                  onDateChange={(newDate) => setEditedSession({ ...editedSession, end_date: newDate })}
                  placeholder={i18n.t('selectDate')}
                />
              </View>
              <View style={styles.editFieldContainer}>
                <Text style={styles.editLabel}>{i18n.t('startTimeInputLabel')}</Text>
                <TextInput
                  style={[styles.editInput, Platform.OS === 'web' && styles.webTextInput]}
                  value={editedSession.start_time}
                  onChangeText={(time) => {
                    setEditedSession({ ...editedSession, start_time: time });
                  }}
                  placeholder="HH:MM"
                  keyboardType={Platform.OS === 'web' ? 'default' : 'numeric'}
                  maxLength={5}
                      onBlur={() => {
                        if (Platform.OS === 'ios') {
                          Keyboard.dismiss();
                        }
                      }}
                      returnKeyType="done"
                      onSubmitEditing={Keyboard.dismiss}
                />
              </View>
              <View style={styles.editFieldContainer}>
                <Text style={styles.editLabel}>{i18n.t('endTimeInputLabel')}</Text>
                <TextInput
                  style={[styles.editInput, Platform.OS === 'web' && styles.webTextInput]}
                  value={editedSession.end_time}
                  onChangeText={(time) => {
                    setEditedSession({ ...editedSession, end_time: time });
                  }}
                  placeholder="HH:MM"
                  keyboardType={Platform.OS === 'web' ? 'default' : 'numeric'}
                  maxLength={5}
                      onBlur={() => {
                        if (Platform.OS === 'ios') {
                          Keyboard.dismiss();
                        }
                      }}
                      returnKeyType="done"
                      onSubmitEditing={Keyboard.dismiss}
                />
              </View>
              <View style={styles.editFieldContainer}>
                <Text style={styles.editLabel}>{i18n.t('jobOrderInputLabel')}</Text>
                <TextInput
                  style={[styles.editInput, Platform.OS === 'web' && styles.webTextInput]}
                  value={editedSession.job_order}
                  onChangeText={(text) => setEditedSession({ ...editedSession, job_order: text })}
                  placeholder={i18n.t('enterDescription')}
                      onBlur={() => {
                        if (Platform.OS === 'ios') {
                          Keyboard.dismiss();
                        }
                      }}
                      returnKeyType="done"
                      onSubmitEditing={Keyboard.dismiss}
                />
              </View>
              <View style={styles.editFieldContainer}>
                <Text style={styles.editLabel}>{i18n.t('notesInputLabel')}</Text>
                <TextInput
                  style={[styles.editInput, styles.textArea, Platform.OS === 'web' && styles.webTextInput]}
                  value={editedSession.notes}
                  onChangeText={(text) => setEditedSession({ ...editedSession, notes: text })}
                  placeholder={i18n.t('additionalNotes')}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                      onBlur={() => {
                        if (Platform.OS === 'ios') {
                          Keyboard.dismiss();
                        }
                      }}
                      blurOnSubmit={true}
                />
              </View>
              {calculatedDuration !== null && (
                <Text style={styles.calculatedDuration}>{`${i18n.t('calculatedWorkTime')} ${formatDuration(calculatedDuration)}`}</Text>
              )}
            </ScrollView>
            <View style={styles.modalFooter}>
              <TouchableOpacity 
                style={styles.cancelButton}
                    onPress={() => {
                      Keyboard.dismiss();
                      setShowEditModal(false);
                    }}
                disabled={saving}
              >
                <Text style={styles.cancelButtonText}>{i18n.t('cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.saveButton, saving && styles.disabledButton]}
                    onPress={() => {
                      Keyboard.dismiss();
                      handleSaveSession();
                    }}
                disabled={saving}
              >
                {saving ? (
                  <ActivityIndicator color="#FFFFFF" size="small" />
                ) : (
                  <Text style={styles.saveButtonText}>{i18n.t('save')}</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
            </TouchableWithoutFeedback>
        </View>
        </TouchableWithoutFeedback>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Changed from '#F3F4F6' to white
  },
  headerContainer: {
    backgroundColor: '#FFFFFF', // White background for header area
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12, // Space between back button and title row
    display: 'none', // Hide old back button
  },
  backButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '500',
    display: 'none', // Hide back button text
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    display: 'none', // Hide old title row
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#2563EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    display: 'none', // Hide edit button text
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Add white background
  },
  content: {
    padding: 16,
    backgroundColor: '#FFFFFF', // Add white background
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 12,
  },
  manualInfoBox: {
    backgroundColor: '#F3E8FF', // Light purple
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  manualInfoText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6B21A8', // Darker purple
    fontWeight: '500',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  icon: {
    marginRight: 10,
    marginTop: 2, // Align icon slightly better with text
    width: 20, 
  },
  infoLabel: {
    fontSize: 14,
    color: '#4B5563',
    width: 140, // Fixed width for labels
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1, // Allow value to take remaining space
    textAlign: 'right',
  },
  linkText: {
    color: '#2563EB',
    textDecorationLine: 'underline',
  },
  sessionItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  sessionTime: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  sessionDuration: {
    fontSize: 13,
    color: '#6B7280',
  },
  activityItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  activityTaskName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    flex: 1,
    marginRight: 8,
  },
  activityStatus: {
    fontSize: 12,
    color: '#6B7280',
    backgroundColor: '#E5E7EB',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    overflow: 'hidden',
  },
  activityTime: {
    fontSize: 13,
    color: '#6B7280',
    marginTop: 2,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '100%',
    maxWidth: 500,
    maxHeight: Platform.OS === 'web' ? '90%' : '80%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    paddingBottom: 16,
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalBody: {
    marginBottom: 16,
  },
  editFieldContainer: {
    marginBottom: 16,
  },
  editLabel: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 6,
    fontWeight: '500',
  },
  editInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  webTextInput: {
    outlineWidth: 0,
    outlineColor: 'transparent',
  },
  calculatedDuration: {
    fontSize: 14,
    color: '#4B5563',
    marginTop: 8,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 16,
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginRight: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#4B5563',
    fontWeight: '500',
  },
  saveButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 80,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default WorkSessionDetails; 