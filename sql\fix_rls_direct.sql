-- Ten skrypt naprawia problem z polityką RLS dla tabeli companies
-- Wykonaj ten skrypt w konsoli SQL Supabase

-- Sprawdź istniejące polityki RLS dla tabeli companies
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'companies';

-- Upewnij się, że RLS jest włączone dla tabeli companies
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

-- Dodaj politykę umożliwiającą aktualizację przez serwis
-- Ta polityka NIE używa kolumny user_id, która nie istnieje w tabeli companies
CREATE POLICY "Service role can update companies" 
  ON companies FOR UPDATE 
  USING (true)
  WITH CHECK (true);

-- Alternatywnie, jeśli chcesz ograniczyć politykę tylko do roli serwisowej:
-- CREATE POLICY "Service role can update companies" 
--   ON companies FOR UPDATE 
--   TO service_role
--   USING (true)
--   WITH CHECK (true);

-- <PERSON><PERSON><PERSON><PERSON><PERSON>, czy polityka została dodana
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'companies';

-- Ręcznie zaktualizuj firmę z ID z obrazka (repap) na plan Basic
UPDATE companies
SET account_type = 'Basic',
    verification_code_limit = 10
WHERE id = '90c35057-24c0-432e-8d06-68ae46ce3979';

-- Sprawdź wyniki po aktualizacji
SELECT id, name, account_type, verification_code_limit
FROM companies
WHERE id = '90c35057-24c0-432e-8d06-68ae46ce3979'; 