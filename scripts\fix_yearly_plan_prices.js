const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixYearlyPlanPrices() {
  try {
    console.log('=== Fixing Yearly Plan Prices ===\n');
    
    // Pobierz wszystkie plany
    const { data: allPlans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    if (plansError) {
      console.error('Error fetching plans:', plansError);
      return;
    }

    const monthlyPlans = allPlans.filter(p => p.billing_period === 'monthly');
    const yearlyPlans = allPlans.filter(p => p.billing_period === 'yearly');

    console.log('Current pricing analysis:');
    
    // Oblicz nowe ceny z 20% rabatem
    const priceUpdates = [];
    
    for (const monthlyPlan of monthlyPlans) {
      const yearlyEquivalent = yearlyPlans.find(y => 
        y.name.toLowerCase().includes(monthlyPlan.name.toLowerCase())
      );
      
      if (yearlyEquivalent) {
        const monthlyYearlyCost = monthlyPlan.price * 12;
        const currentYearlyCost = yearlyEquivalent.price;
        const suggestedYearlyCost = Math.round(monthlyYearlyCost * 0.8); // 20% rabat
        const savings = monthlyYearlyCost - suggestedYearlyCost;
        const savingsPercent = Math.round((savings / monthlyYearlyCost) * 100);
        
        console.log(`\n${monthlyPlan.name}:`);
        console.log(`  Monthly: ${monthlyPlan.price/100} PLN/month`);
        console.log(`  Monthly × 12: ${monthlyYearlyCost/100} PLN/year`);
        console.log(`  Current yearly: ${currentYearlyCost/100} PLN/year`);
        console.log(`  Suggested yearly: ${suggestedYearlyCost/100} PLN/year`);
        console.log(`  Savings: ${savings/100} PLN (${savingsPercent}%)`);
        
        if (currentYearlyCost !== suggestedYearlyCost) {
          priceUpdates.push({
            id: yearlyEquivalent.id,
            name: yearlyEquivalent.name,
            currentPrice: currentYearlyCost,
            newPrice: suggestedYearlyCost
          });
        }
      }
    }

    if (priceUpdates.length === 0) {
      console.log('\n✅ All yearly plans already have correct pricing!');
      return;
    }

    console.log(`\n=== Updating ${priceUpdates.length} yearly plans ===`);
    
    for (const update of priceUpdates) {
      console.log(`\nUpdating ${update.name}:`);
      console.log(`  From: ${update.currentPrice/100} PLN`);
      console.log(`  To: ${update.newPrice/100} PLN`);
      
      const { error: updateError } = await supabase
        .from('subscription_plans')
        .update({ price: update.newPrice })
        .eq('id', update.id);

      if (updateError) {
        console.log(`❌ Error updating ${update.name}: ${updateError.message}`);
      } else {
        console.log(`✅ Updated ${update.name} successfully`);
      }
    }

    // Sprawdź zaktualizowane ceny
    console.log('\n=== Verification ===');
    
    const { data: updatedPlans, error: verifyError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    if (verifyError) {
      console.error('Error verifying updates:', verifyError);
      return;
    }

    const updatedMonthlyPlans = updatedPlans.filter(p => p.billing_period === 'monthly');
    const updatedYearlyPlans = updatedPlans.filter(p => p.billing_period === 'yearly');

    console.log('\nUpdated pricing:');
    
    for (const monthlyPlan of updatedMonthlyPlans) {
      const yearlyEquivalent = updatedYearlyPlans.find(y => 
        y.name.toLowerCase().includes(monthlyPlan.name.toLowerCase())
      );
      
      if (yearlyEquivalent) {
        const monthlyYearlyCost = monthlyPlan.price * 12;
        const actualYearlyCost = yearlyEquivalent.price;
        const savings = monthlyYearlyCost - actualYearlyCost;
        const savingsPercent = Math.round((savings / monthlyYearlyCost) * 100);
        
        console.log(`\n${monthlyPlan.name}:`);
        console.log(`  Monthly: ${monthlyPlan.price/100} PLN/month (${monthlyYearlyCost/100} PLN/year)`);
        console.log(`  Yearly: ${actualYearlyCost/100} PLN/year`);
        
        if (savings > 0) {
          console.log(`  ✅ Savings: ${savings/100} PLN (${savingsPercent}%)`);
        } else {
          console.log(`  ❌ Still more expensive: ${Math.abs(savings)/100} PLN`);
        }
      }
    }

    console.log('\n🎉 Yearly plan pricing has been fixed!');
    console.log('\n📝 Important: You also need to update the prices in Stripe Dashboard');
    console.log('to match these new prices, or create new Price objects with these amounts.');

  } catch (error) {
    console.error('Error in fixYearlyPlanPrices:', error);
  }
}

fixYearlyPlanPrices();
