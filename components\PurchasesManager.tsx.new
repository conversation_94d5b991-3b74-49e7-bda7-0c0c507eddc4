import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ActivityIndicator, TextInput, Switch, Modal, Alert, Platform, KeyboardAvoidingView, Image, ScrollView, Dimensions, Pressable, RefreshControl, ToastAndroid, useWindowDimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import * as ImagePicker from 'expo-image-picker';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { i18n } from '../utils/localization';
import FilterDrawer from './FilterDrawer';
import { TopBar } from './TopBar';
import DatePicker from './DatePicker';
import { Buffer } from 'buffer'; // Dodajemy import dla Buffer

// Definicje typów
interface PurchasesManagerProps {
  companyId: string;
  activeTab: 'add' | 'list';
  userType: 'company' | 'employee' | 'coordinator';
  userId: string;
  onTabChange: (tab: 'add' | 'list') => void;
  onMenuPress?: () => void;
  onLogoPress?: () => void;
  onPurchaseSelect?: (purchase: Purchase) => void;
  isLargeScreen?: boolean;
}

interface Purchase {
  id: string;
  title: string;
  description: string;
  category: string;
  price_estimate: number;
  actual_price?: number;
  status: 'pending' | 'approved' | 'ordered' | 'delivered' | 'canceled';
  requested_by: string;
  requested_by_name: string;
  requested_at: string;
  approved_by?: string;
  approved_by_name?: string;
  approved_at?: string;
  ordered_at?: string;
  delivered_at?: string;
  canceled_at?: string;
  canceled_by?: string;
  canceled_by_name?: string;
  supplier?: string;
  invoice_number?: string;
  invoice_date?: string;
  warranty_end_date?: string;
  attachments?: any[];
  location?: string;
  notes?: string;
  priority: string;
  company_id: string;
}

interface PurchaseForm {
  title: string;
  description: string;
  category?: string; // Pole opcjonalne - można je pominąć
  price_estimate: string;
  quantity: string; // Nowe pole - ilość
  priority: 'low' | 'medium' | 'high' | 'critical'; // Add 'critical' option
  notes: string;
  attachments: string[]; // Zachowujemy dla kompatybilności wstecznej, ale będziemy używać jako photos
}

type SortField = 'date' | 'title' | 'category' | 'price' | 'status' | 'priority';
type SortOrder = 'asc' | 'desc';

// Near the top of the file where interfaces are defined, add a proper type for categoryData
interface CategoryData {
  name: string;
  company_id: string;
  description: string;
  is_active?: boolean;
}

export const PurchasesManager: React.FC<PurchasesManagerProps> = ({
  companyId,
  activeTab,
  userType,
  userId,
  onTabChange,
  onMenuPress,
  onLogoPress,
  onPurchaseSelect,
  isLargeScreen = false,
}) => {
  // State
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [filteredPurchases, setFilteredPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [filterStatus, setFilterStatus] = useState<string>('pending');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortField, setSortField] = useState<SortField>('priority');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [categories, setCategories] = useState<{id: string, name: string}[]>([]);
  const [showCategoryModal, setShowCategoryModal] = useState<boolean>(false);
  
  // Dodajemy state dla niestandardowego okna potwierdzenia
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  
  // Form state dla dodawania nowego zakupu
  const [form, setForm] = useState<PurchaseForm>({
    title: '',
    description: '',
    category: undefined,
    price_estimate: '',
    quantity: '',
    priority: 'medium', // Default priority
    notes: '',
    attachments: [],
  });

  // Dodajemy nowe stany dla filtra
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [filterButtonPosition, setFilterButtonPosition] = useState<{top: number; left: number; width: number}>({top: 0, left: 0, width: 0});
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterCategorySearch, setFilterCategorySearch] = useState<string>('');
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');

  // Dodajemy referencję do filtra
  const filterButtonRef = React.useRef<View>(null);

  // Dodajemy nowe stany dla dodawania nowej kategorii
  const [newCategoryName, setNewCategoryName] = useState<string>('');
  const [showAddCategoryForm, setShowAddCategoryForm] = useState<boolean>(false);

  // Dodajemy stan dla modalu wyświetlającego pełnowymiarowe zdjęcie
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showImageModal, setShowImageModal] = useState<boolean>(false);

  // Dodajemy nowy stan dla modala filtrowania daty
  const [showDateFilters, setShowDateFilters] = useState<boolean>(false);

  // Near the top of the component function, add a new state variable
  const [hiddenCategoryIds, setHiddenCategoryIds] = useState<string[]>([]);

  // Add userName state below the other state declarations (around line 100-120)
  const [userName, setUserName] = useState<string>('');

  // Add a function to fetch the user's name after fetchCategories()
  const fetchUserName = async () => {
    try {
      const { data, error } = await supabase
        .from('employees')
        .select('full_name')
        .eq('id', userId)
        .single();
      
      if (error) {
        console.error('Error fetching user name:', error);
        return;
      }
      
      if (data) {
        setUserName(data.full_name);
      }
    } catch (error) {
      console.error('Exception in fetchUserName:', error);
    }
  };

  // Add this useEffect after the state declarations
  React.useEffect(() => {
    console.log("🔄 EFFECT: hiddenCategoryIds changed:", hiddenCategoryIds);
  }, [hiddenCategoryIds]);

  React.useEffect(() => {
    console.log("🔄 EFFECT: categories changed, count:", categories.length);
  }, [categories]);

  // Efekt dla pobierania zakupów i kategorii
  useEffect(() => {
    if (companyId) {
      fetchPurchases();
      fetchCategories();
      checkCategoryPermissions(); // Dodanie sprawdzenia uprawnień
      fetchUserName(); // Add this line
    }
  }, [companyId]);

  // Efekt dla filtrowania i sortowania zakupów
  useEffect(() => {
    if (purchases.length > 0) {
      filterAndSortPurchases();
    }
  }, [purchases, filterStatus, searchQuery, sortField, sortOrder, filterPriority, filterCategorySearch, filterDateFrom, filterDateTo]);

  // Nowa funkcja do sprawdzania uprawnień do kategorii
  const checkCategoryPermissions = async () => {
    try {
      console.log("Sprawdzanie uprawnień do tabeli purchase_categories...");
      
      // Najpierw pobieramy informacje o sesji
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !sessionData?.session) {
        console.error("Problem z sesją użytkownika:", sessionError);
        return;
      }
      
      // Potem sprawdzamy, czy użytkownik może odczytać kategorie
      const { data: readData, error: readError } = await supabase
        .from('purchase_categories')
        .select('id')
        .limit(1);
        
      console.log("Test odczytu kategorii:", readError ? "Błąd" : "Sukces");
      if (readError) {
        console.error("Błąd odczytu kategorii:", readError);
      }
      
      // Sprawdzenie, czy tabela purchase_categories jest pusta
      const { count, error: countError } = await supabase
        .from('purchase_categories')
        .select('*', { count: 'exact', head: true })
        .eq('company_id', companyId);
      
      if (countError) {
        console.error("Błąd podczas sprawdzania liczby kategorii:", countError);
      } else {
        console.log(`Liczba kategorii dla firmy ${companyId}: ${count}`);
        
        // Jeśli firma nie ma kategorii, możemy rozważyć dodanie domyślnych
        if (count === 0 && userType === 'company') {
          console.log("Firma nie ma kategorii. Warto rozważyć dodanie domyślnych.");
        }
      }
    } catch (error) {
      console.error("Błąd podczas sprawdzania uprawnień kategorii:", error);
    }
  };

  // Funkcja do pobierania zakupów z Supabase
  const fetchPurchases = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('purchases')
        .select('*')
        .eq('company_id', companyId)
        .order('requested_at', { ascending: false });

      if (error) {
        console.error('Error fetching purchases:', error);
        Alert.alert(i18n.t('error'), i18n.t('purchasesFailedToSave'));
      } else {
        setPurchases(data || []);
      }
    } catch (error) {
      console.error('Error in fetchPurchases:', error);
      Alert.alert(i18n.t('error'), i18n.t('purchasesUnexpectedError'));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Funkcja do pobierania kategorii z Supabase
  const fetchCategories = async () => {
    setLoading(true);
    console.log("🔄 Rozpoczynam pobieranie kategorii dla firmy:", companyId);
    
    try {
      // Najpierw sprawdzamy, czy kolumna is_active istnieje
      const { data: columnCheckData, error: columnCheckError } = await supabase
        .from('purchase_categories')
        .select('*')
        .limit(1);
      
      const hasIsActiveColumn = columnCheckData && 
                             columnCheckData.length > 0 && 
                             'is_active' in columnCheckData[0];
      
      console.log("🔄 Czy tabela purchase_categories ma kolumnę is_active:", hasIsActiveColumn);
      
      // Budujemy zapytanie pobierające kategorie
      let query = supabase
        .from('purchase_categories')
        .select('*')
        .eq('company_id', companyId);
      
      // Na iOS zawsze pobieramy wszystkie kategorie, żeby rozwiązać problemy z wyświetlaniem
      const isIOS = Platform.OS === 'ios';
      
      // Jeśli to nie iOS i tabela ma kolumnę is_active, pobieramy tylko aktywne kategorie
      if (hasIsActiveColumn && !isIOS) {
        console.log("🔄 Dodaję filtr is_active=true do zapytania");
        query = query.eq('is_active', true);
        } else {
        console.log("🔄 Pomijam filtrowanie is_active - pobieramy wszystkie kategorie");
      }
      
      const { data: categoriesData, error: categoriesError } = await query;
      
      if (categoriesError) {
        console.error("❌ Błąd podczas pobierania kategorii:", categoriesError);
        Alert.alert("Błąd", "Nie udało się pobrać kategorii");
        return;
      }
      
      console.log(`🔄 Pobrano ${categoriesData?.length || 0} kategorii z bazy danych`);
      
      // Ustawiamy wszystkie pobrane kategorie
      if (isIOS) {
        console.log("📱 iOS - ustawiam kategorie bez dodatkowego filtrowania");
        console.log("📱 iOS - liczba kategorii przed ustawieniem:", categoriesData?.length);
        
        if (categoriesData && categoriesData.length > 0) {
          // Dodajemy wyraźne logowanie każdej kategorii na iOS
          console.log("📱 iOS KATEGORIE SZCZEGÓŁY:");
          categoriesData.forEach((cat, idx) => {
            console.log(`📱 Kategoria ${idx + 1}: ID=${cat.id}, Nazwa=${cat.name}`);
          });
        }
        
        setCategories(categoriesData || []);
        // Dodatkowe sprawdzenie
        setTimeout(() => {
          console.log("📱 iOS - SPRAWDZENIE PO 500ms: categories.length =", categories.length);
        }, 500);
        } else {
        // Na innych platformach możemy filtrować problematyczną kategorię
      const problemCategoryId = "69c1dd83-69a0-4b41-88fb-0b685892173b";
      const filteredCategories = categoriesData?.filter(cat => cat.id !== problemCategoryId) || [];
      
      console.log(`🔄 Ostateczna liczba kategorii po odfiltrowaniu problematycznej: ${filteredCategories.length}`);
      
      if (filteredCategories.length !== (categoriesData?.length || 0)) {
        console.log("🔄 Odfiltrowano problematyczną kategorię Narzędzia");
      }
      
      setCategories(filteredCategories);
      }
      
    } catch (error) {
      console.error("❌ Nieoczekiwany błąd podczas pobierania kategorii:", error);
      Alert.alert("Błąd", "Wystąpił nieoczekiwany problem");
      return;
    } finally {
      setLoading(false);
    }
  };

  // Funkcja do filtrowania i sortowania zakupów
  const filterAndSortPurchases = () => {
    let filtered = [...purchases];

    // Filtrowanie według statusu
    if (filterStatus !== 'all') {
      filtered = filtered.filter(purchase => purchase.status === filterStatus);
    }

    // Filtrowanie według priorytetu
    if (filterPriority !== 'all') {
      filtered = filtered.filter(purchase => purchase.priority === filterPriority);
    }

    // Filtrowanie według kategorii
    if (filterCategorySearch.trim() !== '') {
      const categoryQuery = filterCategorySearch.toLowerCase().trim();
      filtered = filtered.filter(purchase => 
        purchase.category?.toLowerCase().includes(categoryQuery)
      );
    }

    // Filtrowanie według zapytania wyszukiwania
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(purchase => 
        purchase.title.toLowerCase().includes(query) || 
        purchase.description?.toLowerCase().includes(query) ||
        purchase.requested_by_name?.toLowerCase().includes(query)
      );
    }

    // Filtrowanie według daty - od
    if (filterDateFrom) {
      try {
        const fromDate = new Date(filterDateFrom);
        fromDate.setHours(0, 0, 0, 0);
        filtered = filtered.filter(purchase => {
          const purchaseDate = new Date(purchase.requested_at);
          return purchaseDate >= fromDate;
        });
      } catch (e) {
        console.error('Błąd filtrowania po dacie początkowej:', e);
      }
    }

    // Filtrowanie według daty - do
    if (filterDateTo) {
      try {
        const toDate = new Date(filterDateTo);
        toDate.setHours(23, 59, 59, 999);
        filtered = filtered.filter(purchase => {
          const purchaseDate = new Date(purchase.requested_at);
          return purchaseDate <= toDate;
        });
      } catch (e) {
        console.error('Błąd filtrowania po dacie końcowej:', e);
      }
    }

    // Sortowanie
    filtered.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortField) {
        case 'date':
          valueA = new Date(a.requested_at).getTime();
          valueB = new Date(b.requested_at).getTime();
          break;
        case 'title':
          valueA = a.title.toLowerCase();
          valueB = b.title.toLowerCase();
          break;
        case 'category':
          valueA = a.category?.toLowerCase() || '';
          valueB = b.category?.toLowerCase() || '';
          break;
        case 'price':
          valueA = a.price_estimate || 0;
          valueB = b.price_estimate || 0;
          break;
        case 'status':
          valueA = a.status;
          valueB = b.status;
          break;
        case 'priority':
          // Konwersja priorytetu na wartość liczbową dla prawidłowego sortowania
          valueA = getPriorityValue(a.priority || 'medium');
          valueB = getPriorityValue(b.priority || 'medium');
          break;
        default:
          valueA = new Date(a.requested_at).getTime();
          valueB = new Date(b.requested_at).getTime();
      }

      const result = typeof valueA === 'string' 
        ? valueA.localeCompare(valueB as string) 
        : (valueA as number) - (valueB as number);

      return sortOrder === 'asc' ? result : -result;
    });

    setFilteredPurchases(filtered);
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchPurchases();
  };

  // Funkcja pomocnicza do formatowania daty
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear().toString().slice(-2)}`;
  };

  // Funkcja pomocnicza do formatowania ceny
  const formatPrice = (price?: number) => {
    if (!price && price !== 0) return '-';
    return price.toLocaleString('pl-PL', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' zł';
  };

  // Funkcja pomocnicza do kolorowania statusu
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#F59E0B'; // Amber
      case 'approved': return '#3B82F6'; // Blue
      case 'ordered': return '#8B5CF6'; // Purple
      case 'delivered': return '#10B981'; // Green
      case 'canceled': return '#6B7280'; // Gray
      default: return '#F59E0B';
    }
  };

  // Function to render priority text with proper translation
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low':
        return i18n.t('priorityLow');
      case 'medium':
        return i18n.t('priorityMedium');
      case 'high':
        return i18n.t('priorityHigh');
      case 'critical':
        return i18n.t('priorityCritical');
      default:
        return i18n.t('priorityMedium');
    }
  };

  // Function to render status text with proper translation
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return i18n.t('purchaseStatusPending');
      case 'approved':
        return i18n.t('purchaseStatusApproved');
      case 'ordered':
        return i18n.t('purchaseStatusOrdered');
      case 'delivered':
        return i18n.t('purchaseStatusDelivered');
      case 'canceled':
        return i18n.t('purchaseStatusCanceled');
      default:
        return i18n.t('unknown');
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return '#10B981'; // Green
      case 'medium': return '#3B82F6'; // Blue
      case 'high': return '#F59E0B'; // Amber
      case 'critical': return '#EF4444'; // Red
      default: return '#3B82F6'; // Blue
    }
  };

  const handleMenuPress = () => {
    if (onMenuPress) {
      onMenuPress();
    } else {
      console.log('Menu pressed, but no handler provided');
    }
  };

  const handleLogoPress = () => {
    if (onLogoPress) {
      onLogoPress();
    } else {
      console.log('Logo pressed, but no handler provided');
    }
  };

  const renderTopBar = () => (
    // TopBar is removed completely as the Dashboard will provide the TopBar
    null
  );

  const renderPurchaseList = () => {
    if (loading) {
      return (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text style={styles.loaderText}>{i18n.t('purchasesLoadingPurchases')}</Text>
        </View>
      );
    }

    if (filteredPurchases.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="cart-outline" size={64} color="#D1D5DB" />
          <Text style={styles.emptyText}>{i18n.t('purchasesNoPurchases')}</Text>
        </View>
      );
    }

    return (
      <ScrollView 
        style={styles.purchasesList}
        contentContainerStyle={styles.purchasesListContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={true}
      >
        <View style={styles.tableContainer}>
          <View style={styles.tableHeader}>
            <TouchableOpacity 
              style={[styles.headerCell, { flex: 0.8 }]}
              onPress={() => handleSort('date')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('purchasesDate')}</Text>
                {sortField === 'date' && (
                  <Ionicons 
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                    size={14} 
                    color="#4B5563" 
                    style={styles.sortIcon} 
                  />
                )}
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.headerCell, { flex: 2 }]}
              onPress={() => handleSort('title')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('purchasesTitle')}</Text>
                {sortField === 'title' && (
                  <Ionicons 
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                    size={14} 
                    color="#4B5563" 
                    style={styles.sortIcon} 
                  />
                )}
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.headerCell, { flex: 1 }]}
              onPress={() => handleSort('priority')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('purchasesPriority')}</Text>
                {sortField === 'priority' && (
                  <Ionicons 
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                    size={14} 
                    color="#4B5563" 
                    style={styles.sortIcon} 
                  />
                )}
              </View>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.headerCell, { flex: 1 }]}
              onPress={() => handleSort('status')}
            >
              <View style={styles.headerContent}>
                <Text style={styles.headerText}>{i18n.t('purchasesStatus')}</Text>
                {sortField === 'status' && (
                  <Ionicons 
                    name={sortOrder === 'asc' ? 'chevron-up' : 'chevron-down'} 
                    size={14} 
                    color="#4B5563" 
                    style={styles.sortIcon} 
                  />
                )}
              </View>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.tableBody}>
            {filteredPurchases.map((purchase) => (
              <TouchableOpacity 
                key={purchase.id} 
                style={styles.tableRow}
                onPress={() => onPurchaseSelect && onPurchaseSelect(purchase)}
              >
                <Text style={[styles.cell, { flex: 0.8 }]}>
                  {formatDate(purchase.requested_at)}
                </Text>
                <Text style={[styles.cell, { flex: 2 }]} numberOfLines={1} ellipsizeMode="tail">
                  {purchase.title}
                </Text>
                <View style={[styles.statusContainer, { flex: 1 }]}>
                  <Text style={[styles.statusBadge, getPriorityStyle(purchase.priority)]}>
                    {getPriorityText(purchase.priority)}
                  </Text>
                </View>
                <View style={[styles.statusContainer, { flex: 1 }]}>
                  <Text style={[styles.statusBadge, getStatusStyle(purchase.status)]}>
                    {getStatusText(purchase.status)}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    );
  };

  // Dodajemy funkcje pomocnicze do stylizacji statusów i priorytetów
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'pending': return styles.statusPending;
      case 'approved': return styles.statusApproved;
      case 'ordered': return styles.statusOrdered;
      case 'delivered': return styles.statusDelivered;
      case 'canceled': return styles.statusCanceled;
      default: return {};
    }
  };

  const getPriorityStyle = (priority: string) => {
    switch (priority) {
      case 'low': return styles.priorityLow;
      case 'medium': return styles.priorityMedium;
      case 'high': return styles.priorityHigh;
      case 'critical': return styles.priorityCritical;
      default: return {};
    }
  };

  // Renderowanie sortowania
  const renderSorting = () => (
    <View style={styles.sortingContainer}>
      <Text style={styles.sortingLabel}>{i18n.t('purchasesSortBy')}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.sortingButtons}>
        <TouchableOpacity 
          style={[
            styles.sortButton,
            sortField === 'date' && styles.sortButtonActive
          ]}
          onPress={() => handleSort('date')}
        >
          <Text
            style={[
              styles.sortButtonText,
              sortField === 'date' && styles.sortButtonTextActive
            ]}
          >
            {i18n.t('purchasesSortByDate')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.sortButton,
            sortField === 'title' && styles.sortButtonActive
          ]}
          onPress={() => handleSort('title')}
        >
          <Text
            style={[
              styles.sortButtonText,
              sortField === 'title' && styles.sortButtonTextActive
            ]}
          >
            {i18n.t('purchasesSortByTitle')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.sortButton,
            sortField === 'category' && styles.sortButtonActive
          ]}
          onPress={() => handleSort('category')}
        >
          <Text
            style={[
              styles.sortButtonText,
              sortField === 'category' && styles.sortButtonTextActive
            ]}
          >
            {i18n.t('purchasesSortByCategory')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.sortButton,
            sortField === 'price' && styles.sortButtonActive
          ]}
          onPress={() => handleSort('price')}
        >
          <Text
            style={[
              styles.sortButtonText,
              sortField === 'price' && styles.sortButtonTextActive
            ]}
          >
            {i18n.t('purchasesSortByPrice')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.sortButton,
            sortField === 'status' && styles.sortButtonActive
          ]}
          onPress={() => handleSort('status')}
        >
          <Text
            style={[
              styles.sortButtonText,
              sortField === 'status' && styles.sortButtonTextActive
            ]}
          >
            {i18n.t('purchasesSortByStatus')}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.sortButton,
            sortField === 'priority' && styles.sortButtonActive
          ]}
          onPress={() => handleSort('priority')}
        >
          <Text
            style={[
              styles.sortButtonText,
              sortField === 'priority' && styles.sortButtonTextActive
            ]}
          >
            {i18n.t('purchasesSortByPriority')}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  // Update the bottom tabs
  const renderBottomTabs = () => (
    <View style={styles.bottomTabs}>
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'add' && styles.activeTab,
        ]}
        onPress={() => onTabChange('add')}
      >
        <Ionicons
          name="add-circle-outline"
          size={24}
          color={activeTab === 'add' ? '#2563EB' : '#6B7280'}
        />
        <Text
          style={[
            styles.tabText,
            activeTab === 'add' && styles.activeTabText,
          ]}
        >
          {i18n.t('purchasesAddPurchase')}
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.tab,
          activeTab === 'list' && styles.activeTab,
        ]}
        onPress={() => onTabChange('list')}
      >
        <Ionicons
          name="list-outline"
          size={24}
          color={activeTab === 'list' ? '#2563EB' : '#6B7280'}
        />
        <Text
          style={[
            styles.tabText,
            activeTab === 'list' && styles.activeTabText,
          ]}
        >
          {i18n.t('purchasesPurchasesList')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Funkcja do dodawania nowej kategorii
  const createCategory = async () => {
    try {
      // Sprawdź czy nazwa kategorii nie jest pusta
      if (!newCategoryName.trim()) {
        Alert.alert("Błąd", "Nazwa kategorii nie może być pusta");
        return;
      }

      console.log(`Próba dodania kategorii: ${newCategoryName} dla firmy: ${companyId}`);
      
      let targetCompanyId = companyId;
      
      // Jeśli nie mamy companyId (np. dla pracownika), próbujemy go pobrać z bazy
      if (!targetCompanyId) {
        console.log("Brak ID firmy, próbuję pobrać dla użytkownika:", userId);
        
        const { data: employeeData, error: employeeError } = await supabase
          .from('employees')
          .select('company_id')
          .eq('user_id', userId)
          .single();
        
        if (employeeError) {
          console.error("Błąd pobierania ID firmy:", employeeError);
          // Zamiast przerywać, tworzymy kategorię tymczasową
          createTempCategory();
          return;
        }
        
        if (employeeData) {
          targetCompanyId = employeeData.company_id;
          console.log("Pobrano ID firmy:", targetCompanyId);
        } else {
          console.error("Nie znaleziono firmy dla użytkownika");
          createTempCategory();
          return;
        }
      }
      
      if (!targetCompanyId) {
        console.error("Nie można określić ID firmy - tworzę kategorię tymczasową");
        createTempCategory();
        return;
      }
      
      // Próba dodania kategorii do bazy danych
      const categoryData: CategoryData = { 
        name: newCategoryName.trim(),
        company_id: targetCompanyId,
        description: '' // Simplified as newCategoryDescription doesn't seem to be defined
      };
      
      // Sprawdź najpierw, czy tabela ma kolumnę is_active
      const { data: sampleCategory, error: sampleError } = await supabase
        .from('purchase_categories')
        .select('*')
        .limit(1)
        .single();
      
      const hasIsActiveColumn = sampleCategory && 'is_active' in sampleCategory;
      console.log("Czy tabela ma kolumnę is_active:", hasIsActiveColumn);
      
      // Dodajemy pole is_active jeśli istnieje w tabeli
      if (hasIsActiveColumn) {
        (categoryData as any).is_active = true;
      }
      
      const { data, error } = await supabase
        .from('purchase_categories')
        .insert([categoryData])
        .select()
        .single();
      
      if (error) {
        console.error("Błąd dodawania kategorii:", error);
        
        // Jeśli błąd dotyczy uprawnień
        if (error.code === '42501' || error.message.includes('permission denied') || error.message.includes('violates row-level security policy')) {
          console.log("Problem z uprawnieniami - tworzę kategorię tymczasową");
          createTempCategory();
          return;
        }
        
        // Inny rodzaj błędu
        Alert.alert("Błąd", `Nie udało się dodać kategorii: ${error.message}`);
        return;
      }
      
      console.log("Kategoria dodana pomyślnie:", data);
      
      // Odświeżenie listy kategorii
      fetchCategories();
      
      // Aktualizacja formularza, aby używał nowej kategorii
      setForm(prevForm => ({
        ...prevForm,
        category: newCategoryName
      }));
      
      // Zamknięcie modala i wyczyszczenie pola
      setShowAddCategoryForm(false);
      setNewCategoryName('');
      
    } catch (error) {
      console.error("Nieoczekiwany błąd podczas dodawania kategorii:", error);
      Alert.alert("Błąd", "Wystąpił nieoczekiwany problem podczas dodawania kategorii");
      // W przypadku nieoczekiwanego błędu, również tworzymy kategorię tymczasową
      createTempCategory();
    }
  };
  
  // Pomocnicza funkcja do tworzenia kategorii tymczasowej
  const createTempCategory = () => {
    const tempId = 'temp-' + new Date().getTime();
    const categoryName = newCategoryName.trim();
    
    console.log("Tworzenie kategorii tymczasowej:", categoryName, "z ID:", tempId);
    
    // Dodaj kategorię tymczasowo do lokalnej listy
    const newTempCategory = { id: tempId, name: categoryName };
    setCategories(prev => [...prev, newTempCategory]);
    
    // Ustaw aktualnie wybraną kategorię
    setForm({...form, category: categoryName});
    
    // Zamknij formularz i powiadom użytkownika
    setNewCategoryName('');
    setShowAddCategoryForm(false);
    
    Alert.alert(
      "Kategoria tymczasowa", 
      `Kategoria "${categoryName}" została dodana tymczasowo. Będzie dostępna tylko w tym formularzu.`,
      [{ text: "OK" }]
    );
  };

  // Replace saveHiddenCategories with a dummy function that does nothing
  const saveHiddenCategories = (ids: string[]) => {
    console.log("📌 [DEPRECATED] saveHiddenCategories called with:", ids);
    // This function is a no-op now
  };

  // Replace loadHiddenCategories with a dummy function that does nothing
  const loadHiddenCategories = async () => {
    console.log("📌 [DEPRECATED] loadHiddenCategories called");
    // This function is a no-op now
  };

  // Add useEffect to load hidden categories on component mount
  React.useEffect(() => {
    if (companyId) {
      console.log("🔄 Loading hidden categories for company:", companyId);
      loadHiddenCategories();
    }
  }, [companyId]);

  // Add this function near debugCategory or other debug functions
  const debugCategoryUpdate = async (categoryId: string) => {
    console.log("🔬 DEBUG: Próba bezpośredniej aktualizacji is_active dla kategorii:", categoryId);
    
    try {
      // Test 1: Sprawdź, czy kategoria istnieje
      const { data: category, error: getError } = await supabase
        .from('purchase_categories')
        .select('*')
        .eq('id', categoryId)
        .single();
      
      if (getError) {
        console.error("🔬 DEBUG: Błąd podczas sprawdzania kategorii:", getError);
        return;
      }
      
      console.log("🔬 DEBUG: Znaleziono kategorię:", category);
      
      // Test 2: Bezpośrednia próba aktualizacji
      const newValue = !(category.is_active === true);
      console.log(`🔬 DEBUG: Próba zmiany is_active z ${category.is_active} na ${newValue}`);
      
      const { data: updateData, error: updateError } = await supabase
        .from('purchase_categories')
        .update({ is_active: newValue })
        .eq('id', categoryId)
        .select();
      
      if (updateError) {
        console.error("🔬 DEBUG: Błąd podczas aktualizacji is_active:", updateError);
      } else {
        console.log("🔬 DEBUG: Aktualizacja udana:", updateData);
      }
      
      // Test 3: Sprawdź, czy zmiana została zapisana
      const { data: checkData, error: checkError } = await supabase
        .from('purchase_categories')
        .select('*')
        .eq('id', categoryId)
        .single();
      
      if (checkError) {
        console.error("🔬 DEBUG: Błąd podczas sprawdzania zaktualizowanej kategorii:", checkError);
      } else {
        console.log("🔬 DEBUG: Stan kategorii po aktualizacji:", checkData);
      }
    } catch (error) {
      console.error("🔬 DEBUG: Nieoczekiwany błąd:", error);
    }
  };

  // Update the hideCategory function to ensure it correctly updates the UI
  const hideCategory = async (categoryId: string, categoryName: string) => {
    console.log("🔍 Próba ukrycia kategorii:", categoryName, "ID:", categoryId);
    
    // Identyfikator problematycznej kategorii
    const problemCategoryId = "69c1dd83-69a0-4b41-88fb-0b685892173b";
    
    // Specjalna obsługa dla problematycznej kategorii
    if (categoryId === problemCategoryId) {
      Alert.alert(
        "Ukrycie problematycznej kategorii",
        `Kategoria "${categoryName}" wymaga specjalnej obsługi. Wybierz opcję:`,
        [
          {
            text: "Anuluj",
            style: "cancel"
          },
          {
            text: "Ukryj lokalnie",
            onPress: () => {
              // Ukryj tylko w UI
              setCategories(prev => prev.filter(cat => cat.id !== categoryId));
              if (form.category === categoryName) {
                setForm(prev => ({...prev, category: ''}));
              }
              Alert.alert("Ukryto lokalnie", `Kategoria "${categoryName}" została ukryta w interfejsie.`);
            }
          },
          {
            text: "Napraw bazę",
            style: "destructive",
            onPress: repairDatabasePermissions
          }
        ]
      );
      return;
    }
    
    // Dla wszystkich innych kategorii - proponujemy ukrycie lokalne lub naprawę bazy
    Alert.alert(
      "Ukrywanie kategorii",
      `Wybierz opcję dla kategorii "${categoryName}":`,
      [
        {
          text: "Anuluj",
          style: "cancel"
        },
        {
          text: "Ukryj lokalnie",
          onPress: () => {
            // Ukryj tylko w UI
            setCategories(prev => prev.filter(cat => cat.id !== categoryId));
            if (form.category === categoryName) {
              setForm(prev => ({...prev, category: ''}));
            }
            Alert.alert("Ukryto lokalnie", `Kategoria "${categoryName}" została ukryta w interfejsie.`);
          }
        },
        {
          text: "Spróbuj w bazie",
          onPress: async () => {
            setLoading(true);
            
            try {
              console.log("🔍 Aktualizuję bazę danych, ustawiam is_active=false dla kategorii:", categoryId);
              
              // Używamy bezpośredniej metody update
              const { error } = await supabase
                .from('purchase_categories')
                .update({ is_active: false })
                .eq('id', categoryId);
              
              if (error) {
                console.error("🔍 Błąd podczas aktualizacji statusu kategorii:", error);
                
                Alert.alert(
                  "Błąd bazy danych",
                  `Nie udało się ukryć kategorii w bazie: ${error.message}. 
                  
Czy chcesz ukryć kategorię tylko lokalnie lub naprawić bazę danych?`,
                  [
                    { 
                      text: "Anuluj",
                      style: "cancel"
                    },
                    {
                      text: "Ukryj lokalnie",
                      onPress: () => {
                        setCategories(prev => prev.filter(cat => cat.id !== categoryId));
                        if (form.category === categoryName) {
                          setForm(prev => ({...prev, category: ''}));
                        }
                        Alert.alert("Ukryto lokalnie", `Kategoria "${categoryName}" została ukryta lokalnie w interfejsie.`);
                      }
                    },
                    {
                      text: "Napraw bazę",
                      style: "destructive",
                      onPress: repairDatabasePermissions
                    }
                  ]
                );
                return;
              }
              
              console.log("🔍 Sukces: Kategoria oznaczona jako nieaktywna w bazie");
              
              // Aktualizuj lokalny stan - usuń kategorię z listy
              setCategories(prev => prev.filter(cat => cat.id !== categoryId));
              
              // Jeśli kategoria była wybrana, wyczyść wybór
              if (form.category === categoryName) {
                setForm(prev => ({...prev, category: ''}));
              }
              
              Alert.alert("Sukces", `Kategoria "${categoryName}" została ukryta w bazie danych.`);
              
              // Odśwież kategorię po krótkim opóźnieniu, aby dać czas na zakończenie transakcji
              setTimeout(() => {
                fetchCategories();
              }, 500);
            } catch (error) {
              console.error("🔍 Nieoczekiwany błąd:", error);
              Alert.alert("Błąd", `Wystąpił nieoczekiwany problem: ${error instanceof Error ? error.message : String(error)}`);
            } finally {
              setLoading(false);
            }
          }
        },
        {
          text: "Napraw bazę",
          style: "destructive",
          onPress: repairDatabasePermissions
        }
      ]
    );
  };

  // Now let's create a direct workaround for the problematic category
  const directUpdateNarzedziaCategoryStatus = async () => {
    const categoryId = "69c1dd83-69a0-4b41-88fb-0b685892173b";
    console.log("🛠️ Bezpośrednia aktualizacja kategorii Narzędzia rozpoczęta");
    
    try {
      // Method 1: Standard update
      const { error: updateError } = await supabase
        .from('purchase_categories')
        .update({ is_active: false })
        .eq('id', categoryId);
      
      if (updateError) {
        console.error("🛠️ Metoda 1 (update) nie zadziałała:", updateError);
        
        // Method 2: Try raw SQL through RPC
        const { error: rpcError } = await supabase.rpc('update_category_active_status', {
          category_id: categoryId,
          active_status: false
        });
        
        if (rpcError) {
          console.error("🛠️ Metoda 2 (RPC) nie zadziałała:", rpcError);
          
          // Method 3: Try using select and then update
          console.log("🛠️ Próba metody 3 (select + update)");
          const { data: catData, error: catError } = await supabase
            .from('purchase_categories')
            .select('*')
            .eq('id', categoryId)
            .single();
          
          if (catError) {
            console.error("🛠️ Metoda 3 (select) nie zadziałała:", catError);
            
            // If all database methods fail, handle in UI
            console.log("🛠️ Wszystkie metody bazy danych zawiodły. Ukrywam kategorię lokalnie.");
            
            // Ensure it's filtered from the UI
            setCategories(prev => prev.filter(cat => cat.id !== categoryId));
            Alert.alert("Informacja", "Kategoria została ukryta lokalnie, ale nie zaktualizowano bazy danych.");
          } else {
            // Now try to update it
            const { error: updateError } = await supabase
              .from('purchase_categories')
              .update({ is_active: false })
              .eq('id', categoryId);
            
            if (updateError) {
              console.error("🛠️ Metoda 3 (update po select) nie zadziałała:", updateError);
              setCategories(prev => prev.filter(cat => cat.id !== categoryId));
              Alert.alert("Informacja", "Kategoria została ukryta lokalnie, ale nie zaktualizowano bazy danych.");
            } else {
              console.log("🛠️ Metoda 3 (update po select) zadziałała!");
              Alert.alert("Sukces", "Kategoria została ukryta (metoda 3).");
              fetchCategories();
            }
          }
        } else {
          console.log("🛠️ Metoda 2 (RPC) zadziałała!");
          Alert.alert("Sukces", "Kategoria została ukryta (metoda 2).");
          fetchCategories();
        }
      } else {
        console.log("🛠️ Metoda 1 (update) zadziałała!");
        Alert.alert("Sukces", "Kategoria została ukryta (metoda 1).");
        fetchCategories();
      }
    } catch (error) {
      console.error("🛠️ Nieoczekiwany błąd:", error);
      Alert.alert("Błąd", "Wystąpił nieoczekiwany problem podczas aktualizacji kategorii.");
    }
  };

  // Add this forceRefresh function to ensure UI updates
  const forceRefresh = () => {
    console.log("🔄 Force refreshing categories display");
    // Force a re-render by creating a new categories array with the same content
    setCategories([...categories.filter(cat => !hiddenCategoryIds.includes(cat.id))]);
  };

  // Dodajemy funkcję renderującą modal wyboru kategorii
  const renderCategoryModal = () => {
    // Dodajemy szczegółowe logowanie przy renderowaniu modalu
    console.log("🔍 RENDERING CATEGORY MODAL. Categories RAW:", categories);
    console.log("🔍 Categories length:", categories.length);
    console.log("🔍 Categories mapped:", categories.map(c => ({id: c.id, name: c.name})));
    console.log("🔍 Platform:", Platform.OS, "Loading:", loading, "ShowAddCategoryForm:", showAddCategoryForm);
    
    return (
    <Modal
      visible={showCategoryModal}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowCategoryModal(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Wybierz kategorię</Text>
            <View style={{ flexDirection: 'row' }}>
            <TouchableOpacity 
              style={styles.modalCloseButton}
              onPress={() => setShowCategoryModal(false)}
            >
              <Ionicons name="close" size={24} color="#4B5563" />
              </TouchableOpacity>
            </View>
          </View>
          
          {/* Debug panel dla wszystkich platform */}
          <View style={{padding: 10, backgroundColor: '#f0f0f0', marginBottom: 10}}>
            <Text>Debug Info - Wszędzie:</Text>
            <Text>Categories Count: {categories.length}</Text>
            <Text>Platform: {Platform.OS}</Text>
            <Text>Loading: {loading ? 'Tak' : 'Nie'}</Text>
            <Text>ShowAddForm: {showAddCategoryForm ? 'Tak' : 'Nie'}</Text>
            
            <TouchableOpacity 
              style={{backgroundColor: '#2563EB', padding: 8, borderRadius: 4, marginTop: 8}}
              onPress={() => {
                console.log("🔄 Manual fetch categories triggered");
                fetchCategories();
              }}
            >
              <Text style={{color: 'white', textAlign: 'center'}}>Odśwież Kategorie</Text>
            </TouchableOpacity>
          </View>
          
          {loading ? (
            <View style={styles.modalLoading}>
              <ActivityIndicator size="large" color="#2563EB" />
              <Text style={styles.modalLoadingText}>Ładowanie kategorii...</Text>
            </View>
          ) : showAddCategoryForm ? (
            <View style={styles.addCategoryForm}>
              <Text style={styles.formLabel}>Nazwa nowej kategorii</Text>
              <TextInput
                style={styles.formInput}
                value={newCategoryName}
                onChangeText={setNewCategoryName}
                placeholder="Wpisz nazwę kategorii"
                placeholderTextColor="#9CA3AF"
              />
              <View style={styles.formButtons}>
                <TouchableOpacity
                  style={styles.formCancelButton || { paddingVertical: 8, paddingHorizontal: 16, marginRight: 12 }}
                  onPress={() => {
                    setNewCategoryName('');
                    setShowAddCategoryForm(false);
                  }}
                >
                  <Text style={styles.formCancelButtonText || { color: '#6B7280', fontWeight: '500' }}>Anuluj</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.formSubmitButton}
                  onPress={createCategory}
                >
                  <Text style={styles.submitButtonText || { color: 'white', fontWeight: '500' }}>Zapisz</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : categories.length === 0 ? (
            <View style={styles.modalEmptyState}>
              <Text style={styles.modalEmptyText}>Brak dostępnych kategorii</Text>
              
              {/* Opakcje dodawania w zależności od typu użytkownika */}
              {userType === 'company' || userType === 'coordinator' ? (
                <TouchableOpacity
                  style={styles.addCategoryButton}
                  onPress={() => setShowAddCategoryForm(true)}
                >
                  <Ionicons name="add-circle-outline" size={20} color="white" />
                  <Text style={styles.addCategoryButtonText}>Dodaj kategorię</Text>
                </TouchableOpacity>
              ) : (
                <View>
                  <Text style={styles.modalEmptySubtext}>
                    Twoja firma nie ma zdefiniowanych kategorii. 
                    Poproś administratora o dodanie kategorii.
                  </Text>
                  <TouchableOpacity
                    style={styles.addCategoryButton}
                    onPress={() => setShowAddCategoryForm(true)}
                  >
                    <Ionicons name="add-circle-outline" size={20} color="white" />
                    <Text style={styles.addCategoryButtonText}>Dodaj tymczasową kategorię</Text>
                  </TouchableOpacity>
                </View>
              )}
                </View>
              ) : (
            // Kategorie są dostępne - wyświetlamy listę
            <View style={{flex: 1}}>
              {/* Dedykowany renderer kategorii dla iOS */}
              {Platform.OS === 'ios' ? (
                <View style={{ flex: 1, paddingVertical: 10 }}>
                  {/* Rozszerzony debug panel tylko dla iOS */}
                  <View style={{
                    backgroundColor: '#EFF6FF',
                    padding: 10,
                    borderRadius: 8,
                    marginBottom: 10,
                    borderWidth: 1,
                    borderColor: '#BFDBFE'
                  }}>
                    <Text style={{ fontSize: 14, fontWeight: '600', color: '#2563EB', marginBottom: 5 }}>
                      Debug Info - iOS:
                    </Text>
                    <Text style={{ fontSize: 12, color: '#4B5563' }}>
                      • Liczba kategorii: {categories.length}
                    </Text>
                    <Text style={{ fontSize: 12, color: '#4B5563' }}>
                      • Przykładowe kategorie: {categories.slice(0, 3).map(c => c?.name || '?').join(', ')}
                    </Text>
                    <Text style={{ fontSize: 12, color: '#4B5563' }}>
                      • Typ danych kategorii: {typeof categories}
                    </Text>
                    <Text style={{ fontSize: 12, color: '#4B5563' }}>
                      • Czy to tablica: {Array.isArray(categories) ? 'Tak' : 'Nie'}
                    </Text>
                    
                    <View style={{ flexDirection: 'row', marginTop: 8 }}>
                <TouchableOpacity
                        style={{
                          backgroundColor: '#3B82F6',
                          paddingVertical: 8,
                          paddingHorizontal: 12,
                          borderRadius: 6,
                          flex: 1,
                          alignItems: 'center',
                          marginRight: 5
                        }}
                        onPress={() => {
                          console.log("🔄 Odświeżam kategorie...");
                          fetchCategories();
                        }}
                      >
                        <Text style={{ color: 'white', fontWeight: '500', fontSize: 13 }}>Odśwież kategorie</Text>
                      </TouchableOpacity>
                      
                      <TouchableOpacity 
                        style={{
                          backgroundColor: '#10B981',
                          paddingVertical: 8,
                          paddingHorizontal: 12,
                          borderRadius: 6,
                          flex: 1,
                          alignItems: 'center',
                          marginLeft: 5
                        }}
                        onPress={() => {
                          // Reset kategorii i odświeżenie
                          console.log("🧹 Reset stanu kategorii...");
                          setCategories([]);
                          setTimeout(() => {
                            fetchCategories();
                          }, 500);
                        }}
                      >
                        <Text style={{ color: 'white', fontWeight: '500', fontSize: 13 }}>Reset i Odśwież</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                  
                  <Text style={{ fontSize: 14, fontWeight: '500', marginBottom: 10, color: '#4B5563' }}>
                    Wybierz z {categories.length} dostępnych kategorii:
                  </Text>
                  
                  {/* Na początku logujemy wszystkie kategorie */}
                  {(() => {
                    console.log("📱 iOS: Próbuję renderować kategorie:", JSON.stringify(categories.slice(0, 3)));
                    return null;
                  })()}
                  
                  {/* Użyjemy ScrollView zamiast samego View, aby można było przewijać dużą listę */}
                  <ScrollView style={{ flex: 1, maxHeight: 400 }}>
                    {categories.length > 0 ? (
                      <>
                        {/* Dodatkowa diagnostyka */}
                        <Text style={{ fontSize: 12, color: '#6B7280', marginBottom: 8 }}>
                          Znaleziono {categories.length} kategorii
                        </Text>
                        
                        {/* Obsługa przypadku, gdy mapowanie może nie działać */}
                        {(() => {
                          try {
                            // Próba normalnego renderowania
                            return categories.slice(0, 20).map((category, index) => {
                              // Bezpieczne pobieranie ID i nazwy
                              const categoryId = category?.id || `fallback-${index}`;
                              const categoryName = category?.name || `Kategoria ${index+1}`;
                              const isProblemCategory = categoryId === "69c1dd83-69a0-4b41-88fb-0b685892173b";
                              const isSelected = form.category === categoryName;
                              
                              console.log(`📱 iOS: Renderuję kategorię ${index}: ${categoryName}`);
                              
                              return (
                                <TouchableOpacity
                                  key={`ios-cat-${categoryId}-${index}`}
                                  style={{
                                    padding: 14,
                                    marginVertical: 6,
                                    borderRadius: 8,
                                    backgroundColor: isSelected ? '#EBF5FF' : '#F9FAFB',
                                    borderWidth: 1,
                                    borderColor: isSelected ? '#3B82F6' : '#E5E7EB',
                                    opacity: isProblemCategory ? 0.5 : 1,
                                    flexDirection: 'row',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                  }}
                                  onPress={() => {
                                    if (isProblemCategory) {
                                      console.log("⚠️ Prevented selection of problematic category:", categoryName);
                                      Alert.alert("Kategoria niedostępna", "Ta kategoria jest obecnie niedostępna.");
                                      return;
                                    }
                                    setForm({...form, category: categoryName});
                                    setShowCategoryModal(false);
                                  }}
                                >
                                  <Text style={{ 
                                    fontSize: 15, 
                                    fontWeight: isSelected ? '600' : '400',
                                    color: isSelected ? '#2563EB' : '#1F2937'
                                  }}>
                                    {categoryName}
                                  </Text>
                                  
                                  {(userType === 'company' || userType === 'coordinator') && (
                                    <TouchableOpacity
                                      style={{
                                        padding: 8,
                                        borderRadius: 20,
                                        backgroundColor: '#FEF2F2'
                                      }}
                                      onPress={() => {
                                        console.log("Direct hide button pressed for:", categoryName);
                                        
                                        // Bezpośrednio ukryj kategorię w UI bez alertów
                                        setCategories(prev => prev.filter(c => c.id !== categoryId));
                                        
                                        // Jeśli kategoria była wybrana, wyczyść wybór
                                        if (form.category === categoryName) {
                                          setForm(prev => ({...prev, category: ''}));
                                        }
                                        
                                        // Spróbuj ukryć w bazie danych w tle
                                        supabase
                                          .from('purchase_categories')
                                          .update({ is_active: false })
                                          .eq('id', categoryId)
                                          .then(({ error }) => {
                                            if (error) {
                                              console.error(`Błąd ukrywania kategorii ${categoryName} w bazie:`, error);
                                            } else {
                                              console.log(`Kategoria ${categoryName} ukryta pomyślnie w bazie`);
                                            }
                                          });
                                      }}
                                    >
                                      <Ionicons name="trash-outline" size={20} color="#EF4444" />
                </TouchableOpacity>
              )}
                                </TouchableOpacity>
                              );
                            });
                          } catch (error) {
                            // Awaryjny mechanizm renderowania
                            console.error("❌ Error rendering categories:", error);
                            
                            // Prosta tablica z nazwami kategorii
                            const categoryNames = categories.map(c => c?.name || 'Unknown').filter(Boolean);
                            
                            // Wyświetl kategorie jako prostą listę
                            return (
                              <View style={{ marginTop: 10 }}>
                                <Text style={{ color: '#EF4444', marginBottom: 10 }}>
                                  Wystąpił problem z renderowaniem kategorii. Używamy widoku awaryjnego:
                                </Text>
                                {categoryNames.slice(0, 20).map((name, idx) => (
                                  <TouchableOpacity
                                    key={`fallback-${idx}`}
                                    style={{
                                      padding: 14,
                                      marginVertical: 4,
                                      backgroundColor: '#F9FAFB',
                                      borderWidth: 1,
                                      borderColor: '#E5E7EB',
                                      borderRadius: 8
                                    }}
                                    onPress={() => {
                                      setForm({...form, category: name});
                                      setShowCategoryModal(false);
                                    }}
                                  >
                                    <Text>{name}</Text>
                                  </TouchableOpacity>
                                ))}
            </View>
                            );
                          }
                        })()}
                        
                        {/* Usunięto zbędny kod renderowania kategorii */}
                      </>
                    ) : (
                      <Text style={{ fontSize: 14, color: '#6B7280', textAlign: 'center', marginTop: 20 }}>
                        Brak dostępnych kategorii
                      </Text>
                    )}
                  </ScrollView>
                  
                  <TouchableOpacity
                    style={{
                      marginTop: 16,
                      padding: 12,
                      backgroundColor: '#2563EB',
                      borderRadius: 8,
                      alignItems: 'center'
                    }}
                    onPress={() => setShowAddCategoryForm(true)}
                  >
                    <Text style={{ color: 'white', fontWeight: '500' }}>Dodaj nową kategorię</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                // Standardowy renderer dla innych platform
                <ScrollView 
                  style={styles.categoryList}
                  contentContainerStyle={{ paddingBottom: 20 }}
                >
                  {/* Istniejący kod dla Android/Web */}
                {categories.map((category) => {
                    // Istniejący kod dla kategorii
                  const categoryId = category.id || `fallback-${Math.random()}`;
                  const categoryName = category.name || 'Brak nazwy';
                  const isTempCategory = categoryId.toString().startsWith('temp-');
                  const isProblemCategory = categoryId === "69c1dd83-69a0-4b41-88fb-0b685892173b";
                  
                  // Rejestrujemy każdą renderowaną kategorię - pomoże w debugowaniu
                  console.log(`🏷️ Renderuję kategorię: ${categoryName}, ID: ${typeof categoryId === 'string' ? categoryId.substring(0, 8) : categoryId}...`);
                  
                  return (
                    <View
                      key={categoryId}
                      style={[
                        styles.categoryItem, 
                        isTempCategory && styles.categoryItemTemp,
                          isProblemCategory && { backgroundColor: '#FEF2F2' } // Delikatnie czerwone tło dla problematycznej kategorii
                      ]}
                    >
                      <TouchableOpacity 
                        style={styles.categoryMainContent}
                      onPress={() => {
                          // Sanity check: Don't allow selection of the problematic category
                          if (categoryId === "69c1dd83-69a0-4b41-88fb-0b685892173b") {
                            console.log("⚠️ Prevented selection of problematic category:", categoryName);
                            Alert.alert("Kategoria niedostępna", "Ta kategoria jest obecnie niedostępna.");
                            return;
                          }
                          
                          setForm({...form, category: categoryName});
                        setShowCategoryModal(false);
                      }}
                    >
                      <View style={styles.categoryItemContent}>
                          <Text style={styles.categoryItemText}>{categoryName}</Text>
                        {isTempCategory && (
                          <Text style={styles.tempCategoryBadge}>Tymczasowa</Text>
                        )}
                      </View>
                        
                        {form.category === categoryName && (
                        <Ionicons name="checkmark" size={20} color="#2563EB" />
                      )}
                    </TouchableOpacity>
                      
                      {(userType === 'company' || userType === 'coordinator') && (
                        <View style={{ flexDirection: 'row' }}>
                          <TouchableOpacity 
                            style={[styles.hideCategoryButton, { padding: 8, borderRadius: 20 }]}
                            onPress={() => {
                              console.log("Direct hide button pressed for:", categoryName);
                              
                                // Bezpośrednio ukryj kategorię w UI bez alertów
                                setCategories(prev => prev.filter(c => c.id !== categoryId));
                                
                                // Jeśli kategoria była wybrana, wyczyść wybór
                                if (form.category === categoryName) {
                                  setForm(prev => ({...prev, category: ''}));
                                }
                                
                                // Spróbuj ukryć w bazie danych w tle
                                supabase
                                  .from('purchase_categories')
                                  .update({ is_active: false })
                                  .eq('id', categoryId)
                                  .then(({ error }) => {
                                    if (error) {
                                      console.error(`Błąd ukrywania kategorii ${categoryName} w bazie:`, error);
                                    } else {
                                      console.log(`Kategoria ${categoryName} ukryta pomyślnie w bazie`);
                                    }
                                  });
                                
                                // Pokaż widoczną informację na ekranie zamiast alertu
                                const message = `Kategoria "${categoryName}" została ukryta`;
                                console.log("UKRYTO KATEGORIĘ:", message);
                            }}
                            activeOpacity={0.6}
                          >
                            <Ionicons name="trash-outline" size={20} color="#EF4444" />
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  );
                })}
              
                {/* Przycisk dodawania kategorii dla innych platform */}
              <TouchableOpacity
                style={styles.addCategoryButtonSmall}
                onPress={() => setShowAddCategoryForm(true)}
              >
                <Ionicons name="add" size={16} color="#2563EB" />
                <Text style={styles.addCategoryButtonSmallText}>Dodaj nową kategorię</Text>
              </TouchableOpacity>
              </ScrollView>
              )}
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

  // Dodajemy funkcję renderującą niestandardowe okno dialogowe
  const renderConfirmDialog = () => (
    <Modal
      visible={showConfirmDialog}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowConfirmDialog(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.confirmModal}>
          <Text style={styles.confirmModalTitle}>Potwierdź</Text>
          <Text style={styles.confirmModalMessage}>Czy na pewno chcesz złożyć wniosek o zakup?</Text>
          <View style={styles.confirmModalButtons}>
            <TouchableOpacity
              style={styles.confirmModalCancelButton}
              onPress={() => {
                console.log("Anulowano wysyłanie wniosku (w niestandardowym oknie dialogowym)");
                setShowConfirmDialog(false);
              }}
            >
              <Text style={styles.confirmModalCancelButtonText}>Anuluj</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.confirmModalConfirmButton}
              onPress={() => {
                console.log("Potwierdzono wysyłanie wniosku (w niestandardowym oknie dialogowym)");
                setShowConfirmDialog(false);
                handleSubmitPurchase();
              }}
            >
              <Text style={styles.confirmModalConfirmButtonText}>Wyślij</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  // Dodajemy zmienną do śledzenia czy już sprawdziliśmy strukturę tabeli
  let tableStructureChecked = false;

  // Dodajemy funkcję do debugowania struktury tabeli purchases, którą wywołamy ręcznie
  const debugTableStructure = async () => {
    try {
      console.log("Rozpoczynam debugowanie struktury tabeli purchases...");
      
      // Metoda 1: Sprawdzenie struktury przez dodanie minimalnego rekordu i wybór wszystkich kolumn
      const testData = {
        company_id: companyId,
        title: "TEST STRUKTURY TABELI - PROSZĘ USUNĄĆ",
        description: "Automatyczne sprawdzenie struktury tabeli"
      };
      
      console.log("Dodaję testowy rekord...");
      const { data: insertResult, error: insertError } = await supabase
        .from('purchases')
        .insert(testData)
        .select();
      
      if (insertError) {
        console.error("Błąd podczas dodawania testowego rekordu:", insertError);
      } else if (insertResult && insertResult.length > 0) {
        console.log("Struktura tabeli purchases:", Object.keys(insertResult[0]).join(", "));
        
        // Usuwamy testowy rekord
        const { error: deleteError } = await supabase
          .from('purchases')
          .delete()
          .eq('id', insertResult[0].id);
          
        if (deleteError) {
          console.error("Nie udało się usunąć testowego rekordu:", deleteError);
        }
      }
      
      console.log("Zakończono debugowanie struktury tabeli");
    } catch (error) {
      console.error("Nieoczekiwany błąd podczas debugowania struktury tabeli:", error);
    }
  };

  // Modyfikujemy funkcję tworzenia miniatury
  const createThumbnailFromUri = async (uri: string): Promise<string | null> => {
    try {
      console.log("Tworzenie referencji dla:", uri.substring(0, 50) + "...");
      
      // Generujemy unikalny identyfikator dla referencji
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 10);
      const referenceId = `img_ref_${timestamp}_${randomString}`;
      
      console.log("Utworzono referencję:", referenceId);
      
      // Tutaj moglibyśmy zapisać mapowanie referencji -> faktyczny URI
      // w lokalnym storage lub innym mechanizmie
      
      // Zwracamy referencję bez samego URI
      return referenceId;
    } catch (error) {
      console.error("Błąd podczas tworzenia referencji obrazu:", error);
      return null;
    }
  };

  // Renderowanie widoku formularza dodawania zakupu
  const renderAddPurchaseForm = () => (
    <ScrollView style={styles.formContainer}>
      <View style={styles.formScrollContent}>
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>
            {i18n.t('purchasesTitle')} *
          </Text>
          <TextInput
            style={styles.formInput}
            placeholder={i18n.t('purchasesTitlePlaceholder')}
            placeholderTextColor="#9CA3AF"
            value={form.title}
            onChangeText={(text) => setForm({ ...form, title: text })}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('purchasesDescription')}</Text>
          <TextInput
            style={[styles.formInput, styles.textArea]}
            placeholder={i18n.t('purchasesDescriptionPlaceholder')}
            placeholderTextColor="#9CA3AF"
            multiline={true}
            numberOfLines={4}
            textAlignVertical="top"
            value={form.description}
            onChangeText={(text) => setForm({ ...form, description: text })}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('purchasesCategory')}</Text>
          <View style={styles.categoryContainer}>
            <TouchableOpacity 
              style={styles.categorySelector}
              onPress={() => setShowCategoryModal(true)}
            >
              <Text style={form.category ? styles.categorySelectorText : styles.categorySelectorPlaceholder}>
                {form.category || i18n.t('purchasesSelectCategory')}
              </Text>
              <Ionicons name="chevron-down" size={20} color="#6B7280" />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.addCategoryButton}
              onPress={() => setShowCategoryModal(true)}
            >
              <Ionicons name="add" size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>
            {i18n.t('purchasesEstimatedCost')} *
          </Text>
          <TextInput
            style={styles.formInput}
            placeholder={i18n.t('purchasesCostPlaceholder')}
            placeholderTextColor="#9CA3AF"
            keyboardType="numeric"
            value={form.price_estimate}
            onChangeText={(text) => setForm({ ...form, price_estimate: text })}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('purchasesQuantity')}</Text>
          <TextInput
            style={styles.formInput}
            placeholder={i18n.t('purchasesQuantityPlaceholder')}
            placeholderTextColor="#9CA3AF"
            keyboardType="numeric"
            value={form.quantity}
            onChangeText={(text) => setForm({ ...form, quantity: text })}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('purchasesPriority')}</Text>
          <View style={styles.priorityContainer}>
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'low' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm({ ...form, priority: 'low' })}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotLow]} />
                <Text style={styles.priorityText}>{i18n.t('priorityLow')}</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'medium' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm({ ...form, priority: 'medium' })}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotMedium]} />
                <Text style={styles.priorityText}>{i18n.t('priorityMedium')}</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'high' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm({ ...form, priority: 'high' })}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotHigh]} />
                <Text style={styles.priorityText}>{i18n.t('priorityHigh')}</Text>
              </View>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.priorityCircle,
                form.priority === 'critical' && styles.priorityCircleSelected
              ]}
              onPress={() => setForm({ ...form, priority: 'critical' })}
            >
              <View style={styles.priorityCircleInner}>
                <View style={[styles.priorityDot, styles.priorityDotCritical]} />
                <Text style={styles.priorityText}>{i18n.t('priorityCritical')}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('purchasesAdditionalNotes')}</Text>
          <TextInput
            style={[styles.formInput, styles.textArea]}
            placeholder={i18n.t('purchasesNotesPlaceholder')}
            placeholderTextColor="#9CA3AF"
            multiline={true}
            numberOfLines={4}
            textAlignVertical="top"
            value={form.notes}
            onChangeText={(text) => setForm({ ...form, notes: text })}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('purchasesPhotos')}</Text>
          <Text style={styles.formFieldNote}>{i18n.t('purchasesPhotosWillBeSaved')}</Text>
          
          {form.attachments.length > 0 && (
            <View style={styles.imagesContainer}>
              {form.attachments.map((uri, index) => (
                <View key={index} style={styles.imageItem}>
                  <Image source={{ uri }} style={styles.productImage} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => removeImage(index)}
                  >
                    <Ionicons name="close-circle" size={24} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          )}
          
          <TouchableOpacity
            style={styles.photoButton}
            onPress={handleImagePick}
            disabled={loading}
          >
            <Ionicons name="camera-outline" size={24} color="#2563EB" />
            <Text style={styles.photoButtonText}>{i18n.t('purchasesAddPhoto')}</Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.formRequiredFieldsInfo}>{i18n.t('requiredFields')}</Text>
        
        <TouchableOpacity
          style={[
            styles.formSubmitButton,
            (!form.title.trim() || loading) && styles.formSubmitButtonDisabled
          ]}
          onPress={handleSubmitPurchase}
          disabled={!form.title.trim() || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <Text style={styles.submitButtonText}>{i18n.t('purchasesCreatePurchase')}</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  // Form validation and submission
  const validateForm = () => {
    if (!form.title.trim()) {
      Alert.alert(i18n.t('error'), i18n.t('purchasesFillRequiredFields'));
      return false;
    }
    return true;
  };

  const handleSubmitPurchase = async () => {
    if (!validateForm()) {
        return;
      }
      
    setLoading(true);
    try {
      // Create purchase in database
      const { data, error } = await supabase
          .from('purchases')
        .insert([
          {
            title: form.title,
            description: form.description || '',
            category: form.category || '',
            price_estimate: parseFloat(form.price_estimate) || 0,
            quantity: parseInt(form.quantity) || 1,
            status: 'pending',
            requested_by: userId,
            requested_by_name: userName || '',
            requested_at: new Date().toISOString(),
            priority: form.priority,
            notes: form.notes || '',
            attachments: form.attachments,
            company_id: companyId,
          },
        ])
        .select();

      if (error) {
        console.error('Error submitting purchase:', error);
        Alert.alert(i18n.t('purchasesSaveError'), i18n.t('purchasesFailedToSave'));
        } else {
        // Success!
        Alert.alert(i18n.t('success'), i18n.t('purchasesPurchaseCreated'));
        resetForm();
        // Fetch updated purchase list
        fetchPurchases();
        // Optionally switch to list view
        onTabChange('list');
      }
    } catch (error) {
      console.error('Exception in handleSubmitPurchase:', error);
      Alert.alert(i18n.t('error'), i18n.t('purchasesUnexpectedError'));
    } finally {
      setLoading(false);
    }
  };

  // Reset form to initial state
  const resetForm = () => {
      setForm({
        title: '',
        description: '',
      category: '',
        price_estimate: '',
      quantity: '',
        priority: 'medium',
        notes: '',
      attachments: [],
    });
  };

  // Funkcja do obsługi sortowania
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle sort order if clicking the same field
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort field with default descending order
      setSortField(field);
      setSortOrder('desc');
    }
  };

  // Funkcja do przesyłania zdjęcia na serwer - uproszczona i bardziej niezawodna
  const uploadImage = async (base64Image: string): Promise<string | null> => {
    // Sprawdzamy czy mamy dane obrazu
    if (!base64Image) {
      console.log("Brak danych obrazu");
      return null;
    }
    
    // W uproszczonym podejściu po prostu zwracamy URI
    console.log("Używamy obrazu jako URI zamiast próbować przesyłać go do Storage");
    
    // W przyszłości, kiedy bucket będzie prawidłowo skonfigurowany, możemy odkomentować kod przesyłania
    // i zwracać publicURL zamiast samego URI
    
    return base64Image;
  };

  // Update the decode function to not use base-64 library
  const decode = (base64String: string) => {
    try {
    // Usuwamy prefix 'data:image/jpeg;base64,' jeśli istnieje
    const base64Data = base64String.includes('base64,') 
      ? base64String.split('base64,')[1] 
      : base64String;
    
      // Używamy wbudowanej funkcji Buffer zamiast base-64
      return Buffer.from(base64Data, 'base64');
    } catch (error) {
      console.error('Error decoding base64:', error);
      return Buffer.from('');
    }
  };

  // Funkcja do wybierania zdjęcia z galerii
  const handleImagePick = async () => {
    try {
      setLoading(true);
      
      // Request permissions
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert(i18n.t('purchasesNoPermissions'), i18n.t('purchasesPhotoAccessNeeded'));
        setLoading(false);
        return;
      }
      
        // Sprawdzamy czy nie przekroczyliśmy limitu zdjęć
        if (form.attachments.length >= 3) {
          Alert.alert(
          i18n.t('error'), 
          i18n.t('purchasesImageLimitReached')
          );
          setLoading(false);
          return;
      }
      
      // Pick image - zmniejszamy jakość z 0.8 na 0.5 (większa kompresja)
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.5, // Zmniejszenie jakości dla mniejszego rozmiaru pliku
        base64: false,
        exif: false, // Wyłączamy metadane EXIF aby dodatkowo zmniejszyć rozmiar
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        
        // Log for debugging
        console.log('Selected image URI type:', typeof selectedImage.uri);
        console.log('Selected image URI preview:', selectedImage.uri.substring(0, 30) + '...');
        console.log('Image size estimate:', (selectedImage.uri.length / 1024).toFixed(2) + ' KB');
        
        // Dodajemy URI obrazu bezpośrednio
        setForm(prev => ({
          ...prev,
          attachments: [...prev.attachments, selectedImage.uri]
        }));
      }
    } catch (error) {
      console.error("Błąd podczas wybierania zdjęcia:", error);
      Alert.alert(i18n.t('error'), i18n.t('purchasesImagePickError'));
    } finally {
      setLoading(false);
    }
  };

  // Funkcja do usuwania zdjęcia
  const removeImage = (index: number) => {
    setForm(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
    console.log("Usunięto zdjęcie z indeksem:", index);
  };

  // Dodajemy funkcję renderFilters, która była wywoływana w renderListContainer
  const renderFilters = () => (
    <View style={styles.filtersContainer}>
      <View style={styles.searchBarContainer}>
        <Ionicons name="search" size={20} color="#6B7280" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder={i18n.t('purchasesSearchPlaceholder')}
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#9CA3AF"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearSearchButton}>
            <Ionicons name="close-circle" size={20} color="#6B7280" />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={[
            styles.dateButton,
            (filterDateFrom || filterDateTo) && styles.dateButtonActive,
          ]}
          onPress={() => setShowDateFilters(true)}
        >
          <Ionicons
            name="calendar-outline"
            size={18}
            color={(filterDateFrom || filterDateTo) ? '#2563EB' : '#4B5563'}
          />
          <Text
            style={[
              styles.dateButtonText,
              (filterDateFrom || filterDateTo) && styles.dateButtonTextActive,
            ]}
          >
            {i18n.t('purchasesDateFilter')}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.statusFilterContainer}>
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'all' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('all')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'all' && styles.statusFilterTextActive
          ]}>{i18n.t('purchasesAllFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'pending' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('pending')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'pending' && styles.statusFilterTextActive
          ]}>{i18n.t('purchasesPendingFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'approved' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('approved')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'approved' && styles.statusFilterTextActive
          ]}>{i18n.t('purchasesApprovedFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'ordered' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('ordered')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'ordered' && styles.statusFilterTextActive
          ]}>{i18n.t('purchasesOrderedFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'delivered' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('delivered')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'delivered' && styles.statusFilterTextActive
          ]}>{i18n.t('purchasesDeliveredFilter')}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.statusFilterButton,
            filterStatus === 'canceled' && styles.statusFilterButtonActive
          ]}
          onPress={() => setFilterStatus('canceled')}
        >
          <Text style={[
            styles.statusFilterText,
            filterStatus === 'canceled' && styles.statusFilterTextActive
          ]}>{i18n.t('purchasesCanceledFilter')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Zamiana funkcji renderListContainer na nową, bliższą stylowi MaintenanceManager
  const renderListContainer = () => (
    <View style={styles.listContainer}>
      {renderFilters()}
      {renderPurchaseList()}
    </View>
  );

  // Funckja do wyświetlania zdjęcia w pełnym wymiarze
  const handleImagePress = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    setShowImageModal(true);
  };
  
  // Funckja do zamykania modalu ze zdjęciem
  const closeImageModal = () => {
    setShowImageModal(false);
    setSelectedImage(null);
  };
  
  // Dodajemy nowy komponent renderujący modal ze zdjęciem
  const renderImageModal = () => (
    <Modal
      visible={showImageModal}
      transparent={true}
      animationType="fade"
      onRequestClose={closeImageModal}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.imageModalContent}>
          <TouchableOpacity 
            style={styles.closeImageButton}
            onPress={closeImageModal}
          >
            <Ionicons name="close-circle" size={32} color="white" />
          </TouchableOpacity>
          
          {selectedImage && (
            <Image 
              source={{ uri: selectedImage }} 
              style={styles.fullSizeImage}
              resizeMode="contain"
            />
          )}
        </View>
      </View>
    </Modal>
  );

  // Replace the debugCategory function
  const debugCategory = async (categoryId: string) => {
    console.log("Debugging category with ID:", categoryId);
    
    try {
      // Get current state of the category using a direct query that ignores is_active filter
      const { data: categoryData, error: categoryError } = await supabase
        .from('purchase_categories')
        .select('*')
        .eq('id', categoryId)
        .single();
      
      if (categoryError) {
        console.error("Error fetching category for debug:", categoryError);
        
        // Log detailed error info
        console.log("Error code:", categoryError.code);
        console.log("Error message:", categoryError.message);
        console.log("Error details:", categoryError.details);
        
        // Try a direct SQL query as a fallback to bypass RLS
        console.log("Attempting direct SQL query to debug category...");
        try {
          const { data: sqlData, error: sqlError } = await supabase.rpc('get_category_by_id', { 
            cat_id: categoryId 
          });
          
          if (sqlError) {
            console.error("SQL query error:", sqlError);
          } else if (sqlData) {
            console.log("SQL query result:", JSON.stringify(sqlData));
            return sqlData;
          }
        } catch (sqlCatchError) {
          console.error("Exception in SQL debug query:", sqlCatchError);
        }
        
        return null;
      }
      
      console.log("Current category data:", JSON.stringify(categoryData));
      
      // Log specific fields of interest
      if (categoryData) {
        console.log("Name:", categoryData.name);
        console.log("is_active:", categoryData.is_active === undefined ? "undefined" : categoryData.is_active);
        console.log("All fields:", Object.keys(categoryData).join(", "));
      }
      
      return categoryData;
    } catch (error) {
      console.error("Unexpected error in debugCategory:", error);
      return null;
    }
  };

  // Add a function to check database permissions
  const checkPermissions = async () => {
    console.log("Sprawdzanie uprawnień do kategorii...");
    
    try {
      // First check if we can read categories
      const { data: readData, error: readError } = await supabase
        .from('purchase_categories')
        .select('count(*)')
        .limit(1);
        
      console.log("Test odczytu kategorii:", readError ? "Błąd" : "Sukces");
      if (readError) {
        console.error("Błąd odczytu:", readError.message);
      } else {
        console.log("Wynik odczytu:", readData);
      }
      
      // Then try to insert a test category
      const testCategoryId = `test-${new Date().getTime()}`;
      const { data: insertData, error: insertError } = await supabase
        .from('purchase_categories')
        .insert([{
          id: testCategoryId,
          name: 'Test Category - To Delete',
          company_id: companyId,
          description: 'Test category for permission check',
          is_active: true
        }])
        .select();
        
      console.log("Test dodawania kategorii:", insertError ? "Błąd" : "Sukces");
      if (insertError) {
        console.error("Błąd dodawania:", insertError.message);
      } else {
        console.log("Wynik dodawania:", insertData);
        
        // If insert worked, try to update it
        const { data: updateData, error: updateError } = await supabase
          .from('purchase_categories')
          .update({ is_active: false })
          .eq('id', testCategoryId)
          .select();
        
        console.log("Test aktualizacji kategorii:", updateError ? "Błąd" : "Sukces");
        if (updateError) {
          console.error("Błąd aktualizacji:", updateError.message);
        } else {
          console.log("Wynik aktualizacji:", updateData);
        }
        
        // Finally, try to delete it
        const { error: deleteError } = await supabase
          .from('purchase_categories')
          .delete()
          .eq('id', testCategoryId);
        
        console.log("Test usuwania kategorii:", deleteError ? "Błąd" : "Sukces");
        if (deleteError) {
          console.error("Błąd usuwania:", deleteError.message);
        }
      }
      
      return !readError; // Return true if we can at least read
    } catch (error) {
      console.error("Nieoczekiwany błąd podczas sprawdzania uprawnień:", error);
      return false;
    }
  };

  // Near the top, after hiddenCategoryIds state declaration
  // Ensure hiddenCategoryIds is initialized during component mounting
  React.useEffect(() => {
    // Log our initially hidden categories
    console.log("🏁 INIT: Component mounted, hiddenCategoryIds:", hiddenCategoryIds);
    
    // Add the problematic category to our hidden list if it isn't already hidden
    const problemCategoryId = "69c1dd83-69a0-4b41-88fb-0b685892173b"; // Narzędzia category
    
    if (!hiddenCategoryIds.includes(problemCategoryId)) {
      console.log("🏁 INIT: Hiding problematic category:", problemCategoryId);
      setHiddenCategoryIds(prev => [...prev, problemCategoryId]);
    }
  }, []); // Empty dependency array means this runs only once at component mount

  // Add an effect to re-filter categories when hiddenCategoryIds changes
  React.useEffect(() => {
    if (hiddenCategoryIds.length > 0) {
      console.log("🔄 hiddenCategoryIds changed, filtering categories");
      
      // Na iOS pomijamy ponowne filtrowanie, żeby uniknąć problemów z wyświetlaniem kategorii
      const isIOS = Platform.OS === 'ios';
      if (isIOS) {
        console.log("📱 Wykryto iOS - całkowicie pomijam filtrowanie kategorii");
        console.log("📱 iOS Categories count:", categories.length);
        return;
      }
      
      // Tylko dla innych platform (web, Android) wykonujemy filtrowanie
      setCategories(prev => {
        const filtered = prev.filter(cat => !hiddenCategoryIds.includes(cat.id));
        console.log(`Filtered categories from ${prev.length} to ${filtered.length}`);
        return filtered;
      });
    }
  }, [hiddenCategoryIds]);

  // Dodaj nową funkcję do naprawy uprawnień bazy danych
  const repairDatabasePermissions = async () => {
    setLoading(true);
    console.log("🛠️ Próba naprawy uprawnień bazy danych...");

    try {
      // Krok 1: Sprawdzenie tabeli purchase_categories
      console.log("🛠️ Sprawdzam strukturę tabeli purchase_categories...");
      
      const { data: tableData, error: tableError } = await supabase
        .from('purchase_categories')
        .select('*')
        .limit(1);
      
      if (tableError) {
        console.error("🛠️ Błąd podczas sprawdzania tabeli:", tableError);
        Alert.alert("Błąd", `Nie można sprawdzić tabeli: ${tableError.message}`);
        return;
      }
      
      // Sprawdź, czy kolumna is_active istnieje
      const hasIsActiveColumn = tableData && tableData.length > 0 && 'is_active' in tableData[0];
      console.log("🛠️ Czy tabela ma kolumnę is_active:", hasIsActiveColumn);
      
      if (!hasIsActiveColumn) {
        console.log("🛠️ Brak kolumny is_active, próba dodania...");
        
        // Nie możemy dodać kolumny przez API Supabase, więc poinformujmy użytkownika
        Alert.alert(
          "Potrzebna aktualizacja",
          "Twoja baza danych wymaga aktualizacji. Skontaktuj się z administratorem, aby dodać kolumnę 'is_active' do tabeli 'purchase_categories'.",
          [{ text: "OK" }]
        );
        return;
      }
      
      // Krok 2: Wykonaj bezpośrednie zapytanie testowe
      console.log("🛠️ Próba wykonania testowej aktualizacji...");
      
      // Pobierz kategorię, którą chcemy zaktualizować
      const testCategoryId = "12fdf5ab-a5e1-4f9f-b5d5-091506295cf1"; // Sprzęt biurowy
      
      const { data: category, error: getCatError } = await supabase
        .from('purchase_categories')
        .select('*')
        .eq('id', testCategoryId)
        .single();
      
      if (getCatError) {
        console.error("🛠️ Nie można znaleźć testowej kategorii:", getCatError);
        Alert.alert("Błąd", `Nie można znaleźć kategorii testowej: ${getCatError.message}`);
        return;
      }
      
      console.log("🛠️ Znaleziono kategorię testową:", category);
      
      // Spróbuj bezpośrednio ukryć kategorię
      const currentStatus = category.is_active !== false;
      console.log(`🛠️ Próba zmiany is_active z ${currentStatus} na false...`);
      
      const { error: updateError } = await supabase
        .from('purchase_categories')
        .update({ is_active: false })
        .eq('id', testCategoryId);
      
      if (updateError) {
        console.error("🛠️ Błąd podczas aktualizacji:", updateError);
        Alert.alert(
          "Błąd aktualizacji",
          `Nie można zaktualizować kategorii: ${updateError.message}. Skontaktuj się z administratorem bazy danych.`,
          [{ text: "OK" }]
        );
        return;
      }
      
      console.log("🛠️ Aktualizacja powiodła się!");
      
      // Krok 3: Sprawdź, czy aktualizacja się powiodła
      const { data: updatedCategory, error: checkError } = await supabase
        .from('purchase_categories')
        .select('*')
        .eq('id', testCategoryId)
        .single();
      
      if (checkError) {
        console.error("🛠️ Błąd podczas sprawdzania aktualizacji:", checkError);
      } else {
        console.log("🛠️ Stan kategorii po aktualizacji:", updatedCategory);
        
        if (updatedCategory.is_active === false) {
          console.log("🛠️ Potwierdzono zmianę is_active na false");
          // Aktualizacja UI
          setCategories(prev => prev.filter(c => c.id !== testCategoryId));
          
          Alert.alert(
            "Naprawa udana!",
            "Naprawiono uprawnienia bazy danych. Teraz możesz ukrywać kategorie.",
            [{ 
              text: "OK",
              onPress: () => fetchCategories()
            }]
          );
        } else {
          console.log("🛠️ Zmiana is_active nie została zapisana");
          Alert.alert(
            "Częściowa naprawa",
            "Aktualizacja została wykonana bez błędów, ale zmiany nie zostały zapisane. Może to być problem z polityką RLS.",
            [{ text: "OK" }]
          );
        }
      }
    } catch (error) {
      console.error("🛠️ Nieoczekiwany błąd:", error);
      Alert.alert("Błąd", `Wystąpił nieoczekiwany problem: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  // Dodaję funkcję pomocniczą do konwersji priorytetów na wartości liczbowe
  const getPriorityValue = (priority: string): number => {
    switch (priority) {
      case 'critical': return 3;
      case 'high': return 2;
      case 'medium': return 1;
      case 'low': return 0;
      default: return 1; // domyślnie średni
    }
  };

  // Modyfikacja dla iOS - całkowicie usuwamy stare podejście z hiddenCategoryIds
  // i zastępujemy nowym, bardziej kompatybilnym kodem
  React.useEffect(() => {
    // Log our initial state
    console.log("🏁 INIT: Component mounted, platform:", Platform.OS);
    
    // Na iOS całkowicie wyłączamy filtrowanie kategorii
    if (Platform.OS === 'ios') {
      console.log("📱 iOS: Całkowicie wyłączam filtrowanie kategorii");
    } else {
      // Tylko dla innych platform (web, Android) możemy zastosować filtrowanie
      console.log("💻 Non-iOS platform: standardowe działanie");
      
      // Dodajemy zabezpieczenie przed problematycznymi kategoriami
      const problemCategoryId = "69c1dd83-69a0-4b41-88fb-0b685892173b"; // Narzędzia
      console.log("💻 Non-iOS: Będziemy filtrować kategorię Narzędzia podczas pobierania");
    }
  }, []); // Empty dependency array means this runs only once at component mount

  // Nowy komponent do zarządzania kategoriami
  const renderCategoryManager = () => {
    if (showCategoryModal) {
  return (
        <Modal
          visible={true}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowCategoryModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{i18n.t('purchasesAddCategory')}</Text>
              <TouchableOpacity 
                  style={styles.modalCloseButton}
                  onPress={() => setShowCategoryModal(false)}
                >
                  <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
              <View style={styles.modalBody}>
                {loading ? (
                  <View style={styles.modalLoading}>
                    <ActivityIndicator size="large" color="#2563EB" />
                    <Text style={styles.modalLoadingText}>{i18n.t('loading')}</Text>
            </View>
                ) : (
                  <>
                    <View style={styles.formGroup}>
                      <Text style={styles.formLabel}>{i18n.t('purchasesNewCategoryName')}</Text>
              <TextInput
                style={styles.formInput}
                        placeholder={i18n.t('purchasesNewCategoryName')}
                placeholderTextColor="#9CA3AF"
                        value={newCategoryName}
                        onChangeText={setNewCategoryName}
              />
            </View>
            
                    <View style={styles.modalButtonsContainer}>
                <TouchableOpacity
                        style={styles.modalCancelButton}
                        onPress={() => setShowCategoryModal(false)}
                      >
                        <Text style={styles.modalCancelButtonText}>{i18n.t('cancel')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                        style={styles.modalCreateButton}
                        onPress={handleAddCategory}
                      >
                        <Text style={styles.modalCreateButtonText}>{i18n.t('purchasesCreate')}</Text>
                </TouchableOpacity>
                  </View>
                  </>
                )}
                  </View>
              </View>
            </View>
        </Modal>
      );
    }
    return null;
  };

  // Modal z datami
  const renderDateFilterModal = () => (
      <Modal
        visible={showDateFilters}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDateFilters(false)}
      >
        <View style={styles.dateModalOverlay}>
          <View style={styles.dateModalContent}>
          <Text style={styles.dateModalTitle}>{i18n.t('purchasesSelectDateRange')}</Text>
          
          <View style={styles.dateInputContainer}>
            <View style={styles.dateInputGroup}>
              <Text style={styles.dateInputLabel}>{i18n.t('purchasesFromDate')}</Text>
              <DatePicker
                date={filterDateFrom}
                onDateChange={(date) => setFilterDateFrom(date)}
                placeholder={i18n.t('purchasesFromDate')}
              />
            </View>
              
            <View style={styles.dateInputGroup}>
              <Text style={styles.dateInputLabel}>{i18n.t('purchasesToDate')}</Text>
              <DatePicker
                date={filterDateTo}
                onDateChange={(date) => setFilterDateTo(date)}
                placeholder={i18n.t('purchasesToDate')}
              />
            </View>
          </View>
              
          <View style={styles.dateModalButtonsContainer}>
                <TouchableOpacity 
              style={styles.dateModalClearButton}
                  onPress={() => {
                    setFilterDateFrom('');
                    setFilterDateTo('');
                  }}
                >
              <Text style={styles.dateModalClearButtonText}>{i18n.t('clear')}</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
              style={styles.dateModalApplyButton}
                  onPress={() => setShowDateFilters(false)}
                >
              <Text style={styles.dateModalApplyButtonText}>{i18n.t('purchasesApply')}</Text>
                </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
  );

  // Add a handleAddCategory function above the renderCategoryModal function
  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      Alert.alert(i18n.t('error'), i18n.t('purchasesCategoryNameEmpty'));
      return;
    }
    
    setLoading(true);
    
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .insert([
          {
            name: newCategoryName.trim(),
            company_id: companyId,
            description: '',
            is_active: true
          }
        ])
        .select();
      
      if (error) throw error;
      
      if (data && data.length > 0) {
        // Success - add to local categories and select it
        const newCategory = data[0];
        setCategories([...categories, newCategory]);
        setForm({...form, category: newCategory.name});
        setShowCategoryModal(false);
        setNewCategoryName('');
      }
    } catch (error) {
      Alert.alert(i18n.t('error'), `${i18n.t('purchasesCategoryAddError')}: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.mainContainer}>
      {/* Remove TopBar rendering */}
      <View style={styles.container}>
        {activeTab === 'list' ? renderListContainer() : (
          <ScrollView style={styles.addContainer} contentContainerStyle={styles.formScrollContent}>
            <View style={styles.header}>
              <TouchableOpacity 
                style={styles.backButton}
                onPress={() => {
                  setForm({
                    title: '',
                    description: '',
                    category: categories.length > 0 ? categories[0].name : '',
                    price_estimate: '',
                    quantity: '',
                    priority: 'medium',
                    notes: '',
                    attachments: []
                  });
                  onTabChange('list');
                }}
              >
                <Ionicons name="arrow-back" size={22} color="#1F2937" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>{i18n.t('purchasesAddNewPurchase')}</Text>
              <View style={styles.placeholderView} />
            </View>
            
            {renderAddPurchaseForm()}
          </ScrollView>
        )}
      </View>
      {renderBottomTabs()}
      {renderCategoryModal()}
      {renderConfirmDialog()}
      {renderImageModal()} {/* Dodajemy modal do wyświetlania zdjęć */}
      {/* Dodajemy modal filtrów daty pod koniec, przed zamknięciem głównego View */}
      {renderDateFilterModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
    width: '100%',
  },
  content: {
    flex: 1,
    width: '100%',
  },
  formScrollContent: {
    padding: 20,
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  formContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  bottomTabs: {
    flexDirection: 'row',
    height: 60,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: 'white',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    borderTopWidth: 2,
    borderTopColor: '#2563EB',
  },
  tabText: {
    fontSize: 10,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
    paddingHorizontal: 2,
  },
  activeTabText: {
    color: '#2563EB',
    fontWeight: '500',
  },
  listContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    width: '100%',
  },
  addContainer: {
    flex: 1,
    padding: 0,
  },
  searchBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 8,
    marginTop: 16,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    color: '#1F2937',
    fontSize: 14,
  },
  clearSearchButton: {
    padding: 4,
  },
  statusFilterContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusFilterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginBottom: 8,
    backgroundColor: '#F3F4F6',
    marginHorizontal: 6,
    marginVertical: 4,
  },
  statusFilterButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  statusFilterText: {
    fontSize: 14,
    color: '#4B5563',
  },
  statusFilterTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  sortingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sortingLabel: {
    color: '#4B5563',
    fontSize: 12,
    marginRight: 8,
  },
  sortingButtons: {
    flexDirection: 'row',
  },
  sortButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    borderRadius: 4,
  },
  sortButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  sortButtonText: {
    color: '#4B5563',
    fontSize: 12,
  },
  sortButtonTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  loaderText: {
    color: '#6B7280',
    marginTop: 8,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 8,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  purchasesList: {
    flex: 1,
    width: '100%',
  },
  purchasesListContent: {
    width: '100%',
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  purchaseItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  purchaseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  purchaseTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
    flex: 1,
    marginRight: 8,
  },
  statusContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 4,
    borderRadius: 12,
    fontSize: 10,
    fontWeight: '500',
    overflow: 'hidden',
    width: 75,
    textAlign: 'center',
    marginHorizontal: 2,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  purchaseDetails: {
    marginBottom: 12,
  },
  purchaseDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  purchaseDetailText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 8,
  },
  purchaseFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  priorityText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 24,
    marginTop: 10,
  },
  formInputGroup: {
    marginBottom: 24,
  },
  formField: {
    marginBottom: 12,
  },
  formLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 10,
  },
  requiredAsterisk: {
    color: '#EF4444',
    fontWeight: 'bold',
  },
  formInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  categoryContainer: {
    flexDirection: 'row',
  },
  categorySelector: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: 'white',
  },
  categorySelectorText: {
    fontSize: 16,
    color: '#1F2937',
  },
  categorySelectorPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  addCategoryButton: {
    width: 48,
    height: 48,
    backgroundColor: '#2563EB',
    borderRadius: 8,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    gap: 8,
  },
  priorityCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    backgroundColor: 'white',
  },
  priorityCircleSelected: {
    borderColor: '#2563EB',
    borderWidth: 2,
  },
  priorityCircleInner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  priorityDot: {
    width: 16, 
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  priorityDotLow: {
    backgroundColor: '#10B981', // Green
  },
  priorityDotMedium: {
    backgroundColor: '#3B82F6', // Blue
  },
  priorityDotHigh: {
    backgroundColor: '#F59E0B', // Amber
  },
  priorityDotCritical: {
    backgroundColor: '#EF4444', // Red
  },
  priorityText: {
    fontSize: 14,
    color: '#4B5563',
  },
  formFieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  formRequiredFieldsInfo: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  imageItem: {
    position: 'relative',
    marginRight: 12,
    marginBottom: 12,
  },
  productImage: {
    width: 120,
    height: 120,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'transparent',
  },
  photoButtonText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  modalCloseButton: {
    padding: 8,
  },
  modalLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalLoadingText: {
    color: '#6B7280',
    marginTop: 8,
    fontSize: 14,
  },
  modalEmptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalEmptyText: {
    color: '#6B7280',
    fontSize: 14,
  },
  modalEmptySubtext: {
    color: '#6B7280',
    fontSize: 12,
    marginBottom: 16,
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  categoryList: {
    flex: 1,
    minHeight: 200,
    maxHeight: 400,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    ...(Platform.OS === 'ios' ? {
      marginVertical: 4,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
      backgroundColor: '#F9FAFB',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
    } : {})
  },
  categoryItemTemp: {
    backgroundColor: '#FAFAFA',
  },
  categoryItemContent: {
    flex: 1,
  },
  categoryItemTouchable: {
    flex: 1,
  },
  categoryItemText: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '500',
  },
  tempCategoryBadge: {
    color: '#92400E',
    fontSize: 10,
    marginTop: 4,
    fontStyle: 'italic',
  },
  confirmModal: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  confirmModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
    textAlign: 'center',
  },
  confirmModalMessage: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 20,
    textAlign: 'center',
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  confirmModalCancelButton: {
    padding: 10,
    minWidth: 100,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 10,
  },
  confirmModalConfirmButton: {
    padding: 10,
    minWidth: 100,
    backgroundColor: '#2563EB',
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmModalCancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  confirmModalConfirmButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  tableContainer: {
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 0,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginHorizontal: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    marginTop: 16,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
  },
  headerCell: {
    paddingHorizontal: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    paddingHorizontal: 2,
  },
  headerText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4B5563',
    textAlign: 'center',
  },
  sortIcon: {
    marginLeft: 4,
  },
  tableBody: {
    flex: 1,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    maxHeight: '75%',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 6,
    borderBottomWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    width: '100%',
  },
  cell: {
    fontSize: 13,
    color: '#1F2937',
    textAlign: 'center',
    paddingHorizontal: 2,
  },
  statusPending: {
    backgroundColor: '#F59E0B',
    color: '#92400E',
  },
  statusApproved: {
    backgroundColor: '#3B82F6',
    color: '#1E40AF',
  },
  statusOrdered: {
    backgroundColor: '#8B5CF6',
    color: '#4C1D95',
  },
  statusDelivered: {
    backgroundColor: '#10B981',
    color: '#065F46',
  },
  statusCanceled: {
    backgroundColor: '#6B7280',
    color: '#1F2937',
  },
  priorityLow: {
    backgroundColor: '#D1FAE5',
    color: '#065F46',
  },
  priorityMedium: {
    backgroundColor: '#FEF3C7',
    color: '#92400E',
  },
  priorityHigh: {
    backgroundColor: '#FEE2E2',
    color: '#991B1B',
  },
  priorityCritical: {
    backgroundColor: '#FECACA',
    color: '#7F1D1D',
  },
  filtersContainer: {
    backgroundColor: 'white',
    paddingVertical: 12,
    paddingBottom: 0,
    width: '100%',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  filterButtonText: {
    marginHorizontal: 8,
    fontSize: 14,
    color: '#1F2937',
  },
  filterContent: {
    padding: 16,
  },
  filterLabel: {
    color: '#4B5563',
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  filterInput: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 12,
  },
  priorityButtonsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  priorityFilterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    marginBottom: 8,
  },
  priorityFilterButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  priorityFilterButtonText: {
    color: '#4B5563',
    fontSize: 12,
    fontWeight: '500',
  },
  priorityFilterButtonTextActive: {
    color: '#2563EB',
    fontWeight: 'bold',
  },
  addCategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginTop: 16,
  },
  addCategoryButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 8,
  },
  addCategoryButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 16,
    marginHorizontal: 16,
  },
  addCategoryButtonSmallText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  addCategoryForm: {
    padding: 16,
  },
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 16,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  imageItem: {
    position: 'relative',
    marginRight: 12,
    marginBottom: 12,
  },
  productImage: {
    width: 120,
    height: 120,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  imagePickerOutline: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'transparent',
  },
  imagePickerText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  imageModalContent: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderRadius: 10,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeImageButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
  },
  fullSizeImage: {
    width: '90%',
    height: '70%',
    borderRadius: 8,
  },
  statusFilterScrollView: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginLeft: 8,
  },
  dateButtonActive: {
    backgroundColor: '#DBEAFE',
  },
  dateButtonText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 4,
  },
  dateButtonTextActive: {
    color: '#2563EB',
    fontWeight: '500',
  },
  dateModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dateModalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    width: '90%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  dateModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  dateModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  dateModalBody: {
    padding: 16,
    gap: 16,
  },
  dateModalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  dateModalCancelButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    marginRight: 8,
    alignItems: 'center',
  },
  dateModalCancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  dateModalConfirmButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#2563EB',
    borderRadius: 6,
    alignItems: 'center',
  },
  dateModalConfirmButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  categoryItemActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  hideCategoryButton: {
    padding: 10,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 44,
    minHeight: 44,
  },
  categoryMainContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  infoBox: {
    backgroundColor: '#f9f9f9',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  infoText: {
    color: '#333',
    fontSize: 12,
    textAlign: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  cancelButtonText: {
    color: '#6B7280',
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  formCancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  formCancelButtonText: {
    color: '#6B7280',
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 16,
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingBottom: 8,
    display: 'none', // Ukrywamy stary nagłówek
  },
  backText: {
    fontSize: 16,
    marginLeft: 8,
    color: '#1F2937',
    display: 'none', // Ukrywamy tekst "Powrót"
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
    display: 'none', // Ukrywamy tytuł, bo jest w headerze
  },
  dateInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  dateInputGroup: {
    flex: 1,
  },
  dateInputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  dateModalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  dateModalClearButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
    marginRight: 8,
    alignItems: 'center',
  },
  dateModalClearButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  dateModalApplyButton: {
    flex: 1,
    padding: 12,
    backgroundColor: '#2563EB',
    borderRadius: 6,
    alignItems: 'center',
  },
  dateModalApplyButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  categorySection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryPicker: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    marginRight: 8,
  },
  categoryPickerText: {
    color: '#1F2937',
    fontSize: 14,
  },
  requiredLabel: {
    color: '#EF4444',
    fontSize: 12,
    marginBottom: 8,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  helperText: {
    color: '#6B7280',
    fontSize: 12,
    marginBottom: 16,
  },
  // Add form specific styles for PurchasesManager
  formContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 6,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: 'white',
  },
  formTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 24,
    textAlign: 'center',
  },
  categorySection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  // Image handling styles
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  imageWrapper: {
    position: 'relative',
    margin: 4,
  },
  imagePreview: {
    width: 100,
    height: 100,
    borderRadius: 4,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'rgba(255,255,255,0.8)',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imagePicker: {
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#3B82F6',
    borderRadius: 8,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    padding: 16,
  },
  // Modal styles
  modalBody: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  modalLoading: {
    padding: 20,
    alignItems: 'center',
  },
  modalButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalCancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: 'white',
  },
  modalCancelButtonText: {
    color: '#4B5563',
    fontWeight: '500',
  },
  modalCreateButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    backgroundColor: '#2563EB',
  },
  modalCreateButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  formHeader: {
    marginBottom: 24,
    alignItems: 'center',
  },
  formTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1F2937',
    textAlign: 'center',
  },
  formInputGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 6,
  },
  requiredAsterisk: {
    color: '#EF4444',
    fontWeight: 'bold',
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: 'white',
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  categoryContainer: {
    flexDirection: 'row',
  },
  categorySelector: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: 'white',
  },
  categorySelectorText: {
    fontSize: 16,
    color: '#1F2937',
  },
  categorySelectorPlaceholder: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  addCategoryButton: {
    width: 48,
    height: 48,
    backgroundColor: '#2563EB',
    borderRadius: 8,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  priorityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    gap: 8,
  },
  priorityCircle: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    backgroundColor: 'white',
  },
  priorityCircleSelected: {
    borderColor: '#2563EB',
    borderWidth: 2,
  },
  priorityCircleInner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  priorityDot: {
    width: 16, 
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  priorityDotLow: {
    backgroundColor: '#10B981', // Green
  },
  priorityDotMedium: {
    backgroundColor: '#3B82F6', // Blue
  },
  priorityDotHigh: {
    backgroundColor: '#F59E0B', // Amber
  },
  priorityDotCritical: {
    backgroundColor: '#EF4444', // Red
  },
  priorityText: {
    fontSize: 14,
    color: '#4B5563',
  },
  formFieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  formRequiredFieldsInfo: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
    marginBottom: 8,
  },
  imageItem: {
    position: 'relative',
    marginRight: 12,
    marginBottom: 12,
  },
  productImage: {
    width: 120,
    height: 120,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'transparent',
    zIndex: 10,
  },
  photoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: '#2563EB',
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'transparent',
  },
  photoButtonText: {
    color: '#2563EB',
    fontWeight: '500',
    marginLeft: 8,
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
});

export default PurchasesManager; 
