const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testStripeCheckoutWithRealCompany() {
  try {
    console.log('=== Testing Stripe Checkout with Real Company ===\n');
    
    // Znajdź prawdziwą firmę
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1);

    if (companiesError || !companies || companies.length === 0) {
      console.error('No companies found:', companiesError);
      return;
    }

    const testCompany = companies[0];
    console.log(`Using company: ${testCompany.name} (${testCompany.id})`);

    // Pobierz plan roczny Basic
    const { data: yearlyPlan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', 'Basic Yearly')
      .single();

    if (planError || !yearlyPlan) {
      console.error('Basic Yearly plan not found:', planError);
      return;
    }

    console.log(`\nTesting plan: ${yearlyPlan.name}`);
    console.log(`Plan ID: ${yearlyPlan.id}`);
    console.log(`Stripe Price ID: ${yearlyPlan.stripe_price_id}`);
    console.log(`Price: ${yearlyPlan.price/100} PLN`);

    // Test Stripe Checkout
    console.log('\n=== Testing Stripe Checkout ===');
    
    try {
      const { data: checkoutData, error: checkoutError } = await supabase.functions.invoke('stripe-checkout', {
        body: {
          companyId: testCompany.id,
          planId: yearlyPlan.id,
          successUrl: 'https://example.com/success',
          cancelUrl: 'https://example.com/cancel'
        }
      });

      if (checkoutError) {
        console.log(`❌ Checkout error: ${checkoutError.message}`);
        console.log('Error details:', checkoutError);
      } else if (checkoutData?.url) {
        console.log(`✅ Checkout session created successfully!`);
        console.log(`URL: ${checkoutData.url}`);
        
        // Sprawdź czy URL zawiera poprawny Price ID
        if (checkoutData.url.includes(yearlyPlan.stripe_price_id)) {
          console.log('✅ URL contains correct Price ID');
        } else {
          console.log('❌ URL does not contain expected Price ID');
        }
      } else {
        console.log(`❌ No URL in response:`, checkoutData);
      }
    } catch (err) {
      console.log(`❌ Exception during checkout: ${err.message}`);
    }

    // Test wszystkich planów rocznych
    console.log('\n=== Testing All Yearly Plans ===');
    
    const { data: yearlyPlans, error: yearlyError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('billing_period', 'yearly')
      .eq('active', true);

    if (yearlyError) {
      console.error('Error fetching yearly plans:', yearlyError);
      return;
    }

    for (const plan of yearlyPlans) {
      console.log(`\nTesting ${plan.name}...`);
      
      try {
        const { data, error } = await supabase.functions.invoke('stripe-checkout', {
          body: {
            companyId: testCompany.id,
            planId: plan.id,
            successUrl: 'https://example.com/success',
            cancelUrl: 'https://example.com/cancel'
          }
        });

        if (error) {
          console.log(`❌ ${plan.name}: ${error.message}`);
        } else if (data?.url) {
          console.log(`✅ ${plan.name}: Checkout created`);
        } else {
          console.log(`❌ ${plan.name}: No URL returned`);
        }
      } catch (err) {
        console.log(`❌ ${plan.name}: Exception - ${err.message}`);
      }
    }

    // Sprawdź ceny planów
    console.log('\n=== Checking Plan Prices ===');
    
    const { data: allPlans, error: allPlansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    if (!allPlansError && allPlans) {
      console.log('\nCurrent plan prices:');
      allPlans.forEach(plan => {
        const pricePerMonth = plan.billing_period === 'yearly' ? 
          Math.round(plan.price / 12) : plan.price;
        console.log(`${plan.name}: ${plan.price/100} PLN/${plan.billing_period} (${pricePerMonth/100} PLN/month)`);
      });

      // Sprawdź czy ceny roczne są sensowne
      const monthlyBasic = allPlans.find(p => p.name === 'Basic' && p.billing_period === 'monthly');
      const yearlyBasic = allPlans.find(p => p.name === 'Basic Yearly');
      
      if (monthlyBasic && yearlyBasic) {
        const monthlyYearlyCost = monthlyBasic.price * 12;
        const actualYearlyCost = yearlyBasic.price;
        const savings = monthlyYearlyCost - actualYearlyCost;
        
        console.log(`\nBasic plan comparison:`);
        console.log(`Monthly × 12: ${monthlyYearlyCost/100} PLN`);
        console.log(`Yearly: ${actualYearlyCost/100} PLN`);
        console.log(`Savings: ${savings/100} PLN`);
        
        if (savings > 0) {
          console.log('✅ Yearly plan offers savings');
        } else {
          console.log('❌ Yearly plan is more expensive than monthly!');
          console.log('💡 Consider updating yearly plan prices to offer discounts');
        }
      }
    }

  } catch (error) {
    console.error('Error in testStripeCheckoutWithRealCompany:', error);
  }
}

testStripeCheckoutWithRealCompany();
