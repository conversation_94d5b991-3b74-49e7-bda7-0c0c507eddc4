-- SQL code to update the purchase_status enum type to include the missing values
-- This version splits operations into separate transactions to avoid the error:
-- "unsafe use of new value of enum type"

-- First transaction: Add 'ordered' value to the enum
DO $$
BEGIN
    -- Check if value already exists to avoid errors when script is run multiple times
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'ordered' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'purchase_status')
    ) THEN
        -- Add the 'ordered' value
        ALTER TYPE purchase_status ADD VALUE 'ordered' AFTER 'approved';
    END IF;
END$$;

-- Force commit of the first transaction
COMMIT;

-- Second transaction: Add 'delivered' value to the enum
DO $$
BEGIN
    -- Check if value already exists to avoid errors when script is run multiple times
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'delivered' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'purchase_status')
    ) THEN
        -- Add the 'delivered' value
        ALTER TYPE purchase_status ADD VALUE 'delivered' AFTER 'ordered';
    END IF;
END$$;

-- Force commit of the second transaction
COMMIT;

-- Verify the current values in the enum
SELECT enum_range(NULL::purchase_status);

-- Alternative method if the above doesn't work
-- This method creates a new enum type with all values and replaces the original type
-- Uncomment these lines if needed:

/*
BEGIN;
-- Create a new type with all the values needed
CREATE TYPE purchase_status_new AS ENUM ('pending', 'approved', 'ordered', 'delivered', 'canceled');

-- Update the columns to use the new type
ALTER TABLE purchases 
    ALTER COLUMN status TYPE purchase_status_new 
    USING (
        CASE status::text
            WHEN 'pending' THEN 'pending'::purchase_status_new
            WHEN 'approved' THEN 'approved'::purchase_status_new
            WHEN 'canceled' THEN 'canceled'::purchase_status_new
            ELSE 'pending'::purchase_status_new
        END
    );

-- Drop the old type
DROP TYPE purchase_status;

-- Rename the new type to the old name
ALTER TYPE purchase_status_new RENAME TO purchase_status;
COMMIT;
*/ 