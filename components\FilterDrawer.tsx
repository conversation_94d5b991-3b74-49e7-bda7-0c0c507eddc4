import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Platform, ScrollView, Modal, TouchableWithoutFeedback } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface FilterDrawerProps {
  isVisible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  position?: { top: number, left: number, width: number };
  onClearFilters?: () => void;
}

const FilterDrawer = ({ 
  isVisible, 
  onClose, 
  children, 
  title = 'Filtry',
  position = { top: 60, left: 10, width: 300 },
  onClearFilters
}: FilterDrawerProps) => {
  const translateY = React.useRef(new Animated.Value(-10)).current;
  const opacity = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: -10,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()}>
            <Animated.View
              style={[
                styles.drawer,
                {
                  transform: [{ translateY }],
                  opacity,
                  position: 'absolute',
                  top: position.top,
                  left: position.left,
                  width: position.width,
                },
              ]}
            >
              <View style={styles.header}>
                <Text style={styles.title}>{title}</Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <Ionicons name="close" size={20} color="#666" />
                </TouchableOpacity>
              </View>
              <ScrollView 
                style={styles.content}
                nestedScrollEnabled={true}
                keyboardShouldPersistTaps="handled"
              >
                {children}
              </ScrollView>
              
              {onClearFilters && (
                <View style={styles.footer}>
                  <TouchableOpacity 
                    style={styles.clearButton}
                    onPress={() => {
                      onClearFilters();
                    }}
                  >
                    <Ionicons name="trash-outline" size={16} color="#666" />
                    <Text style={styles.clearButtonText}>Wyczyść filtry</Text>
                  </TouchableOpacity>
                </View>
              )}
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  drawer: {
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
    borderRadius: 4,
    maxHeight: 400,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  closeButton: {
    padding: 2,
  },
  content: {
    padding: 12,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    padding: 12,
    alignItems: 'center',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  clearButtonText: {
    marginLeft: 8,
    color: '#666',
    fontWeight: '500',
  },
});

export default FilterDrawer; 