const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testYearlyPlanSelection() {
  try {
    console.log('=== Testing Yearly Plan Selection ===\n');
    
    // Pobierz wszystkie plany
    const { data: plans, error: plansError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('billing_period', { ascending: true })
      .order('name', { ascending: true });

    if (plansError) {
      console.error('Error fetching plans:', plansError);
      return;
    }

    console.log('Available plans:');
    plans.forEach(plan => {
      console.log(`- ${plan.name} (${plan.billing_period}): ${plan.stripe_price_id} - ${plan.price/100} PLN`);
    });

    // Test planów rocznych
    const yearlyPlans = plans.filter(plan => plan.billing_period === 'yearly');
    console.log(`\nFound ${yearlyPlans.length} yearly plans:`);

    for (const plan of yearlyPlans) {
      console.log(`\n=== Testing ${plan.name} ===`);
      console.log(`Plan ID: ${plan.id}`);
      console.log(`Stripe Price ID: ${plan.stripe_price_id}`);
      console.log(`Price: ${plan.price/100} PLN`);
      console.log(`Billing period: ${plan.billing_period}`);

      // Test tworzenia sesji checkout
      console.log('\nTesting Stripe Checkout session creation...');
      
      try {
        const { data: checkoutData, error: checkoutError } = await supabase.functions.invoke('stripe-checkout', {
          body: {
            companyId: 'test-company-id',
            planId: plan.id,
            successUrl: 'https://example.com/success',
            cancelUrl: 'https://example.com/cancel'
          }
        });

        if (checkoutError) {
          console.log(`❌ Checkout error: ${checkoutError.message}`);
        } else if (checkoutData?.url) {
          console.log(`✅ Checkout session created successfully!`);
          console.log(`URL: ${checkoutData.url.substring(0, 50)}...`);
        } else {
          console.log(`❌ No URL in response:`, checkoutData);
        }
      } catch (err) {
        console.log(`❌ Exception during checkout: ${err.message}`);
      }

      // Test mapowania nazwy planu
      const planNameNormalized = plan.name.toLowerCase().replace(/\s+/g, '').replace('yearly', '');
      console.log(`Normalized plan name for RPC: "${planNameNormalized}"`);
      
      // Test funkcji RPC
      console.log('Testing RPC function...');
      try {
        const { data: rpcResult, error: rpcError } = await supabase
          .rpc('update_company_account_type_rpc', {
            p_company_id: 'test-company-id',
            p_plan_name: planNameNormalized
          });

        if (rpcError) {
          console.log(`❌ RPC error: ${rpcError.message}`);
        } else {
          console.log(`✅ RPC function works: ${rpcResult}`);
        }
      } catch (err) {
        console.log(`❌ Exception during RPC: ${err.message}`);
      }
    }

    console.log('\n=== Summary ===');
    
    // Sprawdź czy wszystkie plany roczne mają poprawne Price ID
    const yearlyPlansWithCorrectPriceIds = yearlyPlans.filter(plan => 
      plan.stripe_price_id && plan.stripe_price_id.startsWith('price_1RXn')
    );

    console.log(`✅ Yearly plans with correct Stripe Price IDs: ${yearlyPlansWithCorrectPriceIds.length}/${yearlyPlans.length}`);
    
    if (yearlyPlansWithCorrectPriceIds.length === yearlyPlans.length) {
      console.log('🎉 All yearly plans are properly configured!');
      console.log('\n📝 Next steps:');
      console.log('1. Test the frontend plan selection');
      console.log('2. Verify that yearly plans redirect to correct Stripe checkout');
      console.log('3. Test webhook handling for yearly subscriptions');
    } else {
      console.log('❌ Some yearly plans need configuration fixes');
    }

    // Test różnicy między planami miesięcznymi a rocznymi
    console.log('\n=== Plan Comparison ===');
    const monthlyPlans = plans.filter(plan => plan.billing_period === 'monthly');
    
    for (const monthlyPlan of monthlyPlans) {
      const yearlyEquivalent = yearlyPlans.find(yearly => 
        yearly.name.toLowerCase().includes(monthlyPlan.name.toLowerCase())
      );
      
      if (yearlyEquivalent) {
        const monthlyCost = monthlyPlan.price * 12;
        const yearlyCost = yearlyEquivalent.price;
        const savings = monthlyCost - yearlyCost;
        const savingsPercent = Math.round((savings / monthlyCost) * 100);
        
        console.log(`${monthlyPlan.name}:`);
        console.log(`  Monthly: ${monthlyPlan.price/100} PLN/month (${monthlyCost/100} PLN/year)`);
        console.log(`  Yearly: ${yearlyCost/100} PLN/year`);
        console.log(`  Savings: ${savings/100} PLN (${savingsPercent}%)`);
      }
    }

  } catch (error) {
    console.error('Error in testYearlyPlanSelection:', error);
  }
}

testYearlyPlanSelection();
