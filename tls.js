// Prosty polyfill dla modułu tls, potrzebny dla biblioteki ws (WebSocket)
const tls = {
  TLSSocket: function() {
    return {
      connect: () => this,
      on: () => this,
      end: () => this,
      destroy: () => this,
      setTimeout: () => this,
      authorized: true,
      encrypted: true,
      getPeerCertificate: () => ({}),
      getCipher: () => ({})
    };
  },
  
  connect: (options) => {
    const socket = new tls.TLSSocket();
    return socket;
  },
  
  createServer: (options) => ({
    listen: () => ({}),
    on: () => ({}),
    close: () => ({})
  })
};

// Używamy module.exports dla lepszej kompatybilności z modułami CommonJS
module.exports = tls; 