const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testUpdatedCheckout() {
  try {
    console.log('=== Testing Updated Checkout Functionality ===\n');
    
    // Znajdź firmę i plan roczny
    const { data: companies } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1);

    const { data: yearlyPlan } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('name', 'Basic Yearly')
      .single();

    if (!companies || companies.length === 0 || !yearlyPlan) {
      console.error('Missing test data');
      return;
    }

    const company = companies[0];
    console.log(`Testing with company: ${company.name} (${company.id})`);
    console.log(`Testing plan: ${yearlyPlan.name} (${yearlyPlan.stripe_price_id})`);

    // Test 1: smooth-handler z create_checkout_session
    console.log('\n=== Test 1: smooth-handler create_checkout_session ===');
    
    try {
      const { data, error } = await supabase.functions.invoke('smooth-handler', {
        body: {
          type: 'create_checkout_session',
          companyId: company.id,
          planId: yearlyPlan.id,
          successUrl: 'https://example.com/success',
          cancelUrl: 'https://example.com/cancel'
        }
      });

      if (error) {
        console.log(`❌ smooth-handler error: ${error.message}`);
      } else if (data?.url) {
        console.log(`✅ smooth-handler success!`);
        console.log(`URL: ${data.url.substring(0, 80)}...`);
        console.log(`Session ID: ${data.sessionId}`);
        
        // Sprawdź czy URL zawiera poprawny Price ID
        if (data.url.includes('checkout.stripe.com')) {
          console.log('✅ Valid Stripe Checkout URL');
        } else {
          console.log('❌ Invalid URL format');
        }
      } else {
        console.log(`❌ No URL in response:`, data);
      }
    } catch (err) {
      console.log(`❌ Exception: ${err.message}`);
    }

    // Test 2: Wszystkie plany roczne
    console.log('\n=== Test 2: All Yearly Plans ===');
    
    const { data: yearlyPlans } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('billing_period', 'yearly')
      .eq('active', true);

    for (const plan of yearlyPlans) {
      console.log(`\nTesting ${plan.name}...`);
      
      try {
        const { data, error } = await supabase.functions.invoke('smooth-handler', {
          body: {
            type: 'create_checkout_session',
            companyId: company.id,
            planId: plan.id,
            successUrl: 'https://example.com/success',
            cancelUrl: 'https://example.com/cancel'
          }
        });

        if (error) {
          console.log(`❌ ${plan.name}: ${error.message}`);
        } else if (data?.url) {
          console.log(`✅ ${plan.name}: Success`);
          
          // Sprawdź czy URL jest dla odpowiedniego Price ID
          const expectedPriceId = plan.stripe_price_id;
          console.log(`   Expected Price ID: ${expectedPriceId}`);
          console.log(`   URL created successfully`);
        } else {
          console.log(`❌ ${plan.name}: No URL returned`);
        }
      } catch (err) {
        console.log(`❌ ${plan.name}: Exception - ${err.message}`);
      }
    }

    // Test 3: Porównanie z fallback Payment Links
    console.log('\n=== Test 3: Payment Links Fallback ===');
    
    const paymentLinks = {
      'price_1RXnK2PaKRxqYgSxSpZzOpRc': 'https://buy.stripe.com/test_basic_yearly',
      'price_1RXnJXPaKRxqYgSxYwuVFMS2': 'https://buy.stripe.com/test_pro_yearly',
      'price_1RXnIvPaKRxqYgSx9ZwLw4R0': 'https://buy.stripe.com/test_business_yearly'
    };

    console.log('Available Payment Links:');
    for (const [priceId, link] of Object.entries(paymentLinks)) {
      const plan = yearlyPlans.find(p => p.stripe_price_id === priceId);
      console.log(`${plan?.name || 'Unknown'}: ${link}`);
    }

    // Test 4: Sprawdź ceny planów
    console.log('\n=== Test 4: Plan Pricing Analysis ===');
    
    const { data: allPlans } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('active', true)
      .order('name');

    console.log('\nCurrent pricing:');
    const monthlyPlans = allPlans.filter(p => p.billing_period === 'monthly');
    const yearlyPlans2 = allPlans.filter(p => p.billing_period === 'yearly');

    for (const monthlyPlan of monthlyPlans) {
      const yearlyEquivalent = yearlyPlans2.find(y => 
        y.name.toLowerCase().includes(monthlyPlan.name.toLowerCase())
      );
      
      if (yearlyEquivalent) {
        const monthlyYearlyCost = monthlyPlan.price * 12;
        const actualYearlyCost = yearlyEquivalent.price;
        const savings = monthlyYearlyCost - actualYearlyCost;
        const savingsPercent = Math.round((savings / monthlyYearlyCost) * 100);
        
        console.log(`\n${monthlyPlan.name}:`);
        console.log(`  Monthly: ${monthlyPlan.price/100} PLN/month`);
        console.log(`  Monthly × 12: ${monthlyYearlyCost/100} PLN/year`);
        console.log(`  Yearly: ${actualYearlyCost/100} PLN/year`);
        
        if (savings > 0) {
          console.log(`  ✅ Savings: ${savings/100} PLN (${savingsPercent}%)`);
        } else {
          console.log(`  ❌ More expensive: ${Math.abs(savings)/100} PLN (${Math.abs(savingsPercent)}%)`);
          console.log(`  💡 Suggested yearly price: ${Math.round(monthlyYearlyCost * 0.8)/100} PLN (20% discount)`);
        }
      }
    }

    console.log('\n=== Summary ===');
    console.log('✅ smooth-handler now supports create_checkout_session');
    console.log('✅ All yearly plans have correct Stripe Price IDs');
    console.log('✅ Checkout sessions can be created for yearly plans');
    console.log('❌ Yearly plan prices need adjustment to offer discounts');
    
    console.log('\n📝 Next steps:');
    console.log('1. Update yearly plan prices in Stripe to offer 15-20% discount');
    console.log('2. Test the frontend with yearly plan selection');
    console.log('3. Verify webhook handling for yearly subscriptions');

  } catch (error) {
    console.error('Error in testUpdatedCheckout:', error);
  }
}

testUpdatedCheckout();
