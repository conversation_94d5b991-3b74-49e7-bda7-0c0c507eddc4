// Bezpośrednie polyfille dla Node.js
import { Buff<PERSON> } from 'buffer';
import * as streamBrowserify from 'stream-browserify';
import { TextEncoder, TextDecoder } from 'text-encoding';

// Ustawiamy globalne obiekty potrzebne dla aplikacji
global.Buffer = Buffer;
global.process = require('process');
global.stream = streamBrowserify;
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Zapewniamy dostępność innych modułów, które mogą być potrzebne
if (!global.crypto) {
  global.crypto = require('crypto-browserify');
}

console.log('Basic polyfills loaded successfully!'); 