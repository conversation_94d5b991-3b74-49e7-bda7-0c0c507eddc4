const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addUpdatedAtColumn() {
  try {
    console.log('Adding updated_at column to companies table...');
    
    // Dodaj kolumnę updated_at do tabeli companies
    const { data, error } = await supabase.rpc('execute_sql', {
      sql_query: `
        -- Do<PERSON>j kolumnę updated_at do tabeli companies jeśli nie istnieje
        ALTER TABLE companies 
        ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();
        
        -- <PERSON><PERSON><PERSON> war<PERSON> updated_at dla istniejących rekordów
        UPDATE companies 
        SET updated_at = created_at 
        WHERE updated_at IS NULL;
      `
    });

    if (error) {
      console.error('Error adding updated_at column:', error);
      
      // Spróbuj alternatywną metodę
      console.log('Trying alternative method...');
      
      // Sprawdź czy kolumna już istnieje
      const { data: checkData, error: checkError } = await supabase
        .from('companies')
        .select('id, updated_at')
        .limit(1);
        
      if (checkError && checkError.message.includes('updated_at')) {
        console.log('Column updated_at does not exist, but we cannot add it via API');
        console.log('Please run this SQL manually in Supabase SQL Editor:');
        console.log(`
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();

UPDATE companies 
SET updated_at = created_at 
WHERE updated_at IS NULL;
        `);
      } else {
        console.log('✓ Column updated_at already exists or was added successfully');
      }
    } else {
      console.log('✓ Successfully added updated_at column');
    }

    console.log('2. Updating RPC function to not require updated_at...');
    
    // Aktualizuj funkcję RPC, aby nie wymagała kolumny updated_at
    const { data: rpcData, error: rpcError } = await supabase.rpc('execute_sql', {
      sql_query: `
        -- Funkcja RPC do aktualizacji typu konta firmy (bez updated_at)
        CREATE OR REPLACE FUNCTION update_company_account_type_rpc(
          p_company_id UUID,
          p_plan_name TEXT
        ) RETURNS BOOLEAN AS $$
        DECLARE
          verification_limit INTEGER;
          normalized_plan_name TEXT;
        BEGIN
          -- Normalizuj nazwę planu do małych liter
          normalized_plan_name := lower(p_plan_name);
          
          -- Ustaw limit kodów weryfikacyjnych na podstawie planu
          verification_limit := CASE
            WHEN normalized_plan_name LIKE '%basic%' THEN 5
            WHEN normalized_plan_name LIKE '%pro%' THEN 20
            WHEN normalized_plan_name LIKE '%business%' THEN 999999
            ELSE 2 -- dla planu free
          END;
          
          -- Aktualizuj firmę (bez updated_at jeśli kolumna nie istnieje)
          UPDATE companies
          SET account_type = normalized_plan_name,
              verification_code_limit = verification_limit
          WHERE id = p_company_id;
          
          RETURN FOUND;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
      `
    });

    if (rpcError) {
      console.error('Error updating RPC function:', rpcError);
    } else {
      console.log('✓ Successfully updated RPC function');
    }

    console.log('3. Testing updated RPC function...');
    
    // Test funkcji RPC
    const { data: testResult, error: testError } = await supabase
      .rpc('update_company_account_type_rpc', {
        p_company_id: 'a0736ed8-4ac3-4728-b0a0-a0fad9164e4a', // ID firmy "28"
        p_plan_name: 'basic'
      });

    if (testError) {
      console.error('Error testing RPC function:', testError);
    } else {
      console.log('✓ RPC function test result:', testResult);
    }

    // Sprawdź czy aktualizacja się powiodła
    const { data: updatedCompany, error: fetchError } = await supabase
      .from('companies')
      .select('id, name, account_type, verification_code_limit')
      .eq('id', 'a0736ed8-4ac3-4728-b0a0-a0fad9164e4a')
      .single();

    if (fetchError) {
      console.error('Error fetching updated company:', fetchError);
    } else {
      console.log('Updated company data:', updatedCompany);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

addUpdatedAtColumn();
