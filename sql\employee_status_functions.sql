-- Funkcja do aktywacji pracownika
CREATE OR REPLACE FUNCTION activate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_last_change TIMESTAMPTZ;
  v_active_count INT;
  v_subscription_limit INT;
BEGIN
  -- Sprawd<PERSON>, czy pracownik należy do firmy
  IF NOT EXISTS (
    SELECT 1 FROM employees 
    WHERE id = p_employee_id 
    AND company_id = p_company_id
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie należy do tej firmy'
    );
  END IF;

  -- <PERSON><PERSON><PERSON> datę ostatniej zmiany statusu
  SELECT last_status_change INTO v_last_change
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź, czy minęło 7 dni od ostatniej zmiany
  IF v_last_change IS NOT NULL AND 
     v_last_change > NOW() - INTERVAL '7 days' THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', '<PERSON><PERSON><PERSON> poc<PERSON> 7 dni od ostatniej zmiany statusu'
    );
  END IF;

  -- Pobierz limit aktywnych pracowników z planu subskrypcji
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz aktualną liczbę aktywnych pracowników
  SELECT COUNT(*) INTO v_active_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';

  -- Sprawdź, czy nie przekroczono limitu
  IF v_active_count >= v_subscription_limit THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Osiągnięto limit aktywnych pracowników dla tego planu subskrypcji'
    );
  END IF;

  -- Aktualizuj status pracownika
  UPDATE employees
  SET subscription_status = 'ACTIVE',
      last_status_change = NOW()
  WHERE id = p_employee_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Status pracownika został zmieniony na aktywny'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do dezaktywacji pracownika
CREATE OR REPLACE FUNCTION deactivate_employee(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_last_change TIMESTAMPTZ;
BEGIN
  -- Sprawdź, czy pracownik należy do firmy
  IF NOT EXISTS (
    SELECT 1 FROM employees 
    WHERE id = p_employee_id 
    AND company_id = p_company_id
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie należy do tej firmy'
    );
  END IF;

  -- Pobierz datę ostatniej zmiany statusu
  SELECT last_status_change INTO v_last_change
  FROM employees
  WHERE id = p_employee_id;

  -- Sprawdź, czy minęło 7 dni od ostatniej zmiany
  IF v_last_change IS NOT NULL AND 
     v_last_change > NOW() - INTERVAL '7 days' THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Musisz poczekać 7 dni od ostatniej zmiany statusu'
    );
  END IF;

  -- Aktualizuj status pracownika
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW()
  WHERE id = p_employee_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Status pracownika został zmieniony na nieaktywny'
  );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do automatycznej dezaktywacji wszystkich pracowników firmy po wygaśnięciu subskrypcji
CREATE OR REPLACE FUNCTION deactivate_company_employees(
  p_company_id UUID
)
RETURNS VOID AS $$
BEGIN
  UPDATE employees
  SET subscription_status = 'SUBSCRIPTION_EXPIRED',
      last_status_change = NOW()
  WHERE company_id = p_company_id
  AND subscription_status = 'ACTIVE';
END;
$$ LANGUAGE plpgsql;

-- Funkcja do automatycznej reaktywacji pracowników przy ponownym wykupieniu subskrypcji
CREATE OR REPLACE FUNCTION reactivate_company_employees(
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_subscription_limit INTEGER;
  v_expired_count INTEGER;
  v_reactivated_count INTEGER := 0;
  employee_record RECORD;
BEGIN
  -- Pobierz limit aktywnych pracowników z planu subskrypcji
  SELECT verification_code_limit INTO v_subscription_limit
  FROM companies
  WHERE id = p_company_id;

  -- Pobierz liczbę nieaktywnych pracowników
  SELECT COUNT(*) INTO v_expired_count
  FROM employees
  WHERE company_id = p_company_id
  AND subscription_status = 'SUBSCRIPTION_EXPIRED';

  -- Jeśli nie ma nieaktywnych pracowników, zakończ
  IF v_expired_count = 0 THEN
    RETURN jsonb_build_object(
      'success', true,
      'message', 'Brak nieaktywnych pracowników do reaktywacji',
      'reactivated_count', 0
    );
  END IF;

  -- Reaktywuj pracowników do limitu subskrypcji
  FOR employee_record IN
    SELECT id FROM employees
    WHERE company_id = p_company_id
    AND subscription_status = 'SUBSCRIPTION_EXPIRED'
    ORDER BY last_status_change ASC -- Najpierw ci, którzy zostali dezaktywowani najwcześniej
    LIMIT v_subscription_limit
  LOOP
    UPDATE employees
    SET subscription_status = 'ACTIVE',
        last_status_change = NOW()
    WHERE id = employee_record.id;

    v_reactivated_count := v_reactivated_count + 1;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'message', format('Reaktywowano %s z %s nieaktywnych pracowników', v_reactivated_count, v_expired_count),
    'reactivated_count', v_reactivated_count,
    'remaining_expired', v_expired_count - v_reactivated_count
  );
END;
$$ LANGUAGE plpgsql;