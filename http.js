// Prosty polyfill dla modułu http, potrzebny dla biblioteki ws (WebSocket)
const http = {
  createServer: () => ({
    listen: () => ({}),
    on: () => ({}),
    close: () => ({})
  }),
  
  // Inne często używane funkcje/obiekty
  Agent: function() {},
  request: () => ({
    on: () => ({}),
    end: () => ({})
  }),
  get: () => ({
    on: () => ({}),
    end: () => ({})
  })
};

// Używamy module.exports dla lepszej kompatybilności z modułami CommonJS
module.exports = http; 