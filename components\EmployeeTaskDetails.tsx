import React, { useState, useEffect, useCallback } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  ActivityIndicator,
  Platform,
  Alert,
  Dimensions,
  RefreshControl,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import { LinearGradient } from 'expo-linear-gradient';
import theme from '../utils/theme';
import { i18n } from '../utils/localization';

interface EmployeeTaskDetailsProps {
  employeeId: string;
  taskId?: string;
  onBack: () => void;
  onMenuPress: () => void;
}

interface Employee {
  id: string;
  full_name: string;
  email: string;
}

interface Task {
  id: string;
  client_id: string;
  client_name?: string;
  description: string;
  status: string;
  address: string;
  work_scope: string;
  start_time: string;
  end_time?: string;
  company_id: string;
  created_at: string;
  updated_at: string;
  status_changed_at?: string;
  started_at?: string;
  completed_at?: string;
  task_duration_minutes?: number;
  assigned_employees?: string[];
  active_employees_count?: number;
  date?: string;
  additional_info?: string;
}

interface WorkSession {
  id: string;
  employee_id: string;
  task_id: string;
  job_order: string;
  start_time: string;
  end_time: string | null;
  duration_minutes: number | null;
  status: string;
}

// Interface for task times
interface TaskTime {
  id: string;
  task_id: string;
  employee_id: string;
  start_time: string;
  end_time: string | null;
  duration_minutes: number | null;
  status: 'active' | 'completed';
}

const EmployeeTaskDetails: React.FC<EmployeeTaskDetailsProps> = ({ 
  employeeId, 
  taskId, 
  onBack, 
  onMenuPress 
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [task, setTask] = useState<Task | null>(null);
  const [workSession, setWorkSession] = useState<WorkSession | null>(null);
  const [taskTimes, setTaskTimes] = useState<TaskTime[]>([]);
  const [activeTaskTime, setActiveTaskTime] = useState<TaskTime | null>(null);
  const [timerVisible, setTimerVisible] = useState(false);
  const [elapsedSeconds, setElapsedSeconds] = useState(0);
  const [todaysTasks, setTodaysTasks] = useState<Task[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [currentWorkSession, setCurrentWorkSession] = useState<WorkSession | null>(null);
  
  // Modal state for confirmation
  const [confirmationModalVisible, setConfirmationModalVisible] = useState(false);
  const [confirmationMessage, setConfirmationMessage] = useState('');
  const [confirmationCallback, setConfirmationCallback] = useState<() => void>(() => {});

  useEffect(() => {
    fetchDetails();
    fetchWorkSession();

    // Set up a refresh interval to update work time
    const interval = setInterval(() => {
      if (workSession && !workSession.end_time) {
        fetchWorkSession();
        if (task) {
          fetchTaskTimes();
        }
      }
    }, 60000); // Update every minute

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [employeeId, taskId]);

  useEffect(() => {
    if (task) {
      fetchTaskTimes();
      fetchTodaysTasks();
    }
  }, [task]);

  const fetchDetails = async () => {
    setLoading(true);
    
    try {
      // Fetch employee details
      const { data: employeeData, error: employeeError } = await supabase
        .from('employees')
        .select('*')
        .eq('id', employeeId)
        .single();

      if (employeeError) {
        console.error('Error fetching employee details:', employeeError);
      } else if (employeeData) {
        setEmployee(employeeData);
      }

      // Fetch active work session
      await fetchWorkSession();

      // If we have a taskId, fetch that specific task
      if (taskId) {
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();

        if (taskError) {
          console.error('Error fetching task details:', taskError);
        } else if (taskData) {
          setTask(taskData);
        }
      } 
      // Otherwise, try to get task from the work session
      else if (workSession?.task_id) {
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', workSession.task_id)
          .single();

        if (taskError) {
          console.error('Error fetching task from work session:', taskError);
        } else if (taskData) {
          setTask(taskData);
        }
      }

    } catch (error) {
      console.error('Exception in fetchDetails:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchWorkSession = async () => {
    try {
      const { data: sessionData, error: sessionError } = await supabase
        .from('work_sessions')
        .select('*')
        .eq('employee_id', employeeId)
        .is('end_time', null)
        .single();

      if (sessionError && sessionError.code !== 'PGRST116') {
        console.error('Error fetching active work session:', sessionError);
      } else if (sessionData) {
        setWorkSession(sessionData);
        
        // If we don't have a task but the session has a task_id, fetch it
        if (!task && sessionData.task_id) {
          const { data: taskData, error: taskError } = await supabase
            .from('tasks')
            .select('*')
            .eq('id', sessionData.task_id)
            .single();

          if (taskError) {
            console.error('Error fetching task from work session:', taskError);
          } else if (taskData) {
            setTask(taskData);
          }
        }
      }
    } catch (error) {
      console.error('Exception in fetchWorkSession:', error);
    }
  };

  const fetchTaskTimes = async () => {
    if (!task) return;
    
    try {
      // Fetch all task times for this employee and task
      const { data: taskTimesData, error: taskTimesError } = await supabase
        .from('task_times')
        .select('*')
        .eq('task_id', task.id)
        .eq('employee_id', employeeId);
        
      if (taskTimesError) {
        console.error('Error fetching task times:', taskTimesError);
        return;
      }
      
      if (taskTimesData) {
        setTaskTimes(taskTimesData);
        
        // Check if employee has an active task time
        const activeTime = taskTimesData.find(time => time.end_time === null);
        
        if (activeTime) {
          setActiveTaskTime(activeTime);
        } else {
          setActiveTaskTime(null);
        }
      }
    } catch (error) {
      console.error('Error in fetchTaskTimes:', error);
    }
  };

  const fetchTodaysTasks = async () => {
    if (!employee) return;
    
    try {
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      const todayFormatted = today.toISOString().split('T')[0];
      
      // Fetch all tasks for today that have task times for this employee
      const { data, error } = await supabase
        .from('task_times')
        .select('task_id')
        .eq('employee_id', employeeId)
        .gte('start_time', `${todayFormatted}T00:00:00`)
        .lte('start_time', `${todayFormatted}T23:59:59`);
      
      if (error) {
        console.error('Error fetching today\'s task times:', error);
        return;
      }
      
      if (data && data.length > 0) {
        // Extract unique task IDs
        const taskIds = [...new Set(data.map(item => item.task_id))];
        
        // Fetch task details for those IDs
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select('*')
          .in('id', taskIds);
        
        if (tasksError) {
          console.error('Error fetching today\'s tasks:', tasksError);
          return;
        }
        
        if (tasksData && tasksData.length > 0) {
          // For each task, get active employees count
          const tasksWithEmployeeCount = await Promise.all(tasksData.map(async (task) => {
            const activeEmployeesCount = await getActiveEmployeesCount(task.id);
            return {
              ...task,
              active_employees_count: activeEmployeesCount
            };
          }));
          
          setTodaysTasks(tasksWithEmployeeCount || []);
        } else {
          setTodaysTasks([]);
        }
      } else {
        setTodaysTasks([]);
      }
    } catch (error) {
      console.error('Error in fetchTodaysTasks:', error);
    }
  };
  
  // Funkcja do pobierania liczby aktywnych pracowników dla danego zadania
  const getActiveEmployeesCount = async (taskId: string): Promise<number> => {
    try {
      // Pobierz wszystkie aktywne sesje pracy dla tego zadania
      const { data, error } = await supabase
        .from('work_sessions')
        .select('employee_id')
        .eq('task_id', taskId)
        .is('end_time', null);
      
      if (error) {
        console.error('Error fetching active employees for task:', error);
        return 0;
      }
      
      // Zwróć liczbę unikalnych pracowników
      if (data && data.length > 0) {
        const uniqueEmployees = [...new Set(data.map(item => item.employee_id))];
        return uniqueEmployees.length;
      }
      
      return 0;
    } catch (error) {
      console.error('Error in getActiveEmployeesCount:', error);
      return 0;
    }
  };

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleTimeString('pl-PL', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Calculate active work time
  const getWorkDuration = () => {
    if (!workSession || !workSession.start_time) return '00:00';
    
    const startTime = new Date(workSession.start_time);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - startTime.getTime()) / 1000 / 60);
    
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return { bg: '#fef3c7', text: '#d97706' }; // Żółty
      case 'in_progress':
        return { bg: '#dbeafe', text: '#2563eb' }; // Niebieski
      case 'completed':
        return { bg: '#d1fae5', text: '#059669' }; // Zielony
      case 'canceled':
        return { bg: '#fee2e2', text: '#dc2626' }; // Czerwony
      default:
        return { bg: '#e5e7eb', text: '#6b7280' }; // Szary
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Oczekujące';
      case 'in_progress':
        return 'W trakcie';
      case 'completed':
        return 'Zakończone';
      default:
        return 'Nieznany';
    }
  };

  // Dodaję z powrotem funkcję formatDuration, ponieważ jest ona używana w innych miejscach
  const formatDuration = (minutes: number | null) => {
    if (minutes === null) return '00:00';
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // Calculate total task time spent
  const getTotalTaskTimeSpent = () => {
    let totalMinutes = 0;
    
    taskTimes.forEach(time => {
      if (time.duration_minutes) {
        totalMinutes += time.duration_minutes;
      } else if (time.start_time && !time.end_time) {
        // For active task times, calculate current duration
        const start = new Date(time.start_time);
        const now = new Date();
        const diffMinutes = Math.floor((now.getTime() - start.getTime()) / 60000);
        totalMinutes += diffMinutes;
      }
    });
    
    return formatDuration(totalMinutes);
  };
  
  // Calculate active task time duration
  const getActiveTaskTimeDuration = () => {
    if (!activeTaskTime || !activeTaskTime.start_time) return '00:00';
    
    const start = new Date(activeTaskTime.start_time);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - start.getTime()) / 60000);
    return formatDuration(diffMinutes);
  };

  const toggleTimer = () => {
    setTimerVisible(!timerVisible);
  };

  const formatElapsedTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Add showConfirmationModal function
  const showConfirmationModal = (message: string, callback: () => void) => {
    setConfirmationMessage(message);
    setConfirmationCallback(() => callback);
    setConfirmationModalVisible(true);
  };

  // Add handleRefresh function
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDetails();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary.main} />
          <Text style={styles.loadingText}>Ładowanie danych...</Text>
        </View>
      </View>
    );
  }

  if (!employee) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#EF4444" />
          <Text style={styles.errorText}>Nie znaleziono pracownika.</Text>
        </View>
      </View>
    );
  }

  // Dodajemy logging stanu zadania
  console.log('Renderowanie komponentu EmployeeTaskDetails z zadaniem:', task);
  if (task) {
    console.log('Status zadania podczas renderowania:', task.status);
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('employeeOrderDetails')}</Text>
        <View style={styles.placeholderView} />
      </View>
      
      <ScrollView 
        style={styles.content} 
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <Text style={styles.pageTitle}>{i18n.t('employeeOrderDetails')}</Text>
        
        {/* Employee Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{i18n.t('employeeData')}</Text>
          <View style={styles.employeeCard}>
            <View style={styles.employeeHeader}>
              <Ionicons name="person" size={24} color={theme.colors.primary.main} />
              <View style={styles.employeeNameContainer}>
                {workSession && workSession.task_id === task?.id && (
                  <View style={styles.greenDot} />
                )}
              <Text style={styles.employeeName}>{employee?.full_name || 'Nieznany pracownik'}</Text>
              </View>
            </View>
            {employee?.email && (
              <View style={styles.infoRow}>
                <Ionicons name="mail-outline" size={18} color="#6B7280" />
                <Text style={styles.infoText}>{employee.email}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Active Work Session */}
        {workSession && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{i18n.t('activeWorkSession')}</Text>
            <View style={styles.sessionCard}>
              <View style={styles.infoRow}>
                <Ionicons name="briefcase-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>{i18n.t('jobOrderLabel')}</Text>
                <Text style={styles.infoValue}>{workSession.job_order}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="time-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>{i18n.t('startTimeLabel')}</Text>
                <Text style={styles.infoValue}>
                  {`${formatDate(workSession.start_time)} ${formatTime(workSession.start_time)}`}
                </Text>
              </View>
              
              <View style={styles.activeTimeRow}>
                <View style={styles.timeIconContainer}>
                  <Ionicons name="hourglass-outline" size={18} color="#2563EB" />
                </View>
                <Text style={styles.activeTimeLabel}>{i18n.t('workTimeLabel')}</Text>
                <Text style={styles.activeTimeValue}>{getWorkDuration()}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Task Details - Show immediately after work session */}
        {task && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Szczegóły zadania</Text>
            <View style={styles.taskCard}>
              <View style={styles.taskHeader}>
                <Text style={styles.clientName}>{task.client_name}</Text>
                {task.status && (
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(task.status).bg }]}>
                    <Text style={[styles.statusText, { color: getStatusColor(task.status).text }]}>
                      {getStatusText(task.status)}
                    </Text>
                  </View>
                )}
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="location-outline" size={18} color="#6B7280" />
                <Text style={styles.infoText}>{task.address}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="construct-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>Zakres prac:</Text>
                <Text style={styles.infoText}>{task.work_scope}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="calendar-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>Data:</Text>
                <Text style={styles.infoText}>{formatDate(task.date)}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Ionicons name="time-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>Godzina:</Text>
                <Text style={styles.infoText}>{formatTime(task.start_time)}</Text>
              </View>
              
              {/* Add task time information */}
              <View style={styles.infoRow}>
                <Ionicons name="timer-outline" size={18} color="#6B7280" />
                <Text style={styles.infoLabel}>Czas zadania:</Text>
                <Text style={styles.taskTimeText}>{activeTaskTime ? getActiveTaskTimeDuration() : getTotalTaskTimeSpent()}</Text>
              </View>
              
              {task.additional_info && (
                <View style={styles.additionalInfo}>
                  <Text style={styles.infoLabel}>Dodatkowe informacje:</Text>
                  <Text style={styles.additionalInfoText}>{task.additional_info}</Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Today's Tasks Section */}
        {todaysTasks.length > 0 && (
          <View style={styles.todaysTasksSection}>
            <Text style={styles.sectionTitle}>Dzisiejsze zadania</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {todaysTasks.map((task) => (
                <TouchableOpacity
                  key={task.id} 
                  style={styles.taskCardHorizontal}
                  onPress={() => {
                    // Implement the navigation to EmployeeTaskDetails with the taskId
                  }}
                >
                  <View style={styles.taskCardHeader}>
                    <View style={styles.taskNameContainer}>
                      {/* Green dot for tasks the employee is actively working on */}
                      {workSession?.task_id === task.id && (
                        <View style={styles.greenDot} />
                      )}
                      <Text style={styles.taskClientName}>{task.client_name}</Text>
                    </View>
                    {task.active_employees_count && task.active_employees_count > 0 && (
                      <View style={styles.employeesCountContainer}>
                        <Ionicons name="people" size={14} color="#4B5563" />
                        <Text style={styles.employeesCountText}>{task.active_employees_count}</Text>
                      </View>
                    )}
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Ionicons name="location-outline" size={14} color="#6B7280" />
                    <Text style={[styles.infoText, { fontSize: 12 }]} numberOfLines={1}>{task.address}</Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Ionicons name="construct-outline" size={14} color="#6B7280" />
                    <Text style={[styles.infoText, { fontSize: 12 }]} numberOfLines={1}>{task.work_scope}</Text>
                  </View>
                  
                  <View style={styles.taskFooter}>
                    <View style={[styles.statusBadge, { 
                      backgroundColor: getStatusColor(task.status).bg,
                      paddingVertical: 4,
                      paddingHorizontal: 8,
                    }]}>
                      <Text style={[styles.statusText, { 
                        color: getStatusColor(task.status).text,
                        fontSize: 10
                      }]}>
                        {getStatusText(task.status)}
                      </Text>
                    </View>
                    
                    <Text style={styles.taskStartTime}>{formatTime(task.start_time)}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {/* "Pracujesz nad zadaniem" section - update its styling */}
        {activeTask && currentWorkSession && (
          <View style={[styles.workingOnTaskContainer, {marginTop: 16}]}>
            <View style={styles.workingOnTaskHeader}>
              <Text style={[styles.sectionTitle, {marginBottom: 8}]}>Pracujesz nad zadaniem</Text>
              <TouchableOpacity onPress={toggleTimer}>
                <Ionicons 
                  name={timerVisible ? "chevron-up-outline" : "chevron-down-outline"} 
                  size={24} 
                  color="#3B82F6"
                />
              </TouchableOpacity>
            </View>
          
            {timerVisible && (
              <View style={styles.activeTaskCard}>
                <View style={styles.activeTaskHeader}>
                  <Text style={styles.activeTaskClientName}>{activeTask.client_name}</Text>
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor('in_progress').bg }]}>
                    <Text style={[styles.statusText, { color: getStatusColor('in_progress').text }]}>
                      {getStatusText('in_progress')}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.workDetailsContainer}>
                  <View style={styles.infoRow}>
                    <Ionicons name="construct-outline" size={16} color="#6B7280" />
                    <Text style={styles.infoText}>{activeTask.work_scope}</Text>
                  </View>
                  
                  <View style={styles.infoRow}>
                    <Ionicons name="location-outline" size={16} color="#6B7280" />
                    <Text style={styles.infoText}>{activeTask.address}</Text>
                  </View>
                </View>
                
                <View style={styles.timerContainer}>
                  <Text style={styles.timerLabel}>{i18n.t('workTimeLabel')}</Text>
                  <Text style={styles.timer}>{formatElapsedTime(elapsedSeconds)}</Text>
                </View>
              </View>
            )}
          </View>
        )}
      </ScrollView>

      {/* Confirmation Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={confirmationModalVisible}
        onRequestClose={() => {
          setConfirmationModalVisible(false);
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Potwierdź akcję</Text>
            <Text style={styles.modalMessage}>{confirmationMessage}</Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setConfirmationModalVisible(false)}
              >
                <Text style={styles.modalButtonText}>Anuluj</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={() => {
                  setConfirmationModalVisible(false);
                  confirmationCallback();
                }}
              >
                <Text style={[styles.modalButtonText, { color: 'white' }]}>Potwierdź</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerDivider: {
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    width: '100%',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 20,
    display: 'none', // Ukrywamy ten tytuł, gdyż mamy teraz tytuł w headerze
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  section: {
    marginBottom: 32,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  employeeCard: {
    backgroundColor: 'white',
    borderRadius: 0,
    padding: 16,
    marginBottom: 8,
  },
  employeeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  employeeNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  greenDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981', // Green color
    marginRight: 6,
  },
  employeeName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: 10,
  },
  sessionCard: {
    backgroundColor: 'white',
    borderRadius: 0,
    padding: 16,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2563EB',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginLeft: 8,
    marginRight: 4,
  },
  infoValue: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1,
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1,
    marginLeft: 8,
  },
  activeTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    backgroundColor: '#EFF6FF',
    padding: 10,
    borderRadius: 8,
  },
  timeIconContainer: {
    marginRight: 8,
  },
  activeTimeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
  },
  activeTimeValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#2563EB',
    marginLeft: 8,
  },
  workTimeText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary.main,
    marginLeft: 8,
  },
  taskCard: {
    backgroundColor: 'white',
    borderRadius: 0,
    padding: 16,
    marginBottom: 8,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  additionalInfo: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  additionalInfoText: {
    fontSize: 14,
    color: '#4B5563',
    marginTop: 4,
  },
  button: {
    backgroundColor: theme.colors.primary.main,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonDisabled: {
    backgroundColor: '#93C5FD',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  taskTimeText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary.main,
    marginLeft: 8,
  },
  todaysTasksSection: {
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  taskCardHorizontal: {
    backgroundColor: 'white',
    borderRadius: 0,
    padding: 12,
    marginRight: 12,
    width: 220,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  taskCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskClientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  taskDetail: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 4,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  taskStartTime: {
    fontSize: 12,
    color: '#666',
  },
  employeesCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  employeesCountText: {
    fontSize: 12,
    color: '#3498db',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  workingOnTaskContainer: {
    backgroundColor: 'white',
    borderRadius: 0,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  workingOnTaskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  activeTaskCard: {
    backgroundColor: 'white',
    borderRadius: 0,
    padding: 16,
    marginTop: 8,
  },
  activeTaskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  activeTaskClientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  workDetailsContainer: {
    marginBottom: 12,
  },
  timerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  timerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
  },
  timer: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary.main,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  actionButton: {
    backgroundColor: '#ff5722',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonDanger: {
    backgroundColor: '#dc2626',
  },
  taskNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Add modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#1F2937',
  },
  modalMessage: {
    fontSize: 16,
    marginBottom: 20,
    color: '#4B5563',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  modalButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#E5E7EB',
  },
  confirmButton: {
    backgroundColor: '#DC2626',
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
  },
  startTaskButton: {
    marginTop: 10,
    backgroundColor: '#4CAF50',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
  },
  startTaskButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
});

export default EmployeeTaskDetails; 