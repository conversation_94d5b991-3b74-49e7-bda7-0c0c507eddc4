const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const supabaseUrl = 'https://bqjjlxqzlpjjkqzqzqzq.supabase.co';
const supabaseKey = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxampseHF6bHBqamtxenF6cXpxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzQxMzI5NCwiZXhwIjoyMDQ4OTg5Mjk0fQ.Ej8XQJBHgoGVTKWKOdStjhiOJme_1Ej8XQJBHgoGVTKW';

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupRemoveEmployeeFunction() {
  try {
    console.log('🔧 Tworzenie funkcji usuwania pracownika z firmy...');
    
    // Wczytaj SQL z pliku
    const sqlPath = path.join(__dirname, '..', 'sql', 'create_remove_employee_function.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    // Wykonaj SQL
    const { data, error } = await supabase.rpc('execute_sql', { 
      sql_query: sqlContent 
    });
    
    if (error) {
      console.error('❌ Błąd tworzenia funkcji:', error);
      return;
    }
    
    console.log('✅ Funkcja utworzona pomyślnie');
    
    // Test funkcji - sprawdź czy została utworzona
    console.log('\n📋 Sprawdzanie utworzonych funkcji...');
    const { data: functions, error: functionsError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT routine_name, routine_type
        FROM information_schema.routines 
        WHERE routine_name IN (
          'remove_employee_from_company',
          'is_independent_employee',
          'get_independent_employees'
        )
        ORDER BY routine_name;
      `
    });
    
    if (!functionsError && functions) {
      console.log('Utworzone funkcje:');
      functions.forEach(func => {
        console.log(`- ${func.routine_name} (${func.routine_type})`);
      });
    }
    
    // Sprawdź czy kolumny zostały dodane
    console.log('\n📊 Sprawdzanie struktury tabeli employees...');
    const { data: columns, error: columnsError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'employees' 
        AND column_name IN ('employee_type', 'company_id')
        ORDER BY column_name;
      `
    });
    
    if (!columnsError && columns) {
      console.log('Kolumny w tabeli employees:');
      columns.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
    }
    
    // Sprawdź czy tabela employee_history została utworzona
    console.log('\n📝 Sprawdzanie tabeli employee_history...');
    const { data: historyTable, error: historyError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = 'employee_history';
      `
    });
    
    if (!historyError && historyTable && historyTable.length > 0) {
      console.log('✅ Tabela employee_history została utworzona');
    } else {
      console.log('⚠️ Tabela employee_history nie została utworzona');
    }
    
    // Sprawdź aktualny stan pracowników
    console.log('\n👥 Sprawdzanie aktualnego stanu pracowników...');
    const { data: employeeStats, error: statsError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT 
          COUNT(*) as total_employees,
          COUNT(CASE WHEN company_id IS NOT NULL THEN 1 END) as company_employees,
          COUNT(CASE WHEN company_id IS NULL THEN 1 END) as independent_employees
        FROM employees;
      `
    });
    
    if (!statsError && employeeStats && employeeStats.length > 0) {
      const stats = employeeStats[0];
      console.log(`Łącznie pracowników: ${stats.total_employees}`);
      console.log(`Przypisanych do firm: ${stats.company_employees}`);
      console.log(`Niezależnych: ${stats.independent_employees}`);
    }
    
    console.log('\n🎯 Funkcja usuwania pracownika z firmy jest gotowa do użycia!');
    console.log('\nInstrukcje:');
    console.log('1. Przycisk "Usuń pracownika" w AdminPanel teraz wywołuje remove_employee_from_company()');
    console.log('2. Pracownik zostanie odłączony od firmy (company_id = NULL)');
    console.log('3. Kod weryfikacyjny zostanie zwolniony');
    console.log('4. Pracownik stanie się niezależny z ograniczonymi funkcjami');
    
  } catch (err) {
    console.error('❌ Błąd wykonania:', err.message);
  }
}

setupRemoveEmployeeFunction();
