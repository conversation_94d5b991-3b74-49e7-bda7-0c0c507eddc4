import { supabase } from './supabaseClient';

export interface WorkSession {
  id: string;
  employee_id: string;
  company_id: string;
  start_time: string;
  end_time: string | null;
  duration_minutes: number | null;
  job_order: string | null;
  status: 'active' | 'inactive';
  is_main_session: boolean;
  start_location?: any;
  end_location?: any;
}

export interface TaskActivity {
  id: string;
  work_session_id: string | null;
  task_id: string;
  employee_id: string;
  company_id: string;
  start_time: string;
  end_time: string | null;
  duration_minutes: number | null;
  status: 'active' | 'completed';
}

/**
 * Rozpoczyna dzień pracy dla pracownika
 * @param employeeId ID pracownika
 * @param companyId ID firmy
 * @param startLocation Lokalizacja rozpoczęcia pracy (opcjonalnie)
 * @param jobOrder Nazwa zlecenia (opcjonalnie)
 * @returns Utworzona sesja pracy lub null w przypadku błędu
 */
export const startWorkDay = async (
  employeeId: string,
  companyId: string,
  startLocation?: any,
  jobOrder?: string
): Promise<WorkSession | null> => {
  try {
    // Sprawdź, czy pracownik nie ma już aktywnej sesji
    const { data: existingSession, error: checkError } = await supabase
      .from('work_sessions')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('is_main_session', true)
      .is('end_time', null)
      .maybeSingle();
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Błąd sprawdzania istniejącej sesji:', checkError);
      return null;
    }
    
    // Jeżeli sesja już istnieje, zwróć ją
    if (existingSession) {
      console.log('Pracownik ma już aktywną główną sesję pracy:', existingSession);
      return existingSession as WorkSession;
    }
    
    // Utwórz nową główną sesję pracy
    const { data: newSession, error: createError } = await supabase
      .from('work_sessions')
      .insert({
        employee_id: employeeId,
        company_id: companyId,
        start_time: new Date().toISOString(),
        status: 'active',
        is_main_session: true,
        job_order: jobOrder || 'main',
        start_location: startLocation || null
      })
      .select()
      .single();
    
    if (createError) {
      console.error('Błąd tworzenia głównej sesji pracy:', createError);
      return null;
    }
    
    console.log('Utworzono nową główną sesję pracy:', newSession);
    return newSession as WorkSession;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas rozpoczynania dnia pracy:', error);
    return null;
  }
};

/**
 * Zakończenie głównej sesji pracy oraz wszystkich aktywnych aktywności zadaniowych
 * @param employeeId ID pracownika
 * @param endLocation Lokalizacja zakończenia pracy (opcjonalna)
 * @returns true jeśli operacja się powiodła, false w przypadku błędu
 */
export const endWorkDay = async (
  employeeId: string,
  endLocation?: any
): Promise<boolean> => {
  try {
    const now = new Date().toISOString();
    
    // 1. Zakończ wszystkie aktywne aktywności zadaniowe
    const { data: activeActivities, error: activitiesError } = await supabase
      .from('task_activities')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('status', 'active');
    
    if (activitiesError) {
      console.error('Błąd pobierania aktywnych aktywności zadaniowych:', activitiesError);
      return false;
    }
    
    // Keep track of the tasks that need to be checked for completion
    const tasksToCheck = new Set<string>();
    
    for (const activity of activeActivities || []) {
      await completeTaskActivity(activity.id);
      
      // Add this task to our list of tasks to check
      tasksToCheck.add(activity.task_id);
      
      // Aktualizuj licznik aktywnych pracowników w zadaniu
      await updateTaskActiveEmployeesCount(activity.task_id);
    }
    
    // Check each task for remaining active employees and complete if none
    for (const taskId of tasksToCheck) {
      // Get current count of active employees in this task
      const activeCount = await updateTaskActiveEmployeesCount(taskId);
      
      // If this was the last active employee in the task, mark the task as completed
      if (activeCount === 0) {
        console.log(`No active employees left in task ${taskId} - marking as completed`);
        const { error: updateError } = await supabase
          .from('tasks')
          .update({
            status: 'completed',
            completed_at: now
          })
          .eq('id', taskId);
          
        if (updateError) {
          console.error(`Error updating task ${taskId} status to completed:`, updateError);
        } else {
          console.log(`Task ${taskId} marked as completed`);
        }
      }
    }
    
    // 2. Zakończ główną sesję pracy
    const { data: mainSession, error: sessionError } = await supabase
      .from('work_sessions')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('is_main_session', true)
      .is('end_time', null)
      .maybeSingle();
    
    if (sessionError && sessionError.code !== 'PGRST116') {
      console.error('Błąd pobierania głównej sesji pracy:', sessionError);
      return false;
    }
    
    if (mainSession) {
      const durationMinutes = calculateDurationMinutes(
        mainSession.start_time,
        now
      );
      
      const { error: updateError } = await supabase
        .from('work_sessions')
        .update({
          end_time: now,
          duration_minutes: durationMinutes,
          status: 'inactive',
          end_location: endLocation || null
        })
        .eq('id', mainSession.id);
      
      if (updateError) {
        console.error('Błąd aktualizacji głównej sesji pracy:', updateError);
        return false;
      }
    }
    
    console.log('Dzień pracy zakończony pomyślnie dla pracownika:', employeeId);
    return true;
    
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas kończenia dnia pracy:', error);
    return false;
  }
};

/**
 * Rozpoczyna aktywność zadaniową dla pracownika
 * @param employeeId ID pracownika
 * @param taskId ID zadania
 * @param companyId ID firmy
 * @param startLocation Lokalizacja rozpoczęcia pracy (opcjonalnie)
 * @returns Utworzona aktywność zadaniowa lub null w przypadku błędu
 */
export const startTaskActivity = async (
  employeeId: string,
  taskId: string,
  companyId: string,
  startLocation?: any
): Promise<TaskActivity | null> => {
  try {
    let mainSessionId: string | null = null;
    
    // Pobierz dane zadania, aby użyć nazwy klienta jako nazwy zlecenia
    const { data: taskData, error: taskError } = await supabase
      .from('tasks')
      .select('client_name')
      .eq('id', taskId)
      .single();
    
    if (taskError) {
      console.error('Błąd pobierania danych zadania:', taskError);
      // Kontynuuj bez nazwy zadania - zostanie użyta domyślna wartość 'main'
    }
    
    // Nazwa zlecenia na podstawie nazwy klienta z zadania
    const jobOrderName = taskData?.client_name || `Task-${taskId.substring(0, 8)}`;
    
    // 1. Pobierz lub utwórz główną sesję pracy
    const { data: mainSession, error: sessionError } = await supabase
      .from('work_sessions')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('is_main_session', true)
      .is('end_time', null)
      .maybeSingle();
    
    if (sessionError && sessionError.code !== 'PGRST116') {
      console.error('Błąd pobierania głównej sesji pracy:', sessionError);
      return null;
    }
    
    if (mainSession) {
      mainSessionId = mainSession.id;
    } else {
      // Rozpocznij główną sesję pracy jeśli nie istnieje, przekazując nazwę zadania jako jobOrder i lokalizację początkową
      const newMainSession = await startWorkDay(employeeId, companyId, startLocation, jobOrderName);
      if (newMainSession) {
        mainSessionId = newMainSession.id;
      }
    }
    
    // 2. Sprawdź, czy nie istnieje już aktywna aktywność dla tego zadania
    const { data: existingActivity, error: checkError } = await supabase
      .from('task_activities')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('task_id', taskId)
      .eq('status', 'active')
      .maybeSingle();
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Błąd sprawdzania istniejącej aktywności:', checkError);
      return null;
    }
    
    // Jeżeli aktywność już istnieje, zwróć ją
    if (existingActivity) {
      console.log('Pracownik ma już aktywną aktywność dla tego zadania:', existingActivity);
      return existingActivity as TaskActivity;
    }
    
    // 3. Utwórz nową aktywność zadaniową
    const { data: newActivity, error: createError } = await supabase
      .from('task_activities')
      .insert({
        work_session_id: mainSessionId,
        task_id: taskId,
        employee_id: employeeId,
        company_id: companyId,
        start_time: new Date().toISOString(),
        status: 'active'
      })
      .select('*')
      .single();
    
    if (createError) {
      console.error('Błąd tworzenia aktywności zadaniowej:', createError);
      return null;
    }
    
    // 4. Aktualizuj liczbę aktywnych pracowników w zadaniu
    await updateTaskActiveEmployeesCount(taskId);
    
    console.log('Utworzono nową aktywność zadaniową:', newActivity);
    return newActivity as TaskActivity;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas rozpoczynania aktywności zadaniowej:', error);
    return null;
  }
};

/**
 * Zakończenie aktywności zadaniowej
 * @param activityId ID aktywności zadaniowej
 * @returns Zaktualizowana aktywność zadaniowa lub null w przypadku błędu
 */
export const completeTaskActivity = async (
  activityId: string
): Promise<TaskActivity | null> => {
  try {
    // Zamiast korzystać z funkcji RPC, wykonaj bezpośredni UPDATE
    const now = new Date().toISOString();
    
    // Najpierw pobierz aktywność, żeby obliczyć czas trwania
    const { data: activity, error: getError } = await supabase
      .from('task_activities')
      .select('*')
      .eq('id', activityId)
      .single();
    
    if (getError) {
      console.error('Błąd pobierania aktywności zadaniowej:', getError);
      return null;
    }
    
    if (!activity) {
      console.log('Nie znaleziono aktywności o ID:', activityId);
      return null;
    }
    
    // Oblicz czas trwania w minutach
    const startTime = new Date(activity.start_time);
    const endTime = new Date(now);
    const durationMinutes = calculateDurationMinutes(activity.start_time, now);
    
    // Aktualizuj aktywność
    const { data: updatedActivity, error: updateError } = await supabase
      .from('task_activities')
      .update({
        end_time: now,
        duration_minutes: durationMinutes,
        status: 'completed'
      })
      .eq('id', activityId)
      .select('*')
      .single();
    
    if (updateError) {
      console.error('Błąd aktualizacji aktywności zadaniowej:', updateError);
      return null;
    }
    
    console.log('Pomyślnie zakończono aktywność zadaniową:', updatedActivity);
    return updatedActivity as TaskActivity;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas kończenia aktywności zadaniowej:', error);
    return null;
  }
};

/**
 * Zakończenie aktywności zadaniowej dla pracownika i zadania
 * @param employeeId ID pracownika
 * @param taskId ID zadania
 * @returns Zaktualizowana aktywność zadaniowa lub null w przypadku błędu
 */
export const completeTaskActivityByEmployeeAndTask = async (
  employeeId: string,
  taskId: string
): Promise<TaskActivity | null> => {
  try {
    console.log(`Próba zakończenia aktywności dla pracownika ${employeeId} w zadaniu ${taskId}`);
    
    // Znajdź aktywną aktywność
    const { data: activities, error: findError } = await supabase
      .from('task_activities')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('task_id', taskId)
      .eq('status', 'active');
    
    if (findError) {
      console.error('Błąd wyszukiwania aktywności zadaniowej:', findError);
      return null;
    }
    
    if (!activities || activities.length === 0) {
      console.log('Nie znaleziono aktywnej aktywności dla pracownika i zadania');
      return null;
    }
    
    console.log(`Znaleziono ${activities.length} aktywności do zakończenia:`, activities);
    
    // Zakończ wszystkie aktywności
    for (const activity of activities) {
      const result = await completeTaskActivity(activity.id);
      if (result) {
        console.log(`Zakończono aktywność ${activity.id}`);
      }
    }
    
    // Zwróć pierwszą zakończoną aktywność
    return activities.length > 0 ? 
      await completeTaskActivity(activities[0].id) : 
      null;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas kończenia aktywności zadaniowej:', error);
    return null;
  }
};

/**
 * Aktualizacja liczby aktywnych pracowników w zadaniu
 * @param taskId ID zadania
 * @returns Liczba aktywnych pracowników lub -1 w przypadku błędu
 */
export const updateTaskActiveEmployeesCount = async (
  taskId: string
): Promise<number> => {
  try {
    console.log(`Updating active employees count for task ${taskId}`);
    
    // Count active task activities
    const { data: activeActivities, error: countError } = await supabase
      .from('task_activities')
      .select('id')
      .eq('task_id', taskId)
      .eq('status', 'active');
    
    if (countError) {
      console.error('Błąd liczenia aktywnych pracowników:', countError);
      return -1;
    }
    
    const activeCount = activeActivities?.length || 0;
    console.log(`Zliczono ${activeCount} aktywnych pracowników dla zadania ${taskId}`);
    
    try {
      // Skip schema check and directly attempt to update the column
      // This will fail gracefully if the column doesn't exist
      const { error: updateError } = await supabase
        .from('tasks')
        .update({ active_employees_count: activeCount })
        .eq('id', taskId);
        
      if (updateError) {
        // Check if error is about column not existing
        if (updateError.message.includes('column "active_employees_count" of relation "tasks" does not exist')) {
          console.warn('Kolumna active_employees_count nie istnieje w tabeli tasks');
          // Column doesn't exist, but we can continue without error
          console.log('Kolumna nie istnieje, ale to nie wpływa na działanie aplikacji');
        } else {
          console.error('Błąd aktualizacji liczby aktywnych pracowników:', updateError);
        }
      } else {
        console.log(`Successfully updated active_employees_count to ${activeCount} for task ${taskId}`);
      }
    } catch (error) {
      console.error('Nieoczekiwany błąd podczas aktualizacji licznika pracowników:', error);
    }
    
    return activeCount;
  } catch (error) {
    console.error('Exception in updateTaskActiveEmployeesCount:', error);
    return -1;
  }
};

/**
 * Pobranie aktywnych aktywności zadaniowych dla pracownika
 * @param employeeId ID pracownika
 * @returns Lista aktywnych aktywności zadaniowych
 */
export const getActiveTaskActivities = async (
  employeeId: string
): Promise<TaskActivity[]> => {
  try {
    const { data, error } = await supabase
      .from('task_activities')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('status', 'active');
    
    if (error) {
      console.error('Błąd pobierania aktywnych aktywności zadaniowych:', error);
      return [];
    }
    
    return data as TaskActivity[];
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas pobierania aktywnych aktywności:', error);
    return [];
  }
};

/**
 * Pobranie aktywnych pracowników dla zadania
 * @param taskId ID zadania
 * @returns Lista ID aktywnych pracowników
 */
export const getActiveEmployeesForTask = async (
  taskId: string
): Promise<string[]> => {
  try {
    console.log(`getActiveEmployeesForTask: Pobieranie aktywnych pracowników dla zadania ${taskId}`);
    
    const { data, error } = await supabase
      .from('task_activities')
      .select('employee_id')
      .eq('task_id', taskId)
      .eq('status', 'active');
    
    if (error) {
      console.error('Błąd pobierania aktywnych pracowników dla zadania:', error);
      return [];
    }
    
    const activeEmployeeIds = data.map(item => item.employee_id);
    console.log(`getActiveEmployeesForTask: Znaleziono ${activeEmployeeIds.length} aktywnych pracowników:`, activeEmployeeIds);
    
    return activeEmployeeIds;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas pobierania aktywnych pracowników:', error);
    return [];
  }
};

/**
 * Obliczenie czasu trwania w minutach
 * @param startTime Czas rozpoczęcia
 * @param endTime Czas zakończenia
 * @returns Czas trwania w minutach
 */
export const calculateDurationMinutes = (
  startTime: string,
  endTime: string
): number => {
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  return Math.round((end - start) / (1000 * 60));
};

/**
 * Sprawdzenie, czy pracownik jest aktywny w zadaniu
 * @param employeeId ID pracownika
 * @param taskId ID zadania
 * @returns true jeśli pracownik jest aktywny w zadaniu, false w przeciwnym razie
 */
export const isEmployeeActiveInTask = async (
  employeeId: string,
  taskId: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('task_activities')
      .select('id')
      .eq('employee_id', employeeId)
      .eq('task_id', taskId)
      .eq('status', 'active')
      .maybeSingle();
    
    if (error && error.code !== 'PGRST116') {
      console.error('Błąd sprawdzania aktywności pracownika w zadaniu:', error);
      return false;
    }
    
    return !!data;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas sprawdzania aktywności pracownika:', error);
    return false;
  }
};

// Dodaj funkcję pomocniczą do sprawdzania kolumn tabeli
export const addRpcFunction = async () => {
  try {
    await supabase.rpc('create_column_check_function');
    console.log('Funkcja pomocnicza utworzona');
    return true;
  } catch (error) {
    console.error('Błąd tworzenia funkcji pomocniczej:', error);
    return false;
  }
};

/**
 * Zakończenie aktywności zadaniowej dla pracownika i zadania i zapis lokalizacji końcowej
 * @param employeeId ID pracownika
 * @param taskId ID zadania
 * @param companyId ID firmy
 * @param endLocation Lokalizacja zakończenia pracy (opcjonalnie)
 * @returns Zaktualizowana aktywność zadaniowa lub null w przypadku błędu
 */
export const stopTaskActivity = async (
  employeeId: string,
  taskId: string,
  companyId: string,
  endLocation?: any
): Promise<TaskActivity | null> => {
  try {
    console.log(`Próba zakończenia aktywności dla pracownika ${employeeId} w zadaniu ${taskId}`);
    
    // Znajdź aktywną aktywność
    const { data: activities, error: findError } = await supabase
      .from('task_activities')
      .select('*')
      .eq('employee_id', employeeId)
      .eq('task_id', taskId)
      .eq('status', 'active');
    
    if (findError) {
      console.error('Błąd wyszukiwania aktywności zadaniowej:', findError);
      return null;
    }
    
    if (!activities || activities.length === 0) {
      console.log('Nie znaleziono aktywnej aktywności dla pracownika i zadania');
      return null;
    }
    
    console.log(`Znaleziono ${activities.length} aktywności do zakończenia:`, activities);
    
    const activityResults: TaskActivity[] = [];
    
    // Zakończ wszystkie aktywności
    for (const activity of activities) {
      const result = await completeTaskActivity(activity.id);
      if (result) {
        console.log(`Zakończono aktywność ${activity.id}`);
        activityResults.push(result);
      }
    }
    
    // Zaktualizuj licznik aktywnych pracowników w zadaniu
    await updateTaskActiveEmployeesCount(taskId);
    
    // Zaktualizuj lokalizację końcową w głównej sesji pracy, jeśli istnieje
    if (endLocation) {
      const { data: mainSession, error: sessionError } = await supabase
        .from('work_sessions')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('is_main_session', true)
        .is('end_time', null)
        .single();
        
      if (!sessionError && mainSession) {
        // Aktualizuj tylko lokalizację końcową w głównej sesji pracy, nie kończąc jej
        const { error: updateError } = await supabase
          .from('work_sessions')
          .update({
            end_location: endLocation
          })
          .eq('id', mainSession.id);
          
        if (updateError) {
          console.error('Błąd aktualizacji lokalizacji końcowej w głównej sesji pracy:', updateError);
        } else {
          console.log('Zaktualizowano lokalizację końcową w głównej sesji pracy');
        }
      }
    }
    
    // Zwróć pierwszą zakończoną aktywność
    return activityResults.length > 0 ? activityResults[0] : null;
  } catch (error) {
    console.error('Nieoczekiwany błąd podczas kończenia aktywności zadaniowej:', error);
    return null;
  }
}; 