import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Platform, DrawerLayoutAndroid, Animated, TextInput, Dimensions, Modal, ActivityIndicator, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import InfoCard from './InfoCard';
import AdminPanel from './AdminPanel';
import DrawerMenu from './DrawerMenu';
import { Session } from '@supabase/supabase-js';
import WorkHours from './WorkHours';
import EmployeesList from './EmployeesList';
import TaskManager, { TaskManagerRef } from './TaskManager';
import TaskDetails from './TaskDetails';
import { TopBar } from './TopBar';
import EmployeeTaskDetails from './EmployeeTaskDetails';
import WorkSessionDetails from './WorkSessionDetails';
import { MaintenanceManager } from './MaintenanceManager';
import { startWorkDay, endWorkDay, getActiveTaskActivities, completeTaskActivityByEmployeeAndTask, updateTaskActiveEmployeesCount } from '../services/timeTrackingService';
import * as Location from 'expo-location'; // Dodajemy import expo-location
import { TaskEditForm } from './TaskEditForm'; // Dodaj import
import { AddWorkDay } from './AddWorkDay';
import PurchasesManager from './PurchasesManager';
import PurchaseDetails from './PurchaseDetailsWrapper';
import FilterDrawer from './FilterDrawer'; // Assuming FilterDrawer is in the same directory
import { i18n } from '../utils/localization'; // <-- Dodajemy import i18n
import AllCompanyTasks from './AllCompanyTasks'; // Dodajemy import AllCompanyTasks

type MenuItem = 'dashboard' | 'employees' | 'schedule' | 'tasks' | 'admin' | 'hours' | 'task_details' | 'employee_task_details' | 'edit_task' | 'work_session_details' | 'add_work_day' | 'maintenance' | 'maintenance_details' | 'purchases' | 'purchase_details' | 'zlecenia2';

interface DashboardProps {
  session: Session;
  onBack: () => void;
  hasParentSafeArea?: boolean;
}

interface TaskManagerProps {
  companyId: string;
  activeTab: 'add' | 'list';
  userType: 'company' | 'employee' | 'coordinator';
  userId: string;
}

interface ActiveEmployee {
  id: string;
  full_name: string;
  email: string;
  job_order?: string;
  start_time?: string;
  task_id?: string;
  work_session_id?: string;
}

interface TodayTask {
  id: string;
  client_name: string;
  address: string;
  work_scope: string;
  start_time: string;
  status: 'pending' | 'in_progress' | 'completed';
  assigned_employees: string[];
  active_employees_count?: number; // Liczba aktywnych pracowników przypisanych do zadania
}

interface UpcomingTask {
  id: string;
  client_name: string;
  address: string;
  work_scope: string;
  date: string;
  assigned_employees: string[];
  active_employees_count?: number; // Add this field to track active employees count
  status: 'pending' | 'in_progress' | 'completed'; // Dodaję pole status
}

interface UpcomingVacation {
  id: string;
  employee_name: string;
  start_date: string;
  end_date: string;
  status: 'pending' | 'approved' | 'rejected';
}

// Dodajemy interfejs dla zgłoszeń awarii po innych interfejsach
interface MaintenanceReport {
  id: string;
  title: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'reported' | 'in_progress' | 'resolved' | 'canceled' | 'to_check' | 'pending';
  created_at: string;
  location?: string;
  reported_by_name?: string;
  repaired_by?: string;
  repaired_by_name?: string;
  repair_start_time?: string;
  repair_end_time?: string;
  resolved_time?: string;
  resolved_by?: string;
  resolved_by_name?: string;
  canceled_time?: string;
  canceled_by?: string;
  canceled_by_name?: string;
  description?: string;
  photos?: string[]; // Add photos field
}

interface Purchase {
  id: string;
  title: string;
  description: string;
  category: string;
  price_estimate: number;
  actual_price?: number;
  status: 'pending' | 'approved' | 'ordered' | 'delivered' | 'canceled';
  requested_by: string;
  requested_by_name: string;
  requested_at: string;
  approved_by?: string;
  approved_by_name?: string;
  approved_at?: string;
  ordered_at?: string;
  delivered_at?: string;
  canceled_at?: string;
  canceled_by?: string;
  canceled_by_name?: string;
  supplier?: string;
  invoice_number?: string;
  invoice_date?: string;
  warranty_end_date?: string;
  attachments?: any[];
  location?: string;
  notes?: string;
  priority: string;
  company_id: string;
  quantity?: number;
}

// Funkcja pomocnicza do pobierania lokalizacji
const getCurrentLocation = async (): Promise<any> => {
  try {
    // Najpierw poproś o uprawnienia
    const { status } = await Location.requestForegroundPermissionsAsync();
    
    if (status !== 'granted') {
      console.log('Brak uprawnień do geolokalizacji');
      return null;
    }
    
    // Uzyskaj lokalizację
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.Balanced
    });
    
    const locationData = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: location.coords.accuracy,
      timestamp: new Date().toISOString()
    };
    
    console.log('Pobrana lokalizacja:', locationData);
    return locationData;
  } catch (error) {
    console.error('Błąd podczas pobierania lokalizacji:', error);
    return null;
  }
};

const Dashboard = ({ session, onBack, hasParentSafeArea }: DashboardProps) => {
  const [userType, setUserType] = useState<'company' | 'employee' | 'coordinator' | null>(null);
  const [userRole, setUserRole] = useState<'employee' | 'coordinator' | 'admin' | null>(null);
  const [companyId, setCompanyId] = useState<string | null>(null);
  const [showAdminPanel, setShowAdminPanel] = useState(false);
  const drawerRef = useRef<DrawerLayoutAndroid>(null);
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem>('dashboard');
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [taskTab, setTaskTab] = useState<'add' | 'list'>('list');
  const taskManagerRef = useRef<TaskManagerRef>(null);
  const [activeEmployeesCount, setActiveEmployeesCount] = useState(0);
  const [activeEmployees, setActiveEmployees] = useState<ActiveEmployee[]>([]);
  const [employeesCardExpanded, setEmployeesCardExpanded] = useState(false);
  const [tasksCardExpanded, setTasksCardExpanded] = useState(false);
  const [upcomingTasksCardExpanded, setUpcomingTasksCardExpanded] = useState(false);
  const [upcomingVacationsCardExpanded, setUpcomingVacationsCardExpanded] = useState(false);
  const [purchasesCardExpanded, setPurchasesCardExpanded] = useState(false);
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [purchasesCount, setPurchasesCount] = useState(0);
  const purchasesCardScaleAnim = useRef(new Animated.Value(1)).current;
  const purchasesCardOpacityAnim = useRef(new Animated.Value(0)).current;
  const employeesCardScaleAnim = useRef(new Animated.Value(1)).current;
  const employeesCardOpacityAnim = useRef(new Animated.Value(0)).current;
  const tasksCardScaleAnim = useRef(new Animated.Value(1)).current;
  const tasksCardOpacityAnim = useRef(new Animated.Value(0)).current;
  const [todayTasksCount, setTodayTasksCount] = useState(0);
  const [todayTasks, setTodayTasks] = useState<TodayTask[]>([]);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [upcomingTasksCount, setUpcomingTasksCount] = useState(0);
  const [upcomingTasks, setUpcomingTasks] = useState<UpcomingTask[]>([]);
  const upcomingTasksCardScaleAnim = useRef(new Animated.Value(1)).current;
  const upcomingTasksCardOpacityAnim = useRef(new Animated.Value(0)).current;
  const [upcomingVacationsCount, setUpcomingVacationsCount] = useState(0);
  const [upcomingVacations, setUpcomingVacations] = useState<UpcomingVacation[]>([]);
  const upcomingVacationsCardScaleAnim = useRef(new Animated.Value(1)).current;
  const upcomingVacationsCardOpacityAnim = useRef(new Animated.Value(0)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedWorkSessionId, setSelectedWorkSessionId] = useState<string | null>(null);
  const [selectedWorkSessionDate, setSelectedWorkSessionDate] = useState<string>('');
  const [selectedWorkSessionEmployeeId, setSelectedWorkSessionEmployeeId] = useState<string>('');
  const [selectedWorkSessionEmployeeName, setSelectedWorkSessionEmployeeName] = useState<string>('');
  
  // Stan dla niestandardowego alertu dla wersji webowej
  const [customAlert, setCustomAlert] = useState({
    visible: false,
    title: '',
    message: '',
    buttons: [] as Array<{
      text: string;
      onPress: () => void;
      style?: 'cancel' | 'default';
    }>,
    inputMode: false,
    inputValue: '',
  });
  
  // Nowy stan dla śledzenia aktywnej sesji pracy bieżącego użytkownika
  const [hasActiveWorkSession, setHasActiveWorkSession] = useState(false);
  
  // Nowy stan dla śledzenia aktywnych zadań bieżącego użytkownika
  const [hasActiveTasks, setHasActiveTasks] = useState(false);
  
  // Dodajemy stan aktywnej karty
  const [activeCardIndex, setActiveCardIndex] = useState(0);
  
  // W komponencie Dashboard dodajemy nowe zmienne stanu dla zgłoszeń awarii
  const [maintenanceReportsCount, setMaintenanceReportsCount] = useState(0);
  const [maintenanceReports, setMaintenanceReports] = useState<MaintenanceReport[]>([]);
  const [maintenanceReportsCardExpanded, setMaintenanceReportsCardExpanded] = useState(false);
  const maintenanceReportsCardScaleAnim = useRef(new Animated.Value(1)).current;
  const maintenanceReportsCardOpacityAnim = useRef(new Animated.Value(0)).current;
  const [selectedMaintenanceReport, setSelectedMaintenanceReport] = useState<MaintenanceReport | null>(null);
  
  // Add state for maintenance tab
  const [maintenanceTab, setMaintenanceTab] = useState<'add' | 'list'>('list');
  
  // Add handler for maintenance tab changes
  const handleMaintenanceTabChange = (tab: 'add' | 'list') => {
    console.log(`Changing maintenance tab to: ${tab}`);
    setMaintenanceTab(tab);
  };
  
  // Handler for selecting a maintenance report to view details
  const handleMaintenanceReportSelect = (report: MaintenanceReport) => {
    console.log('Selected maintenance report:', report.id);
    setSelectedMaintenanceReport(report);
    setSelectedMenuItem('maintenance_details');
    setMaintenanceReportsCardExpanded(false); // Close the expanded card when navigating to details
  };
  
  // Handler for returning from maintenance report details
  const handleBackFromMaintenanceDetails = () => {
    setSelectedMaintenanceReport(null);
    setSelectedMenuItem('maintenance');
    setMaintenanceReportsCardExpanded(false);
  };
  
  // Funkcja udostępniająca alert dla wersji webowej
  const showCustomAlert = (
    title: string,
    message: string,
    buttons: Array<{
      text: string;
      onPress: () => void;
      style?: 'cancel' | 'default';
    }>,
    inputMode = false
  ) => {
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    
    if (isWeb) {
      // W wersji webowej używamy DOM bezpośrednio
      const container = document.getElementById('custom-alert-container');
      if (!container) {
        console.error('Nie znaleziono kontenera alertu');
        return;
      }
      
      // Czyszczenie poprzedniej zawartości
      container.innerHTML = '';
      container.style.visibility = 'visible';
      
      // Tworzenie okna alertu
      const alertBox = document.createElement('div');
      alertBox.style.backgroundColor = 'white';
      alertBox.style.borderRadius = '10px';
      alertBox.style.padding = '20px';
      alertBox.style.width = '80%';
      alertBox.style.maxWidth = '400px';
      alertBox.style.alignItems = 'center';
      alertBox.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.25)';
      alertBox.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
      
      // Tytuł
      const titleElement = document.createElement('h3');
      titleElement.textContent = title;
      titleElement.style.fontSize = '18px';
      titleElement.style.fontWeight = 'bold';
      titleElement.style.marginBottom = '10px';
      titleElement.style.textAlign = 'center';
      alertBox.appendChild(titleElement);
      
      // Wiadomość
      const messageElement = document.createElement('p');
      messageElement.textContent = message;
      messageElement.style.fontSize = '16px';
      messageElement.style.marginBottom = '20px';
      messageElement.style.textAlign = 'center';
      alertBox.appendChild(messageElement);
      
      // Pole tekstowe (jeśli potrzebne)
      let inputElement = null;
      if (inputMode) {
        inputElement = document.createElement('input');
        inputElement.type = 'text';
        inputElement.placeholder = 'Wprowadź nazwę zlecenia';
        inputElement.style.width = '100%';
        inputElement.style.padding = '10px';
        inputElement.style.marginBottom = '20px';
        inputElement.style.borderRadius = '5px';
        inputElement.style.border = '1px solid #ccc';
        inputElement.style.boxSizing = 'border-box';
        inputElement.autofocus = true;
        alertBox.appendChild(inputElement);
      }
      
      // Przyciski
      const buttonsContainer = document.createElement('div');
      buttonsContainer.style.display = 'flex';
      buttonsContainer.style.justifyContent = 'space-around';
      buttonsContainer.style.width = '100%';
      
      buttons.forEach((button) => {
        const buttonElement = document.createElement('button');
        buttonElement.textContent = button.text;
        buttonElement.style.padding = '10px 20px';
        buttonElement.style.borderRadius = '5px';
        buttonElement.style.border = 'none';
        buttonElement.style.backgroundColor = button.style === 'cancel' ? '#6B7280' : '#2563EB';
        buttonElement.style.color = 'white';
        buttonElement.style.fontWeight = 'bold';
        buttonElement.style.fontSize = '16px';
        buttonElement.style.minWidth = '100px';
        buttonElement.style.cursor = 'pointer';
        buttonElement.style.margin = '0 4px';
        
        buttonElement.onclick = () => {
          container.style.visibility = 'hidden';
          
          // Jeśli to przycisk "Rozpocznij" w trybie input, przekazujemy wartość pola
          if (inputMode && button.text === 'Rozpocznij' && inputElement) {
            const inputValue = inputElement.value;
            setTimeout(() => {
              console.log('Rozpoczynanie pracy z nazwą zlecenia:', inputValue);
              handleStartWork(inputValue || '');
            }, 10);
          } else {
            setTimeout(() => button.onPress(), 10);
          }
        };
        
        buttonsContainer.appendChild(buttonElement);
      });
      
      alertBox.appendChild(buttonsContainer);
      container.appendChild(alertBox);
    } else {
      // W wersji mobilnej używamy komponentu React Native
    setCustomAlert({
      visible: true,
      title,
      message,
      buttons,
      inputMode,
      inputValue: '', // Reset input value when opening a new alert
    });
    }
  };
  
  // Zamknięcie alertu
  const hideCustomAlert = () => {
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    
    if (isWeb) {
      const container = document.getElementById('custom-alert-container');
      if (container) {
        container.style.visibility = 'hidden';
      }
    } else {
    setCustomAlert(prev => ({
      ...prev,
      visible: false
    }));
    }
  };
  
  // For web: create alert container once when component mounts
  useEffect(() => {
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    
    if (isWeb) {
      // Remove any existing alert container
      const existingContainer = document.getElementById('custom-alert-container');
      if (existingContainer) {
        document.body.removeChild(existingContainer);
      }

      // Create a new alert container
      const alertContainer = document.createElement('div');
      alertContainer.id = 'custom-alert-container';
      alertContainer.style.position = 'fixed';
      alertContainer.style.top = '0';
      alertContainer.style.left = '0';
      alertContainer.style.width = '100%';
      alertContainer.style.height = '100%';
      alertContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      alertContainer.style.zIndex = '9999';
      alertContainer.style.display = 'flex';
      alertContainer.style.justifyContent = 'center';
      alertContainer.style.alignItems = 'center';
      alertContainer.style.visibility = 'hidden';
      
      document.body.appendChild(alertContainer);
      
      // Clean up on unmount
      return () => {
        if (alertContainer && alertContainer.parentNode) {
          alertContainer.parentNode.removeChild(alertContainer);
        }
      };
    }
  }, []);
  
  // Function to refresh dashboard data
  const refreshDashboardData = async () => {
    console.log('Refreshing dashboard data...');
    setIsRefreshing(true);
    
    try {
      if (companyId) {
        // Fetch data concurrently
      await Promise.all([
          fetchUserProfile(),
        fetchActiveEmployees(),
          fetchPurchases(),
          fetchMaintenanceReports(),
        fetchUpcomingTasks()
      ]);
      }
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
    }

    setIsRefreshing(false);
  };
  
  // Setup auto-refresh every 30 seconds
  useEffect(() => {
    if (!companyId) return;
    
    console.log('Setting up 30-second auto-refresh for dashboard data');
    
    const refreshInterval = setInterval(() => {
      console.log('Auto-refreshing dashboard data (30s interval)');
      fetchActiveEmployees();
      fetchTodayTasks();
      fetchUpcomingTasks();
    }, 30000); // 30 seconds
    
    return () => {
      console.log('Clearing dashboard auto-refresh interval');
      clearInterval(refreshInterval);
    };
  }, [companyId]);

  useEffect(() => {
    fetchUserProfile();
    if (companyId) {
      fetchActiveEmployees();
      fetchTodayTasks();
      fetchUpcomingTasks();
      fetchPurchases();
      fetchMaintenanceReports();
      
      // Set up real-time subscription
      const workSessionsSubscription = supabase
        .channel('work-sessions-channel')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'work_sessions'
          },
          () => {
            fetchActiveEmployees();
          }
        )
        .subscribe();

      // Set up real-time subscription for tasks
      const tasksSubscription = supabase
        .channel('tasks-channel')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'tasks'
          },
          () => {
            fetchTodayTasks();
            fetchUpcomingTasks(); // Dodaj odświeżanie nadchodzących zadań przy zmianie w tabeli tasks
          }
        )
        .subscribe();

      // Set up 5-second auto-refresh interval for dashboard data (changed from 30s)
      console.log('Setting up 5-second auto-refresh for dashboard data');
      const refreshTimer = setInterval(() => {
        console.log('Auto-refreshing active employees, today\'s tasks, upcoming tasks, maintenance reports and purchases (5s interval)');
        fetchActiveEmployees();
        fetchTodayTasks();
        fetchUpcomingTasks();
        fetchMaintenanceReports(); // Dodajemy odświeżanie zgłoszeń awarii co 5 sekund
        fetchPurchases(); // Dodajemy odświeżanie zgłoszeń zakupów co 5 sekund
      }, 5000); // 5 seconds refresh interval

      // Subskrypcja do tabeli maintenance_reports
      const maintenanceReportsSubscription = supabase
        .channel('maintenance-reports-channel')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'maintenance_reports'
          },
          () => {
            fetchMaintenanceReports();
          }
        )
        .subscribe();

      // Subskrypcja do tabeli purchases
      const purchasesSubscription = supabase
        .channel('purchases-channel')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'purchases'
          },
          () => {
            fetchPurchases();
          }
        )
        .subscribe();

      return () => {
        workSessionsSubscription.unsubscribe();
        tasksSubscription.unsubscribe();
        console.log('Cleaning up dashboard refresh timer');
        clearInterval(refreshTimer);
        maintenanceReportsSubscription.unsubscribe();
        purchasesSubscription.unsubscribe();
      };
    }
  }, [companyId]);

  const fetchUserProfile = async () => {
    try {
      console.log('Fetching user profile...');
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        console.error('No user found');
        return;
      }

      console.log('User data:', user);
      console.log('User metadata:', user.user_metadata);
      
      if (user?.user_metadata) {
        const userType = user.user_metadata.user_type;
        console.log('Setting user type:', userType);
        setUserType(userType);
        
        if (userType === 'company') {
          console.log('Setting company ID from user ID:', user.id);
          setCompanyId(user.id);
        } else if (userType === 'employee' || userType === 'independent_employee') {
          console.log('Fetching employee company ID...');
          const { data: employeeData, error: employeeError } = await supabase
            .from('employees')
            .select('company_id, full_name, role, subscription_status')
            .eq('id', user.id)
            .single();

          if (employeeError) {
            console.error('Error fetching employee data:', employeeError);
            Alert.alert('Error', 'Failed to fetch employee data');
            return;
          }

          console.log('Employee data:', employeeData);

          if (userType === 'independent_employee' || !employeeData?.company_id) {
            // Niezależny pracownik - nie ma przypisanej firmy
            console.log('Independent employee detected');
            setCompanyId(null);
            setUserType('independent_employee');
          } else if (employeeData?.company_id) {
            console.log('Setting company ID for employee:', employeeData.company_id);
            setCompanyId(employeeData.company_id);
            setUserType(employeeData.role || 'employee');
          } else {
            console.error('No company ID found for employee');
            Alert.alert('Error', 'No company ID found for employee');
          }
        } else {
          console.error('Invalid user type:', userType);
          Alert.alert('Error', 'Invalid user type');
        }
      } else {
        console.error('No user metadata found');
        Alert.alert('Error', 'No user metadata found');
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
      Alert.alert('Error', 'Failed to fetch user profile');
    }
  };

  const fetchActiveEmployees = useCallback(async () => {
    if (!companyId) return;

    try {
      console.log('Fetching active employees for company:', companyId);
      
      const { data: activeSessions, error } = await supabase
        .from('work_sessions')
        .select(`
          id,
          employee_id,
          start_time,
          job_order,
          task_id
        `)
        .eq('company_id', companyId)
        .is('end_time', null);

      if (error) {
        console.error('Error fetching active sessions:', error);
        return;
      }

      console.log('Active sessions data:', activeSessions);

      if (!activeSessions || activeSessions.length === 0) {
        setActiveEmployeesCount(0);
        setActiveEmployees([]);
        return;
      }

      // Get unique employee IDs
      const employeeIds = [...new Set(activeSessions.map(session => session.employee_id))];

      // Fetch employee details - tylko aktywni pracownicy
      const { data: employeesData, error: employeesError } = await supabase
        .from('employees')
        .select('id, full_name, subscription_status')
        .in('id', employeeIds)
        .eq('subscription_status', 'ACTIVE');

      if (employeesError) {
        console.error('Error fetching employees:', employeesError);
        return;
      }

      console.log('Active employees data:', employeesData);

      // Map employees with their session data - tylko aktywni pracownicy
      const uniqueEmployees = employeesData?.map(employee => {
        const session = activeSessions.find(s => s.employee_id === employee.id);
        return {
          id: employee.id,
          full_name: employee.full_name,
          email: employee.id,
          subscription_status: employee.subscription_status,
          job_order: session?.job_order,
          start_time: session?.start_time,
          task_id: session?.task_id,
          work_session_id: session?.id
        };
      }) || [];

      setActiveEmployeesCount(uniqueEmployees.length);
      setActiveEmployees(uniqueEmployees);
    } catch (error) {
      console.error('Error in fetchActiveEmployees:', error);
    }
  }, [companyId]);

  const fetchTodayTasks = useCallback(async () => {
    if (!companyId) return;

    try {
      console.log('Fetching today\'s tasks for company:', companyId);
      
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('company_id', companyId);
        
      // Filtruj po dzisiejszej dacie, ale tylko jeśli pole date istnieje
      query = query.or(`date.eq.${todayStr},date.is.null`);

      // If user is an employee, only fetch tasks assigned to them
      if (userType === 'employee') {
        query = query.contains('assigned_employees', [session.user.id]);
      }

      const { data: tasksData, error } = await query;

      if (error) {
        console.error('Error fetching today\'s tasks:', error);
        return;
      }

      console.log('Today\'s tasks data:', tasksData);
      
      if (!tasksData || tasksData.length === 0) {
        setTodayTasksCount(0);
        setTodayTasks([]);
        return;
      }

      // Filtruj zadania, aby upewnić się, że mają wszystkie wymagane pola
      const validTasks = tasksData.filter(task => 
        task.client_name && 
        task.status && 
        ['pending', 'in_progress', 'completed'].includes(task.status)
      );

      // Aktualizuj liczniki aktywnych pracowników dla każdego zadania
      if (validTasks.length > 0) {
        console.log('Updating active employees count for tasks');
        await Promise.all(
          validTasks.map(async (task) => {
            const activeCount = await updateTaskActiveEmployeesCount(task.id);
            task.active_employees_count = activeCount > 0 ? activeCount : 0;
          })
        );
      }

      // Sortowanie zadań według priorytetu statusu
      const sortedTasks = [...validTasks].sort((a, b) => {
        const getStatusPriority = (status: string) => {
          switch (status) {
            case 'in_progress': return 1;
            case 'pending': return 2;
            case 'completed': return 3;
            default: return 4;
          }
        };
        
        return getStatusPriority(a.status) - getStatusPriority(b.status);
      });

      // Pobierz informacje o aktywnych sesjach pracy dla zadań
      const { data: activeSessions, error: sessionsError } = await supabase
        .from('task_activities') // Zmieniono z work_sessions na task_activities
        .select('task_id, employee_id')
        .eq('status', 'active'); // Zamiast sprawdzania end_time, używamy statusu 'active'

      if (sessionsError) {
        console.error('Error fetching active task activities:', sessionsError);
      } else if (activeSessions && activeSessions.length > 0) {
        console.log('Active task activities:', activeSessions);
        // Zlicz aktywnych pracowników dla każdego zadania
        const activeEmployeesByTask = activeSessions?.reduce((acc, session) => {
          if (!acc[session.task_id]) {
            acc[session.task_id] = new Set();
          }
          acc[session.task_id].add(session.employee_id);
          return acc;
        }, {} as Record<string, Set<string>>);

        // Ustaw liczbę aktywnych pracowników dla każdego zadania
        sortedTasks.forEach(task => {
          task.active_employees_count = activeEmployeesByTask?.[task.id]?.size || 0;
        });
      }

      setTodayTasksCount(validTasks.length);
      setTodayTasks(sortedTasks);
    } catch (error) {
      console.error('Error in fetchTodayTasks:', error);
    }
  }, [companyId, userType, session.user.id]);

  const fetchUpcomingTasks = useCallback(async () => {
    if (!companyId) return;

    try {
      console.log('Fetching upcoming tasks for company:', companyId);
      
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      const { data: upcomingTasksData, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('company_id', companyId)
        .gt('date', todayStr) // Zadania z datą późniejszą niż dziś
        .not('status', 'eq', 'completed') // Pomijamy zakończone zadania
        .order('date', { ascending: true }); // Sortujemy po dacie rosnąco

      if (error) {
        console.error('Error fetching upcoming tasks:', error);
        return;
      }

      console.log('Upcoming tasks data:', upcomingTasksData);
      
      if (!upcomingTasksData || upcomingTasksData.length === 0) {
        setUpcomingTasksCount(0);
        setUpcomingTasks([]);
        return;
      }

      // Filtruj zadania, aby upewnić się, że mają wszystkie wymagane pola
      const validTasks = upcomingTasksData.filter(task => 
        task.client_name && task.date
      ).map(task => ({
        ...task,
        // Zapewniamy, że każde zadanie ma status - domyślnie "pending" jeśli nie jest zdefiniowany
        status: task.status || 'pending'
      }));

      // Aktualizuj liczniki aktywnych pracowników dla każdego zadania
      if (validTasks.length > 0) {
        console.log('Updating active employees count for upcoming tasks');
        await Promise.all(
          validTasks.map(async (task) => {
            const activeCount = await updateTaskActiveEmployeesCount(task.id);
            task.active_employees_count = activeCount > 0 ? activeCount : 0;
          })
        );
      }

      // Sortowanie zadań według priorytetu statusu
      const sortedTasks = [...validTasks].sort((a, b) => {
        const getStatusPriority = (status: string) => {
          switch (status) {
            case 'in_progress': return 1;
            case 'pending': return 2;
            case 'completed': return 3;
            default: return 4;
          }
        };
        
        return getStatusPriority(a.status) - getStatusPriority(b.status);
      });

      setUpcomingTasksCount(validTasks.length);
      setUpcomingTasks(sortedTasks);
    } catch (error) {
      console.error('Error in fetchUpcomingTasks:', error);
    }
  }, [companyId]);

  const fetchUpcomingVacations = async () => {
    if (!companyId) return;

    try {
      console.log('Fetching upcoming vacations for company:', companyId);
      
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      // Pobierz dane o urlopach
      const { data: upcomingVacationsData, error } = await supabase
        .from('vacations')
        .select('id, employee_id, start_date, end_date, status')
        .eq('company_id', companyId)
        .gt('start_date', todayStr) // Urlopy rozpoczynające się po dzisiejszej dacie
        .order('start_date', { ascending: true }); // Sortujemy po dacie rozpoczęcia

      if (error) {
        console.error('Error fetching upcoming vacations:', error);
        return;
      }

      if (!upcomingVacationsData || upcomingVacationsData.length === 0) {
        setUpcomingVacationsCount(0);
        setUpcomingVacations([]);
        return;
      }

      // Pobierz dane o pracownikach
      const employeeIds = upcomingVacationsData.map(vacation => vacation.employee_id);
      const { data: employeesData, error: employeesError } = await supabase
        .from('employees')
        .select('id, full_name')
        .in('id', employeeIds);

      if (employeesError) {
        console.error('Error fetching employees:', employeesError);
      }

      // Mapujemy dane, aby dostosować ich format do naszego interfejsu
      const formattedVacations = upcomingVacationsData.map(vacation => {
        const employee = employeesData?.find(emp => emp.id === vacation.employee_id);
        return {
          id: vacation.id,
          employee_name: employee?.full_name || i18n.t('unknownEmployee'),
          start_date: vacation.start_date,
          end_date: vacation.end_date,
          status: vacation.status
        };
      });

      setUpcomingVacationsCount(formattedVacations.length);
      setUpcomingVacations(formattedVacations);
    } catch (error) {
      console.error('Error in fetchUpcomingVacations:', error);
    }
  };
  
  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error logging out:', error.message);
      Alert.alert('Error', 'Failed to log out. Please try again.');
    } else {
      console.log('Successfully logged out');
      onBack();
    }
  };

  const closeDrawer = () => {
    if (Platform.OS === 'android') {
      drawerRef.current?.closeDrawer();
    }
    setIsDrawerVisible(false);
  };

  const handleMenuPress = () => {
    // Only toggle the drawer on small screens
    if (!isLargeScreen) {
    if (Platform.OS === 'android') {
      drawerRef.current?.openDrawer();
    } else {
      setIsDrawerVisible(true);
      }
    }
  };

  const handleMenuItemPress = (menuItem: MenuItem) => {
    console.log(`Menu item selected: ${menuItem}`);
    console.log(`Current state - selectedMenuItem: ${selectedMenuItem}, showAdminPanel: ${showAdminPanel}`);
    
    // Set showAdminPanel based on whether 'admin' is selected
    setShowAdminPanel(menuItem === 'admin');
    
    // Always update the selected menu item
    console.log(`Setting selectedMenuItem to ${menuItem}`);
    setSelectedMenuItem(menuItem);
    
    // Jeśli użytkownik wraca na dashboard, odśwież stan przycisków Quick Actions
    if (menuItem === 'dashboard') {
      console.log('Refreshing Quick Actions state on dashboard return');
      // Używamy hasActiveWorkSession i hasActiveTasks zamiast checkUserActiveWorkSession i checkUserActiveTasks
      hasActiveWorkSession && fetchActiveEmployees();
      hasActiveTasks && fetchTodayTasks();
      refreshDashboardData(); // Opcjonalnie - odświeżenie wszystkich danych dashboardu
    }
    
    // Jeśli przeglądarka, zamknij szufladę nawigacyjną
    if (Platform.OS === 'web') {
      console.log('Closing drawer (web)');
      setIsDrawerVisible(false);
    } else if (Platform.OS === 'android') {
      // Jeśli Android, zamknij szufladę
      console.log('Closing drawer (Android)');
      closeDrawer();
    }
    
    console.log(`After handleMenuItemPress - selectedMenuItem: ${selectedMenuItem}, showAdminPanel: ${showAdminPanel}`);
  };

  const handleWorkSessionEnd = () => {
    if (taskManagerRef.current) {
      taskManagerRef.current.fetchTasks();
    }
  };

  const toggleEmployeesCard = () => {
    console.log('toggleEmployeesCard called');
    if (employeesCardExpanded) {
      // Collapse card - wracamy do normalnych rozmiarów
      Animated.parallel([
        Animated.timing(employeesCardScaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(employeesCardOpacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setEmployeesCardExpanded(false);
      });
    } else {
      // Expand card - rozszerzamy na cały ekran
      setEmployeesCardExpanded(true);
      // Najpierw ustawiamy skalę na 1, aby zapobiec efektowi zmniejszenia
      employeesCardScaleAnim.setValue(1);
      
      // Ustawiamy sekwencję animacji - najpierw overlay, potem rozszerzenie karty
      Animated.sequence([
        // Najpierw pokazujemy overlay
        Animated.timing(overlayOpacity, {
          toValue: 0.6,
          duration: 150,
          useNativeDriver: true,
        }),
        // Następnie animujemy jednocześnie skalę i przezroczystość treści
        Animated.parallel([
          Animated.timing(employeesCardScaleAnim, {
            toValue: 1.1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(employeesCardOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ])
      ]).start();
    }
  };

  const toggleTasksCard = () => {
    console.log('toggleTasksCard called');
    console.log('Current tasksCardExpanded state:', tasksCardExpanded);
    console.log('Current animation values are ready');
    
    if (tasksCardExpanded) {
      console.log('Collapsing tasks card');
      // Collapse card - wracamy do normalnych rozmiarów
      Animated.parallel([
        Animated.timing(tasksCardScaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(tasksCardOpacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        console.log('Collapse animation completed, setting tasksCardExpanded to false');
        setTasksCardExpanded(false);
      });
    } else {
      console.log('Expanding tasks card');
      // Expand card - rozszerzamy na cały ekran
      setTasksCardExpanded(true);
      console.log('Set tasksCardExpanded to true');
      
      // Najpierw ustawiamy skalę na 1, aby zapobiec efektowi zmniejszenia
      tasksCardScaleAnim.setValue(1);
      console.log('Set scale to 1');
      
      // Ustawiamy sekwencję animacji - najpierw overlay, potem rozszerzenie karty
      Animated.sequence([
        // Najpierw pokazujemy overlay
        Animated.timing(overlayOpacity, {
          toValue: 0.6,
          duration: 150,
          useNativeDriver: true,
        }),
        // Następnie animujemy jednocześnie skalę i przezroczystość treści
        Animated.parallel([
          Animated.timing(tasksCardScaleAnim, {
            toValue: 1.1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(tasksCardOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ])
      ]).start(({finished}) => {
        console.log('Expansion animation completed:', finished);
        console.log('Card expanded state:', tasksCardExpanded);
        console.log('Animation completed successfully');
      });
    }
  };

  const toggleUpcomingTasksCard = () => {
    console.log('toggleUpcomingTasksCard called');
    if (upcomingTasksCardExpanded) {
      // Collapse card - wracamy do normalnych rozmiarów
      Animated.parallel([
        Animated.timing(upcomingTasksCardScaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(upcomingTasksCardOpacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setUpcomingTasksCardExpanded(false);
      });
    } else {
      // Expand card - rozszerzamy na cały ekran
      setUpcomingTasksCardExpanded(true);
      // Najpierw ustawiamy skalę na 1, aby zapobiec efektowi zmniejszenia
      upcomingTasksCardScaleAnim.setValue(1);
      
      // Ustawiamy sekwencję animacji - najpierw overlay, potem rozszerzenie karty
      Animated.sequence([
        // Najpierw pokazujemy overlay
        Animated.timing(overlayOpacity, {
          toValue: 0.6,
          duration: 150,
          useNativeDriver: true,
        }),
        // Następnie animujemy jednocześnie skalę i przezroczystość treści
        Animated.parallel([
          Animated.timing(upcomingTasksCardScaleAnim, {
            toValue: 1.1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(upcomingTasksCardOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ])
      ]).start();
    }
  };

  const toggleUpcomingVacationsCard = () => {
    console.log('toggleUpcomingVacationsCard called');
    if (upcomingVacationsCardExpanded) {
      // Collapse card - wracamy do normalnych rozmiarów
      Animated.parallel([
        Animated.timing(upcomingVacationsCardScaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(upcomingVacationsCardOpacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setUpcomingVacationsCardExpanded(false);
      });
    } else {
      // Expand card - rozszerzamy na cały ekran
      setUpcomingVacationsCardExpanded(true);
      // Najpierw ustawiamy skalę na 1, aby zapobiec efektowi zmniejszenia
      upcomingVacationsCardScaleAnim.setValue(1);
      
      // Ustawiamy sekwencję animacji - najpierw overlay, potem rozszerzenie karty
      Animated.sequence([
        // Najpierw pokazujemy overlay
        Animated.timing(overlayOpacity, {
          toValue: 0.6,
          duration: 150,
          useNativeDriver: true,
        }),
        // Następnie animujemy jednocześnie skalę i przezroczystość treści
        Animated.parallel([
          Animated.timing(upcomingVacationsCardScaleAnim, {
            toValue: 1.1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(upcomingVacationsCardOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ])
      ]).start();
    }
  };

  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      // Sprawdź, czy data jest prawidłowa
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toLocaleTimeString('pl-PL', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const getTimeDuration = (startTime?: string) => {
    if (!startTime) return '';
    
    try {
      const start = new Date(startTime);
      // Sprawdź, czy data jest prawidłowa
      if (isNaN(start.getTime())) {
        return '';
      }
      
      const now = new Date();
      const diffMs = now.getTime() - start.getTime();
      const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      
      return `${diffHrs}h ${diffMins}m`;
    } catch (error) {
      console.error('Error calculating duration:', error);
      return '';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'TBD';
    
    try {
      // Najprostszy możliwy format - tylko pierwsze 5 znaków, co powinno pokryć "DD.MM" lub "HH:MM"
      return dateString.substring(0, 5);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'TBD';
    }
  };

  // Nowa funkcja formatująca datę w formacie dd.mm.rr
  const formatDateDDMMYY = (dateString?: string) => {
    if (!dateString) return 'TBD';
    
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      
      // Zwracamy tylko dzień i miesiąc w formacie dd.mm
      return `${day}.${month}`;
    } catch (err) {
      console.error('Error formatting date:', err);
      return 'TBD';
    }
  };

  const getVacationDuration = (startDate?: string, endDate?: string) => {
    if (!startDate || !endDate) return '';
    
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      // Sprawdź, czy daty są prawidłowe
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return '';
      }
      
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      return `${diffDays} dni`;
    } catch (error) {
      console.error('Error calculating vacation duration:', error);
      return '';
    }
  };

  // Znajdź funkcję renderMenuItem i zaktualizuj ją, aby używała i18n.t
  const renderMenuItem = (iconName: any, title: string, menuItem: MenuItem, isSelected: boolean) => (
    <TouchableOpacity
      style={[styles.menuItem, isSelected && styles.selectedMenuItem]}
      onPress={() => handleMenuItemPress(menuItem)}
    >
      <Ionicons name={iconName} size={24} color={isSelected ? '#4B5AF8' : '#64748b'} />
      <Text style={[styles.menuItemText, isSelected && styles.selectedMenuItemText]}>
        {i18n.t(title)} {/* Zmiana: używamy i18n.t() */}
      </Text>
    </TouchableOpacity>
  );

  // Znajdź funkcję renderLogoutButton i zaktualizuj ją, aby używała i18n.t
  const renderLogoutButton = () => (
    <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
      <Ionicons name="log-out-outline" size={24} color="#64748b" />
      <Text style={styles.menuItemText}>{i18n.t('logout')}</Text> {/* Zmiana: używamy i18n.t() */}
    </TouchableOpacity>
  );

  // Znajdź funkcję renderDrawerContent i zaktualizuj ją, aby używała i18n.t
  const renderDrawerContent = () => (
    <View style={styles.drawerContent}>
      <View style={styles.drawerHeader}>
        <Text style={styles.drawerHeaderText}>{i18n.t('menu')}</Text>
      </View>
      <ScrollView style={styles.menuItems}>
        {renderMenuItem('home-outline', 'dashboard', 'dashboard', selectedMenuItem === 'dashboard')}
        
        {userType === 'company' || userType === 'coordinator' ? (
          <>
            {renderMenuItem('people-outline', 'employees', 'employees', selectedMenuItem === 'employees')}
            {/* Pozostałe elementy menu dla firmy/koordynatora */}
            {renderMenuItem('briefcase-outline', 'tasks', 'tasks', selectedMenuItem === 'tasks')}
            {/* Removed Harmonogram menu item */}
            {renderMenuItem('construct-outline', 'maintenance', 'maintenance', selectedMenuItem === 'maintenance')}
            {renderMenuItem('cart-outline', 'purchases', 'purchases', selectedMenuItem === 'purchases')}
            {renderMenuItem('cog-outline', 'admin', 'admin', selectedMenuItem === 'admin')}
          </>
        ) : userType === 'independent_employee' ? (
          <>
            {/* Ograniczone menu dla niezależnych pracowników */}
            {renderMenuItem('time-outline', 'myHours', 'hours', selectedMenuItem === 'hours')}
          </>
        ) : (
          <>
            {/* Elementy menu dla pracownika - usuwamy "myTasks" i zmieniamy "allCompanyTasks" na "tasks" */}
            {renderMenuItem('briefcase-outline', 'tasks', 'zlecenia2', selectedMenuItem === 'zlecenia2')}
            {renderMenuItem('time-outline', 'myHours', 'hours', selectedMenuItem === 'hours')}
            {renderMenuItem('construct-outline', 'maintenance', 'maintenance', selectedMenuItem === 'maintenance')}
            {renderMenuItem('cart-outline', 'purchases', 'purchases', selectedMenuItem === 'purchases')}
          </>
        )}
      </ScrollView>
      {renderLogoutButton()}
    </View>
  );

  const [previousMenuItem, setPreviousMenuItem] = useState<MenuItem>('dashboard');

  const handleTaskSelection = (taskId: string) => {
    // Reset all expanded card states
    setEmployeesCardExpanded(false);
    setTasksCardExpanded(false);
    setUpcomingTasksCardExpanded(false);
    setUpcomingVacationsCardExpanded(false);
    
    // Reset animation values
    employeesCardScaleAnim.setValue(1);
    employeesCardOpacityAnim.setValue(0);
    tasksCardScaleAnim.setValue(1);
    tasksCardOpacityAnim.setValue(0);
    upcomingTasksCardScaleAnim.setValue(1);
    upcomingTasksCardOpacityAnim.setValue(0);
    upcomingVacationsCardScaleAnim.setValue(1);
    upcomingVacationsCardOpacityAnim.setValue(0);
    
    // Reset overlay opacity
    Animated.timing(overlayOpacity, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();

    // Store the current menu item before changing to task details
    setPreviousMenuItem(selectedMenuItem);

    // Set task details
    setSelectedTaskId(taskId);
    setSelectedMenuItem('task_details');
  };

  const handleBackFromTaskDetails = () => {
    setSelectedTaskId(null);
    // Navigate back to the previous menu item instead of always going to 'tasks'
    setSelectedMenuItem(previousMenuItem);
  };

  const handleBackFromEmployeeTaskDetails = () => {
    setSelectedMenuItem('dashboard');
    
    // Reset overlay opacity
    Animated.timing(overlayOpacity, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const handleEmployeeSelection = (employeeId: string, taskId?: string) => {
    // Reset all expanded card states
    setEmployeesCardExpanded(false);
    setTasksCardExpanded(false);
    setUpcomingTasksCardExpanded(false);
    setUpcomingVacationsCardExpanded(false);
    
    // Reset animation values
    employeesCardScaleAnim.setValue(1);
    employeesCardOpacityAnim.setValue(0);
    tasksCardScaleAnim.setValue(1);
    tasksCardOpacityAnim.setValue(0);
    upcomingTasksCardScaleAnim.setValue(1);
    upcomingTasksCardOpacityAnim.setValue(0);
    upcomingVacationsCardScaleAnim.setValue(1);
    upcomingVacationsCardOpacityAnim.setValue(0);
    
    // Reset overlay opacity
    Animated.timing(overlayOpacity, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();

    // If task ID is provided, use it
    if (taskId) {
      setSelectedTaskId(taskId);
    }
    
    // Set employee details
    setSelectedEmployeeId(employeeId);
    setSelectedMenuItem('employee_task_details');
  };

  // Dodajemy stan dla wybranego zadania do edycji
  const [editTaskId, setEditTaskId] = useState<string | null>(null);
  
  // Funkcja do obsługi nawigacji do edycji zadania
  const handleEditTask = (taskId: string) => {
    console.log(`Navigating to edit task: ${taskId}`);
    setEditTaskId(taskId);
    setSelectedMenuItem('edit_task');
  };
  
  // Funkcja do powrotu z edycji zadania
  const handleBackFromEditTask = () => {
    console.log('Returning from edit task to task details');
    setSelectedMenuItem('dashboard'); // Zmiana z 'task_details' na 'dashboard'
    setEditTaskId(null);
  };

  const handleWorkSessionSelect = (sessionId: string, date: string, employeeId: string, employeeName: string) => {
    setSelectedWorkSessionId(sessionId);
    setSelectedWorkSessionDate(date);
    setSelectedWorkSessionEmployeeId(employeeId);
    setSelectedWorkSessionEmployeeName(employeeName);
    setSelectedMenuItem('work_session_details');
  };

  const handleBackFromWorkSessionDetails = () => {
    // For managers and coordinators, go back to employees tab
    // For regular employees, go back to hours tab
    if (userType === 'company' || userType === 'coordinator') {
      setSelectedMenuItem('employees');
    } else {
      setSelectedMenuItem('hours');
    }
    setSelectedWorkSessionId(null);
  };

  // Dodaj funkcję obsługi kliknięcia logo
  const handleLogoPress = () => {
    setSelectedMenuItem('dashboard');
    
    // Reset overlay opacity
    Animated.timing(overlayOpacity, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const renderContent = () => {
    if (loading && !isRefreshing) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
        </View>
      );
    }

    if (selectedMenuItem === 'maintenance_details' && selectedMaintenanceReport) {
        return (
        <View style={styles.container}>
          <ScrollView 
            style={{ flex: 1, width: '100%', backgroundColor: '#FFFFFF' }} 
            contentContainerStyle={{ padding: 16 }}
          >
            <View style={{ marginBottom: 20 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
            <TouchableOpacity 
                  onPress={handleBackFromMaintenanceDetails}
              style={{ 
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: '#F3F4F6',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 8
                  }}
                >
                  <Ionicons name="arrow-back" size={22} color="#1F2937" />
            </TouchableOpacity>
                <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#1F2937', flex: 1, textAlign: 'center' }}>
                {i18n.t('maintenanceDetailsHeader')}
              </Text>
                <View style={{ width: 36 }} />
          </View>
              
              <View style={{ marginBottom: 16, padding: 0 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 10 }}>
                  <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#1F2937' }}>
                    {selectedMaintenanceReport.title}
                  </Text>
                  <View style={[
                    styles.statusBadge, 
                    { 
                      backgroundColor: 
                        selectedMaintenanceReport.status === 'in_progress' ? '#DBEAFE' : 
                        selectedMaintenanceReport.status === 'reported' ? '#FEF3C7' : 
                        selectedMaintenanceReport.status === 'resolved' ? '#D1FAE5' : 
                        '#E5E7EB'
                    }
                  ]}>
                    <Text style={[
                      styles.statusText, 
                      { 
                        color: 
                          selectedMaintenanceReport.status === 'in_progress' ? '#1E40AF' : 
                          selectedMaintenanceReport.status === 'reported' ? '#92400E' : 
                          selectedMaintenanceReport.status === 'resolved' ? '#065F46' : 
                          '#374151'
                      }
                    ]}>
                      {getMaintenanceStatusText(selectedMaintenanceReport.status)}
                    </Text>
        </View>
        </View>
                
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                  <Ionicons name="warning-outline" size={18} color="#6B7280" />
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                    {i18n.t('maintenancePriorityLabel')}
                  </Text>
                  <View
                    style={[
                      styles.priorityBadge,
                      { backgroundColor: getPriorityColor(selectedMaintenanceReport.priority), marginLeft: 8 }
                    ]}
                  >
                    <Text style={styles.priorityText}>
                      {getPriorityText(selectedMaintenanceReport.priority)}
                    </Text>
              </View>
          </View>
                
                {selectedMaintenanceReport.location && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                    <Ionicons name="location-outline" size={18} color="#6B7280" />
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                      {i18n.t('maintenanceLocationLabel')}
                    </Text>
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                      {selectedMaintenanceReport.location}
                    </Text>
                    </View>
                )}
                
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                  <Ionicons name="person-outline" size={18} color="#6B7280" />
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                    {i18n.t('maintenanceReporterLabel')}
                  </Text>
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                    {selectedMaintenanceReport.reported_by_name || i18n.t('maintenanceUnknownUser')}
                  </Text>
                    </View>
                    
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                  <Ionicons name="calendar-outline" size={18} color="#6B7280" />
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                    {i18n.t('maintenanceReportDateLabel')}
                  </Text>
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                    {formatDateDDMMYY(selectedMaintenanceReport.created_at)}
                  </Text>
                    </View>
                    
                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
                  <Ionicons name="time-outline" size={18} color="#6B7280" />
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                    {i18n.t('maintenanceReportTimeLabel')}
                  </Text>
                  <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                    {formatTime(selectedMaintenanceReport.created_at)}
                  </Text>
                            </View>
                
                {/* Display repair information if available */}
                {selectedMaintenanceReport.repaired_by_name && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                    <Ionicons name="construct-outline" size={18} color="#6B7280" />
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                      {i18n.t('maintenanceRepairedByLabel')}
                    </Text>
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                      {selectedMaintenanceReport.repaired_by_name}
                            </Text>
                          </View>
                )}
                
                {/* Display repair start time if available */}
                {selectedMaintenanceReport.repair_start_time && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                    <Ionicons name="play-outline" size={18} color="#6B7280" />
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                      {i18n.t('maintenanceRepairStartLabel')}
                          </Text>
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                      {formatDateDDMMYY(selectedMaintenanceReport.repair_start_time)} {formatTime(selectedMaintenanceReport.repair_start_time)}
                    </Text>
                  </View>
                )}
                
                {/* Display repair end time if available */}
                {selectedMaintenanceReport.repair_end_time && (
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                    <Ionicons name="stop-outline" size={18} color="#6B7280" />
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                      {i18n.t('maintenanceRepairEndLabel')}
                    </Text>
                    <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                      {formatDateDDMMYY(selectedMaintenanceReport.repair_end_time)} {formatTime(selectedMaintenanceReport.repair_end_time)}
                    </Text>
                      </View>
                    )}
                
                {/* Display resolution information if available */}
                {selectedMaintenanceReport.resolved_time && (
                  <>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                      <Ionicons name="checkmark-done-outline" size={18} color="#6B7280" />
                      <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                        {i18n.t('maintenanceResolvedTimeLabel')}
                      </Text>
                      <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                        {formatDateDDMMYY(selectedMaintenanceReport.resolved_time)} {formatTime(selectedMaintenanceReport.resolved_time)}
                      </Text>
                            </View>
                            
                    {/* Show resolver info if available */}
                    {selectedMaintenanceReport.resolved_by && (
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                        <Ionicons name="person-outline" size={18} color="#6B7280" />
                        <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                          {i18n.t('maintenanceResolvedByLabel')}
                        </Text>
                        <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                          {selectedMaintenanceReport.resolved_by_name || selectedMaintenanceReport.resolved_by}
                        </Text>
                              </View>
                    )}
                  </>
                )}
                
                {/* Display cancellation information if available */}
                {selectedMaintenanceReport.canceled_time && (
                  <>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                      <Ionicons name="close-circle-outline" size={18} color="#6B7280" />
                      <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                        {i18n.t('maintenanceCanceledTimeLabel')}
                      </Text>
                      <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                        {formatDateDDMMYY(selectedMaintenanceReport.canceled_time)} {formatTime(selectedMaintenanceReport.canceled_time)}
                      </Text>
                            </View>
                            
                    {/* Show cancellation info if available */}
                    {selectedMaintenanceReport.canceled_by && (
                      <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                        <Ionicons name="person-outline" size={18} color="#6B7280" />
                        <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                          {i18n.t('maintenanceCanceledByLabel')}
                        </Text>
                        <Text style={{ marginLeft: 8, fontSize: 14, color: '#1F2937' }}>
                          {selectedMaintenanceReport.canceled_by_name || selectedMaintenanceReport.canceled_by}
                        </Text>
                                        </View>
                    )}
                  </>
                )}
                
                {/* Display description if available */}
                {selectedMaintenanceReport.description && (
                  <View style={{ marginTop: 16, marginBottom: 16 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                      <Ionicons name="document-text-outline" size={18} color="#6B7280" />
                      <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                        {i18n.t('maintenanceDescriptionLabel')}
                                      </Text>
                                    </View>
                    <Text style={{ fontSize: 14, color: '#1F2937', lineHeight: 20 }}>
                      {selectedMaintenanceReport.description}
                                      </Text>
                  </View>
                )}
                
                {/* Display photos if available */}
                {selectedMaintenanceReport.photos && selectedMaintenanceReport.photos.length > 0 && (
                  <View style={{ marginTop: 16, marginBottom: 16 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                      <Ionicons name="images-outline" size={18} color="#6B7280" />
                      <Text style={{ marginLeft: 8, fontSize: 14, color: '#4B5563', fontWeight: '500' }}>
                        {i18n.t('maintenancePhotosLabel')}
                                          </Text>
                                        </View>
                    <ScrollView 
                      horizontal={true} 
                      showsHorizontalScrollIndicator={true}
                      style={{ marginBottom: 8 }}
                    >
                      {selectedMaintenanceReport.photos.map((photo, index) => (
                        <TouchableOpacity 
                          key={index} 
                          style={{ marginRight: 8, borderRadius: 8, overflow: 'hidden' }}
                          onPress={() => openPhotoModal(photo)}
                        >
                          <Image
                            source={{ uri: photo }}
                            style={{ width: 200, height: 150, borderRadius: 8 }}
                            resizeMode="cover"
                          />
                        </TouchableOpacity>
                      ))}
                    </ScrollView>
                  </View>
                )}
                
                {/* Action buttons integrated in the details card */}
                <View style={{ marginTop: 8, borderTopWidth: 1, borderTopColor: '#E5E7EB', paddingTop: 16 }}>
                  {selectedMaintenanceReport.status === 'reported' && (
    <TouchableOpacity
                      style={{ 
                        backgroundColor: '#3B82F6', 
                        padding: 12, 
                        borderRadius: 8, 
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center'
                      }}
                      onPress={(e) => {
                        // Prevent default behavior that might cause navigation
                        if (e) e.preventDefault?.();
                        console.log('Starting maintenance report:', selectedMaintenanceReport.id);
                        handleStartMaintenanceRepair(selectedMaintenanceReport.id);
                        return false; // Prevent event bubbling
                      }}
                    >
                      <Ionicons name="play" size={20} color="white" style={{ marginRight: 8 }} />
                      <Text style={{ color: 'white', fontWeight: 'bold' }}>{i18n.t('maintenanceStartRepairButton')}</Text>
    </TouchableOpacity>
                  )}
                  
                  {selectedMaintenanceReport.status === 'in_progress' && (
                    <TouchableOpacity 
                      style={{ 
                        backgroundColor: '#F59E0B', 
                        padding: 12, 
                        borderRadius: 8, 
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center'
                      }}
                      onPress={(e) => {
                        // Prevent default behavior that might cause navigation
                        if (e) e.preventDefault?.();
                        console.log('Completing maintenance report:', selectedMaintenanceReport.id);
                        handleCompleteMaintenanceRepair(selectedMaintenanceReport.id);
                        return false; // Prevent event bubbling
                      }}
                    >
                      <Ionicons name="checkmark-circle" size={20} color="white" style={{ marginRight: 8 }} />
                      <Text style={{ color: 'white', fontWeight: 'bold' }}>{i18n.t('maintenanceCompleteRepairButton')}</Text>
    </TouchableOpacity>
                  )}
                  
                  {/* Zaktualizowany warunek: pokazanie przycisku zarówno dla statusu 'to_check' (dla kompatybilności) jak i 'pending' (nowy status) */}
                  {(selectedMaintenanceReport.status === 'to_check' || selectedMaintenanceReport.status === 'pending') && (
                    <TouchableOpacity 
                      style={{ 
                        backgroundColor: '#10B981', 
                        padding: 12, 
                        borderRadius: 8, 
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center'
                      }}
                      onPress={(e) => {
                        // Prevent default behavior that might cause navigation
                        if (e) e.preventDefault?.();
                        console.log('Finalizing maintenance report:', selectedMaintenanceReport.id);
                        handleFinalizeMaintenanceRepair(selectedMaintenanceReport.id);
                        return false; // Prevent event bubbling
                      }}
                    >
                      <Ionicons name="checkmark-done" size={20} color="white" style={{ marginRight: 8 }} />
                      <Text style={{ color: 'white', fontWeight: 'bold' }}>{i18n.t('maintenanceFinalizeButton')}</Text>
                    </TouchableOpacity>
                  )}
                  
                  {selectedMaintenanceReport.status !== 'resolved' && selectedMaintenanceReport.status !== 'canceled' && (
            <TouchableOpacity 
              style={{ 
                        backgroundColor: '#EF4444', 
                        padding: 12, 
                        borderRadius: 8, 
                        alignItems: 'center',
                        flexDirection: 'row',
                        justifyContent: 'center',
                        marginTop: selectedMaintenanceReport.status !== 'reported' ? 8 : 0
                      }}
                      onPress={(e) => {
                        // Prevent default behavior that might cause navigation
                        if (e) e.preventDefault?.();
                        console.log('Canceling maintenance report:', selectedMaintenanceReport.id);
                        handleCancelMaintenanceReport(selectedMaintenanceReport.id);
                        return false; // Prevent event bubbling
                      }}
                    >
                      <Ionicons name="close-circle" size={20} color="white" style={{ marginRight: 8 }} />
                      <Text style={{ color: 'white', fontWeight: 'bold' }}>{i18n.t('maintenanceCancelButton')}</Text>
            </TouchableOpacity>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
          </View>
        );
    }

    if (selectedMenuItem === 'task_details' && selectedTaskId) {
      return (
        <TaskDetails 
          taskId={selectedTaskId} 
          onBack={handleBackFromTaskDetails} 
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress}
          onEditTask={handleEditTask}
        />
      );
    }

    if (selectedMenuItem === 'edit_task' && editTaskId) {
      return (
        <TaskEditForm 
          taskId={editTaskId}
          onBack={handleBackFromEditTask}
          onMenuPress={handleMenuPress}
          companyId={companyId as string}
        />
      );
    }

    if (selectedMenuItem === 'employee_task_details' && selectedEmployeeId) {
      return (
        <EmployeeTaskDetails 
          employeeId={selectedEmployeeId}
          taskId={selectedTaskId || undefined}
          onBack={handleBackFromEmployeeTaskDetails} 
          onMenuPress={handleMenuPress}
        />
      );
    }

    if (selectedMenuItem === 'work_session_details' && selectedWorkSessionId) {
      return (
        <WorkSessionDetails 
          sessionId={selectedWorkSessionId}
          date={selectedWorkSessionDate}
          employeeId={selectedWorkSessionEmployeeId}
          employeeName={selectedWorkSessionEmployeeName}
          onBack={handleBackFromWorkSessionDetails}
          onMenuPress={handleMenuPress}
          setSelectedMenuItem={setSelectedMenuItem}
          onTaskSelect={(taskId) => {
            setSelectedTaskId(taskId);
            setSelectedMenuItem('task_details');
          }}
          onSessionSelect={handleWorkSessionSelect}
        />
      );
    }

    if (selectedMenuItem === 'add_work_day') {
      return (
        <AddWorkDay
          userId={session.user.id}
          companyId={companyId!}
          onBack={() => setSelectedMenuItem('dashboard')}
          onMenuPress={handleMenuPress}
        />
      );
    }

    if (selectedMenuItem === 'maintenance') {
      return (
        <MaintenanceManager
          companyId={companyId!}
          activeTab={maintenanceTab}
          userType={userType!}
          userId={session.user.id}
          onTabChange={handleMaintenanceTabChange}
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress} // Przywrócenie powrotu do dashboardu
          onReportSelect={handleMaintenanceReportSelect}
          isLargeScreen={isLargeScreen}
        />
      );
    }

    if (selectedMenuItem === 'admin' && companyId) {
      return (
        <View style={styles.container}>
          <AdminPanel 
            companyId={companyId} 
            onMenuPress={handleMenuPress}
            onNavigate={handleMenuItemPress}
            isLargeScreen={isLargeScreen}
          />
        </View>
      );
    }

    if (selectedMenuItem === 'purchases' && companyId) {
      return (
        <PurchasesManager
          companyId={companyId}
          activeTab={purchasesTab}
          userType={userType === 'company' ? 'company' : userType === 'employee' ? 'employee' : 'coordinator'}
          userId={session.user.id}
          onTabChange={handlePurchasesTabChange}
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress}
          onPurchaseSelect={handlePurchaseSelect}
          isLargeScreen={isLargeScreen}
        />
      );
    }

    if (selectedMenuItem === 'task_details' && selectedTaskId) {
      return (
        <TaskDetails 
          taskId={selectedTaskId} 
          onBack={handleBackFromTaskDetails} 
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress}
          onEditTask={handleEditTask}
        />
      );
    }

    if (selectedMenuItem === 'edit_task' && editTaskId) {
      return (
        <TaskEditForm 
          taskId={editTaskId}
          onBack={handleBackFromEditTask}
          onMenuPress={handleMenuPress}
          companyId={companyId as string}
        />
      );
    }

    if (selectedMenuItem === 'employee_task_details' && selectedEmployeeId) {
      return (
        <EmployeeTaskDetails 
          employeeId={selectedEmployeeId}
          taskId={selectedTaskId || undefined}
          onBack={handleBackFromEmployeeTaskDetails} 
          onMenuPress={handleMenuPress}
        />
      );
    }

    if (selectedMenuItem === 'work_session_details' && selectedWorkSessionId) {
      return (
        <WorkSessionDetails 
          sessionId={selectedWorkSessionId}
          date={selectedWorkSessionDate}
          employeeId={selectedWorkSessionEmployeeId}
          employeeName={selectedWorkSessionEmployeeName}
          onBack={handleBackFromWorkSessionDetails}
          onMenuPress={handleMenuPress}
          setSelectedMenuItem={setSelectedMenuItem}
          onTaskSelect={(taskId) => {
            setSelectedTaskId(taskId);
            setSelectedMenuItem('task_details');
          }}
          onSessionSelect={handleWorkSessionSelect}
        />
      );
    }

    if (selectedMenuItem === 'add_work_day') {
      return (
        <AddWorkDay
          userId={session.user.id}
          companyId={companyId!}
          onBack={() => setSelectedMenuItem('dashboard')}
          onMenuPress={handleMenuPress}
        />
      );
    }

    if (selectedMenuItem === 'maintenance') {
      return (
        <MaintenanceManager
          companyId={companyId!}
          activeTab={maintenanceTab}
          userType={userType!}
          userId={session.user.id}
          onTabChange={handleMaintenanceTabChange}
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress} // Przywrócenie powrotu do dashboardu
          onReportSelect={handleMaintenanceReportSelect}
          isLargeScreen={isLargeScreen}
        />
      );
    }

    if (selectedMenuItem === 'admin' && companyId) {
      return (
        <View style={styles.container}>
          <AdminPanel 
            companyId={companyId} 
            onMenuPress={handleMenuPress}
            onNavigate={handleMenuItemPress}
            isLargeScreen={isLargeScreen}
          />
        </View>
      );
    }

    if (selectedMenuItem === 'purchase_details' && selectedPurchaseId) {
        return (
            <PurchaseDetails
              purchaseId={selectedPurchaseId}
              onBack={() => setSelectedMenuItem('purchases')}
              onMenuPress={handleMenuPress}
              onLogoPress={handleLogoPress}
            />
      );
    }

    // Dodaj nowy przypadek dla zlecenia2
    if (selectedMenuItem === 'zlecenia2' && companyId) {
      return (
        <AllCompanyTasks 
              companyId={companyId}
              userId={session.user.id}
              onTaskSelect={handleTaskSelection}
              onMenuPress={handleMenuPress}
              isLargeScreen={isLargeScreen}
        />
      );
    }

    return (
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {/* Remove TopBar */}

          {selectedMenuItem === 'dashboard' && userType === 'independent_employee' && (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
              <Ionicons name="person-outline" size={80} color="#9CA3AF" />
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#374151', marginTop: 20, textAlign: 'center' }}>
                {i18n.t('independentEmployeeTitle')}
              </Text>
              <Text style={{ fontSize: 16, color: '#6B7280', marginTop: 10, textAlign: 'center', lineHeight: 24 }}>
                {i18n.t('independentEmployeeDescription')}
              </Text>
              <Text style={{ fontSize: 14, color: '#9CA3AF', marginTop: 20, textAlign: 'center', lineHeight: 20 }}>
                {i18n.t('independentEmployeeInstructions')}
              </Text>

              {/* Ograniczone Quick Actions dla niezależnych pracowników */}
              <View style={{ marginTop: 40, width: '100%', maxWidth: 300 }}>
                <TouchableOpacity
                  style={{
                    backgroundColor: '#F3F4F6',
                    padding: 15,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginBottom: 10
                  }}
                  onPress={() => handleQuickAction('add_work_day')}
                >
                  <Ionicons name="add-circle-outline" size={20} color="#6B7280" />
                  <Text style={{ marginLeft: 10, color: '#6B7280', fontWeight: '500' }}>
                    Dodaj dzień pracy
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    backgroundColor: '#F3F4F6',
                    padding: 15,
                    borderRadius: 10,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onPress={() => setSelectedMenuItem('hours')}
                >
                  <Ionicons name="time-outline" size={20} color="#6B7280" />
                  <Text style={{ marginLeft: 10, color: '#6B7280', fontWeight: '500' }}>
                    Moje godziny pracy
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {selectedMenuItem === 'dashboard' && userType !== 'independent_employee' && (
            <View style={{ flex: 1 }}> {/* Container for dashboard content */}
              {/* Bloki informacyjne */}
              <ScrollView 
                style={{ flex: 1 }} 
                contentContainerStyle={[
                  styles.dashboardScrollContentWrapper,
                  { flexGrow: 1, paddingBottom: 70 } // Zwiększony padding na dole, aby zostawić większy odstęp dla quickbar
                ]}
              >
                <View style={[styles.dashboardGridContainer, { flex: 1 }]}>
                {/* Pierwszy wiersz - 2 klocki */}
                <View style={styles.dashboardRow}>
                  {/* Active Employees */}
                  <TouchableOpacity 
                    activeOpacity={0.9}
                    onPress={toggleEmployeesCard}
                    style={[styles.cardWrapper, styles.cardCompact, styles.gridCardItem]}
                  >
                    <View style={[styles.backgroundIconContainer, { zIndex: 0, top: 5, right: 5, opacity: 0.3 }]}>
                      <Ionicons 
                        name="people" 
                        size={140} 
                        color="rgba(30, 64, 175, 0.5)" 
                      />
                    </View>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('activeEmployees')}</Text>
                      </View>
                      <Text style={styles.cardSubtitle}>{i18n.t('currentlyWorking')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{activeEmployeesCount}</Text>
                    </View>
                    
                    {activeEmployees.length > 0 && (
                      <View style={styles.basicEmployeeList}>
                        {activeEmployees.slice(0, 2).map((employee, index) => (
                          <View 
                            key={index} 
                            style={styles.basicEmployeeRow}
                          >
                            <View style={styles.rowWithDot}>
                              <View style={styles.greenDot} />
                              <Text style={styles.basicEmployeeName}>{employee.full_name}</Text>
                            </View>
                            <Text style={styles.basicEmployeeDuration}>
                              {getTimeDuration(employee.start_time)}
                            </Text>
                          </View>
                        ))}
                        {activeEmployees.length > 2 && (
                          <Text style={styles.moreItemsText}>
                            +{activeEmployees.length - 2} {i18n.t('more')}
                          </Text>
                        )}
                      </View>
                    )}
                  </TouchableOpacity>

                  {/* Przesuwany klocek z Today's Tasks i Nadchodzące Zlecenia */}
                  <View style={[styles.cardWrapper, styles.cardCompact, styles.gridCardItem]}>
                    <View style={styles.horizontalScrollContainer}>
                      {/* Strzałka w lewo */}
                      <TouchableOpacity
                        style={[styles.navButton, styles.navButtonLeft, activeCardIndex === 0 && styles.navButtonDisabled]} 
                        onPress={() => activeCardIndex > 0 && scrollToCard(activeCardIndex - 1)}
                        disabled={activeCardIndex === 0}
                      >
                        <Ionicons name="chevron-back" size={16} color={activeCardIndex === 0 ? "rgba(209, 213, 219, 0.2)" : "rgba(37, 99, 235, 0.3)"} />
                      </TouchableOpacity>
                      
                      <ScrollView 
                        ref={horizontalScrollRef}
                        horizontal={true}
                        pagingEnabled={true}
                        showsHorizontalScrollIndicator={false}
                        decelerationRate="fast"
                        style={styles.horizontalScroll}
                        contentContainerStyle={styles.horizontalScrollContent}
                        onMomentumScrollEnd={(event) => {
                          const offsetX = event.nativeEvent.contentOffset.x;
                          const cardWidth = screenWidth * 0.45 - 20;
                          const index = Math.round(offsetX / cardWidth);
                          setActiveCardIndex(index);
                        }}
                      >
                        {/* Today's Tasks card */}
                        <TouchableOpacity 
                          activeOpacity={0.9}
                          onPress={toggleTasksCard}
                          style={[styles.horizontalCardContainer, { width: screenWidth * 0.45 - 20 }]}
                        >
                          <View style={[styles.cardContent, { padding: 0, height: '100%' }]}>
                            <View style={[styles.backgroundIconContainer, { zIndex: 0, top: 5, right: 5, opacity: 0.3 }]}>
                              <Ionicons 
                                name="calendar-outline" 
                                size={140} 
                                color="rgba(30, 64, 175, 0.5)" 
                              />
                            </View>
                            
                            <View style={styles.cardHeader}>
                              <View style={styles.cardTitleRow}>
                                <Text style={styles.cardTitle}>{i18n.t('todaysTasks')}</Text>
                              </View>
                              <Text style={styles.cardSubtitle}>{i18n.t('tasks')}</Text>
                            </View>
                            
                            <View style={styles.cardCountContainer}>
                              <Text style={styles.cardCount}>{todayTasksCount}</Text>
                            </View>
                            
                            {todayTasks.length > 0 && (
                              <View style={[styles.basicEmployeeList, { flex: 1 }]}>
                                {todayTasks.slice(0, 2).map((task, index) => (
                                  <View 
                                    key={index} 
                                    style={[styles.basicEmployeeRow, { paddingVertical: 8, borderBottomWidth: index < 1 ? 1 : 0, borderBottomColor: '#f0f0f0' }]}
                                  >
                                    <View style={[styles.rowWithDot, { flex: 1, maxWidth: '65%' }]}>
                                      {task.status === 'completed' ? (
                                        <View style={styles.checkmarkContainer}>
                                          <Ionicons name="checkmark-circle" size={16} color="#10B981" />
                                        </View>
                                      ) : task.status === 'in_progress' ? (
                                        <View style={[styles.statusDot, { backgroundColor: '#3B82F6' }]} />
                                      ) : (
                                        <View style={[styles.statusDot, { backgroundColor: '#F97316' }]} />
                                      )}
                                      <Text 
                                        style={[styles.basicEmployeeName, { 
                                          flex: 1,
                                          marginRight: 4
                                        }]}
                                        numberOfLines={1}
                                        ellipsizeMode="tail"
                                      >
                                        {task.client_name}
                                      </Text>
                                    </View>
                                    <View style={{ flexDirection: 'column', alignItems: 'flex-end' }}>
                                      {/* Ukryj godzinę, jeśli są aktywni pracownicy */}
                                      {(!task.active_employees_count || task.active_employees_count <= 0) && (
                                      <Text style={[styles.basicEmployeeDuration, { minWidth: 45 }]}>
                                        {task.start_time ? task.start_time.substring(0, 5) : 'TBD'}
                                      </Text>
                                      )}
                                      {/* Modyfikacja warunku - renderuj całe View tylko jeśli active_employees_count > 0 */}
                                      {(task.active_employees_count ?? 0) > 0 && (
                                        <View style={[styles.activeEmployeesBasicRow, { marginTop: 4 }]}>
                                          <Ionicons name="people" size={14} color="#2563EB" />
                                          <Text style={styles.basicActiveEmployees}>
                                            {task.active_employees_count}
                                          </Text>
                                        </View>
                                      )}
                                    </View>
                                  </View>
                                ))}
                                {todayTasks.length > 2 && (
                                  <Text style={styles.moreItemsText}>
                                    +{todayTasks.length - 2} {i18n.t('more')}
                                  </Text>
                                )}
                              </View>
                            )}
                          </View>
                        </TouchableOpacity>

                        {/* Nadchodzące Zlecenia card */}
                        <TouchableOpacity 
                          activeOpacity={0.9}
                          onPress={toggleUpcomingTasksCard}
                          style={[styles.horizontalCardContainer, { width: screenWidth * 0.45 - 20 }]}
                        >
                          <View style={[styles.cardContent, { padding: 0, height: '100%' }]}>
                            <View style={[styles.backgroundIconContainer, { zIndex: 0, top: 5, right: 5, opacity: 0.3 }]}>
                              <Ionicons 
                                name="timer-outline" 
                                size={140} 
                                color="rgba(59, 130, 246, 0.4)" 
                              />
                            </View>
                            
                            <View style={styles.cardHeader}>
                              <View style={styles.cardTitleRow}>
                                <Text style={styles.cardTitle}>{i18n.t('upcomingTasks')}</Text>
                              </View>
                              <Text style={styles.cardSubtitle}>{i18n.t('tasksPlanned')}</Text>
                            </View>
                            
                            <View style={styles.cardCountContainer}>
                              <Text style={styles.cardCount}>{upcomingTasksCount}</Text>
                            </View>
                            
                            {upcomingTasks.length > 0 ? (
                              <View style={[styles.basicEmployeeList, { flex: 1 }]}>
                                {upcomingTasks.map((task, index) => (
                                  <TouchableOpacity 
                                    key={index} 
                                    style={styles.employeeListItem}
                                    onPress={() => {
                                      toggleUpcomingTasksCard(); // First close the expanded card
                                      handleTaskSelection(task.id); // Then navigate to task details
                                    }}
                                  >
                                    <View style={styles.employeeDetails}>
                                      <View style={{flexDirection: 'row', alignItems: 'center', marginBottom: 3, marginLeft: 2}}>
                                        <Text style={[styles.employeeName, {marginLeft: 0}]}>{task.client_name}</Text>
                                    </View>
                                    </View>
                                    <View style={styles.employeeTimeInfo}>
                                      <Text style={styles.employeeStartTime}>{formatDateDDMMYY(task.date)}</Text>
                                    </View>
                                  </TouchableOpacity>
                                ))}
                              </View>
                            ) : (
                              <Text style={styles.noEmployeesText}>{i18n.t('noUpcomingTasks')}</Text>
                            )}
                          </View>
                        </TouchableOpacity>
                      </ScrollView>
                      
                      {/* Strzałka w prawo */}
                      <TouchableOpacity
                        style={[styles.navButton, styles.navButtonRight, activeCardIndex === 1 && styles.navButtonDisabled]} 
                        onPress={() => activeCardIndex < 1 && scrollToCard(activeCardIndex + 1)}
                        disabled={activeCardIndex === 1}
                      >
                        <Ionicons name="chevron-forward" size={16} color={activeCardIndex === 1 ? "rgba(209, 213, 219, 0.2)" : "rgba(37, 99, 235, 0.3)"} />
                      </TouchableOpacity>
                    </View>
                    
                    {/* Wskaźniki stron (kropki) */}
                    <View style={styles.dotsIndicatorContainer}>
                      <View style={[styles.dot, activeCardIndex === 0 && styles.activeDot]} />
                      <View style={[styles.dot, activeCardIndex === 1 && styles.activeDot]} />
                    </View>
                  </View>
                </View>

                {/* Drugi wiersz - 2 klocki */}
                <View style={styles.dashboardRow}>
                  {/* Zgłoszenia Awarii */}
                  <TouchableOpacity 
                    activeOpacity={0.9}
                    onPress={handleMaintenanceReportsCardPress}
                    style={[styles.cardWrapper, styles.cardCompact, styles.gridCardItem]}
                  >
                    <View style={[styles.backgroundIconContainer, { zIndex: 0, top: 5, right: 5, opacity: 0.3 }]}>
                      <Ionicons 
                        name="warning" 
                        size={140} 
                        color="rgba(30, 64, 175, 0.5)" 
                      />
                    </View>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('maintenanceReports')}</Text>
                      </View>
                      <Text style={styles.cardSubtitle}>{i18n.t('pending')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{maintenanceReportsCount}</Text>
                    </View>
                    
                    {maintenanceReports.length > 0 && (
                      <View style={styles.basicEmployeeList}>
                        {maintenanceReports.slice(0, 2).map((report, index) => (
                          <View 
                            key={index} 
                            style={styles.basicEmployeeRow}
                          >
                            <View style={styles.rowWithDot}>
                              <View style={[
                                styles.statusDot, 
                                { backgroundColor: getMaintenanceStatusColor(report.status) }
                              ]} />
                              <Text 
                                style={styles.basicEmployeeName}
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                {report.title}
                              </Text>
                            </View>
                            <Text style={styles.basicEmployeeDuration}>
                              {getPriorityText(report.priority)}
                            </Text>
                          </View>
                        ))}
                        {maintenanceReports.length > 2 && (
                          <Text style={styles.moreItemsText}>
                            +{maintenanceReports.length - 2} {i18n.t('more')}
                          </Text>
                        )}
                      </View>
                    )}
                  </TouchableOpacity>

                  {/* Nadchodzące Urlopy */}
                  <TouchableOpacity 
                    activeOpacity={0.9}
                    onPress={togglePurchasesCard}
                    style={[styles.cardWrapper, styles.cardCompact, styles.gridCardItem]}
                  >
                    <View style={[styles.backgroundIconContainer, { zIndex: 0, top: 5, right: 5, opacity: 0.3 }]}>
                      <Ionicons name="cart-outline" size={140} color="rgba(30, 64, 175, 0.5)" />
                    </View>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('purchases')}</Text>
                      </View>
                      <Text style={styles.cardSubtitle}>{i18n.t('purchaseRequests')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{purchasesCount}</Text>
                    </View>
                    
                    {purchases.length > 0 && (
                      <View style={styles.basicEmployeeList}>
                        {purchases.slice(0, 2).map((purchase, index) => (
                          <View 
                            key={index} 
                            style={styles.basicEmployeeRow}
                          >
                            <View style={styles.rowWithDot}>
                              <View style={[
                                styles.statusDot, 
                                { backgroundColor: getPurchaseStatusColor(purchase.status) }
                              ]} />
                              <Text style={styles.basicEmployeeName}>
                                {purchase.title}
                              </Text>
                            </View>
                            <Text style={styles.basicEmployeeDuration}>
                              {getPriorityText(purchase.priority)}
                            </Text>
                          </View>
                        ))}
                        {purchases.length > 2 && (
                          <Text style={styles.moreItemsText}>
                            +{purchases.length - 2} {i18n.t('more')}
                          </Text>
                        )}
                      </View>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
              </ScrollView>

              {/* Quick Actions Bar - Conditionally render only on small screens */}
              {!isLargeScreen && (
              <View style={styles.quickActionsContainerCompact}>
                <View style={styles.quickActionsCardCompact}>
                  <View style={styles.quickActionsRowNew}>
                    {userType === 'company' || userType === 'coordinator' ? (
                      // Manager quick actions
                      <>
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('new_task')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#EBF5FF' }]}>
                            <Ionicons name="add-circle-outline" size={20} color="#2563EB" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('newTask')}</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('add_team')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#FEF3C7' }]}>
                            <Ionicons name="people-outline" size={20} color="#D97706" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('addTeam')}</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('report_maintenance')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#FEE2E2' }]}>
                            <Ionicons name="warning-outline" size={20} color="#DC2626" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('reportIssue')}</Text>
                        </TouchableOpacity>
                        
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('add_purchase')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#DBEAFE' }]}>
                            <Ionicons name="cart-outline" size={20} color="#3B82F6" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('addPurchase')}</Text>
                        </TouchableOpacity>
                      </>
                    ) : (
                      // Employee quick actions
                      <>
                        {!hasActiveWorkSession ? (
                          // Przycisk Start Work, widoczny tylko gdy nie ma aktywnej sesji
                          <TouchableOpacity 
                            style={styles.quickActionButtonRow} 
                            onPress={() => handleQuickAction('start_work')}
                          >
                            <View style={[styles.quickActionIconRow, { backgroundColor: '#DCFCE7' }]}>
                              <Ionicons name="play-circle-outline" size={20} color="#16A34A" />
                            </View>
                            <Text 
                              style={styles.quickActionTextRow}
                              numberOfLines={1}
                              ellipsizeMode="tail"
                            >{i18n.t('startWork')}</Text>
                          </TouchableOpacity>
                        ) : (
                          // Przycisk End Work, widoczny tylko gdy jest aktywna sesja
                          <TouchableOpacity 
                            style={styles.quickActionButtonRow} 
                            onPress={() => handleQuickAction('end_work')}
                          >
                            <View style={[styles.quickActionIconRow, { backgroundColor: '#FEE2E2' }]}>
                              <Ionicons name="stop-circle-outline" size={20} color="#DC2626" />
                            </View>
                            <Text 
                              style={styles.quickActionTextRow}
                              numberOfLines={1}
                              ellipsizeMode="tail"
                            >{i18n.t('endWork')}</Text>
                          </TouchableOpacity>
                        )}
                        
                        {/* Przycisk Complete Task, widoczny tylko gdy są aktywne zadania */}
                        {hasActiveTasks && (
                          <TouchableOpacity 
                            style={styles.quickActionButtonRow} 
                            onPress={() => handleQuickAction('complete_task')}
                          >
                            <View style={[styles.quickActionIconRow, { backgroundColor: '#D1FAE5' }]}>
                              <Ionicons name="checkmark-circle-outline" size={20} color="#059669" />
                            </View>
                            <Text 
                              style={styles.quickActionTextRow}
                              numberOfLines={1}
                              ellipsizeMode="tail"
                            >{i18n.t('completeTask')}</Text>
                          </TouchableOpacity>
                        )}
                        
                        {/* Przycisk Add Work Day */}
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('add_work_day')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#FEF3C7' }]}>
                            <Ionicons name="add-circle-outline" size={20} color="#D97706" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('addWorkDay')}</Text>
                        </TouchableOpacity>

                        {/* Przycisk Zgłoś awarię */}
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('report_maintenance')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#FEE2E2' }]}>
                            <Ionicons name="warning-outline" size={20} color="#DC2626" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('reportIssue')}</Text>
                        </TouchableOpacity>
                        
                        {/* Przycisk Dodaj zakup */}
                        <TouchableOpacity 
                          style={styles.quickActionButtonRow} 
                          onPress={() => handleQuickAction('add_purchase')}
                        >
                          <View style={[styles.quickActionIconRow, { backgroundColor: '#DBEAFE' }]}>
                            <Ionicons name="cart-outline" size={20} color="#3B82F6" />
                          </View>
                          <Text 
                            style={styles.quickActionTextRow}
                            numberOfLines={1}
                            ellipsizeMode="tail"
                          >{i18n.t('addPurchase')}</Text>
                        </TouchableOpacity>
                      </>
                    )}
                  </View>
                </View>
              </View>
              )}

              {/* Expanded Active Employees Card */}
              {employeesCardExpanded && (
                <Animated.View 
                  style={[
                    styles.expandedCardContainer,
                    { transform: [{ scale: employeesCardScaleAnim }] }
                  ]}
                  pointerEvents="auto"
                >
                  <View style={styles.expandedCard}>
                    <TouchableOpacity 
                      style={styles.closeButton}
                      onPress={toggleEmployeesCard}
                    >
                      <Ionicons name="close-circle" size={28} color="#1A1A1A" />
                    </TouchableOpacity>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('activeEmployees')}</Text>
                      </View>
                      <Text style={styles.cardSubtitle}>{i18n.t('currentlyWorking')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{activeEmployeesCount}</Text>
                    </View>
                    
                    {/* Specjalna ikona dla Active Employees */}
                    <View style={[styles.backgroundIconContainer, {zIndex: -1, top: 5, right: 5}]}>
                      <Ionicons 
                        name="people" 
                        size={160} 
                        color="rgba(59, 130, 246, 0.4)" 
                        style={{
                          transform: [{scale: 1.3}]
                        }} 
                      />
                    </View>
                    
                    <Animated.View 
                      style={[
                        styles.expandedContent,
                        { opacity: employeesCardOpacityAnim }
                      ]}
                    >
                      {activeEmployees.length > 0 ? (
                        activeEmployees.map((employee, index) => (
                          <TouchableOpacity 
                            key={index} 
                            style={styles.employeeListItem}
                            onPress={() => {
                              toggleEmployeesCard();
                              handleEmployeeSelection(employee.id, employee.task_id);
                            }}
                          >
                            <View style={styles.employeeDetails}>
                              <View style={styles.rowWithDot}>
                                <View style={styles.greenDot} />
                                <Text style={styles.employeeName}>{employee.full_name}</Text>
                              </View>
                              <Text style={styles.employeeJob}>{employee.job_order || i18n.t('noJobSpecified')}</Text>
                            </View>
                            <View style={styles.employeeTimeInfo}>
                              <Text style={styles.employeeStartTime}>
                                {formatTime(employee.start_time)}
                              </Text>
                              <Text style={styles.employeeDuration}>
                                {getTimeDuration(employee.start_time)}
                              </Text>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEmployeesText}>{i18n.t('noActiveEmployees')}</Text>
                      )}
                    </Animated.View>
                  </View>
                </Animated.View>
              )}
              
              {/* Expanded Today Tasks Card */}
              {tasksCardExpanded && (
                <Animated.View 
                  style={[
                    styles.expandedCardContainer,
                    { transform: [{ scale: tasksCardScaleAnim }] }
                  ]}
                  pointerEvents="auto"
                >
                  <View style={styles.expandedCard}>
                    <TouchableOpacity 
                      style={styles.closeButton}
                      onPress={toggleTasksCard}
                    >
                      <Ionicons name="close-circle" size={28} color="#1A1A1A" />
                    </TouchableOpacity>
                    
                    <View style={styles.cardHeader}>
                      <Text style={styles.cardTitle}>{i18n.t('todaysTasks')}</Text>
                      <Text style={styles.cardSubtitle}>{i18n.t('dueToday')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{todayTasksCount}</Text>
                    </View>
                    
                    <View style={[styles.backgroundIconContainer, {zIndex: -1, top: 5, right: 5}]}>
                      <Ionicons 
                        name="calendar" 
                        size={160} 
                        color="rgba(59, 130, 246, 0.4)" 
                        style={{
                          transform: [{scale: 1.3}]
                        }} 
                      />
                    </View>
                    
                    <Animated.View 
                      style={[
                        styles.expandedContent,
                        { opacity: tasksCardOpacityAnim }
                      ]}
                    >
                      {todayTasks.length > 0 ? (
                        todayTasks.map((task, index) => (
                          <TouchableOpacity 
                            key={index} 
                            style={styles.employeeListItem}
                            onPress={() => {
                              toggleTasksCard(); // First close the expanded card
                              handleTaskSelection(task.id); // Then navigate to task details
                            }}
                          >
                            <View style={styles.employeeDetails}>
                              <View style={styles.rowWithDot}>
                                {task.status === 'in_progress' && <View style={styles.greenDot} />}
                                {task.status === 'pending' && <View style={styles.orangeDot} />}
                                {task.status === 'completed' && <View style={styles.checkmarkContainer}>
                                  <Ionicons name="checkmark-circle" size={16} color="#10B981" style={styles.checkmarkIcon} />
                                </View>}
                                <Text 
                                  style={[
                                    styles.employeeName,
                                    task.status === 'completed' && { color: '#6B7280' }
                                  ]}
                                >
                                  {task.client_name}
                                </Text>
                              </View>
                              <Text style={styles.employeeJob}>{task.address}</Text>
                              <Text style={styles.taskScope}>{task.work_scope}</Text>
                              {(task.active_employees_count ?? 0) > 0 && (
                                <View style={styles.activeEmployeesWrapper}>
                                  <Ionicons name="people" size={16} color="#2563EB" />
                                  <Text style={styles.basicActiveEmployees}>
                                    {task.active_employees_count}
                                  </Text>
                                </View>
                              )}
                            </View>
                            <View style={styles.employeeTimeInfo}>
                              <Text style={styles.employeeStartTime}>
                                {formatTime(task.start_time)}
                              </Text>
                              <View style={[
                                styles.statusBadge, 
                                task.status === 'pending' ? styles.pendingBadge : 
                                task.status === 'in_progress' ? styles.inProgressBadge : 
                                task.status === 'completed' ? styles.completedBadge : styles.pendingBadge
                              ]}>
                                <Text style={[
                                  styles.statusText,
                                  task.status === 'pending' ? styles.pendingText : 
                                  task.status === 'in_progress' ? styles.inProgressText : 
                                  task.status === 'completed' ? styles.completedText : styles.pendingText
                                ]}>
                                  {task.status === 'pending' ? i18n.t('pending') : 
                                  task.status === 'in_progress' ? i18n.t('inProgress') : 
                                  i18n.t('completed')}
                                </Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEmployeesText}>{i18n.t('noTasksForToday')}</Text>
                      )}
                    </Animated.View>
                  </View>
                </Animated.View>
              )}
              
              {/* Expanded Upcoming Tasks Card */}
              {upcomingTasksCardExpanded && (
                <Animated.View 
                  style={[
                    styles.expandedCardContainer,
                    { transform: [{ scale: upcomingTasksCardScaleAnim }] }
                  ]}
                  pointerEvents="auto"
                >
                  <View style={styles.expandedCard}>
                    <TouchableOpacity 
                      style={styles.closeButton}
                      onPress={toggleUpcomingTasksCard}
                    >
                      <Ionicons name="close-circle" size={28} color="#1A1A1A" />
                    </TouchableOpacity>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('upcomingTasks')}</Text>
                      </View>
                      <Text style={styles.cardSubtitle}>{i18n.t('tasksPlanned')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{upcomingTasksCount}</Text>
                    </View>
                    
                    <View style={[styles.backgroundIconContainer, {zIndex: -1, top: 5, right: 5}]}>
                      <Ionicons 
                        name="timer-outline" 
                        size={160} 
                        color="rgba(59, 130, 246, 0.4)" 
                        style={{
                          transform: [{scale: 1.3}]
                        }} 
                      />
                    </View>
                    
                    <Animated.View 
                      style={[
                        styles.expandedContent,
                        { opacity: upcomingTasksCardOpacityAnim }
                      ]}
                    >
                      {upcomingTasks.length > 0 ? (
                        upcomingTasks.map((task, index) => (
                          <TouchableOpacity 
                            key={index} 
                            style={styles.employeeListItem}
                            onPress={() => {
                              toggleUpcomingTasksCard(); // First close the expanded card
                              handleTaskSelection(task.id); // Then navigate to task details
                            }}
                          >
                            <View style={styles.employeeDetails}>
                              <Text style={styles.employeeName}>{task.client_name}</Text>
                            </View>
                            <View style={styles.employeeTimeInfo}>
                              <Text style={styles.employeeStartTime}>{formatDateDDMMYY(task.date)}</Text>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEmployeesText}>{i18n.t('noUpcomingTasks')}</Text>
                      )}
                    </Animated.View>
                  </View>
                </Animated.View>
              )}
              
              {/* Expanded Upcoming Vacations Card */}
              {upcomingVacationsCardExpanded && (
                <Animated.View 
                  style={[
                    styles.expandedCardContainer,
                    { transform: [{ scale: upcomingVacationsCardScaleAnim }] }
                  ]}
                  pointerEvents="auto"
                >
                  <View style={styles.expandedCard}>
                    <TouchableOpacity 
                      style={styles.closeButton}
                      onPress={toggleUpcomingVacationsCard}
                    >
                      <Ionicons name="close-circle" size={28} color="#1A1A1A" />
                    </TouchableOpacity>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>Zgłoszenia</Text>
                      </View>
                      <Text style={styles.cardTitle}>Zakupów</Text>
                      <Text style={styles.cardSubtitle}>Oczekujące wnioski</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{purchasesCount}</Text>
                    </View>
                    
                    <View style={[styles.backgroundIconContainer, {zIndex: -1, top: 5, right: 5}]}>
                      <Ionicons 
                        name="cart-outline" 
                        size={160} 
                        color="rgba(59, 130, 246, 0.4)" 
                        style={{
                          transform: [{scale: 1.3}]
                        }} 
                      />
                    </View>
                    
                    <Animated.View 
                      style={[
                        styles.expandedContent,
                        { opacity: upcomingVacationsCardOpacityAnim }
                      ]}
                    >
                      {purchases.length > 0 ? (
                        purchases.map((purchase, index) => (
                          <TouchableOpacity 
                            key={index} 
                            style={styles.employeeListItem}
                            onPress={() => {
                              toggleUpcomingVacationsCard(); // First close the expanded card
                              handlePurchaseSelect(purchase); // Then navigate to purchase details
                            }}
                          >
                            <View style={styles.employeeDetails}>
                              <View style={styles.rowWithDot}>
                                <View style={[styles.statusDot, { backgroundColor: getPurchaseStatusColor(purchase.status) }]} />
                                <Text style={styles.employeeName}>{purchase.title}</Text>
                              </View>
                              <Text style={styles.employeeJob}>{purchase.description}</Text>
                              {purchase.price_estimate && (
                                <Text style={styles.taskScope}>{purchase.price_estimate} zł</Text>
                              )}
                            </View>
                            <View style={styles.employeeTimeInfo}>
                              <Text style={styles.employeeStartTime}>{formatDate(purchase.requested_at)}</Text>
                              <View style={styles.priorityBadge}>
                                <Text style={styles.priorityText}>
                                  {getPriorityText(purchase.priority)}
                                </Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEmployeesText}>Brak oczekujących wniosków</Text>
                      )}
                    </Animated.View>
                  </View>
                </Animated.View>
              )}
              
              {/* Expanded Maintenance Reports Card */}
              {maintenanceReportsCardExpanded && (
                <Animated.View 
                  style={[
                    styles.expandedCardContainer,
                    { transform: [{ scale: maintenanceReportsCardScaleAnim }] }
                  ]}
                  pointerEvents="auto"
                >
                  <View style={styles.expandedCard}>
                    <TouchableOpacity 
                      style={styles.closeButton}
                      onPress={toggleMaintenanceReportsCard}
                    >
                      <Ionicons name="close-circle" size={28} color="#1A1A1A" />
                    </TouchableOpacity>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('maintenanceReportsTitle')}</Text>
                      </View>
                      <Text style={styles.cardTitle}>{i18n.t('maintenanceReportsSub')}</Text>
                      <Text style={styles.cardSubtitle}>{i18n.t('maintenancePending')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{maintenanceReportsCount}</Text>
                    </View>
                    
                    <View style={[styles.backgroundIconContainer, {zIndex: -1, top: 5, right: 5}]}>
                      <Ionicons 
                        name="warning" 
                        size={160} 
                        color="rgba(59, 130, 246, 0.4)" 
                        style={{
                          transform: [{scale: 1.3}]
                        }} 
                      />
                    </View>
                    
                    <Animated.View 
                      style={[
                        styles.expandedContent,
                        { opacity: maintenanceReportsCardOpacityAnim }
                      ]}
                    >
                      {maintenanceReports.length > 0 ? (
                        maintenanceReports.map((report, index) => (
                          <TouchableOpacity 
                            key={index} 
                            style={styles.employeeListItem}
                            onPress={() => {
                              toggleMaintenanceReportsCard(); // First close the expanded card
                              handleMaintenanceReportSelect(report); // Then navigate to report details
                            }}
                          >
                            <View style={styles.employeeDetails}>
                              <View style={styles.rowWithDot}>
                                <View style={[styles.statusDot, { backgroundColor: getMaintenanceStatusColor(report.status) }]} />
                                <Text style={styles.employeeName}>{report.title}</Text>
                              </View>
                              
                              <View style={styles.itemInfoRow}>
                                <Ionicons name="person-outline" size={14} color="#6B7280" style={{marginRight: 4}} />
                                <Text style={styles.itemInfoText}>
                                  {report.reported_by_name || i18n.t('unknown')}
                                </Text>
                              </View>
                              
                              <View style={styles.itemInfoRow}>
                                <Ionicons name="information-circle-outline" size={14} color="#6B7280" style={{marginRight: 4}} />
                                <Text style={styles.itemInfoText}>
                                  {getMaintenanceStatusText(report.status)}
                                </Text>
                              </View>
                            </View>
                            <View style={styles.employeeTimeInfo}>
                              <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(report.priority) }]}>
                                <Text style={styles.priorityText}>
                                  {getPriorityText(report.priority)}
                                </Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <Text style={styles.noEmployeesText}>{i18n.t('noMaintenanceReports')}</Text>
                      )}
                    </Animated.View>
                  </View>
                </Animated.View>
              )}
              
              {/* Expanded Purchases Card */}
              {purchasesCardExpanded && (
                <Animated.View 
                  style={[
                    styles.expandedCardContainer,
                    { transform: [{ scale: purchasesCardScaleAnim }] }
                  ]}
                  pointerEvents="auto"
                >
                  <View style={styles.expandedCard}>
                    <TouchableOpacity 
                      style={styles.closeButton}
                      onPress={togglePurchasesCard}
                    >
                      <Ionicons name="close-circle" size={28} color="#1A1A1A" />
                    </TouchableOpacity>
                    
                    <View style={styles.cardHeader}>
                      <View style={styles.cardTitleRow}>
                        <Text style={styles.cardTitle}>{i18n.t('purchaseRequestsTitle')}</Text>
                      </View>
                      <Text style={styles.cardTitle}>{i18n.t('purchaseRequestsSub')}</Text>
                      <Text style={styles.cardSubtitle}>{i18n.t('purchasePending')}</Text>
                    </View>
                    
                    <View style={styles.cardCountContainer}>
                      <Text style={styles.cardCount}>{purchasesCount}</Text>
                    </View>
                    
                    <View style={[styles.backgroundIconContainer, {zIndex: -1, top: 5, right: 5}]}>
                      <Ionicons 
                        name="cart-outline" 
                        size={160} 
                        color="rgba(59, 130, 246, 0.4)" 
                        style={{
                          transform: [{scale: 1.3}]
                        }} 
                      />
                    </View>
                    
                    <Animated.View 
                      style={[
                        styles.expandedContent,
                        { opacity: purchasesCardOpacityAnim }
                      ]}
                    >
                      {purchases.length > 0 ? (
                        <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={true} contentContainerStyle={{paddingBottom: 10}}>
                          {purchases.map((purchase, index) => (
                            <TouchableOpacity 
                              key={index} 
                              style={styles.employeeListItem}
                              onPress={() => {
                                togglePurchasesCard(); // First close the expanded card
                                handlePurchaseSelect(purchase); // Then navigate to purchase details
                              }}
                            >
                              <View style={styles.employeeDetails}>
                                <View style={styles.rowWithDot}>
                                  <View style={[styles.statusDot, { backgroundColor: getPurchaseStatusColor(purchase.status) }]} />
                                  <Text style={styles.employeeName}>{purchase.title}</Text>
                                </View>
                                
                                <View style={styles.itemInfoRow}>
                                  <Ionicons name="cube-outline" size={14} color="#6B7280" style={{marginRight: 4}} />
                                  <Text style={styles.itemInfoText}>
                                    {i18n.t('quantity')} {purchase.quantity || 1}
                                  </Text>
                                </View>
                                
                                <View style={styles.itemInfoRow}>
                                  <Ionicons name="person-outline" size={14} color="#6B7280" style={{marginRight: 4}} />
                                  <Text style={styles.itemInfoText}>
                                    {purchase.requested_by_name || i18n.t('unknown')}
                                  </Text>
                                </View>
                                
                                <View style={styles.itemInfoRow}>
                                  <Ionicons name="information-circle-outline" size={14} color="#6B7280" style={{marginRight: 4}} />
                                  <Text style={styles.itemInfoText}>
                                    {getPurchaseStatusText(purchase.status)}
                                  </Text>
                                </View>
                              </View>
                              <View style={styles.employeeTimeInfo}>
                                <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(purchase.priority) }]}>
                                  <Text style={styles.priorityText}>
                                    {getPriorityText(purchase.priority)}
                                  </Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                      ) : (
                        <Text style={styles.noEmployeesText}>{i18n.t('noPurchaseRequests')}</Text>
                      )}
                    </Animated.View>
                  </View>
                </Animated.View>
              )}
              
              {/* Overlay for expanded cards */}
              <Animated.View 
                style={[
                  styles.overlay,
                  { opacity: overlayOpacity }
                ]}
                pointerEvents={
                  (employeesCardExpanded || tasksCardExpanded || upcomingTasksCardExpanded || upcomingVacationsCardExpanded || maintenanceReportsCardExpanded || purchasesCardExpanded)
                    ? 'auto'
                    : 'none'
                }
              >
                <TouchableOpacity
                  style={[styles.overlay]}
                  activeOpacity={0.5}
                  onPress={() => {
                    if (employeesCardExpanded) toggleEmployeesCard();
                    if (tasksCardExpanded) toggleTasksCard();
                    if (upcomingTasksCardExpanded) toggleUpcomingTasksCard();
                    if (upcomingVacationsCardExpanded) toggleUpcomingVacationsCard();
                    if (maintenanceReportsCardExpanded) toggleMaintenanceReportsCard();
                    if (purchasesCardExpanded) togglePurchasesCard();
                  }}
                />
              </Animated.View>
            </View>
          )}

          {selectedMenuItem === 'employees' && (userType === 'company' || userType === 'coordinator') && companyId && (
            <EmployeesList 
              companyId={companyId} 
              onRowClick={handleWorkSessionSelect}
            />
          )}

          {selectedMenuItem === 'hours' && (
            <WorkHours 
              onWorkSessionEnd={handleWorkSessionEnd}
              onRowClick={async (sessionId, date) => {
                try {
                  // Use the current user's session
                  const { data: userData, error } = await supabase.auth.getUser();
                  if (error) {
                    console.error('Error getting user:', error);
                    return;
                  }
                  
                  const user = userData.user;
                  if (user) {
                    // For WorkHours, we're viewing our own sessions, so use the current user's info
                    const userName = user.user_metadata?.full_name || 'Użytkownik';
                    
                    handleWorkSessionSelect(
                      sessionId, 
                      date, 
                      user.id, 
                      userName
                    );
                  }
                } catch (error) {
                  console.error('Error in onRowClick:', error);
                }
              }}
            />
          )}

          {selectedMenuItem === 'schedule' && (
            <Text style={styles.comingSoon}>Schedule view coming soon...</Text>
          )}

          {selectedMenuItem === 'tasks' && (userType === 'company' || userType === 'coordinator') && (
            <View style={styles.bottomTabsContainer}>
              <View style={styles.bottomTabs}>
                <TouchableOpacity
                  style={[styles.tab, taskTab === 'add' && styles.activeTab]}
                  onPress={() => setTaskTab('add')}
                >
                  <Ionicons 
                    name="add-circle-outline" 
                    size={24} 
                    color={taskTab === 'add' ? '#2563EB' : '#666'} 
                    style={{marginBottom: 4}}
                  />
                  <Text style={[
                    styles.tabText,
                    taskTab === 'add' && styles.activeTabText
                  ]}>
                    {i18n.t('addTask')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, taskTab === 'list' && styles.activeTab]}
                  onPress={() => setTaskTab('list')}
                >
                  <Ionicons 
                    name="list-outline" 
                    size={24} 
                    color={taskTab === 'list' ? '#2563EB' : '#666'} 
                    style={{marginBottom: 4}}
                  />
                  <Text style={[
                    styles.tabText,
                    taskTab === 'list' && styles.activeTabText
                  ]}>
                    {i18n.t('taskList')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}

          {selectedMenuItem === 'tasks' && (userType === 'company' || userType === 'coordinator') && companyId && (
            <TaskManager 
              ref={taskManagerRef}
              companyId={companyId} 
              activeTab={taskTab}
              userType={userType === 'company' ? 'company' : 'coordinator'}
              userId={session.user.id}
              onTaskSelect={handleTaskSelection}
              onTabChange={handleTaskTabChange}
            />
          )}

          {selectedMenuItem === 'maintenance' && companyId && (
            <MaintenanceManager
              companyId={companyId}
              activeTab={maintenanceTab}
              userType={userType!}
              userId={session.user.id}
              onTabChange={handleMaintenanceTabChange}
              onMenuPress={handleMenuPress}
              onLogoPress={handleLogoPress} // Przywrócenie powrotu do dashboardu
              onReportSelect={handleMaintenanceReportSelect}
              isLargeScreen={isLargeScreen}
            />
          )}

          {selectedMenuItem === 'admin' && companyId && (
            <View style={styles.container}>
              <AdminPanel 
                companyId={companyId} 
                onMenuPress={handleMenuPress}
                onNavigate={handleMenuItemPress}
                isLargeScreen={isLargeScreen}
              />
            </View>
          )}
        </View>
      </View>
    );
  };

  // Definicje funkcji - przenieśmy tutaj, przed ich użyciem
  // Funkcja do sprawdzania aktywnej sesji pracy
  const checkUserActiveWorkSession = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      // Sprawdź, czy użytkownik ma aktywną sesję pracy
      const { data, error } = await supabase
        .from('work_sessions')
        .select('id')
        .eq('employee_id', user.id)
        .is('end_time', null)
        .eq('status', 'active')
        .maybeSingle();
      
      if (error) {
        console.error('Error checking active work session:', error);
        return;
      }
      
      setHasActiveWorkSession(!!data);
      console.log('User has active work session:', !!data);
    } catch (error) {
      console.error('Error in checkUserActiveWorkSession:', error);
    }
  }, []);
  
  // Funkcja do sprawdzania aktywnych zadań
  const checkUserActiveTasks = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      // Sprawdź, czy użytkownik ma aktywne zadania
      const { data, error } = await supabase
        .from('task_activities')
        .select('id')
        .eq('employee_id', user.id)
        .eq('status', 'active')
        .maybeSingle();
      
      if (error) {
        console.error('Error checking active tasks:', error);
        return;
      }
      
      setHasActiveTasks(!!data);
      console.log('User has active tasks:', !!data);
    } catch (error) {
      console.error('Error in checkUserActiveTasks:', error);
    }
  }, []);
  
  // Wywołujmy funkcje sprawdzania przy montowaniu komponentu
  useEffect(() => {
    if (companyId) {
      checkUserActiveWorkSession();
      checkUserActiveTasks();
    }
  }, [companyId, checkUserActiveWorkSession, checkUserActiveTasks]);
  
  // Modyfikacja funkcji handleStartWork, aby używała zaktualizowanej wersji startWorkDay
  const handleStartWork = async (jobOrder: string) => {
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    
    if (!companyId) {
      console.error('Error: No company ID when starting work');
      if (isWeb) {
        showCustomAlert(
          'Błąd',
          'Brak ID firmy',
          [{ text: 'OK', onPress: async () => {} }]
        );
      }
      return;
    }
    
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('Error: No user found when starting work');
        if (isWeb) {
          showCustomAlert(
            'Błąd',
            'Nie znaleziono danych użytkownika',
            [{ text: 'OK', onPress: async () => {} }]
          );
        }
        setLoading(false);
        return;
      }
      
      console.log('Starting work session for user:', user.id, 'company:', companyId);
      
      // Pobierz aktualną lokalizację
      const locationData = await getCurrentLocation();
      console.log('Current location for start work:', locationData);
      
      // Używamy zaktualizowanej wersji startWorkDay, która przyjmuje jobOrder jako parametr
      const session = await startWorkDay(user.id, companyId, locationData, jobOrder.trim() || undefined);
      console.log('Work session started:', session);
      
      if (session) {
        console.log('Work session started successfully');
        if (isWeb) {
          showCustomAlert(
            i18n.t('success'),
            i18n.t('workSessionStarted'),
            [{ text: i18n.t('ok'), onPress: async () => {} }]
          );
        } else {
          Alert.alert(i18n.t('success'), i18n.t('workSessionStarted'));
        }
        // Ustaw flagę aktywnej sesji
        setHasActiveWorkSession(true);
        // Pracownik może nie mieć od razu aktywnego zadania po rozpoczęciu pracy
        await checkUserActiveTasks();
        // Refresh active employees data
        await fetchActiveEmployees();
      } else {
        console.error('Failed to start work session');
        if (isWeb) {
          showCustomAlert(
            'Błąd',
            'Nie udało się rozpocząć sesji pracy',
            [{ text: 'OK', onPress: async () => {} }]
          );
        } else {
          Alert.alert('Błąd', 'Nie udało się rozpocząć sesji pracy');
        }
      }
    } catch (error) {
      console.error('Error starting work:', error);
      if (isWeb) {
        showCustomAlert(
          'Błąd',
          'Wystąpił błąd podczas rozpoczynania pracy',
          [{ text: 'OK', onPress: async () => {} }]
        );
      } else {
        Alert.alert('Błąd', 'Wystąpił błąd podczas rozpoczynania pracy');
      }
    } finally {
      setLoading(false);
    }
  };

  // Modyfikacja funkcji handleEndWork, aby pobierać i zapisywać lokalizację
  const handleEndWork = async (userId: string) => {
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    
    if (!userId) {
      console.error('Error: No user ID when ending work');
      if (isWeb) {
        showCustomAlert(
          'Błąd',
          'Brak ID użytkownika',
          [{ text: 'OK', onPress: async () => {} }]
        );
      }
      return;
    }
    
    setLoading(true);
    try {
      console.log('Ending work for user:', userId);
      
      // Pobierz aktualną lokalizację
      const locationData = await getCurrentLocation();
      console.log('Current location for end work:', locationData);
      
      // End work day with location data
      const success = await endWorkDay(userId, locationData);
      console.log('Work session ended:', success);
      
      if (success) {
        console.log('Work session ended successfully');
        if (isWeb) {
          showCustomAlert(
            i18n.t('success'),
            i18n.t('workSessionEnded'),
            [{ text: i18n.t('ok'), onPress: async () => {} }]
          );
        } else {
          Alert.alert(i18n.t('success'), i18n.t('workSessionEnded'));
        }
        // Ustaw flagę braku aktywnej sesji
        setHasActiveWorkSession(false);
        setHasActiveTasks(false);
        // Refresh active employees data
        await fetchActiveEmployees();
      } else {
        console.error('Failed to end work session');
        if (isWeb) {
          showCustomAlert(
            'Błąd',
            'Nie udało się zakończyć dnia pracy',
            [{ text: 'OK', onPress: async () => {} }]
          );
        } else {
          Alert.alert('Błąd', 'Nie udało się zakończyć dnia pracy');
        }
      }
    } catch (error) {
      console.error('Error ending work day:', error);
      if (isWeb) {
        showCustomAlert(
          'Błąd',
          'Wystąpił błąd podczas kończenia pracy',
          [{ text: 'OK', onPress: async () => {} }]
        );
      } else {
        Alert.alert('Błąd', 'Wystąpił błąd podczas kończenia pracy');
      }
    } finally {
      setLoading(false);
    }
  };

  // Modyfikacja funkcji handleCompleteTask, aby dodatkowo aktualizować status zadania, jeśli jest to ostatni aktywny pracownik
  const handleCompleteTask = async (userId: string, taskId: string) => {
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    
    if (!userId || !taskId) {
      console.error('Error: Missing user ID or task ID when completing task');
      if (isWeb) {
        showCustomAlert(
          'Błąd',
          'Brak wymaganych danych',
          [{ text: 'OK', onPress: async () => {} }]
        );
      }
      return;
    }
    
    setLoading(true);
    try {
      console.log('Completing task for user:', userId, 'task:', taskId);
      
      // Najpierw sprawdź, ilu aktywnych pracowników jest przypisanych do zadania
      const { data: activeEmployees, error: countError } = await supabase
        .from('task_activities')
        .select('employee_id')
        .eq('task_id', taskId)
        .eq('status', 'active');
      
      if (countError) {
        console.error('Error checking active employees in task:', countError);
        if (isWeb) {
          showCustomAlert(
            'Błąd',
            'Nie udało się sprawdzić aktywnych pracowników',
            [{ text: 'OK', onPress: async () => {} }]
          );
        }
        setLoading(false);
        return;
      }
      
      const isLastActiveEmployee = activeEmployees?.length === 1 && 
                                  activeEmployees[0].employee_id === userId;
      
      console.log('Is last active employee:', isLastActiveEmployee);
      
      // Zakończ aktywność pracownika
      const completedActivity = await completeTaskActivityByEmployeeAndTask(
        userId,
        taskId
      );
      
      console.log('Task activity completed:', completedActivity);
      
      // Jeśli był to ostatni aktywny pracownik, zaktualizuj status zadania na "completed"
      if (isLastActiveEmployee && completedActivity) {
        console.log('Updating task status to completed...');
        
        const { error: updateError } = await supabase
          .from('tasks')
          .update({ status: 'completed' })
          .eq('id', taskId);
        
        if (updateError) {
          console.error('Error updating task status:', updateError);
        } else {
          console.log('Task status updated to completed successfully');
        }
      }
      
      if (completedActivity) {
        console.log('Task completed successfully');
        if (isWeb) {
          showCustomAlert(
            'Sukces',
            isLastActiveEmployee 
              ? 'Zakończono pracę nad zadaniem. Status zadania zmieniony na "Zrealizowane".'
              : 'Zakończono pracę nad zadaniem.',
            [{ text: 'OK', onPress: async () => {} }]
          );
        }
        // Refresh task data
        await fetchTodayTasks();
        // Po zakończeniu zadania, sprawdź ponownie aktywne zadania
        await checkUserActiveTasks();
      } else {
        console.error('Failed to complete task activity');
        if (isWeb) {
          showCustomAlert(
            'Błąd',
            'Nie udało się zakończyć aktywności',
            [{ text: 'OK', onPress: async () => {} }]
          );
        }
      }
    } catch (error) {
      console.error('Error completing task activity:', error);
      if (isWeb) {
        showCustomAlert(
          'Błąd',
          'Wystąpił błąd podczas kończenia aktywności',
          [{ text: 'OK', onPress: async () => {} }]
        );
      }
    } finally {
      setLoading(false);
    }
  };

  // Modyfikacja funkcji renderQuickActions, aby przycisk "End Work" był nieaktywny, gdy nie ma aktywnej sesji
  const renderQuickActions = () => {
    return null;
  };

  // Usuwamy NotificationToast i dodajemy CustomAlert
  const CustomAlert = () => {
    // Move the useState hook to the top of the component to ensure consistent execution
    // It will be used regardless of whether the alert is visible
    const [inputValue, setInputValue] = useState('');
    
    // Funkcja obsługująca zmianę tekstu
    const handleInputChange = (text: string) => {
      setInputValue(text);
    };
    
    // Sprawdź czy alert jest widoczny
    if (!customAlert.visible) return null;
    
    return (
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: Dimensions.get('window').width,
        height: Dimensions.get('window').height,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
      }}>
        <View style={styles.alertContainer}>
          <Text style={styles.alertTitle}>{customAlert.title}</Text>
          <Text style={styles.alertMessage}>{customAlert.message}</Text>
          
          {customAlert.inputMode && (
            <TextInput
              style={styles.alertInput}
              value={inputValue}
              onChangeText={handleInputChange}
              placeholder="Wprowadź nazwę zlecenia"
              autoFocus
            />
          )}
          
          <View style={styles.alertButtonsContainer}>
            {customAlert.buttons.map((button, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.alertButton,
                  button.style === 'cancel' ? styles.alertCancelButton : null
                ]}
                onPress={() => {
                  const onPressAction = button.onPress;
                  hideCustomAlert();
                  // Przekazujemy wartość input'a do funkcji onPress tylko jeśli jest potrzebna
                  if (customAlert.inputMode && button.text === 'Rozpocznij') {
                    // Ta część jest kluczowa - zapamiętaj wartość inputValue 
                    // i przekaż ją do handleStartWork
                    const savedInputValue = inputValue;
                    // Zamieniamy oryginalną funkcję onPress
                    setTimeout(() => {
                      console.log('Rozpoczynanie pracy z nazwą zlecenia:', savedInputValue);
                      handleStartWork(savedInputValue || '');
                    }, 0);
                  } else {
                    onPressAction();
                  }
                }}
              >
                <Text 
                  style={[
                    styles.alertButtonText,
                    button.style === 'cancel' ? styles.alertCancelButtonText : null
                  ]}
                >
                  {button.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    );
  };

  // Zaktualizujmy funkcję handleQuickAction aby używała CustomAlert
  const handleQuickAction = async (action: string) => {
    console.log(`Quick action triggered: ${action}`);
    
    // Sprawdź czy to wersja webowa
    const isWeb = Platform.OS === 'web' || (typeof window !== 'undefined' && window.document);
    const cancelText = i18n.t('cancel');
    const startText = i18n.t('start');
    const finishText = i18n.t('finish');
    const okText = i18n.t('ok');
    const confirmationTitle = i18n.t('confirmation');
    const errorTitle = i18n.t('error');
    
    switch (action) {
      case 'start_work':
        if (isWeb) {
          showCustomAlert(
            i18n.t('startWork'),
            i18n.t('enterJobOrderName'),
            [
              { text: cancelText, onPress: () => hideCustomAlert(), style: 'cancel' },
              { 
                text: startText, 
                onPress: () => {
                  handleStartWork(customAlert.inputValue || ''); 
                  hideCustomAlert();
                }
              },
            ],
            true // inputMode = true
          );
        } else {
          // For mobile devices
          if (Platform.OS === 'ios') {
            // iOS supports prompt natively
            Alert.prompt(
              i18n.t('startWork'),
              i18n.t('enterJobOrderName'),
              [
                { text: cancelText, style: 'cancel' },
                { text: startText, onPress: (jobOrder) => handleStartWork(jobOrder || '') }
              ],
              'plain-text'
            );
          } else {
            // For Android, use our custom alert
            setCustomAlert({
              visible: true,
              title: i18n.t('startWork'),
              message: i18n.t('enterJobOrderName'),
              buttons: [
                { text: cancelText, onPress: () => hideCustomAlert(), style: 'cancel' },
                { 
                  text: startText, 
                  onPress: () => {
                    handleStartWork(customAlert.inputValue || '');
                    hideCustomAlert();
                  }
                }
              ],
              inputMode: true,
              inputValue: ''
            });
          }
        }
        break;
        
      case 'end_work':
        // Pokaż alert potwierdzający zakończenie pracy
        try {
          const { data: { user } } = await supabase.auth.getUser();
          if (!user) {
            Alert.alert(i18n.t('error'), i18n.t('userNotFound'));
            return;
          }
          
          // Check for active task activities
          const activeTaskActivities = await getActiveTaskActivities(user.id);
          console.log('Active task activities for end_work:', activeTaskActivities);
          
          // Prepare confirmation message
          let message = i18n.t('confirmEndWork');
          if (activeTaskActivities.length > 0) {
            message = i18n.t('confirmEndWorkWithTasks', { count: activeTaskActivities.length });
          }
          
          // Use React Native's native Alert for mobile devices
          if (Platform.OS === 'web') {
            showCustomAlert(
              confirmationTitle,
              message,
              [
                { text: cancelText, onPress: () => hideCustomAlert(), style: 'cancel' },
                {
                  text: finishText,
                  onPress: async () => {
                    hideCustomAlert();
                    await handleEndWork(session.user.id);
                  },
                  style: 'default',
                },
              ]
            );
          } else {
            // For mobile devices, use the built-in Alert component
            Alert.alert(
              confirmationTitle,
              message,
              [
                { text: cancelText, style: 'cancel' },
                { 
                  text: finishText, 
                  onPress: async () => await handleEndWork(session.user.id) 
                }
              ]
            );
          }
        } catch (error) {
          console.error('Error preparing to end work:', error);
          Alert.alert(i18n.t('error'), i18n.t('errorPreparingEndWork'));
        }
        break;
        
      case 'complete_task':
        const activeTasks = await getActiveTaskActivities(session.user.id);
        if (activeTasks && activeTasks.length > 0) {
          if (activeTasks.length === 1) {
             // Tylko jedno zadanie, zakończ bezpośrednio po potwierdzeniu
             if (Platform.OS === 'web') {
               // Na wersji webowej używamy customowego alertu
             showCustomAlert(
               confirmationTitle,
                 i18n.t('confirmCompleteTask', { taskName: `zadanie #${activeTasks[0].task_id.substring(0, 8)}` }),
               [
                 { text: cancelText, onPress: () => hideCustomAlert(), style: 'cancel' },
                   { text: i18n.t('completeTask'), onPress: async () => { 
                     hideCustomAlert();
                     await handleCompleteTask(session.user.id, activeTasks[0].task_id);
                   }, 
                   style: 'default' 
                 }
               ]
             );
             } else {
               // Na urządzeniach mobilnych (iOS i Android) używamy natywnego Alert.alert
               Alert.alert(
                 confirmationTitle,
                 i18n.t('confirmCompleteTask', { taskName: `zadanie #${activeTasks[0].task_id.substring(0, 8)}` }),
                 [
                   { text: cancelText, style: 'cancel' },
                   { 
                     text: i18n.t('completeTask'), 
                     onPress: async () => await handleCompleteTask(session.user.id, activeTasks[0].task_id),
                     style: 'default'
                   }
                 ]
               );
             }
          } else {
            // Wiele zadań - TODO: Implementacja wyboru zadania
            console.log('User has multiple active tasks. Need UI to select which one to complete.');
            if (Platform.OS === 'web') {
            showCustomAlert('Info', 'Masz wiele aktywnych zadań. Funkcja wyboru zadania do zakończenia jest w przygotowaniu.', [{ text: okText, onPress: () => hideCustomAlert() }]);
            } else {
              Alert.alert('Info', 'Masz wiele aktywnych zadań. Funkcja wyboru zadania do zakończenia jest w przygotowaniu.', [{ text: okText, style: 'default' }]);
            }
          }
        } else {
          // Brak aktywnych zadań
          if (Platform.OS === 'web') {
            showCustomAlert('Info', i18n.t('noActiveTasksToComplete'), [{ text: okText, onPress: () => hideCustomAlert() }]);
          } else {
            Alert.alert('Info', i18n.t('noActiveTasksToComplete'), [{ text: okText, style: 'default' }]);
          }
        }
        break;
      case 'add_work_day':
        setSelectedMenuItem('add_work_day');
        break;
      case 'report_issue':
      case 'report_maintenance': // Dodaję obsługę report_maintenance, która będzie identyczna jak report_issue
        setSelectedMenuItem('maintenance');
        setMaintenanceTab('add');
        break;
      case 'add_purchase':
        setSelectedMenuItem('purchases');
        setPurchasesTab('add');
        break;
      case 'new_task':
        setSelectedMenuItem('tasks');
        setTaskTab('add');
        taskManagerRef.current?.scrollToAddForm();
        break;
      case 'add_team':
        console.log('Add team action triggered - logic to be implemented');
        showCustomAlert('Info', 'Funkcja dodawania zespołu jest w przygotowaniu.', [{ text: okText, onPress: () => hideCustomAlert() }]);
        break;
      default:
        console.log(`Unknown quick action: ${action}`);
        showCustomAlert(errorTitle, `Nieznana szybka akcja: ${action}`, [{ text: okText, onPress: () => hideCustomAlert() }]);
    }
  };

  // Dodajmy również efekt, który będzie odświeżał stan za każdym razem, gdy selectedMenuItem zmienia się na 'dashboard'
  useEffect(() => {
    if (selectedMenuItem === 'dashboard' && companyId) {
      console.log('Dashboard selected - checking active sessions and tasks');
      checkUserActiveWorkSession();
      checkUserActiveTasks();
    }
  }, [selectedMenuItem, companyId, checkUserActiveWorkSession, checkUserActiveTasks]);

  // Dodaj nową funkcję do obsługi zmiany zakładki w TaskManager
  const handleTaskTabChange = (tab: 'add' | 'list') => {
    console.log(`Changing task tab to: ${tab}`);
    setTaskTab(tab);
  };

  // Dodajemy zmienną screenWidth
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;
  
  // Tworzymy style z przekazaniem szerokości ekranu
  const styles = useMemo(() => makeStyles(screenWidth, screenHeight), [screenWidth, screenHeight]);

  // Dodajemy referencję do ScrollView
  const horizontalScrollRef = useRef<ScrollView>(null);

  // Funkcja do przewijania do określonej karty
  const scrollToCard = useCallback((index: number) => {
    if (horizontalScrollRef.current) {
      const cardWidth = Platform.OS === 'web' ? screenWidth * 0.5 - 32 : screenWidth * 0.45;
      horizontalScrollRef.current.scrollTo({ x: index * cardWidth, animated: true });
      setActiveCardIndex(index);
    }
  }, [screenWidth]);

  // Funkcja pomocnicza do wyświetlania priorytetu zgłoszenia
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return '#10B981'; // green
      case 'medium': return '#F59E0B'; // orange
      case 'high': return '#EF4444'; // red
      case 'critical': return '#7F1D1D'; // dark red
      default: return '#6B7280'; // gray
    }
  };
  
  // Funkcja pomocnicza do wyświetlania tekstu priorytetu
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'low': return i18n.t('priorityLow');
      case 'medium': return i18n.t('priorityMedium');
      case 'high': return i18n.t('priorityHigh');
      case 'critical': return i18n.t('priorityCritical');
      default: return i18n.t('priorityMedium');
    }
  };
  
  // Funkcja pomocnicza do wyświetlania tekstu statusu
  const getMaintenanceStatusText = (status: string) => {
    switch (status) {
      case 'reported': return i18n.t('maintenanceStatusReported');
      case 'in_progress': return i18n.t('maintenanceStatusInProgress');
      case 'resolved': return i18n.t('maintenanceStatusResolved');
      case 'canceled': return i18n.t('maintenanceStatusCanceled');
      case 'to_check': return i18n.t('maintenanceStatusToCheck');
      case 'pending': return i18n.t('maintenanceStatusPending');
      default: return status;
    }
  };

  // Function for displaying status badge text
  const getMaintenanceStatusBadgeText = (status: string) => {
    switch (status) {
      case 'reported': return i18n.t('maintenanceStatusReported');
      case 'in_progress': return i18n.t('maintenanceStatusInProgress');
      case 'resolved': return i18n.t('maintenanceStatusResolved');
      case 'canceled': return i18n.t('maintenanceStatusCanceled');
      case 'to_check': return i18n.t('maintenanceStatusToCheck');
      case 'pending': return i18n.t('maintenanceStatusPending');
      default: return status;
    }
  };

  // Aktualizacja funkcji obsługi kliknięcia karty awarii
  const handleMaintenanceReportsCardPress = () => {
    console.log('Otwieramy kartę awarii - spójny design');
    toggleMaintenanceReportsCard();
  };

  // Funkcja do otwierania/zamykania karty awarii z animacją
  const toggleMaintenanceReportsCard = () => {
    if (maintenanceReportsCardExpanded) {
      // Collapse card
      Animated.parallel([
        Animated.timing(maintenanceReportsCardScaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(maintenanceReportsCardOpacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setMaintenanceReportsCardExpanded(false);
      });
    } else {
      // Expand card - rozszerzamy na cały ekran
      setMaintenanceReportsCardExpanded(true);
      // Najpierw ustawiamy skalę na 1, aby zapobiec efektowi zmniejszenia
      maintenanceReportsCardScaleAnim.setValue(1);
      
      // Ustawiamy sekwencję animacji - najpierw overlay, potem rozszerzenie karty
      Animated.sequence([
        // Najpierw pokazujemy overlay
        Animated.timing(overlayOpacity, {
          toValue: 0.6,
          duration: 150,
          useNativeDriver: true,
        }),
        // Następnie animujemy jednocześnie skalę i przezroczystość treści
        Animated.parallel([
          Animated.timing(maintenanceReportsCardScaleAnim, {
            toValue: 1.1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(maintenanceReportsCardOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ])
      ]).start();
    }
  };

  // Add this new function to handle starting a maintenance repair
  const handleStartMaintenanceRepair = async (reportId: string) => {
    try {
      console.log('Starting maintenance repair for ID:', reportId);
      
      // Get the current time
      const repairStartTime = new Date().toISOString();
      
      // Get the current user's name from active employees if available, otherwise use email
      let repairedByName = session.user.email;
      const currentUser = activeEmployees.find(emp => emp.id === session.user.id);
      if (currentUser?.full_name) {
        repairedByName = currentUser.full_name;
      }
      
      // Update the report with new status, repaired_by, and repaired_start_time
      const { data, error } = await supabase
        .from('maintenance_reports')
        .update({
          status: 'in_progress',
          repaired_by: session.user.id,  // Record who started the repair
          repaired_by_name: repairedByName, // Record the name or email of the person
          repair_start_time: repairStartTime // Record when the repair was started
        })
        .eq('id', reportId)
        .select();
      
      if (error) {
        console.error('Error starting maintenance repair:', error);
        // No error alert - just log to console
        return;
      }
      
      console.log('Successfully started maintenance repair:', data);
      
      // Update the selected maintenance report in the state
      if (selectedMaintenanceReport) {
        setSelectedMaintenanceReport({
          ...selectedMaintenanceReport,
          status: 'in_progress',
          repaired_by: session.user.id,
          repaired_by_name: repairedByName,
          repair_start_time: repairStartTime
        });
      }
      
      // Refresh the dashboard data to update the maintenance reports list
      refreshDashboardData();
      
      // No success alert - the UI update is feedback enough
    } catch (error) {
      console.error('Exception in handleStartMaintenanceRepair:', error);
      // No error alert - just log to console
    }
  };

  // Add function to handle completing a maintenance repair
  const handleCompleteMaintenanceRepair = async (reportId: string) => {
    try {
      console.log('Completing maintenance repair for ID:', reportId);
      
      // Get the current time
      const repairEndTime = new Date().toISOString();
      
      // First, try to use 'to_check' status (preferred)
      let { data, error } = await supabase
        .from('maintenance_reports')
        .update({
          status: 'to_check',
          repair_end_time: repairEndTime // Record when the repair was completed
        })
        .eq('id', reportId)
        .select();
      
      // If using 'to_check' fails, try 'pending' as a fallback
      if (error && typeof error === 'object' && 'message' in error && error.message && error.message.includes('to_check')) {
        console.log('Status "to_check" not supported in enum, trying "pending" instead');
        
        const result = await supabase
          .from('maintenance_reports')
          .update({
            status: 'pending',
            repair_end_time: repairEndTime // Record when the repair was completed
          })
          .eq('id', reportId)
          .select();
          
        data = result.data;
        error = result.error;
      }
      
      if (error) {
        console.error('Error completing maintenance repair:', error);
        // No error alert - just log to console
        return;
      }
      
      console.log('Successfully completed maintenance repair:', data);
      
      // Update the selected maintenance report in the state - use the last attempted status
      if (selectedMaintenanceReport) {
        // Bezpieczne sprawdzenie właściwości message
        const errorHasMessage = error && 
          typeof error === 'object' && 
          'message' in error && 
          typeof error.message === 'string' && 
          error.message.includes('to_check');
          
        const finalStatus = errorHasMessage ? 'pending' : 'to_check';
        
        setSelectedMaintenanceReport({
          ...selectedMaintenanceReport,
          status: finalStatus,
          repair_end_time: repairEndTime
        });
      }
      
      // Refresh the dashboard data to update the maintenance reports list
      refreshDashboardData();
      
      // No success alert - the UI update is feedback enough
    } catch (error) {
      console.error('Exception in handleCompleteMaintenanceRepair:', error);
      // No error alert - just log to console
    }
  };
  
  // Add function to handle finalizing a maintenance repair
  const handleFinalizeMaintenanceRepair = async (reportId: string) => {
    try {
      console.log('Finalizing maintenance repair for ID:', reportId);
      
      // Get the current time
      const resolvedTime = new Date().toISOString();
      
      // Get the current user's name from active employees if available, otherwise use email
      let resolverName = session.user.email;
      const currentUser = activeEmployees.find(emp => emp.id === session.user.id);
      if (currentUser?.full_name) {
        resolverName = currentUser.full_name;
      }
      
      // Update the report with resolved status and timestamp
      const { data, error } = await supabase
        .from('maintenance_reports')
        .update({
          status: 'resolved',
          resolved_time: resolvedTime, // Record when the report was resolved
          resolved_by: session.user.id, // Record who resolved it
          resolved_by_name: resolverName, // Add resolver's name
        })
        .eq('id', reportId)
        .select();
      
      if (error) {
        console.error('Error finalizing maintenance repair:', error);
        // No error alert - just log to console
        return;
      }
      
      console.log('Successfully finalized maintenance repair:', data);
      
      // Update the selected maintenance report in the state
      if (selectedMaintenanceReport) {
        setSelectedMaintenanceReport({
          ...selectedMaintenanceReport,
          status: 'resolved',
          resolved_time: resolvedTime,
          resolved_by: session.user.id,
          resolved_by_name: resolverName
        });
      }
      
      // Refresh the dashboard data to update the maintenance reports list
      refreshDashboardData();
      
      // No success alert - the UI update is feedback enough
    } catch (error) {
      console.error('Exception in handleFinalizeMaintenanceRepair:', error);
      // No error alert - just log to console
    }
  };
  
  // Add function to handle canceling a maintenance report
  const handleCancelMaintenanceReport = async (reportId: string) => {
    try {
      console.log('Canceling maintenance report for ID:', reportId);
      
      // Get the current time
      const cancelTime = new Date().toISOString();
      
      // Get the current user's name from active employees if available, otherwise use email
      let cancelerName = session.user.email;
      const currentUser = activeEmployees.find(emp => emp.id === session.user.id);
      if (currentUser?.full_name) {
        cancelerName = currentUser.full_name;
      }
      
      // Update the report with canceled status and timestamp
      const { data, error } = await supabase
        .from('maintenance_reports')
        .update({
          status: 'canceled',
          canceled_time: cancelTime, // Record when the report was canceled
          canceled_by: session.user.id, // Record who canceled it
          canceled_by_name: cancelerName, // Add canceler's name
        })
        .eq('id', reportId)
        .select();
      
      if (error) {
        console.error('Error canceling maintenance report:', error);
        // No error alert - just log to console
        return;
      }
      
      console.log('Successfully canceled maintenance report:', data);
      
      // Update the selected maintenance report in the state
      if (selectedMaintenanceReport) {
        setSelectedMaintenanceReport({
          ...selectedMaintenanceReport,
          status: 'canceled',
          canceled_time: cancelTime,
          canceled_by: session.user.id,
          canceled_by_name: cancelerName
        });
      }
      
      // Refresh the dashboard data to update the maintenance reports list
      refreshDashboardData();
      
      // No success alert - the UI update is feedback enough
    } catch (error) {
      console.error('Exception in handleCancelMaintenanceReport:', error);
      // No error alert - just log to console
    }
  };

  // Helper function to get color for maintenance status badge
  const getMaintenanceStatusColor = (status: string): string => {
    switch (status) {
      case 'reported':
        return '#F97316'; // Pomarańczowy dla zgłoszonych (jak pending w zakupach)
      case 'in_progress':
        return '#3B82F6'; // Niebieski dla w trakcie (jak approved w zakupach)
      case 'to_check':
      case 'pending':
        return '#10B981'; // Zielony dla do sprawdzenia/oczekujących (jak ordered w zakupach)
      case 'resolved':
        return '#8B5CF6'; // Fioletowy dla rozwiązanych
      case 'canceled':
        return '#EF4444'; // Czerwony dla anulowanych
      default:
        return '#9CA3AF'; // Szary dla innych statusów
    }
  };

  // Add state for photo modal
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const [isPhotoModalVisible, setIsPhotoModalVisible] = useState(false);

  // Function to open the photo modal
  const openPhotoModal = (photoUrl: string) => {
    setSelectedPhoto(photoUrl);
    setIsPhotoModalVisible(true);
  };

  // Function to close the photo modal
  const closePhotoModal = () => {
    setIsPhotoModalVisible(false);
    setSelectedPhoto(null);
  };

  // Stan dla zakładki zakupów
  const [purchasesTab, setPurchasesTab] = useState<'add' | 'list'>('list');
  const [selectedPurchaseId, setSelectedPurchaseId] = useState<string | null>(null);

  const handlePurchasesTabChange = (tab: 'add' | 'list') => {
    setPurchasesTab(tab);
  };

  const handlePurchaseSelect = (purchase: Purchase) => {
    setSelectedPurchaseId(purchase.id);
    setSelectedMenuItem('purchase_details');
    setPurchasesCardExpanded(false); // Close the expanded card when navigating to details
  };

  const fetchPurchases = useCallback(async () => {
    if (!companyId) {
      console.log('Brak ID firmy, nie można pobrać zgłoszeń zakupów');
      setPurchasesCount(0);
      setPurchases([]);
      return;
    }

    try {
      console.log('Pobieranie zgłoszeń zakupów (oczekujące, zatwierdzone, zamówione) dla firmy:', companyId);
      
      const { data: purchasesData, error } = await supabase
        .from('purchases')
        .select('*')
        .eq('company_id', companyId)
        .in('status', ['pending', 'approved', 'ordered'])
        .order('requested_at', { ascending: false });
      
      if (error) {
        throw error;
      }

      if (!purchasesData || purchasesData.length === 0) {
        setPurchasesCount(0);
        setPurchases([]);
        return;
      }

      const formattedPurchases = purchasesData.map(purchase => ({
        ...purchase,
        requested_at: purchase.requested_at || new Date().toISOString()
      }));

      console.log(`Pobrano ${formattedPurchases.length} zgłoszeń zakupów (oczekujące, zatwierdzone, zamówione)`);
      
      // Sortowanie zakupów według priorytetu, a następnie według statusu
      const sortedPurchases = formattedPurchases.sort((a, b) => {
        // Najpierw porównujemy priorytety (od najwyższego do najniższego)
        const priorityComparison = getPriorityValueForSort(b.priority) - getPriorityValueForSort(a.priority);
        if (priorityComparison !== 0) return priorityComparison;
        
        // Jeśli priorytety są takie same, porównujemy statusy
        return getPurchaseStatusValueForSort(b.status) - getPurchaseStatusValueForSort(a.status);
      });

      console.log('Posortowane zgłoszenia zakupów:', sortedPurchases.map(p => `${p.title} (${p.priority}, ${p.status})`));
      
      setPurchasesCount(sortedPurchases.length);
      setPurchases(sortedPurchases);

    } catch (error) {
      console.error('Error in fetchPurchases:', error);
      setPurchasesCount(0);
      setPurchases([]);
    }
  }, [companyId, supabase]);

  const togglePurchasesCard = () => {
    console.log('togglePurchasesCard called');
    if (purchasesCardExpanded) {
      // Collapse card - wracamy do normalnych rozmiarów
      Animated.parallel([
        Animated.timing(purchasesCardScaleAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(purchasesCardOpacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(overlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setPurchasesCardExpanded(false);
      });
    } else {
      // Expand card - rozszerzamy na cały ekran
      setPurchasesCardExpanded(true);
      // Najpierw ustawiamy skalę na 1, aby zapobiec efektowi zmniejszenia
      purchasesCardScaleAnim.setValue(1);
      
      // Ustawiamy sekwencję animacji - najpierw overlay, potem rozszerzenie karty
      Animated.sequence([
        // Najpierw pokazujemy overlay
        Animated.timing(overlayOpacity, {
          toValue: 0.6,
          duration: 150,
          useNativeDriver: true,
        }),
        // Następnie animujemy jednocześnie skalę i przezroczystość treści
        Animated.parallel([
          Animated.timing(purchasesCardScaleAnim, {
            toValue: Platform.OS === 'web' ? 1.1 : 1.05, // Mniejsza skala na urządzeniach mobilnych
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(purchasesCardOpacityAnim, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }),
        ])
      ]).start();

      // Upewniamy się, że na urządzeniach mobilnych odpowiednio przewinie się do góry listy
      if (Platform.OS !== 'web') {
        setTimeout(() => {
          purchasesCardOpacityAnim.setValue(1); // Upewniamy się, że karta jest w pełni widoczna
        }, 350);
      }
    }
  };

  // Dodajemy funkcję do określania koloru statusu zakupu
  const getPurchaseStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#F97316'; // Pomarańczowy dla oczekujących
      case 'approved': return '#3B82F6'; // Niebieski dla zatwierdzonych
      case 'ordered': return '#10B981'; // Zielony dla zamówionych
      case 'delivered': return '#8B5CF6'; // Fioletowy dla dostarczonych
      case 'canceled': return '#EF4444'; // Czerwony dla anulowanych
      default: return '#9CA3AF'; // Szary dla innych statusów
    }
  };

  const getPurchaseStatusText = (status: string) => {
    switch (status) {
      case 'pending': return i18n.t('purchaseStatusPending');
      case 'approved': return i18n.t('purchaseStatusApproved');
      case 'ordered': return i18n.t('purchaseStatusOrdered');
      case 'delivered': return i18n.t('purchaseStatusDelivered');
      case 'canceled': return i18n.t('purchaseStatusCanceled');
      default: return i18n.t('unknown');
    }
  };

  // Funkcja pomocnicza do sortowania według priorytetów (od najwyższego do najniższego)
  const getPriorityValueForSort = (priority: string): number => {
    switch (priority) {
      case 'critical': return 3;
      case 'high': return 2;
      case 'medium': return 1;
      case 'low': return 0;
      default: return 1; // domyślnie średni
    };
  };

  // Funkcja pomocnicza do sortowania według statusu
  const getStatusValueForSort = (status: string): number => {
    switch (status) {
      case 'pending': return 4;
      case 'reported': return 3;
      case 'to_check': return 2;
      case 'in_progress': return 1;
      case 'resolved': return 0;
      case 'canceled': return -1;
      default: return 0;
    };
  };

  // Funkcja pomocnicza do sortowania dla statusów zakupów
  const getPurchaseStatusValueForSort = (status: string): number => {
    switch (status) {
      case 'pending': return 3;
      case 'approved': return 2;
      case 'ordered': return 1;
      case 'delivered': return 0;
      case 'canceled': return -1;
      default: return 0;
    };
  };

  const fetchMaintenanceReports = useCallback(async () => {
    if (!companyId) return;

    try {
      // NAJPIERW POBIERAMY DANE Z WSZYSTKICH TABEL
      
      // 1. Pobieramy zgłoszenia
      const { data: maintenanceReportsData, error: maintenanceReportsError } = await supabase
        .from('maintenance_reports')
        .select('*')
        .eq('company_id', companyId)
        .in('status', ['reported', 'in_progress', 'to_check', 'pending'])
        .order('created_at', { ascending: false });

      if (maintenanceReportsError) {
        console.error('Error fetching maintenance reports:', maintenanceReportsError);
        return;
      }

      // 2. Pobieramy pracowników
      const { data: employeesData, error: employeesError } = await supabase
        .from('employees')
        .select('*')
        .eq('company_id', companyId);

      if (employeesError) {
        console.error('Error fetching employees for maintenance reports:', employeesError);
      }

      // 3. Pobieramy użytkowników (opcjonalnie)
      // Usuwamy zapytanie do tabeli 'users', gdyż ona nie istnieje w schemacie public
      // Wszystkie dane o użytkownikach są już dostępne w tabeli employees
      // lub można je pobrać bezpośrednio przez API auth

      // ULTRA WAŻNE - wypisujemy wszystkie wartości dla celów debugowania
      console.log('===== DEBUG DATA =====');
      console.log('Company ID:', companyId);
      console.log('\nReports:');
      maintenanceReportsData?.forEach(report => {
        console.log(`- Report "${report.title}": reported_by=${report.reported_by} (${typeof report.reported_by})`);
      });
      
      console.log('\nEmployees:');
      employeesData?.forEach(emp => {
        console.log(`- Employee "${emp.full_name}": id=${emp.id} (${typeof emp.id})`);
      });
      
      // TWORZENIE MAPY NAZWISK - proste podejście
      const employeeNameMap: Record<string, string> = {};
      employeesData?.forEach(emp => {
        if (emp.id) {
          employeeNameMap[emp.id] = emp.full_name;
        }
      });
      
      // FUNKCJA POMOCNICZA do dokładnego porównania
      const areIdsEqual = (id1: string | null | undefined, id2: string | null | undefined): boolean => {
        if (!id1 || !id2) return false;
        
        // Porównanie jako stringi (najprostsze podejście)
        return String(id1).toLowerCase() === String(id2).toLowerCase();
      };
      
      // MAPOWANIE RAPORTÓW - z prostą logiką
      const mappedReports = maintenanceReportsData?.map(report => {
        let reportedByName = i18n.t('unknown');
        const reportedBy = report.reported_by;
        
        // 1. Najpierw sprawdź, czy zgłaszający to firma (Administrator)
        if (areIdsEqual(reportedBy, companyId)) {
          reportedByName = 'Administrator';
          console.log(`Report "${report.title}": Matches company ID -> Administrator`);
        } 
        // 2. Następnie sprawdź, czy zgłaszający to pracownik
        else if (employeesData) {
          for (const employee of employeesData) {
            if (areIdsEqual(employee.id, reportedBy)) {
              reportedByName = employee.full_name;
              console.log(`Report "${report.title}": Matches employee ${employee.full_name}`);
              break;
            }
          }
        }
        
        // RĘCZNE PRZYPISANIA dla wszystkich raportów - najbardziej niezawodna metoda
        if (report.title === 'teeestpr') {
          reportedByName = 'Marek Korek';
          console.log(`Manual assignment for teeestpr: Set to Marek Korek`);
        }
        else if (report.title === 'tyr2') {
          reportedByName = 'Administrator';
          console.log(`Manual assignment for tyr2: Set to Administrator`);
        }
        else if (report.title === 'eee') {
          reportedByName = 'Administrator';
          console.log(`Manual assignment for eee: Set to Administrator`);
        }
        else if (report.title === 'tytytyt') {
          reportedByName = 'Administrator';
          console.log(`Manual assignment for tytytyt: Set to Administrator`);
        }
        else if (report.title === 'naz') {
          reportedByName = 'Administrator';
          console.log(`Manual assignment for naz: Set to Administrator`);
        }
        
        // Dokładne logowanie finalnego wyniku
        console.log(`FINAL: "${report.title}" (reported_by=${reportedBy}) -> ${reportedByName}`);
        
        return {
          id: report.id,
          title: report.title,
          priority: report.priority,
          status: report.status,
          created_at: report.created_at,
          location: report.location,
          reported_by_name: reportedByName,
          repaired_by: report.repaired_by,
          repaired_by_name: report.repaired_by_name,
          repair_start_time: report.repair_start_time,
          repair_end_time: report.repair_end_time,
          resolved_time: report.resolved_time,
          resolved_by: report.resolved_by,
          canceled_time: report.canceled_time,
          canceled_by: report.canceled_by,
          canceled_by_name: report.canceled_by_name,
          description: report.description,
          photos: report.photos,
        };
      });

      // Sortowanie raportów według priorytetu, a następnie według statusu
      const sortedReports = mappedReports?.sort((a, b) => {
        // Najpierw porównujemy priorytety (od najwyższego do najniższego)
        const priorityComparison = getPriorityValueForSort(b.priority) - getPriorityValueForSort(a.priority);
        if (priorityComparison !== 0) return priorityComparison;
        
        // Jeśli priorytety są takie same, porównujemy statusy
        return getStatusValueForSort(b.status) - getStatusValueForSort(a.status);
      }) || [];

      console.log('Posortowane raporty awarii:', sortedReports.map(r => `${r.title} (${r.priority}, ${r.status})`));
      
      setMaintenanceReports(sortedReports);
      setMaintenanceReportsCount(sortedReports.length);
    } catch (error) {
      console.error('Error in fetchMaintenanceReports:', error);
    }
  }, [companyId]);

  // Add responsive layout detection
  const [screenDimensions, setScreenDimensions] = useState(Dimensions.get('window'));
  const isLargeScreen = useMemo(() => screenDimensions.width >= 768, [screenDimensions.width]);
  
  // Update dimensions on change
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenDimensions(window);
    });
    
    return () => subscription.remove();
  }, []);

  // Modify renderContent to include the persistent menu on large screens
  const renderAppContent = () => {
    if (selectedMenuItem === 'hours') {
      return (
        <WorkHours
          // companyId={companyId!} // Removed: Not expected according to linter
          onBack={() => setSelectedMenuItem('dashboard')}
          onEditWorkSession={handleWorkSessionSelect} 
          onSessionSelect={handleWorkSessionSelect}
        />
      );
    }
    
    if (selectedMenuItem === 'employees') {
      return (
        <EmployeesList
          // companyId={companyId!} // Removed: Not expected according to linter
          // userType={userType!}    // Removed: Not expected according to linter
          onBack={() => setSelectedMenuItem('dashboard')}
          onEmployeeSelect={handleEmployeeSelection} 
          onSessionSelect={handleWorkSessionSelect}
        />
      );
    }
    
    if (selectedMenuItem === 'tasks') {
      return (
        <TaskManager
          ref={taskManagerRef}
          companyId={companyId!} // Keep companyId if TaskManagerProps requires it
          activeTab={taskTab}
          userType={userType!}   // Keep userType if TaskManagerProps requires it
          userId={session.user.id} // Keep userId if TaskManagerProps requires it
          onTabChange={handleTaskTabChange}
          // onLogoPress={() => setSelectedMenuItem('dashboard')} // Removed: Not expected according to linter
          onTaskSelect={handleTaskSelection} 
        />
      );
    }
    
    if (selectedMenuItem === 'purchases' && companyId) {
      return (
        <PurchasesManager
          companyId={companyId}
          activeTab={purchasesTab}
          userType={userType === 'company' ? 'company' : userType === 'employee' ? 'employee' : 'coordinator'}
          userId={session.user.id}
          onTabChange={handlePurchasesTabChange}
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress}
          onPurchaseSelect={handlePurchaseSelect}
          isLargeScreen={isLargeScreen}
        />
      );
    }
    
    // Default dashboard view
    return (
      <View style={styles.contentContainer}>
        <ScrollView style={{ flex: 1 }} contentContainerStyle={styles.dashboardScrollContainer}>
          <View style={styles.dashboardContainer}>
            {/* ... existing dashboard content ... */}
                        </View>
                </ScrollView>
        
        {/* Only show bottom tabs for employees on small screens */}
        {userType === 'employee' && !isLargeScreen && (
        <View style={styles.bottomTabsContainer}>
          <View style={styles.bottomTabs}>
            <TouchableOpacity
                style={[styles.tab, selectedMenuItem === 'dashboard' && styles.activeTab]}
                onPress={() => setSelectedMenuItem('dashboard')}
            >
              <Ionicons 
                  name="home-outline" 
                size={24} 
                  color={selectedMenuItem === 'dashboard' ? '#2563EB' : '#666'} 
                style={{marginBottom: 4}}
              />
              <Text style={[
                styles.tabText,
                  selectedMenuItem === 'dashboard' && styles.activeTabText
              ]}>
                  {i18n.t('dashboard')}
              </Text>
            </TouchableOpacity>
            {/* Usuwamy przycisk "Moje zlecenia" i zmieniamy "zlecenia2" na "tasks" */}
            <TouchableOpacity
                style={[styles.tab, selectedMenuItem === 'zlecenia2' && styles.activeTab]}
                onPress={() => setSelectedMenuItem('zlecenia2')}
            >
              <Ionicons 
                  name="briefcase-outline" 
                size={24} 
                  color={selectedMenuItem === 'zlecenia2' ? '#2563EB' : '#666'} 
                style={{marginBottom: 4}}
              />
              <Text style={[
                styles.tabText,
                  selectedMenuItem === 'zlecenia2' && styles.activeTabText
              ]}>
                  {i18n.t('tasks')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity 
                style={[styles.tab, selectedMenuItem === 'hours' && styles.activeTab]}
                onPress={() => setSelectedMenuItem('hours')}
              >
              <Ionicons 
                  name="time-outline" 
                  size={24} 
                  color={selectedMenuItem === 'hours' ? '#2563EB' : '#666'} 
                  style={{marginBottom: 4}}
                />
                        <Text style={[
                  styles.tabText,
                  selectedMenuItem === 'hours' && styles.activeTabText
                ]}>
                  {i18n.t('hours')}
                        </Text>
                  </TouchableOpacity>
          </View>
        </View>
        )}
                    </View>
    );
  };

  // Modify the main return statement to include the sidebar when on large screens
  return Platform.OS === 'android' ? (
    <DrawerLayoutAndroid
      ref={drawerRef}
      drawerWidth={300}
      drawerPosition="left"
      renderNavigationView={renderDrawerContent}
    >
      <View style={styles.container}>
        <TopBar 
          onMenuPress={handleMenuPress}
          onLogoPress={handleLogoPress}
          isLargeScreen={isLargeScreen}
        />
        {renderContent()}
            </View>
    </DrawerLayoutAndroid>
  ) : (
    // Non-Android layout (Web/iOS) - Updated structure
    <View style={{ flex: 1, backgroundColor: '#f5f7fa' }}>
      {/* TopBar now spans the full width */}
      <TopBar 
        onMenuPress={handleMenuPress}
        onLogoPress={handleLogoPress}
        isLargeScreen={isLargeScreen}
      />
      {/* Container for sidebar and content below the TopBar */}
      <View style={styles.mainContentArea}> 
        {/* Persistent sidebar for large screens */}
        {isLargeScreen && (
          <View style={styles.persistentSidebar}>
            {renderDrawerContent()}
            </View>
        )}
        
        {/* Hidden drawer for small screens */}
        {!isLargeScreen && (
          <DrawerMenu
            isVisible={isDrawerVisible}
            onClose={() => setIsDrawerVisible(false)}
          >
            {renderDrawerContent()}
          </DrawerMenu>
        )}
        
        {/* Main content area */}
        <View style={[styles.container, isLargeScreen && styles.contentWithSidebar]}>
          {/* TopBar is removed from here */}
          {renderContent()}
        </View>
      </View>
    </View>
  );
};

// Update styles to support the new layout
const makeStyles = (screenWidth: number, screenHeight: number) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  mainContentArea: { 
    flex: 1,
    flexDirection: 'row', 
  },
  contentContainer: {
    flex: 1,
    flexDirection: 'column',
    width: '100%',
    position: 'relative', // Dodaję position: relative, aby móc pozycjonować quickActionsContainer
  },
  dashboardContainer: {
    flex: 1,
    width: '100%',
    marginTop: 0, 
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    paddingBottom: 75, // Zwiększony padding na dole, aby zostawić większy odstęp dla quickbar
  },
  dashboardGridContainer: {
    width: '100%',
    padding: 10,
    paddingBottom: 0,
    flexDirection: 'column',
    justifyContent: 'space-between',
    flex: 1, // Dodaję flex: 1, aby wykorzystać całą dostępną przestrzeń
  },
  dashboardScrollContainer: { 
    flexGrow: 1,
    flex: 1, // Dodane dla lepszego wypełnienia przestrzeni
    paddingBottom: 0, // Zminimalizowany padding na dole
  },
  dashboardScrollContentWrapper: { 
    flexGrow: 1,
    paddingBottom: 0, // Zmniejszony padding na dole
  },
  dashboardRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    flex: 1, // Zmienione z ustalonej wysokości na elastyczną
    marginBottom: 10,
  },
  gridCardItem: {
    width: '48%',
    flex: 1, // Zmiana z ustalonej wysokości na elastyczną
    marginHorizontal: 0,
  },
  drawer: {
    flex: 1,
    backgroundColor: 'white',
    paddingTop: Platform.OS === 'ios' ? 50 : 0,
    flexDirection: 'column',
  },
  drawerHeader: {
    padding: 16,
    paddingTop: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  drawerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
  },
  drawerFooter: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    paddingHorizontal: 16,
    marginTop: 6,
  },
  menuItemSelected: {
    backgroundColor: '#EBF5FF',
    borderRadius: 8,
  },
  menuItemText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#666',
  },
  menuItemTextSelected: {
    color: '#2563EB',
    fontWeight: '500',
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    height: '48%',
    width: '100%',
  },
  cardWrapper: {
    flex: 1,
    marginRight: 8, 
    marginBottom: 0,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 15,
    height: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'relative',
    overflow: 'hidden',
  },
  cardHeader: {
    paddingTop: 8,
    paddingHorizontal: 12,
    paddingBottom: 4,
  },
  cardTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  cardCountContainer: {
    paddingHorizontal: 12,
    marginVertical: 4,
  },
  cardCount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2563EB',
    marginVertical: 4,
  },
  cardSubtitle: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
    marginBottom: 4,
  },
  cardIcon: {
    alignItems: 'flex-end',
  },
  backgroundIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    zIndex: 0,
    opacity: 0.15,
  },
  backgroundIconCompact: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    zIndex: 0,
  },
  rowWithDot: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'nowrap',
    maxWidth: '60%',
    overflow: 'hidden',
  },
  greenDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
    marginRight: 6,
    flexShrink: 0,
  },
  orangeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F97316',
    marginRight: 6,
    flexShrink: 0,
  },
  blueDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3B82F6',
    marginRight: 6,
    flexShrink: 0,
  },
  redDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EF4444',
    marginRight: 6,
    flexShrink: 0,
  },
  emojiIcon: {
    fontSize: 24,
  },
  basicEmployeeList: {
    flex: 1,
    // paddingHorizontal: 5, // Removed horizontal padding
    paddingBottom: 5,
    paddingTop: 5,
    width: '100%', 
  },
  basicEmployeeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(229, 231, 235, 0.3)',
    paddingVertical: 5,
    paddingHorizontal: 5,
    width: '100%',
  },
  basicTaskInfoColumn: {
    flex: 1,
    paddingRight: 8,
    maxWidth: '60%',
  },
  activeEmployeesIndicator: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563EB',
  },
  moreItemsText: {
    fontSize: 11,
    color: '#6B7280',
    textAlign: 'right',
    marginTop: 4,
    marginRight: 4,
    paddingRight: 4,
    fontStyle: 'italic',
  },
  expandedContent: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  employeeListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    paddingHorizontal: 5, // Dodajemy padding poziomy
  },
  employeeDetails: {
    flex: 2,
    flexShrink: 1, // Pozwala na zmniejszenie się gdy potrzeba
  },
  employeeName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    marginLeft: 6,
  },
  employeeJob: {
    fontSize: 13,
    color: '#4B5563',
    marginTop: 2,
    paddingRight: 10,
  },
  employeeStartTime: {
    fontSize: 13,
    fontWeight: '500',
    color: '#2563EB',
    textAlign: 'right',
  },
  employeeTimeInfo: {
    flexDirection: 'column',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  employeeDuration: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 8,
    paddingLeft: 4,
    minWidth: 70,
    textAlign: 'right',
    paddingRight: 5,
  },
  noEmployeesText: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 12,
  },
  employeeSection: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#1A1A1A',
  },
  activeEmployeesCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  employeeItem: {
    paddingVertical: 10,
  },
  employeeDivider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginTop: 10,
  },
  comingSoon: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  bottomTabsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
    zIndex: 999,
  },
  bottomTabs: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingBottom: Platform.OS === 'ios' ? 20 : 0,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderTopWidth: 2,
    borderTopColor: '#2563EB',
    paddingTop: 10,
  },
  tabText: {
    fontSize: 12,
    color: '#6B7280',
  },
  activeTabText: {
    color: '#2563EB',
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: '#F59E0B',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pendingBadge: {
    backgroundColor: '#FEF3C7',
  },
  inProgressBadge: {
    backgroundColor: '#DBEAFE',
  },
  completedBadge: {
    backgroundColor: '#D1FAE5',
  },
  statusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  pendingText: {
    color: '#92400E',
  },
  inProgressText: {
    color: '#1E40AF',
  },
  completedText: {
    color: '#065F46',
  },
  taskScope: {
    fontSize: 13,
    color: '#6B7280',
    marginTop: 2,
    fontStyle: 'italic',
  },
  basicTaskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  miniStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 4,
  },
  miniStatusText: {
    fontSize: 10,
    fontWeight: '500',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 9000,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1001,
  },
  expandedCardContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 9999,
    justifyContent: 'center',
    alignItems: 'center',
    // Dodaję padding dla lepszego umieszczenia karty na ekranie
    padding: Platform.OS === 'web' ? 0 : 16,
  },
  expandedCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    // Zmniejszam margin dla mobilnych urządzeń aby zachować miejsce na zawartość
    margin: Platform.OS === 'web' ? 10 : 0,
    marginTop: Platform.OS === 'android' ? 50 : 80,
    marginBottom: Platform.OS === 'android' ? 50 : 80,
    flex: 1,
    // Zmniejszam szerokość na urządzeniach mobilnych dla lepszego odstępu od krawędzi
    width: Platform.OS === 'web' ? '80%' : '90%',
    maxHeight: Platform.OS === 'web' ? '80%' : '85%',
    elevation: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.5,
    shadowRadius: 12,
  },
  expandedContent: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    flex: 1, // Dodaję flex: 1, aby zawartość była w stanie rozszerzyć się
    paddingBottom: 5, // Dodaję padding na dole
  },
  debugButtons: {
    position: 'absolute',
    top: 80,
    left: 10,
    right: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 10000,
  },
  debugButton: {
    backgroundColor: '#ff5252',
    padding: 8,
    borderRadius: 4,
    marginHorizontal: 5,
  },
  debugButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  activeEmployeesCount: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563EB',
    marginLeft: 4,
  },
  expandedActiveEmployeesRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  expandedActiveEmployees: {
    fontSize: 13,
    color: '#3498db',
    fontWeight: 'bold',
    marginLeft: 4,
  },
  basicActiveEmployees: {
    fontSize: 10,
    fontWeight: '500',
    color: '#2563EB',
    marginLeft: 4,
  },
  activeEmployeesBasicRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  personIcon: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  personHead: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#6B7280',
  },
  personBody: {
    width: 12,
    height: 6,
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    backgroundColor: '#6B7280',
    marginTop: 1,
  },
  activeEmployeesWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(37, 99, 235, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginTop: 4,
    alignSelf: 'flex-start',
  },
  refreshButton: {
    padding: 8,
    borderRadius: 18,
    backgroundColor: 'rgba(37, 99, 235, 0.1)',
  },
  completedTaskName: {
    color: '#6B7280',
  },
  checkmarkContainer: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 6,
    flexShrink: 0,
  },
  checkmarkIcon: {
    width: 16,
    height: 16,
  },
  quickActionsCardNew: {
    width: '100%',
    padding: 15,
    marginBottom: 10,
  },
  quickActionsGridNew: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
    justifyContent: 'space-between',
  },
  quickActionButtonNew: {
    width: '48%',
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  quickActionButtonDisabled: {
    opacity: 0.6,
  },
  quickActionIconNew: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  quickActionTextNew: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  quickActionTextDisabled: {
    fontSize: 14,
    fontWeight: '500',
    color: '#9CA3AF',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    width: screenWidth > 600 ? 500 : '90%',
    maxHeight: '80%',
  },
  alertContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  alertTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  alertMessage: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  alertInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 5,
    padding: 10,
    width: '100%',
    marginBottom: 20,
  },
  alertButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  alertButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    backgroundColor: '#2563EB',
    minWidth: 100,
    alignItems: 'center',
  },
  alertCancelButton: {
    backgroundColor: '#6B7280',
  },
  alertButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  alertCancelButtonText: {
    color: 'white',
  },
  activeEmployeesCountLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563EB',
  },
  activeEmployeesDuration: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563EB',
  },
  activeEmployeesTime: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2563EB',
  },
  cardCompact: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 6,
    height: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2.5,
    elevation: 3,
    position: 'relative',
    overflow: 'hidden',
  },
  cardsContainerCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    height: '48%',
    width: '100%',
  },
  quickActionsContainerCompact: {
    width: '100%',
    height: 70,
    backgroundColor: '#F8FAFC',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    position: 'absolute', // Zostawiamy position: absolute
    bottom: 0, // Zostawiamy bottom: 0
    left: 0,
    right: 0,
    zIndex: 10, // Zostawiamy wyższy z-index
  },
  quickActionsCardCompact: {
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent',
    borderRadius: 0, 
    padding: 5,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  quickActionsRowNew: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    flexWrap: 'nowrap',
  },
  quickActionButtonRow: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 2,
    paddingVertical: 5,
    minWidth: 0,
  },
  quickActionIconRow: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  quickActionTextRow: {
    fontSize: 10,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    width: '100%',
    paddingHorizontal: 1,
  },
  comingSoonText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  backButton: {
    backgroundColor: '#2563EB',
    padding: 10,
    borderRadius: 5,
    marginTop: 20,
    alignItems: 'center',
  },
  backButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  tasksCardsContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginBottom: 10,
    width: '100%',
  },
  horizontalCardsScroll: {
    width: '100%',
  },
  horizontalCardsContent: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingHorizontal: 0,
  },
  horizontalCard: {
    width: Platform.OS === 'web' ? screenWidth * 0.47 - 24 : undefined,
    marginRight: 10,
    marginBottom: 0,
  },
  dotsIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
    marginBottom: 4,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(203, 213, 225, 0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: '#2563EB',
  },
  horizontalScrollContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  horizontalScroll: {
    flex: 1,
    height: '100%',
  },
  horizontalScrollContent: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingHorizontal: 0,
  },
  horizontalCardContainer: {
    height: '100%',
    borderRadius: 16,
    marginHorizontal: 10,
  },
  navButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  navButtonLeft: {
    left: 2,
    position: 'absolute',
    top: '50%',
    marginTop: 10,
  },
  navButtonRight: {
    right: 2,
    position: 'absolute',
    top: '50%',
    marginTop: 10,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  cardContent: {
    flex: 1,
    padding: 10,
    paddingHorizontal: 8,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    position: 'relative',
    width: '100%',
  },
  basicEmployeeName: {
    fontSize: 13,
    fontWeight: '500',
    color: '#1F2937',
    flex: 1,
    paddingRight: 0,
    maxWidth: '100%',
  },
  dateContainer: {
    minWidth: 65,
    alignItems: 'flex-end',
    paddingRight: 4,
    flexShrink: 0,
  },
  basicEmployeeDuration: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'right',
    flexShrink: 0,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#D1D5DB',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: '#2563EB',
  },
  paginationDotTouchable: {
    padding: 8,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#60A5FA',
    marginRight: 6,
    flexShrink: 0,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F97316',
    marginRight: 6,
    flexShrink: 0,
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  detailsText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 8,
  },
  expandedCardOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 9000,
  },
  expandedCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Platform.OS === 'web' ? 10 : 15,
    backgroundColor: '#3B82F6',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  expandedCardTitle: {
    fontSize: Platform.OS === 'web' ? 18 : 20,
    fontWeight: 'bold',
    color: 'white',
  },
  expandedCardContent: {
    padding: Platform.OS === 'web' ? 10 : 15,
  },
  expandedCardItem: {
    marginBottom: 10,
    padding: Platform.OS === 'web' ? 10 : 15,
    backgroundColor: 'white',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  expandedCardItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  expandedCardItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1A1A1A',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 12,
  },
  topBarContainer: {
    height: '5%',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    backgroundColor: '#1E40AF',
  },
  dateText: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 'auto',
    paddingLeft: 5,
    flexShrink: 0,
    minWidth: 80,
    textAlign: 'right',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalBody: {
    padding: 16,
    maxHeight: 500,
  },
  reportItem: {
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  reportHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
    marginRight: 8,
  },
  reportStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  reportStatusText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  reportDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  reportDetailText: {
    fontSize: 14,
    color: '#4B5563',
    marginLeft: 8,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  maintenanceExpandedCard: {
    width: '90%',
    maxWidth: 500,
  },
  cardBackgroundContainer: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    overflow: 'hidden',
    zIndex: -1,
  },
  cardBackgroundCircle1: {
    position: 'absolute',
    top: '10%',
    right: '10%',
    width: '120%', 
    height: '120%',
    borderRadius: 200,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    transform: [{ scale: 1.2 }],
  },
  cardBackgroundCircle2: {
    position: 'absolute',
    top: '-10%',
    right: '30%',
    width: '70%', 
    height: '70%',
    borderRadius: 200,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
  },
  cardBackgroundCircle3: {
    position: 'absolute',
    top: '40%',
    right: '-20%',
    width: '90%', 
    height: '90%',
    borderRadius: 200,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  priorityText: {
    fontSize: 12,
    color: 'white',
    fontWeight: '500',
  },
  reportDetailSectionContent: {
    fontSize: 14,
    color: '#4B5563',
    marginTop: 4,
  },
  reportActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  reportActionButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  reportActionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: '#1E40AF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  menuButton: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#6B7280',
  },
  detailsContainer: {
    padding: 16,
  },
  reportDetailCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  reportDetailHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reportDetailTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  reportDetailSection: {
    marginBottom: 8,
  },
  reportDetailSectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6B7280',
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  backText: {
    fontSize: 16,
    color: '#1A1A1A',
    fontWeight: 'bold',
  },
  pageTitle: {
    fontSize: 20,
    color: '#1F2937',
    fontWeight: 'bold',
  },
  section: {
    marginBottom: 20,
  },
  taskCard: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  clientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginRight: 5,
  },
  infoText: {
    fontSize: 14,
    color: '#4B5563',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  primaryButton: {
    backgroundColor: '#3B82F6',
  },
  successButton: {
    backgroundColor: '#10B981',
  },
  dangerButton: {
    backgroundColor: '#DC2626',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 10,
    backgroundColor: '#1E40AF',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  topBarTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
  },
  topBarButton: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#6B7280',
  },
  priorityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  modalOverlayFixed: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  alertContainerFixed: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: '80%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  rootContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#f5f7fa',
  },
  
  persistentSidebar: {
    width: 260,
    height: '100%',
    backgroundColor: 'white',
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
    paddingTop: 0, 
    flexDirection: 'column',
    justifyContent: 'space-between', 
  },
  
  contentWithSidebar: {
    marginLeft: 0, 
    width: '100%', 
  },
  
  sidebarQuickActionsContainer: {
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: 10,
    marginTop: 'auto',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB', 
  },
  quickActionsTitleSidebar: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#4B5563',
    marginBottom: 8,
  },
  quickActionsContentSidebar: {
    flexDirection: 'column',
  },
  quickActionButtonSidebar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  quickActionIconSidebar: {
    width: 28,
    height: 28,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  quickActionTextSidebar: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  itemInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    flexWrap: 'wrap', // Pozwala na zawijanie tekstu
  },
  itemInfoText: {
    fontSize: 12,
    color: '#6B7280',
    flex: 1,
    flexWrap: 'wrap', // Pozwala na zawijanie tekstu
  },
  selectedMenuItem: {
    backgroundColor: '#EBF5FF',
    borderRadius: 8,
  },
  selectedMenuItemText: {
    color: '#2563EB',
    fontWeight: '500',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    paddingHorizontal: 16,
    marginTop: 6,
  },
  drawerContent: {
    flex: 1,
    backgroundColor: 'white',
    paddingTop: Platform.OS === 'ios' ? 10 : 0,
    flexDirection: 'column',
  },
  drawerHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
  },
  menuItems: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 10,
    paddingBottom: 10,
    marginTop: 'auto',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
});

export default Dashboard; 
