-- Kod SQL do dodania kolumny photo_urls do tabeli purchases jako alternatywy dla kolumny photos
-- Wykonaj ten kod w SQL Editor w konsoli Supabase

-- Ten skrypt dodaje alternatywną kolumnę do przechowywania linków do zdjęć
-- bez indeksu GIN, co pozwala uniknąć problemów z przekroczeniem maksymalnego rozmiaru wiersza indeksu

-- 1. Dodanie kolumny photo_urls jako tablicy tekstowej (array of text) do przechowywania URLi zdjęć
ALTER TABLE purchases 
ADD COLUMN IF NOT EXISTS photo_urls TEXT[] DEFAULT '{}';

-- 2. Dodanie komentarza do kolumny
COMMENT ON COLUMN purchases.photo_urls IS 'Tablica URLi do zdjęć produktów - alternatywa dla kolumny photos bez indeksu GIN';

-- 3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> czy kolumna została pomyślnie dodana
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'purchases' AND column_name = 'photo_urls'
    ) THEN
        RAISE NOTICE 'Kolumna photo_urls została pomyślnie dodana do tabeli purchases.';
    ELSE
        RAISE NOTICE 'Uwaga: Nie udało się dodać kolumny photo_urls do tabeli purchases!';
    END IF;
END $$;

-- 4. Opcjonalnie: przepisanie danych z kolumny photos do photo_urls (jeśli potrzebne)
-- Ten krok jest opcjonalny i może być wykonany jeśli chcemy zachować istniejące dane
UPDATE purchases
SET photo_urls = photos
WHERE photos IS NOT NULL AND array_length(photos, 1) > 0;

-- 5. Sprawdzenie czy dane zostały przepisane
SELECT 
    id, 
    array_length(photos, 1) AS photos_count, 
    array_length(photo_urls, 1) AS photo_urls_count
FROM 
    purchases
WHERE 
    photos IS NOT NULL AND array_length(photos, 1) > 0
LIMIT 10;

-- 6. Dodanie informacji o wykonaniu skryptu
SELECT NOW() AS czas_wykonania, 'Dodanie kolumny photo_urls do tabeli purchases' AS operacja; 