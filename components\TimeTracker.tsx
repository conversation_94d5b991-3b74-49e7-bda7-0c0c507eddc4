import React, { useState } from 'react';
import { View, Text, Button } from 'react-native';
import { supabase } from '../services/supabaseClient';

const TimeTracker = () => {
  const [isWorking, setIsWorking] = useState(false);

  const handleStartShift = async () => {
    setIsWorking(true);
    const { error } = await supabase.from('work_sessions').insert([{ start_time: new Date() }]);
    if (error) console.error('Error starting shift:', error.message);
  };

  const handleStopShift = async () => {
    setIsWorking(false);
    const { error } = await supabase.from('work_sessions').update({ end_time: new Date() }).match({ is_working: true });
    if (error) console.error('Error stopping shift:', error.message);
  };

  return (
    <View>
      <Text>{isWorking ? 'Working...' : 'Not Working'}</Text>
      <Button title={isWorking ? 'Stop Shift' : 'Start Shift'} onPress={isWorking ? handleStopShift : handleStartShift} />
    </View>
  );
};

export default TimeTracker; 