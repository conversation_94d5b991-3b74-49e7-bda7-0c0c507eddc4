import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.23.0';
import { corsHeaders } from "../_shared/cors.ts";

// Funkcja pomocnicza do obsługi CORS preflight
function handleCors(req: Request) {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
}

// Konfiguracja Stripe
const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
if (!stripeApiKey) {
  throw new Error('Brakujący klucz API Stripe (STRIPE_SECRET_KEY)');
}

const stripe = new Stripe(stripeApiKey, {
  apiVersion: '2022-11-15',
  httpClient: Stripe.createFetchHttpClient(),
});

// Funkcja do anulowania subskrypcji w Stripe
serve(async (req) => {
  // Obsługa CORS dla zapytań preflight
  const corsResponse = handleCors(req);
  if (corsResponse) return corsResponse;

  try {
    // Sprawdź metodę HTTP
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Metoda nie jest dozwolona' }), 
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Parsowanie danych z żądania
    const { companyId, atPeriodEnd = true } = await req.json();

    // Walidacja danych wejściowych
    if (!companyId) {
      return new Response(
        JSON.stringify({ error: 'Brakujący parametr companyId' }), 
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Inicjalizacja klienta Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Brakujące zmienne środowiskowe Supabase');
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Pobierz aktywną subskrypcję firmy
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from('company_subscriptions')
      .select('*')
      .eq('company_id', companyId)
      .eq('status', 'active');
    
    if (subscriptionError) {
      return new Response(
        JSON.stringify({ error: `Błąd pobierania subskrypcji: ${subscriptionError.message}` }), 
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    if (!subscriptions || subscriptions.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Nie znaleziono aktywnej subskrypcji' }), 
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    const subscription = subscriptions[0];

    if (!subscription.stripe_subscription_id) {
      return new Response(
        JSON.stringify({ error: 'Brak identyfikatora subskrypcji Stripe' }), 
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    try {
      // Anuluj subskrypcję w Stripe
      const updatedStripeSubscription = await stripe.subscriptions.update(
        subscription.stripe_subscription_id,
        {
          cancel_at_period_end: atPeriodEnd,
        }
      );

      // Aktualizuj status subskrypcji w bazie danych
      const { error: updateError } = await supabase
        .from('company_subscriptions')
        .update({
          cancel_at_period_end: atPeriodEnd,
          status: updatedStripeSubscription.status,
          updated_at: new Date().toISOString()
        })
        .eq('id', subscription.id);
      
      if (updateError) {
        throw new Error(`Nie udało się zaktualizować statusu subskrypcji: ${updateError.message}`);
      }
      
      // Jeśli anulujemy natychmiast (nie na koniec okresu), zaktualizuj status firmy na 'free'
      if (!atPeriodEnd) {
        const { error: companyUpdateError } = await supabase
          .from('companies')
          .update({
            account_type: 'free',
            updated_at: new Date().toISOString()
          })
          .eq('id', companyId);
        
        if (companyUpdateError) {
          console.error('Błąd aktualizacji statusu firmy:', companyUpdateError);
        }
      }
      
      // Zapisz zdarzenie anulowania w bazie danych
      await supabase.from('subscription_events').insert({
        company_id: companyId,
        event_type: 'subscription_canceled',
        stripe_subscription_id: subscription.stripe_subscription_id,
        metadata: {
          cancel_at_period_end: atPeriodEnd,
          canceled_at: updatedStripeSubscription.canceled_at,
        },
      });
      
      // Zwróć sukces wraz z zaktualizowaną subskrypcją
      return new Response(
        JSON.stringify({ 
          success: true,
          subscription: {
            id: subscription.id,
            status: updatedStripeSubscription.status,
            cancel_at_period_end: updatedStripeSubscription.cancel_at_period_end,
            current_period_end: new Date(updatedStripeSubscription.current_period_end * 1000).toISOString()
          }
        }), 
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    } catch (stripeError) {
      console.error('Błąd Stripe:', stripeError);
      return new Response(
        JSON.stringify({ 
          error: 'Błąd podczas anulowania subskrypcji w Stripe',
          details: stripeError instanceof Error ? stripeError.message : 'Nieznany błąd'
        }), 
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
  } catch (error) {
    console.error('Błąd anulowania subskrypcji:', error);
    const errorMessage = error instanceof Error ? error.message : 'Nieznany błąd';
    return new Response(
      JSON.stringify({ error: `Błąd serwera: ${errorMessage}` }), 
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
}); 