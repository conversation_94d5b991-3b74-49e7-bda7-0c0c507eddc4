import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Platform, ScrollView, SafeAreaView, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface DrawerMenuProps {
  isVisible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  footer?: React.ReactNode;
  hasBottomNav?: boolean;
}

const DrawerMenu = ({ isVisible, onClose, children, footer, hasBottomNav = false }: DrawerMenuProps) => {
  const translateX = React.useRef(new Animated.Value(300)).current;
  const opacity = React.useRef(new Animated.Value(0)).current;
  const windowHeight = Dimensions.get('window').height;
  const bottomNavHeight = hasBottomNav ? (Platform.OS === 'android' ? 56 : 80) : 0;

  React.useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: 300,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <View style={[styles.container, { height: windowHeight }]}>  
      <Animated.View
        style={[
          styles.overlay,
          {
            opacity,
            backgroundColor: 'rgba(0, 0, 0, 0.2)',
          },
        ]}
      >
        <TouchableOpacity
          style={styles.overlayTouch}
          activeOpacity={1}
          onPress={onClose}
        />
      </Animated.View>
      
      <Animated.View
        style={[
          styles.drawer,
          {
            transform: [{ translateX }],
            height: windowHeight,
          },
        ]}
      >
        <View style={[styles.drawerContent, { marginBottom: bottomNavHeight }]}>
          <View style={styles.scrollContainer}>
            <ScrollView 
              showsVerticalScrollIndicator={false}
              bounces={false}
            >
              {children}
            </ScrollView>
          </View>
          {footer && (
            <View style={styles.footer}>
              {footer}
            </View>
          )}
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 9999,
    elevation: 9999,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 9998,
  },
  overlayTouch: {
    flex: 1,
  },
  drawer: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 300,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: -2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 9999,
    zIndex: 9999,
  },
  drawerContent: {
    flex: 1,
    flexDirection: 'column',
  },
  scrollContainer: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 10 : 0,
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
});

export default DrawerMenu; 