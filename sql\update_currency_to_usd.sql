-- Aktualizuj domy<PERSON>ln<PERSON> walutę w tabeli payment_history
ALTER TABLE payment_history ALTER COLUMN currency SET DEFAULT 'usd';

-- Aktualizuj istniejące rekordy z PLN na USD
UPDATE payment_history SET currency = 'usd' WHERE currency = 'pln';

-- Aktualizuj funkcje pomocnicze aby używały USD
UPDATE subscription_plans SET price = price WHERE active = true; -- Trigger update

-- Sprawdź aktualne ceny
SELECT name, price/100 as price_usd, billing_period FROM subscription_plans WHERE active = true ORDER BY name;

-- Sprawdź payment_history
SELECT currency, COUNT(*) as count FROM payment_history GROUP BY currency;
