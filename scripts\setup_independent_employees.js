const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

const supabaseUrl = 'https://bqjjlxqzlpjjkqzqzqzq.supabase.co';
const supabaseKey = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxampseHF6bHBqamtxenF6cXpxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzQxMzI5NCwiZXhwIjoyMDQ4OTg5Mjk0fQ.Ej8XQJBHgoGVTKWKOdStjhiOJme_1Ej8XQJBHgoGVTKW';

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupIndependentEmployees() {
  try {
    console.log('🔧 Konfigurowanie systemu niezależnych pracowników...');
    
    // Wczytaj i wykonaj SQL
    const sqlPath = path.join(__dirname, '..', 'sql', 'convert_employee_to_independent.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📝 Wykonywanie SQL...');
    const { data, error } = await supabase.rpc('execute_sql', { 
      sql_query: sql 
    });
    
    if (error) {
      console.error('❌ Błąd wykonania SQL:', error);
      return;
    }
    
    console.log('✅ Funkcje SQL utworzone pomyślnie');
    
    // Sprawdź czy funkcje zostały utworzone
    console.log('\n🔍 Sprawdzanie utworzonych funkcji...');
    const { data: functions, error: functionsError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT routine_name, routine_type
        FROM information_schema.routines 
        WHERE routine_name IN (
          'convert_employee_to_independent',
          'is_independent_employee', 
          'get_independent_employees'
        )
        AND routine_schema = 'public'
        ORDER BY routine_name;
      `
    });
    
    if (!functionsError && functions) {
      console.log('Utworzone funkcje:');
      functions.forEach(func => {
        console.log(`- ${func.routine_name} (${func.routine_type})`);
      });
    }
    
    // Sprawdź czy są już jacyś niezależni pracownicy
    console.log('\n👥 Sprawdzanie istniejących niezależnych pracowników...');
    const { data: independentEmployees, error: independentError } = await supabase.rpc('get_independent_employees');
    
    if (!independentError) {
      if (independentEmployees && independentEmployees.length > 0) {
        console.log(`Znaleziono ${independentEmployees.length} niezależnych pracowników:`);
        independentEmployees.forEach(emp => {
          console.log(`- ${emp.full_name} (${emp.email})`);
        });
      } else {
        console.log('Brak niezależnych pracowników w systemie');
      }
    }
    
    // Sprawdź strukturę tabeli employees
    console.log('\n📋 Sprawdzanie struktury tabeli employees...');
    const { data: columns, error: columnsError } = await supabase.rpc('execute_sql', { 
      sql_query: `
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_name = 'employees' 
        AND column_name IN ('company_id', 'verification_code', 'role')
        ORDER BY column_name;
      `
    });
    
    if (!columnsError && columns) {
      console.log('Kolumny tabeli employees:');
      columns.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
    }
    
    console.log('\n🎯 Konfiguracja zakończona!');
    console.log('\nTeraz przycisk "Usuń pracownika" będzie:');
    console.log('1. Przekształcać pracownika w niezależnego (company_id = NULL)');
    console.log('2. Zwalniać kod weryfikacyjny');
    console.log('3. Ustawiać role = "independent"');
    console.log('4. Usuwać powiązania z firmą');
    
  } catch (err) {
    console.error('❌ Błąd konfiguracji:', err.message);
  }
}

setupIndependentEmployees();
