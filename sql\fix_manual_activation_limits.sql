-- <PERSON><PERSON><PERSON> funkcji can_use_verification_code - uwzględnienie limitów dla firm z aktywną subskrypcją
-- Problem: Firmy z aktywną subskrypcją mogły aktywować nieograniczoną liczbę pracowników

-- 1. <PERSON><PERSON><PERSON> funkcję can_use_verification_code
CREATE OR REPLACE FUNCTION can_use_verification_code(
  p_code TEXT,
  p_company_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  code_exists BOOLEAN;
  code_used BOOLEAN;
  has_active_sub BOOLEAN;
  employee_limit INTEGER;
  active_employees_count INTEGER;
BEGIN
  -- Sprawd<PERSON> czy kod istnieje
  SELECT EXISTS(
    SELECT 1 FROM verification_codes 
    WHERE code = p_code
  ) INTO code_exists;
  
  IF NOT code_exists THEN
    RETURN FALSE;
  END IF;
  
  -- Sprawd<PERSON> czy kod nie jest już użyty
  SELECT EXISTS(
    SELECT 1 FROM verification_codes 
    WHERE code = p_code 
    AND employee_id IS NOT NULL
  ) INTO code_used;
  
  IF code_used THEN
    RETURN FALSE;
  END IF;
  
  -- <PERSON><PERSON><PERSON><PERSON><PERSON> czy firma ma aktywną subskrypcję
  SELECT company_has_active_subscription(p_company_id) INTO has_active_sub;
  
  -- Pobierz limit pracowników i aktualną liczbę
  SELECT verification_code_limit INTO employee_limit
  FROM companies WHERE id = p_company_id;
  
  SELECT COUNT(*) INTO active_employees_count
  FROM employees 
  WHERE company_id = p_company_id 
  AND subscription_status = 'ACTIVE';
  
  -- ZAWSZE sprawdź limit - nawet dla firm z aktywną subskrypcją
  -- Firmy z subskrypcją mają wyższe limity, ale nadal mają limity
  RETURN active_employees_count < employee_limit;
END;
$$ LANGUAGE plpgsql;

-- 2. Sprawdź obecną sytuację firm z przekroczonymi limitami
SELECT 
  c.id,
  c.name,
  c.account_type,
  c.verification_code_limit as limit,
  COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') as active_employees,
  COUNT(e.id) FILTER (WHERE e.subscription_status = 'SUBSCRIPTION_EXPIRED') as expired_employees,
  COUNT(e.id) as total_employees,
  CASE 
    WHEN COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') > c.verification_code_limit 
    THEN 'PRZEKROCZONY LIMIT'
    ELSE 'OK'
  END as status,
  CASE 
    WHEN EXISTS(
      SELECT 1 FROM company_subscriptions cs 
      WHERE cs.company_id = c.id 
      AND cs.status = 'active' 
      AND cs.current_period_end > NOW()
    ) THEN 'AKTYWNA'
    ELSE 'BRAK'
  END as subscription_status
FROM companies c
LEFT JOIN employees e ON c.id = e.company_id
GROUP BY c.id, c.name, c.account_type, c.verification_code_limit
HAVING COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') > 0
ORDER BY active_employees DESC;

-- 3. Funkcja do naprawy firm z przekroczonymi limitami (tylko jeśli przekraczają)
CREATE OR REPLACE FUNCTION fix_companies_with_exceeded_limits_v2()
RETURNS TABLE(
  company_id UUID,
  company_name TEXT,
  plan_type TEXT,
  limit_value INTEGER,
  active_before INTEGER,
  deactivated_count INTEGER,
  active_after INTEGER,
  action_taken TEXT
) AS $$
DECLARE
  company_rec RECORD;
  employees_to_deactivate INTEGER;
  deactivated INTEGER;
BEGIN
  FOR company_rec IN
    SELECT 
      c.id,
      c.name,
      c.account_type,
      c.verification_code_limit as limit,
      COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') as active_count
    FROM companies c
    LEFT JOIN employees e ON c.id = e.company_id
    GROUP BY c.id, c.name, c.account_type, c.verification_code_limit
    HAVING COUNT(e.id) FILTER (WHERE e.subscription_status = 'ACTIVE') > c.verification_code_limit
  LOOP
    employees_to_deactivate := company_rec.active_count - company_rec.limit;
    
    -- Dezaktywuj nadmiarowych pracowników (ostatnio aktywowanych)
    UPDATE employees
    SET subscription_status = 'SUBSCRIPTION_EXPIRED',
        last_status_change = NOW()
    WHERE id IN (
      SELECT id FROM employees
      WHERE company_id = company_rec.id
      AND subscription_status = 'ACTIVE'
      ORDER BY last_status_change DESC NULLS LAST
      LIMIT employees_to_deactivate
    );
    
    GET DIAGNOSTICS deactivated = ROW_COUNT;
    
    -- Zwróć wyniki
    company_id := company_rec.id;
    company_name := company_rec.name;
    plan_type := company_rec.account_type;
    limit_value := company_rec.limit;
    active_before := company_rec.active_count;
    deactivated_count := deactivated;
    active_after := company_rec.active_count - deactivated;
    action_taken := format('Dezaktywowano %s nadmiarowych pracowników', deactivated);
    
    RETURN NEXT;
  END LOOP;
  
  -- Jeśli nie ma firm do naprawy
  IF NOT FOUND THEN
    company_id := NULL;
    company_name := 'Brak firm do naprawy';
    plan_type := '';
    limit_value := 0;
    active_before := 0;
    deactivated_count := 0;
    active_after := 0;
    action_taken := 'Wszystkie firmy mają prawidłową liczbę aktywnych pracowników';
    RETURN NEXT;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 4. Wyświetl instrukcje
SELECT 'INSTRUKCJE:' as info;
SELECT '1. Sprawdź firmy z przekroczonymi limitami powyżej' as step;
SELECT '2. Jeśli chcesz naprawić automatycznie, uruchom: SELECT * FROM fix_companies_with_exceeded_limits_v2();' as step;
SELECT '3. Sprawdź wyniki ponownie po naprawie' as step;
SELECT '4. Przetestuj ręczną aktywację pracowników - powinna być ograniczona limitami' as step;
