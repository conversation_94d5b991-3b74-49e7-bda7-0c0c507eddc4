// Modern theme configuration
const colors = {
  primary: {
    light: '#3B82F6',
    main: '#2563EB',
    dark: '#1D4ED8',
  },
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
  success: {
    light: '#10B981',
    main: '#059669',
    dark: '#047857',
  },
  warning: {
    light: '#FBBF24',
    main: '#F59E0B',
    dark: '#D97706',
  },
  error: {
    light: '#EF4444',
    main: '#DC2626',
    dark: '#B91C1C',
  },
  background: {
    default: '#FFFFFF',
    paper: '#F3F4F6',
    dark: '#111827',
  },
};

const shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.30,
    shadowRadius: 4.65,
    elevation: 8,
  },
};

const gradients: {
  primary: readonly [string, string];
  secondary: readonly [string, string];
  success: readonly [string, string];
  warning: readonly [string, string];
  error: readonly [string, string];
} = {
  primary: ['#2563EB', '#1D4ED8'],
  secondary: ['#3B82F6', '#2563EB'],
  success: ['#10B981', '#059669'],
  warning: ['#FBBF24', '#F59E0B'],
  error: ['#EF4444', '#DC2626'],
};

const typography = {
  h1: {
    fontSize: 32,
    fontWeight: '700',
  },
  h2: {
    fontSize: 24,
    fontWeight: '600',
  },
  h3: {
    fontSize: 20,
    fontWeight: '600',
  },
  body1: {
    fontSize: 16,
    fontWeight: '400',
  },
  body2: {
    fontSize: 14,
    fontWeight: '400',
  },
  caption: {
    fontSize: 12,
    fontWeight: '400',
  },
};

const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

const theme = {
  colors,
  shadows,
  gradients,
  typography,
  spacing,
  borderRadius,
};

export default theme; 