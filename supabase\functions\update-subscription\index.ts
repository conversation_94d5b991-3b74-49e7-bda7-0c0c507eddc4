import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import Stripe from 'https://esm.sh/stripe@12.5.0?target=deno';

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { subscriptionId, newPriceId } = await req.json();

    if (!subscriptionId || !newPriceId) {
      throw new Error('Brak wymaganych parametrów');
    }

    // Pobierz aktualną subskrypcję
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Znajdź ID elementu subskrypcji do aktualizacji
    const subscriptionItemId = subscription.items.data[0].id;

    // Zaktualizuj subskrypcję z nowym planem
    const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      items: [{
        id: subscriptionItemId,
        price: newPriceId,
      }],
      proration_behavior: 'create_prorations', // Sprawiedliwe naliczanie opłat za zmianę
    });

    return new Response(
      JSON.stringify({
        success: true,
        subscription: updatedSubscription,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    );
  }
}); 