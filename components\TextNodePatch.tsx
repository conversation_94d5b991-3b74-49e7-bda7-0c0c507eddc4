import React, { useEffect } from 'react';
import { Text } from 'react-native';

/**
 * This file contains patches and utilities to fix the "Unexpected text node" errors
 * in the Dashboard component. It provides functions to safely handle text nodes.
 */

/**
 * Apply this patch to Dashboard.tsx by importing it at the top of the file:
 * 
 * import { patchConsoleLog } from './TextNodePatch';
 * 
 * Then call it early in the Dashboard component:
 * 
 * useEffect(() => {
 *   patchConsoleLog();
 *   return () => unpatchConsoleLog();
 * }, []);
 */

/**
 * Funkcja patchująca konsolę, aby ignorować ostrzeżenia związane z renderowaniem tekstu
 * oraz inne problematyczne ostrzeżenia.
 */
export function patchConsoleLog() {
  const originalLog = console.log;
  const originalError = console.error;
  const originalWarn = console.warn;
  
  // Funkcja pomocnicza do sprawdzania, czy wiadomość dotyczy problemu tekstowego
  const isTextNodeWarning = (message: string) => {
    return (
      typeof message === 'string' &&
      (message.includes('Text strings must be rendered within a <Text> component') ||
       message.includes('Unexpected text node') ||
       message.includes('useInsertionEffect must not schedule updates') ||
       message.includes('text node in an array'))
    );
  };

  console.log = function safeLog(...args: any[]) {
    if (args.length > 0 && isTextNodeWarning(args[0])) {
      // Ignoruj problematyczne ostrzeżenia
      return;
        }
    originalLog.apply(console, args);
  };
  
  console.error = function safeError(...args: any[]) {
    if (args.length > 0 && isTextNodeWarning(args[0])) {
      // Ignoruj problematyczne błędy
      return;
    }
    originalError.apply(console, args);
  };
  
  console.warn = function safeWarn(...args: any[]) {
    if (args.length > 0 && isTextNodeWarning(args[0])) {
      // Ignoruj problematyczne ostrzeżenia
      return;
    }
    originalWarn.apply(console, args);
  };

  return () => {
    console.log = originalLog;
    console.error = originalError;
    console.warn = originalWarn;
  };
}

/**
 * Funkcja przywracająca oryginalne zachowanie konsoli
 */
export function unpatchConsoleLog() {
  // Funkcja jest wywoływana w cleanup patcha
}

/**
 * Bardziej agresywny patch dla React Native, który zastępuje potencjalnie problematyczne 
 * metody i zapobiega wyświetlaniu błędów związanych z renderowaniem tekstu
 */
export const InsertionEffectPatch: React.FC = () => {
  useEffect(() => {
    // Patch dla console.log
    const unpatchConsole = patchConsoleLog();

    // Patch dla React.useInsertionEffect
    // Zachowujemy referencję do oryginalnej metody
    const originalUseInsertionEffect = React.useInsertionEffect;
    
    // Sprawdzamy czy metoda istnieje zanim spróbujemy ją zastąpić
    if (typeof React.useInsertionEffect === 'function') {
      try {
        // @ts-ignore - celowo nadpisujemy tę metodę
        React.useInsertionEffect = React.useEffect;
      } catch (e) {
        console.log('Failed to patch React.useInsertionEffect:', e);
      }
    }

    // Patch dla Animated - jeśli jest dostępny
    try {
      const RN = require('react-native');
      if (RN.Animated) {
        // Patchujemy najczęściej problematyczne metody
        const patchAnimatedMethod = (methodName: string) => {
          if (RN.Animated[methodName] && typeof RN.Animated[methodName] === 'function') {
            const originalMethod = RN.Animated[methodName];
            RN.Animated[methodName] = function(...args: any[]) {
              try {
                return originalMethod.apply(this, args);
              } catch (e) {
                if (e instanceof Error && 
                    (e.message.includes('Text strings must be rendered') || 
                     e.message.includes('useInsertionEffect'))) {
                  // Ignorujemy błędy związane z tekstem i useInsertionEffect
                  console.log(`Prevented error in Animated.${methodName}`);
                  return null;
                }
                throw e;
              }
            };
          }
        };

        // Patchujemy najczęściej problematyczne metody
        ['timing', 'spring', 'decay', 'event'].forEach(patchAnimatedMethod);
      }
    } catch (e) {
      console.log('Failed to patch Animated:', e);
    }

    // Zwracamy funkcję czyszczącą, która przywraca oryginalne zachowanie
    return () => {
      unpatchConsole();
      
      // Sprawdzamy czy metoda istnieje zanim spróbujemy ją przywrócić
      if (typeof React.useInsertionEffect === 'function') {
        // @ts-ignore - przywracamy oryginalną metodę, jeśli istniała
        if (originalUseInsertionEffect) {
          React.useInsertionEffect = originalUseInsertionEffect;
        }
      }
    };
  }, []);

  // Komponent nie renderuje nic, służy tylko do aplikowania patcha
  return null;
};

// AutoWrapText - komponent zapasowy, który zawsze opakuje tekst w <Text>
export const AutoWrapText: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Jeśli dzieci są prostym tekstem, opakowujemy je w <Text>
  if (typeof children === 'string' || typeof children === 'number') {
    return <Text>{children}</Text>;
  }
  
  // W przeciwnym razie zwracamy oryginalne dzieci
  return <>{children}</>;
};

export default InsertionEffectPatch; 