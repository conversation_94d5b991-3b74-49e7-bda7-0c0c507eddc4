import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Platform, StatusBar } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { i18n } from '../utils/localization';
import { MaintenanceReport } from './Dashboard'; // Import the interface from Dashboard

// Calculate the status bar height based on platform
const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 0;

interface MaintenanceReportDetailsProps {
  report: MaintenanceReport;
  onBack: () => void;
  onCancelReport: (reportId: string) => void;
  onStartRepair: (reportId: string) => void;
  onCompleteRepair: (reportId: string) => void;
  onFinalizeRepair: (reportId: string) => void;
  getPriorityColor: (priority: string) => string;
  getPriorityText: (priority: string) => string;
  getMaintenanceStatusText: (status: string) => string;
  formatDateDDMMYY: (dateString?: string) => string;
  formatTime: (dateString?: string) => string;
  hasParentSafeArea?: boolean;
}

export const MaintenanceReportDetails: React.FC<MaintenanceReportDetailsProps> = ({
  report,
  onBack,
  onCancelReport,
  onStartRepair,
  onCompleteRepair,
  onFinalizeRepair,
  getPriorityColor,
  getPriorityText,
  getMaintenanceStatusText,
  formatDateDDMMYY,
  formatTime,
  hasParentSafeArea = false
}) => {
  const [showPhotoView, setShowPhotoView] = useState(false);
  const [selectedPhotoUrl, setSelectedPhotoUrl] = useState<string | null>(null);

  const handlePhotoPress = (photoUrl: string) => {
    setSelectedPhotoUrl(photoUrl);
    setShowPhotoView(true);
  };

  const closePhotoView = () => {
    setShowPhotoView(false);
    setSelectedPhotoUrl(null);
  };

  // Simple photo viewer without Modal or animations
  const renderPhotoViewer = () => {
    if (!showPhotoView || !selectedPhotoUrl) return null;
    
    return (
      <View style={styles.photoViewerContainer}>
        <View style={styles.photoViewerContent}>
          <TouchableOpacity style={styles.closeButton} onPress={closePhotoView}>
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>
          <Image 
            source={{ uri: selectedPhotoUrl }} 
            style={styles.fullSizeImage} 
            resizeMode="contain"
          />
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Status bar space - always render */}
      <View style={[styles.statusBarSpace, { height: STATUSBAR_HEIGHT }]} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('maintenanceDetailsHeader')}</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Content */}
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <View style={styles.section}>
          <Text style={styles.title}>{report.title}</Text>
          
          <View style={styles.metaRow}>
            <View style={[styles.badge, { backgroundColor: getPriorityColor(report.priority) }]}>
              <Text style={styles.badgeText}>{getPriorityText(report.priority)}</Text>
            </View>
            
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>{getMaintenanceStatusText(report.status)}</Text>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="location-outline" size={18} color="#4B5563" />
            <Text style={styles.detailText}>{report.location || i18n.t('maintenanceUnknownLocation')}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="person-outline" size={18} color="#4B5563" />
            <Text style={styles.detailText}>{report.reported_by_name || i18n.t('maintenanceUnknownUser')}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={18} color="#4B5563" />
            <Text style={styles.detailText}>
              {formatDateDDMMYY(report.created_at)} o {formatTime(report.created_at)}
            </Text>
          </View>
          
          {report.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionLabel}>{i18n.t('maintenanceDescriptionLabel')}:</Text>
              <Text style={styles.descriptionText}>{report.description}</Text>
            </View>
          )}
          
          {report.photos && report.photos.length > 0 && (
            <View style={styles.photosContainer}>
              <Text style={styles.photosLabel}>{i18n.t('maintenancePhotosLabel')}:</Text>
              <View style={styles.photosGrid}>
                {report.photos.map((photo, index) => (
                  <TouchableOpacity 
                    key={index} 
                    style={styles.photoThumbnail}
                    onPress={() => handlePhotoPress(photo)}
                  >
                    <Image source={{ uri: photo }} style={styles.thumbnailImage} />
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
          
          {/* Additional information based on status */}
          {report.status === 'in_progress' && (
            <View style={styles.statusInfoContainer}>
              <Text style={styles.statusInfoLabel}>{i18n.t('maintenanceInProgressLabel')}</Text>
              {report.repaired_by_name && (
                <View style={styles.detailRow}>
                  <MaterialIcons name="engineering" size={18} color="#4B5563" />
                  <Text style={styles.detailText}>{i18n.t('maintenanceRepairedByLabel')}: {report.repaired_by_name}</Text>
                </View>
              )}
              {report.repair_start_time && (
                <View style={styles.detailRow}>
                  <Ionicons name="time-outline" size={18} color="#4B5563" />
                  <Text style={styles.detailText}>
                    {i18n.t('maintenanceRepairStartLabel')}: {formatDateDDMMYY(report.repair_start_time)} o {formatTime(report.repair_start_time)}
                  </Text>
                </View>
              )}
            </View>
          )}
          
          {report.status === 'resolved' && (
            <View style={styles.statusInfoContainer}>
              <Text style={styles.statusInfoLabel}>{i18n.t('maintenanceResolvedLabel')}</Text>
              {report.resolved_by_name && (
                <View style={styles.detailRow}>
                  <MaterialIcons name="engineering" size={18} color="#4B5563" />
                  <Text style={styles.detailText}>{i18n.t('maintenanceResolvedByLabel')}: {report.resolved_by_name}</Text>
                </View>
              )}
              {report.resolved_time && (
                <View style={styles.detailRow}>
                  <Ionicons name="time-outline" size={18} color="#4B5563" />
                  <Text style={styles.detailText}>
                    {i18n.t('maintenanceResolvedTimeLabel')}: {formatDateDDMMYY(report.resolved_time)} o {formatTime(report.resolved_time)}
                  </Text>
                </View>
              )}
            </View>
          )}
          
          {report.status === 'canceled' && (
            <View style={styles.statusInfoContainer}>
              <Text style={styles.statusInfoLabel}>{i18n.t('maintenanceCanceledLabel')}</Text>
              {report.canceled_by_name && (
                <View style={styles.detailRow}>
                  <Ionicons name="person-outline" size={18} color="#4B5563" />
                  <Text style={styles.detailText}>{i18n.t('maintenanceCanceledByLabel')}: {report.canceled_by_name}</Text>
                </View>
              )}
              {report.canceled_time && (
                <View style={styles.detailRow}>
                  <Ionicons name="time-outline" size={18} color="#4B5563" />
                  <Text style={styles.detailText}>
                    {i18n.t('maintenanceCanceledTimeLabel')}: {formatDateDDMMYY(report.canceled_time)} o {formatTime(report.canceled_time)}
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
        
        {/* Action buttons */}
        <View style={styles.actionsContainer}>
          {report.status === 'reported' && (
            <>
              <TouchableOpacity 
                style={styles.actionButton} 
                onPress={() => onStartRepair(report.id)}
              >
                <Text style={styles.actionButtonText}>{i18n.t('maintenanceStartRepairButton')}</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.actionButton, styles.cancelButton]} 
                onPress={() => onCancelReport(report.id)}
              >
                <Text style={styles.cancelButtonText}>{i18n.t('maintenanceCancelButton')}</Text>
              </TouchableOpacity>
            </>
          )}
          
          {report.status === 'in_progress' && (
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={() => onCompleteRepair(report.id)}
            >
              <Text style={styles.actionButtonText}>{i18n.t('maintenanceCompleteRepairButton')}</Text>
            </TouchableOpacity>
          )}
          
          {report.status === 'to_check' && (
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={() => onFinalizeRepair(report.id)}
            >
              <Text style={styles.actionButtonText}>{i18n.t('maintenanceFinalizeButton')}</Text>
            </TouchableOpacity>
          )}
          
          {report.status === 'pending' && (
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={() => onFinalizeRepair(report.id)}
            >
              <Text style={styles.actionButtonText}>{i18n.t('maintenanceFinalizeButton')}</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
      
      {/* Photo viewer overlay */}
      {renderPhotoViewer()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  statusBarSpace: {
    width: '100%',
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: 'white',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 12,
  },
  metaRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  badge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    marginRight: 8,
  },
  badgeText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 12,
  },
  statusBadge: {
    backgroundColor: '#E5E7EB',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    color: '#4B5563',
    fontWeight: '500',
    fontSize: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    marginLeft: 8,
    color: '#4B5563',
    fontSize: 14,
  },
  descriptionContainer: {
    marginTop: 16,
  },
  descriptionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  descriptionText: {
    color: '#4B5563',
    fontSize: 14,
    lineHeight: 20,
  },
  photosContainer: {
    marginTop: 16,
  },
  photosLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  photoThumbnail: {
    width: 80,
    height: 80,
    margin: 4,
    borderRadius: 4,
    overflow: 'hidden',
    backgroundColor: '#F3F4F6',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  photoViewerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  photoViewerContent: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 1001,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
  fullSizeImage: {
    width: '90%',
    height: '70%',
    backgroundColor: 'transparent',
  },
  statusInfoContainer: {
    marginTop: 16,
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 8,
  },
  statusInfoLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  actionsContainer: {
    marginTop: 24,
  },
  actionButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelButton: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#EF4444',
  },
  cancelButtonText: {
    color: '#EF4444',
    fontWeight: 'bold',
    fontSize: 16,
  },
  photosScrollView: {
    flexGrow: 0,
  },
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
}); 