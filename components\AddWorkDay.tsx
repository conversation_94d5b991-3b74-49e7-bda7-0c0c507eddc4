import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ScrollView,
  Alert,
  TextInput,
  Modal,
  Dimensions,
  ActivityIndicator,
  Keyboard,
  TouchableWithoutFeedback,
  KeyboardAvoidingView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../services/supabaseClient';
import DatePicker from './DatePicker';
import TimePicker from './TimePicker';
import { i18n } from '../utils/localization';

interface AddWorkDayProps {
  userId: string;
  companyId: string;
  onBack: () => void;
  onMenuPress?: () => void;
  onSessionSelect?: (sessionId: string, date: string, employeeId: string, employeeName: string) => void;
}

// Interface for custom alert buttons
interface AlertButton {
  text: string;
  onPress: () => void;
  style?: 'cancel' | 'default';
}

export const AddWorkDay = ({ userId, companyId, onBack, onMenuPress, onSessionSelect }: AddWorkDayProps) => {
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0]);
  const [startTime, setStartTime] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  const [jobOrder, setJobOrder] = useState('');
  const [loading, setLoading] = useState(false);
  const [calculatedDuration, setCalculatedDuration] = useState<number | null>(null);
  
  // Custom Alert state - used for mobile
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [alertButtons, setAlertButtons] = useState<AlertButton[]>([]);
  
  // Check if we're on web platform
  const isWeb = Platform.OS === 'web';

  // For web: create alert container once when component mounts
  useEffect(() => {
    if (isWeb) {
      // Remove any existing alert container
      const existingContainer = document.getElementById('custom-alert-container');
      if (existingContainer) {
        document.body.removeChild(existingContainer);
      }

      // Create a new alert container
      const alertContainer = document.createElement('div');
      alertContainer.id = 'custom-alert-container';
      alertContainer.style.display = 'none';
      alertContainer.style.position = 'fixed';
      alertContainer.style.top = '0';
      alertContainer.style.left = '0';
      alertContainer.style.width = '100%';
      alertContainer.style.height = '100%';
      alertContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      alertContainer.style.zIndex = '9999';
      alertContainer.style.display = 'flex';
      alertContainer.style.justifyContent = 'center';
      alertContainer.style.alignItems = 'center';
      alertContainer.style.visibility = 'hidden';
      
      document.body.appendChild(alertContainer);
      
      // Clean up on unmount
      return () => {
        if (alertContainer && alertContainer.parentNode) {
          alertContainer.parentNode.removeChild(alertContainer);
        }
      };
    }
  }, [isWeb]);

  // Effect to recalculate duration whenever time or date fields change
  useEffect(() => {
    calculateDuration();
  }, [startTime, endTime, date, endDate]);

  // Show web alert (direct DOM manipulation)
  const showWebAlert = useCallback((title: string, message: string, buttons: AlertButton[]) => {
    if (!isWeb) return;
    
    const container = document.getElementById('custom-alert-container');
    if (!container) return;
    
    // Clear previous content
    container.innerHTML = '';
    container.style.visibility = 'visible';
    
    // Create alert dialog
    const alertBox = document.createElement('div');
    alertBox.style.backgroundColor = 'white';
    alertBox.style.borderRadius = '8px';
    alertBox.style.width = '90%';
    alertBox.style.maxWidth = '400px';
    alertBox.style.overflow = 'hidden';
    // Apply global font for all text inside the alert
    alertBox.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif';
    
    // Title
    const titleDiv = document.createElement('div');
    titleDiv.style.padding = '16px';
    titleDiv.style.textAlign = 'center';
    
    const titleText = document.createElement('h3');
    titleText.textContent = title;
    titleText.style.margin = '0';
    titleText.style.fontSize = '18px';
    titleText.style.fontWeight = 'bold';
    titleText.style.color = 'black';
    
    titleDiv.appendChild(titleText);
    alertBox.appendChild(titleDiv);
    
    // Message
    const messageDiv = document.createElement('div');
    messageDiv.style.padding = '0 16px 16px 16px';
    messageDiv.style.textAlign = 'center';
    
    const messageText = document.createElement('p');
    messageText.textContent = message;
    messageText.style.margin = '0';
    messageText.style.fontSize = '16px';
    messageText.style.color = 'black';
    messageText.style.lineHeight = '22px';
    
    messageDiv.appendChild(messageText);
    alertBox.appendChild(messageDiv);
    
    // Buttons
    const buttonDiv = document.createElement('div');
    buttonDiv.style.display = 'flex';
    buttonDiv.style.padding = '16px';
    buttonDiv.style.justifyContent = 'center';
    
    // Add buttons
    buttons.forEach((button, index) => {
      const btnElement = document.createElement('button');
      btnElement.textContent = button.text;
      btnElement.style.flex = '1';
      btnElement.style.padding = '12px';
      btnElement.style.borderRadius = '4px';
      btnElement.style.border = 'none';
      btnElement.style.margin = '0 4px';
      btnElement.style.color = 'white';
      btnElement.style.fontSize = '16px';
      btnElement.style.fontWeight = '500';
      btnElement.style.cursor = 'pointer';
      btnElement.style.fontFamily = 'inherit'; // Inherit font family from parent
      
      // Set button color based on style
      if (button.style === 'cancel') {
        btnElement.style.backgroundColor = '#6B7280';
      } else {
        btnElement.style.backgroundColor = '#2563EB';
      }
      
      // Add click handler
      btnElement.addEventListener('click', () => {
        container.style.visibility = 'hidden';
        if (button.onPress) {
          setTimeout(() => button.onPress(), 10);
        }
      });
      
      buttonDiv.appendChild(btnElement);
    });
    
    alertBox.appendChild(buttonDiv);
    container.appendChild(alertBox);
    
  }, [isWeb]);

  // Alert function that works differently based on platform
  const showAlert = useCallback((
    title: string, 
    message: string, 
    buttons: AlertButton[]
  ) => {
    // For web, use custom web alert
    if (isWeb) {
      showWebAlert(title, message, buttons);
    } 
    // For mobile, use our custom React Native alert
    else {
      setAlertTitle(title);
      setAlertMessage(message);
      setAlertButtons(buttons);
      setAlertVisible(true);
    }
  }, [isWeb, showWebAlert]);

  const hideCustomAlert = useCallback(() => {
    setAlertVisible(false);
  }, []);

  const createDateTimeFromDateAndTime = (dateStr: string, timeDate: Date): Date => {
    const year = parseInt(dateStr.split('-')[0], 10);
    const month = parseInt(dateStr.split('-')[1], 10) - 1; // miesiące są 0-based
    const day = parseInt(dateStr.split('-')[2], 10);
    
    return new Date(
      year,
      month,
      day,
      timeDate.getHours(),
      timeDate.getMinutes(),
      0,
      0
    );
  };

  // Format duration in hours and minutes
  const formatDuration = (minutes?: number) => {
    if (!minutes && minutes !== 0) return i18n.t('noData');
    
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    return `${hours}h ${mins}min`;
  };

  // Calculate duration between start and end times
  const calculateDuration = () => {
    try {
      // Create date objects with the selected date and times
      const startDateTime = createDateTimeFromDateAndTime(date, startTime);
      const endDateTime = createDateTimeFromDateAndTime(endDate, endTime);
      
      // Calculate difference in milliseconds
      const diffMs = endDateTime.getTime() - startDateTime.getTime();
      
      // If end time is before start time and dates are the same
      if (diffMs < 0 && date === endDate) {
        // Add 24 hours if same date but end time is earlier
        const endDateNextDay = new Date(endDateTime);
        endDateNextDay.setDate(endDateNextDay.getDate() + 1);
        const diffMsNextDay = endDateNextDay.getTime() - startDateTime.getTime();
        const durationMinutes = Math.floor(diffMsNextDay / (1000 * 60));
        setCalculatedDuration(durationMinutes);
        return durationMinutes;
      } else {
        const durationMinutes = Math.floor(diffMs / (1000 * 60));
        setCalculatedDuration(durationMinutes);
        return durationMinutes;
      }
    } catch (error) {
      console.error('Error calculating duration:', error);
      setCalculatedDuration(null);
      return null;
    }
  };

  const handleSubmit = async () => {
    if (!date || !endDate || !startTime || !endTime || !jobOrder.trim()) {
      showAlert(
        i18n.t('confirmation'), 
        i18n.t('fillAllRequiredFields'),
        [{ text: i18n.t('ok'), onPress: () => {}, style: 'default' }]
      );
      return;
    }

    try {
      // Tworzenie pełnych dat z wybraną datą i czasami
      const startDateTime = createDateTimeFromDateAndTime(date, startTime);
      const endDateTime = createDateTimeFromDateAndTime(endDate, endTime);

      if (endDateTime <= startDateTime) {
        showAlert(
          i18n.t('confirmation'),
          i18n.t('endTimeMustBeLater'),
          [{ text: i18n.t('ok'), onPress: () => {}, style: 'default' }]
        );
        return;
      }

      setLoading(true);

      // Oblicz czas trwania w minutach
      const durationMinutes = Math.round((endDateTime.getTime() - startDateTime.getTime()) / (1000 * 60));

      const workSessionData = {
        employee_id: userId,
        company_id: companyId,
        job_order: jobOrder,
        start_time: startDateTime.toISOString(),
        end_time: endDateTime.toISOString(),
        duration_minutes: durationMinutes,
        status: 'completed', // Domyślnie status jako zakończony
        added_manually: true // Oznacz, że to ręczny wpis, a nie automatyczny start/stop
      };

      console.log('Sending work session data:', workSessionData);

      // Najpierw insert bez select
      const { error } = await supabase
        .from('work_sessions')
        .insert(workSessionData);

      if (error) {
        console.error('Error adding work day:', error);
        
        showAlert(
          i18n.t('errorMessage'),
          `${i18n.t('errorSavingSession')}: ${error.message}`,
          [{ text: i18n.t('ok'), onPress: () => {}, style: 'default' }]
        );
      } else {
        // Po udanym insercie, osobno pobieramy dodaną sesję
        // Pobieramy na podstawie employee_id, start_time i end_time, które są unikalne dla tej sesji
        const { data: insertedData, error: fetchError } = await supabase
          .from('work_sessions')
          .select('*')
          .eq('employee_id', userId)
          .eq('start_time', startDateTime.toISOString())
          .eq('end_time', endDateTime.toISOString())
          .order('created_at', { ascending: false })
          .limit(1);
        
        if (fetchError || !insertedData || insertedData.length === 0) {
          console.error('Error fetching added work day:', fetchError);
          // Mimo błędu w pobraniu, insert się udał, więc pokazujemy sukces
          showAlert(
            i18n.t('success'),
            i18n.t('workDayAdded'),
            [{ text: i18n.t('ok'), onPress: () => onBack(), style: 'default' }]
          );
        } else {
          console.log('Successfully added work day:', insertedData);
          
          showAlert(
            i18n.t('success'),
            i18n.t('workDayAdded'),
            [
              { 
                text: i18n.t('ok'), 
                onPress: () => {
                  // Po pomyślnym dodaniu sesji, przejdź do widoku szczegółów
                  if (insertedData && insertedData[0] && onSessionSelect) {
                    const createdSession = insertedData[0];
                    const sessionDate = new Date(createdSession.start_time).toISOString().split('T')[0];
                    
                    // Pobierz informacje o użytkowniku, aby uzyskać nazwę
                    supabase.auth.getUser().then(({ data: userData }) => {
                      const userName = userData?.user?.user_metadata?.full_name || 'Pracownik';
                      
                      onSessionSelect(
                        createdSession.id, 
                        sessionDate, 
                        userId, 
                        userName
                      );
                    }).catch(err => {
                      console.error('Error getting user data:', err);
                      // Fallback - użyj podstawowych danych
                      onSessionSelect(
                        createdSession.id, 
                        sessionDate, 
                        userId, 
                        'Pracownik'
                      );
                    });
                  } else {
                    // Fallback - jeśli nie ma danych sesji lub funkcji onSessionSelect, wróć do dashboardu
                    onBack();
                  }
                },
                style: 'default'
              }
            ]
          );
        }
      }
    } catch (error: any) {
      console.error('Exception in handleSubmit:', error);
      
      showAlert(
        i18n.t('errorMessage'),
        `${i18n.t('unexpectedError')}: ${error.message || 'Unknown error'}`,
        [{ text: i18n.t('ok'), onPress: () => {}, style: 'default' }]
      );
    } finally {
      setLoading(false);
    }
  };

  // Custom alert component for mobile
  const CustomAlert = () => {
    return (
      <Modal
        visible={alertVisible}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{alertTitle}</Text>
            <Text style={styles.modalMessage}>{alertMessage}</Text>
            <View style={styles.buttonContainer}>
              {alertButtons.map((button, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.alertButton,
                    button.style === 'cancel' ? styles.cancelButton : styles.defaultButton,
                    alertButtons.length === 1 ? { flex: 1 } : { flex: 1 / alertButtons.length }
                  ]}
                  onPress={() => {
                    hideCustomAlert();
                    button.onPress();
                  }}
                >
                  <Text 
                    style={[
                      styles.buttonText,
                      button.style === 'cancel' ? styles.cancelButtonText : styles.defaultButtonText
                    ]}
                  >
                    {button.text}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={22} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{i18n.t('addWorkDayTitle')}</Text>
        <View style={styles.placeholderView} />
      </View>
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.formScrollContent}>
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('dateLabel')}</Text>
          <DatePicker
            date={date}
            onDateChange={(date: string) => setDate(date)}
            placeholder={i18n.t('selectDate')}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('startTimeInput')}</Text>
          <TimePicker
            time={startTime}
            onTimeChange={(time: Date) => setStartTime(time)}
            placeholder={i18n.t('chooseTime')}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('endDateLabel')}</Text>
          <DatePicker
            date={endDate}
            onDateChange={(date: string) => setEndDate(date)}
            placeholder={i18n.t('selectDate')}
          />
        </View>
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('endTimeInput')}</Text>
          <TimePicker
            time={endTime}
            onTimeChange={(time: Date) => setEndTime(time)}
            placeholder={i18n.t('chooseTime')}
          />
        </View>
        
        {calculatedDuration !== null && (
          <Text style={styles.calculatedDuration}>
            {`${i18n.t('calculatedWorkTime')} ${formatDuration(calculatedDuration)}`}
          </Text>
        )}
        
        <View style={styles.formInputGroup}>
          <Text style={styles.formLabel}>{i18n.t('jobName')}</Text>
          <TextInput
            style={styles.formInput}
            placeholder={i18n.t('jobNamePlaceholder')}
            value={jobOrder}
            onChangeText={setJobOrder}
                onBlur={() => {
                  if (Platform.OS === 'ios') {
                    Keyboard.dismiss();
                  }
                }}
                returnKeyType="done"
                onSubmitEditing={Keyboard.dismiss}
          />
          <Text style={styles.formFieldNote}>
            {i18n.t('descriptionPlaceholder')}
          </Text>
        </View>
        
        <TouchableOpacity
          style={[
            styles.formSubmitButton,
            loading && styles.formSubmitButtonDisabled
          ]}
              onPress={() => {
                Keyboard.dismiss();
                handleSubmit();
              }}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? i18n.t('loading') : i18n.t('submitAddWorkDay')}
          </Text>
        </TouchableOpacity>
      </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
      
      {/* Custom alert for mobile only */}
      {!isWeb && <CustomAlert />}
    </View>
  );
};

const { width: screenWidth } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    flex: 1,
    textAlign: 'center',
  },
  placeholderView: {
    width: 36, // Ta sama szerokość co backButton, aby tytuł był naprawdę na środku
  },
  scrollView: {
    flex: 1,
  },
  formScrollContent: {
    padding: 20,
    paddingHorizontal: 24,
    paddingBottom: 40,
    paddingTop: 10, // Zmniejszamy padding na górze, bo mamy już tytuł w headerze
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 24,
    marginTop: 10,
    display: 'none', // Ukrywamy, bo teraz tytuł jest w headerze
  },
  formInputGroup: {
    marginBottom: 24,
  },
  formLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 10,
  },
  formInput: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 14,
    fontSize: 14,
    color: '#1F2937',
  },
  formFieldNote: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    fontStyle: 'italic',
  },
  calculatedDuration: {
    fontSize: 14,
    color: '#4B5563',
    marginTop: -10,
    marginBottom: 20,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  formSubmitButton: {
    backgroundColor: '#2563EB',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 28,
    marginBottom: 40,
  },
  formSubmitButtonDisabled: {
    backgroundColor: '#93C5FD',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Custom alert styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    width: screenWidth > 600 ? 500 : '90%',
    maxWidth: 500,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 8,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    color: '#4B5563',
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  alertButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
    margin: 4,
  },
  defaultButton: {
    backgroundColor: '#2563EB',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  defaultButtonText: {
    color: 'white',
  },
  cancelButtonText: {
    color: '#4B5563',
  },
}); 