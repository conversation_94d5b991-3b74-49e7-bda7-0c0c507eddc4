-- Tworzenie tabeli do śledzenia zmian w subskrypcjach
CREATE TABLE subscription_changes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
    old_plan_id TEXT,
    new_plan_id TEXT,
    old_amount INTEGER,
    new_amount INTEGER,
    change_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>danie polityki RLS
ALTER TABLE subscription_changes ENABLE ROW LEVEL SECURITY;

-- Polityka dla właścicieli firm
CREATE POLICY "Company owners can view their subscription changes"
    ON subscription_changes
    FOR SELECT
    USING (
        auth.uid() IN (
            SELECT id 
            FROM employees 
            WHERE company_id = subscription_changes.company_id 
            AND role = 'OWNER'
        )
    );

-- <PERSON><PERSON> dla updated_at
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON subscription_changes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_set_timestamp(); 