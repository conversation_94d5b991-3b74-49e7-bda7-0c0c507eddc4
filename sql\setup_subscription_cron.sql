-- Us<PERSON>wi<PERSON>e cron job do sprawdzania wygasłych subskrypcji
-- Ten job będzie uruchamiany codziennie o 00:01

-- Naj<PERSON><PERSON><PERSON> sprawdź czy rozszerzenie pg_cron jest dostępne
-- W Supabase może być potrzebne włączenie tego rozszerzenia w Dashboard

-- Us<PERSON>ń istniejący job jeśli istnieje
SELECT cron.unschedule('check-expired-subscriptions');

-- <PERSON><PERSON><PERSON> nowy job
SELECT cron.schedule(
  'check-expired-subscriptions',
  '1 0 * * *', -- Codziennie o 00:01
  'SELECT check_and_deactivate_expired_subscriptions();'
);

-- Alternatywnie, jeśli pg_cron nie jest dostępne, można użyć tego zapytania ręcznie:
-- SELECT check_and_deactivate_expired_subscriptions();

-- Sprawd<PERSON> aktywne cron jobs
-- SELECT * FROM cron.job;
