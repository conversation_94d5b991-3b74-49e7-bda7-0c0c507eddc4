const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://eunekpxgtwvqlphtkczi.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV1bmVrcHhndHd2cWxwaHRrY3ppIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDMyMzg5NCwiZXhwIjoyMDU1ODk5ODk0fQ.QMrP8nnWexEaAz7skNfWo82Sg1RAMH-scjycxFciNig';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testWebhookFix() {
  try {
    console.log('Testing webhook fix by simulating subscription cancellation...');
    
    // Znajdź firmę z aktywną subskrypcją do testowania
    const { data: activeSubscriptions, error: subscriptionsError } = await supabase
      .from('company_subscriptions')
      .select(`
        id,
        company_id,
        status,
        companies!inner(id, name, account_type, verification_code_limit),
        subscription_plans!inner(id, name)
      `)
      .eq('status', 'active')
      .limit(1);

    if (subscriptionsError || !activeSubscriptions || activeSubscriptions.length === 0) {
      console.error('No active subscriptions found for testing:', subscriptionsError);
      return;
    }

    const testSubscription = activeSubscriptions[0];
    console.log(`Testing with company: ${testSubscription.companies.name} (${testSubscription.company_id})`);
    console.log(`Current account type: ${testSubscription.companies.account_type}`);
    console.log(`Current verification limit: ${testSubscription.companies.verification_code_limit}`);

    // Symuluj anulowanie subskrypcji
    console.log('\n1. Canceling subscription...');
    const { error: cancelError } = await supabase
      .from('company_subscriptions')
      .update({ status: 'canceled' })
      .eq('id', testSubscription.id);

    if (cancelError) {
      console.error('Error canceling subscription:', cancelError);
      return;
    }

    console.log('✓ Subscription canceled successfully');

    // Sprawdź czy firma została zaktualizowana do free
    console.log('\n2. Checking if company was updated to free plan...');
    
    // Poczekaj chwilę na trigger
    await new Promise(resolve => setTimeout(resolve, 1000));

    const { data: updatedCompany, error: companyError } = await supabase
      .from('companies')
      .select('id, name, account_type, verification_code_limit')
      .eq('id', testSubscription.company_id)
      .single();

    if (companyError) {
      console.error('Error fetching updated company:', companyError);
      return;
    }

    console.log(`Updated company data:`);
    console.log(`- Account type: ${updatedCompany.account_type}`);
    console.log(`- Verification limit: ${updatedCompany.verification_code_limit}`);

    if (updatedCompany.account_type === 'free' && updatedCompany.verification_code_limit === 2) {
      console.log('✓ SUCCESS: Company was correctly updated to free plan!');
    } else {
      console.log('❌ FAILED: Company was not updated correctly');
      
      // Spróbuj ręcznie zaktualizować używając RPC
      console.log('\n3. Manually updating using RPC function...');
      const { data: rpcResult, error: rpcError } = await supabase
        .rpc('update_company_account_type_rpc', {
          p_company_id: testSubscription.company_id,
          p_plan_name: 'free'
        });

      if (rpcError) {
        console.error('RPC function failed:', rpcError);
      } else {
        console.log('✓ RPC function succeeded:', rpcResult);
        
        // Sprawdź ponownie
        const { data: finalCompany, error: finalError } = await supabase
          .from('companies')
          .select('id, name, account_type, verification_code_limit')
          .eq('id', testSubscription.company_id)
          .single();

        if (!finalError) {
          console.log(`Final company data:`);
          console.log(`- Account type: ${finalCompany.account_type}`);
          console.log(`- Verification limit: ${finalCompany.verification_code_limit}`);
        }
      }
    }

    // Przywróć subskrypcję do stanu aktywnego dla dalszych testów
    console.log('\n4. Restoring subscription to active state...');
    const { error: restoreError } = await supabase
      .from('company_subscriptions')
      .update({ status: 'active' })
      .eq('id', testSubscription.id);

    if (restoreError) {
      console.error('Error restoring subscription:', restoreError);
    } else {
      console.log('✓ Subscription restored to active state');
      
      // Przywróć typ konta
      const { error: restoreRpcError } = await supabase
        .rpc('update_company_account_type_rpc', {
          p_company_id: testSubscription.company_id,
          p_plan_name: testSubscription.subscription_plans.name.toLowerCase()
        });

      if (restoreRpcError) {
        console.error('Error restoring company account type:', restoreRpcError);
      } else {
        console.log('✓ Company account type restored');
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testWebhookFix();
