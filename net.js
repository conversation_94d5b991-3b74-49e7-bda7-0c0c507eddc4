// Prosty polyfill dla modułu net, potrzebny dla biblioteki ws (WebSocket)
const net = {
  Socket: function() {
    return {
      connect: () => this,
      on: () => this,
      end: () => this,
      destroy: () => this,
      setTimeout: () => this,
      setKeepAlive: () => this,
      setNoDelay: () => this
    };
  },
  
  createServer: () => ({
    listen: () => ({}),
    on: () => ({}),
    close: () => ({})
  }),
  
  connect: () => {
    const socket = new net.Socket();
    return socket;
  }
};

// Używamy module.exports dla lepszej kompatybilności z modułami CommonJS
module.exports = net; 