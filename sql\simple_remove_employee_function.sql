-- Uproszczona funkcja do usuwania pracownika z firmy (bez kolumny email)
-- Wykonaj w Supabase SQL Editor

CREATE OR REPLACE FUNCTION remove_employee_from_company(
  p_employee_id UUID,
  p_company_id UUID
)
RETURNS JSONB AS $$
DECLARE
  v_employee_name TEXT;
  v_verification_code TEXT;
BEGIN
  -- Spra<PERSON>dź czy pracownik należy do tej firmy
  IF NOT EXISTS (
    SELECT 1 FROM employees
    WHERE id = p_employee_id
    AND company_id = p_company_id
  ) THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Pracownik nie należy do tej firmy',
      'error_type', 'invalid_employee'
    );
  END IF;

  -- Po<PERSON>rz dane pracownika
  SELECT full_name, verification_code
  INTO v_employee_name, v_verification_code
  FROM employees
  WHERE id = p_employee_id;

  -- 1. Us<PERSON>ń powiązanie kodu weryfikacyjnego z pracownikiem
  IF v_verification_code IS NOT NULL THEN
    UPDATE verification_codes
    SET used_by_employee_id = NULL,
        used_at = NULL,
        is_used = false
    WHERE code = v_verification_code
    AND company_id = p_company_id;
    
    RAISE NOTICE 'Zwolniono kod weryfikacyjny % dla pracownika %', v_verification_code, v_employee_name;
  END IF;

  -- 2. Przekształć pracownika w niezależnego
  UPDATE employees
  SET 
    company_id = NULL,                    -- Usuń powiązanie z firmą
    verification_code = NULL,             -- Usuń kod weryfikacyjny
    subscription_status = 'ACTIVE',       -- Niezależni pracownicy są aktywni
    last_status_change = NOW(),
    last_manual_status_change = NULL      -- Wyzeruj ograniczenia czasowe
  WHERE id = p_employee_id;

  RETURN jsonb_build_object(
    'success', true,
    'message', 'Pracownik "' || COALESCE(v_employee_name, 'Nieznany') || '" został usunięty z firmy i przekształcony w niezależnego pracownika',
    'alert_type', 'success',
    'employee_name', v_employee_name,
    'freed_verification_code', v_verification_code
  );

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Błąd podczas usuwania pracownika: ' || SQLERRM,
      'error_type', 'database_error'
    );
END;
$$ LANGUAGE plpgsql;

-- Funkcja do sprawdzania czy pracownik jest niezależny
CREATE OR REPLACE FUNCTION is_independent_employee(
  p_employee_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM employees
    WHERE id = p_employee_id
    AND company_id IS NULL
  );
END;
$$ LANGUAGE plpgsql;

-- Dodaj kolumnę employee_type jeśli nie istnieje
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS employee_type TEXT DEFAULT 'COMPANY' 
CHECK (employee_type IN ('COMPANY', 'INDEPENDENT'));

-- Sprawdź strukturę tabeli employees
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'employees' 
AND column_name IN ('company_id', 'verification_code', 'full_name', 'email', 'employee_type')
ORDER BY column_name;
