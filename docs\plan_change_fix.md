# Rozwiązanie problemu dezaktywacji pracowników podczas zmiany planu na iOS

## Problem
Gdy użytkownik zmienia plan subskrypcji na iOS, system niepotrzebnie dezaktywuje wszystkich pracowników firmy. Dzieje się tak, ponieważ zmiana planu była błędnie interpretowana jako anulowanie subskrypcji.

## Przyczyna
1. **iOS App Store** - zmiana planu tworzy nową subskrypcję i anuluje starą
2. **Webhook `customer.subscription.updated`** - otrzymuje informację o `cancel_at_period_end: true`
3. **Błędna logika** - system interpretował to jako rzeczywiste anulowanie i dezaktywował pracowników

## Rozwiązanie

### 1. Poprawki w webhook handler (`smooth-handler`)

#### Wykrywanie zmiany planu
```typescript
// Sprawdź czy to jest zmiana planu
const currentPriceId = stripeSubscription.items.data[0]?.price?.id;
const isPlanChange = currentPriceId && subscriptionData.stripe_price_id && 
  currentPriceId !== subscriptionData.stripe_price_id;
```

#### Logika dezaktywacji
```typescript
// Jeśli subskrypcja jest oznaczona do anulowania i to nie jest zmiana planu
if (subscription.cancel_at_period_end && !isPlanChange) {
  // Dezaktywuj pracowników tylko przy rzeczywistym anulowaniu
}
```

#### Aktualizacja price_id
```typescript
// Jeśli to zmiana planu, zaktualizuj price_id
if (isPlanChange && currentPriceId) {
  updateData.stripe_price_id = currentPriceId;
}
```

### 2. Funkcje naprawcze SQL

#### `reactivate_employees_after_plan_change()`
- Reaktywuje pracowników dezaktywowanych w ciągu ostatnich 24 godzin
- Sprawdza czy firma ma aktywną subskrypcję
- Bezpieczne - działa tylko jeśli subskrypcja jest aktywna

#### `check_company_employees_status()`
- Sprawdza status pracowników i subskrypcji firmy
- Pomaga zdiagnozować problemy

### 3. Serwis naprawczy w aplikacji

#### `employeeRecoveryService.ts`
```typescript
// Automatyczna naprawa statusu
const result = await autoFixEmployeeStatus(companyId);

// Sprawdzenie czy naprawa jest potrzebna
const needed = await checkIfEmployeeRecoveryNeeded(companyId);
```

### 4. Przycisk naprawy w AdminPanel
- Dodany przycisk "Napraw status" w sekcji pracowników
- Automatycznie wykrywa i naprawia problemy
- Pokazuje komunikaty o wyniku operacji

## Jak używać

### Dla administratorów
1. **W aplikacji**: Kliknij przycisk "Napraw status" w sekcji pracowników
2. **SQL**: Wykonaj `SELECT reactivate_employees_after_plan_change('company-id');`

### Dla deweloperów
```sql
-- Sprawdź status firmy
SELECT check_company_employees_status('company-id');

-- Napraw status pracowników
SELECT reactivate_employees_after_plan_change('company-id');

-- Znajdź firmy z problemami
SELECT * FROM companies WHERE id IN (
  SELECT company_id FROM company_subscriptions 
  WHERE status = 'active'
) AND id IN (
  SELECT company_id FROM employees 
  WHERE subscription_status != 'ACTIVE'
);
```

## Zapobieganie w przyszłości

### Webhook handler
- ✅ Wykrywa zmiany planu vs rzeczywiste anulowania
- ✅ Aktualizuje `stripe_price_id` przy zmianie planu
- ✅ Loguje wydarzenia dla debugowania

### Monitoring
- ✅ Logi w `subscription_events` dla zmian planów
- ✅ Funkcje diagnostyczne
- ✅ Przycisk naprawy w interfejsie

## Testowanie

### Scenariusze testowe
1. **Zmiana planu na iOS** - pracownicy powinni pozostać aktywni
2. **Rzeczywiste anulowanie** - pracownicy powinni zostać dezaktywowani
3. **Naprawa statusu** - przycisk powinien reaktywować pracowników

### Weryfikacja
```sql
-- Przed zmianą planu
SELECT subscription_status, COUNT(*) FROM employees 
WHERE company_id = 'test-company' GROUP BY subscription_status;

-- Po zmianie planu
SELECT subscription_status, COUNT(*) FROM employees 
WHERE company_id = 'test-company' GROUP BY subscription_status;

-- Sprawdź logi
SELECT * FROM subscription_events 
WHERE company_id = 'test-company' 
ORDER BY created_at DESC LIMIT 5;
```

## Pliki zmienione

### Backend
- `supabase/functions/smooth-handler/index.ts` - główna logika webhook
- `sql/fix_plan_change_employee_deactivation.sql` - funkcje naprawcze

### Frontend
- `src/services/employeeRecoveryService.ts` - serwis naprawczy
- `components/AdminPanel.tsx` - przycisk naprawy

### Dokumentacja
- `docs/plan_change_fix.md` - ten dokument

## Status
✅ **Rozwiązane** - Problem z dezaktywacją pracowników podczas zmiany planu został naprawiony.
